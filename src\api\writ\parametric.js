import request from '@/utils/request'

//类型设置-列表
export function getClassifyList(query) {
    return request({
        url: '/document/template/classify/list',
        method: 'get',
        params: query,
        gateway: 'sign'
    })
}

//类型设置-选项
export function getClassifyOption(query) {
    return request({
        url: '/document/template/classify/getOptions',
        method: 'get',
        params: query,
        gateway: 'sign'
    })
}

//类型设置-创建
export function addClassify(data) {
    return request({
        url: '/document/template/classify/add',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}

//类型设置-修改
export function editClassify(data) {
    return request({
        url: '/document/template/classify/edit',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}

//类型设置-编辑状态
export function editStatus(data) {
    return request({
        url: '/document/template/classify/editStatus',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}

//参数变量-列表
export function getVariableList(query) {
    return request({
        url: '/document/template/variable/list',
        method: 'get',
        params: query,
        gateway: 'sign'
    })
}

//变量参数-创建
export function addVariable(data) {
    return request({
        url: '/document/template/variable/add',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}

//变量参数-编辑
export function editVariable(data) {
    return request({
        url: '/document/template/variable/edit',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}


//变量参数-编辑状态
export function editVariableStatus(data) {
    return request({
        url: '/document/template/variable/editStatus',
        method: 'post',
        data: data,
        gateway: 'sign'
    })
}

//文书管理查询已开启的参数信息
export function getOpenOptions(query) {
    return request({
        url: '/document/template/variable/getOpenOptions',
        method: 'get',
        params: query,
        gateway: 'sign'
    })
}