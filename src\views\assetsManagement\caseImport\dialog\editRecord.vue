<template>
  <div>
    <el-dialog title="编辑" v-model="open" @close="cancel">
      <el-row :gutter="20">
        <el-form :model="form" ref="formRef" :rules="rules" label-width="110px">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="packageName">
              <el-input v-model="form.packageName" placeholder="请输入项目名称" maxlength="20" show-word-limit clearable
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计算公式" prop="formulaId">
              <el-select v-model="form.formulaId" placeholder="请选择计算公式" clearable filterable :reserve-keyword="false"
                style="width: 100%">
                <el-option v-for="item in formulaList" :key="item.formulaId" :label="item.formulaName" :value="item.formulaId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目系数" prop="ratio">
              <NumberInput v-model="form.ratio" :decimals="2" placeholder="请输入项目系数"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案件回收日期" prop="recyclingDate">
              <el-date-picker v-model="form.recyclingDate" type="date" style="width: 100%" value-format="YYYY-MM-DD"
                placeholder="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用" prop="enableType">
              <el-switch v-model="form.enableType" :active-value="'启用'" :inactive-value="'禁用'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用日期" prop="useDateType">
              <el-radio-group v-model="form.useDateType" @change="changeUseDateType"  >
                <el-radio-button :label="'短期'">短期</el-radio-button>
                <el-radio-button :label="'长期'">长期</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.useDateType =='短期'">
            <el-form-item label="开始日期" prop="useBeginDate">
              <el-date-picker v-model="form.useBeginDate" type="date" style="width: 100%" value-format="YYYY-MM-DD" placeholder="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.useDateType == '短期'">
            <el-form-item label="结束日期" prop="useEndDate">
              <el-date-picker v-model="form.useEndDate" type="date" style="width: 100%" value-format="YYYY-MM-DD" placeholder="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
          
        </el-form>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submit">
            确 定
          </el-button>
          <el-button :loading="loading" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { editCaseInfo, getAssetDetail } from "@/api/assets/asset/asset";
import { fmlSelect } from "@/api/assetsManagement/formula";
const emit = defineEmits(["queryList"]);
const { proxy } = getCurrentInstance();

const open = ref(false);
const loading = ref(false);
const formulaList = ref([]);
const data = reactive({
  form: {
    id: undefined,
    packageName: undefined,
    formulaId: undefined,
    ratio: undefined,
    recyclingDate: undefined,
    enableType: '启用',
    useDateType: '短期',
    useBeginDate: undefined,
    useEndDate: undefined,
  },
  rules: {
    packageName: [
      { required: true, message: "请输入项目名称", trigger: "blur" },
    ],
    // accountId: [
    //   { required: true, message: "请选择回款开户账号", trigger: "blur" },
    // ],
    ratio: [
      { required: true, message: "请输入项目系数", trigger: "blur" },
    ],
    recyclingDate: [
      { required: true, message: "请选择案件回收日期", trigger: "blur" },
    ],
    formulaId: [
      { required: true, message: "请选择计算公式", trigger: "blur" },
    ],
    enableType: [
      { required: true, message: "请选择是否启用", trigger: "blur" },
    ],
    useDateType: [
      { required: true, message: "请选择使用日期", trigger: "blur" },
    ],
    useBeginDate: [
      { required: true, message: "请输入开始日期", trigger: "blur" },
    ],
    useEndDate: [
      { required: true, message: "请输入结束日期", trigger: "blur" },
    ],
  },
});
const { form, rules } = toRefs(data);


function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      editCaseInfo(form.value)
        .then((res) => {
          emit("queryList");
          cancel();
          proxy.$modal.msgSuccess("操作成功");
        })
        .catch((err) => {
          proxy.$modal.msgError(err.mssage);
        }).finally(() => {
          loading.value = false;
        });
    }
  });
}

// 获取计算公式
function getFmSelectData() {
  fmlSelect().then(res => {
    formulaList.value = res.data
  })
}

// 切换使用日期类型
function changeUseDateType(val) {
  if (val == '长期') {
    form.value.useBeginDate = undefined;
    form.value.useEndDate = undefined;
  }
}


function cancel() {
  proxy.$refs["formRef"].resetFields();
  open.value = false;
  loading.value = false;
  form.value = {
    id: undefined,
    packageName: undefined,
    formulaId: undefined,
    ratio: undefined,
    recyclingDate: undefined,
    enableType: '启用',
    useDateType: '短期',
    useBeginDate: undefined,
    useEndDate: undefined,
  }
}

// 打开弹窗
function opendialog(row) {
  getFmSelectData();
  getAssetDetail(row.id)
    .then((res) => {
      for (const key in form.value) {
        if (row.hasOwnProperty(key) && row[key]) {
          form.value[key] = row[key];
        }
      }
      open.value = true;
    })
    .catch((err) => {
      proxy.$modal.msgError(err.mssage);
    });
}

// 暴露方法给父组件调用
defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
.el-form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.el-form-item {
  font-weight: 700 !important;
}
</style>