<template>
    <div>
        <el-tabs v-model="activeTab">
            <el-tab-pane label="消费记录" name="0" v-if="checkPermi(['settlement:agentService:consumptionRecords'])"></el-tab-pane>
            <el-tab-pane label="充值记录" name="1" v-if="checkPermi(['settlement:agentService:rechargeRecord'])"></el-tab-pane>
        </el-tabs>
        <div class="mt10">
            <consumptionRecords v-if="activeTab == '0'"/>
            <rechargeRecord v-if="activeTab == '1'"/>
        </div>
    </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import consumptionRecords from "../agentService/table/consumptionRecords.vue"
import rechargeRecord from "../agentService/table/rechargeRecord.vue"

const activeTab = ref("0");

</script>

<style lang="scss" scoped></style>