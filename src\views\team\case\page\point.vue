<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="选择处置人员"></el-step>
        <el-step title="预览分案结果"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <!-- 指定分案 -->
    <div v-show="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef">
        <table class="my-table">
          <thead>
            <tr>
              <td colspan="4">搜索结果</td>
            </tr>
          </thead>
          <tbody class="text-center">
            <tr>
              <td>案件量</td>
              <td>总金额</td>
              <td>已分配</td>
              <td>未分配</td>
            </tr>
            <tr>
              <td>{{ queryRes.caseNum }}</td>
              <td>{{ numFilter(queryRes.caseMoney) }}</td>
              <td>{{ queryRes.unAssignedNum }}</td>
              <td>{{ queryRes.assignedNum }}</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td colspan="4">选择处置人员</td>
            </tr>
          </thead>
          <tbody class="text-center">
            <tr>
              <td colspan="4">
                <optTeam ref="optteamRef" :type="form.allocationMode" :queryRes="queryRes" :team="false"
                  :unAssigned="0" />
              </td>
            </tr>
          </tbody>
        </table>
      </el-form>

      <div class="text-center mt20">
        <el-button @click="toBack">返回</el-button>
        <el-button type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
      </div>
    </div>
    <!-- 下一步 -->
    <div v-show="stepActive === 1" class="step-item pt20">
      <div :style="`display:${isShowDivisionalLoad}`" class="preview-loading">
        <divisionalLoad ref="divisionalLoadRef" :scheduleName="`team-case-point-schedule-${route.query.time}`"
          :getData="getDivisionalLoadData" />
      </div>
      <div v-if="schedule == 100">
        <table class="my-table">
          <thead>
            <tr>
              <td colspan="4">搜索结果</td>
            </tr>
          </thead>
          <tbody class="text-center">
            <tr>
              <td>案件量</td>
              <td>总金额</td>
              <td>已分配</td>
              <td>未分配</td>
            </tr>
            <tr>
              <td>{{ queryRes.caseNum }}</td>
              <td>{{ numFilter(queryRes.caseMoney) }}</td>
              <td>{{ queryRes.unAssignedNum }}</td>
              <td>{{ queryRes.assignedNum }}</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td colspan="4">分案规则设置</td>
            </tr>
          </thead>
          <tbody class="text-left">
            <tr>
              <td colspan="2">分案方式：指定分案分案</td>
              <td colspan="2">分配模式：按案件数量分配</td>
            </tr>
            <tr>
              <td colspan="2">分案方式：所选择案件</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td colspan="4">预览分配结果</td>
            </tr>
          </thead>

          <tbody class="text-center">
            <tr>
              <td>工号</td>
              <td>处置人员姓名</td>
              <td>案件量</td>
              <td>金额</td>
            </tr>
            <tr v-if="previewData.data.length === 0">
              <td colspan="4">暂无可分配数据</td>
            </tr>
            <tr v-else v-for="item in previewData.data" :key="item.odvId">
              <td>{{ item.jobNumber }}</td>
              <td>{{ item.odvName }}</td>
              <td>{{ item.number }}</td>
              <td>{{ numFilter(item.money) }}</td>
            </tr>
          </tbody>
        </table>
        <div class="text-center mt20">
          <el-button @click="prevStep">上一步，选择处置人员</el-button>
          <el-button :loading="loading" type="primary" plain @click="submit">提交分案</el-button>
        </div>
      </div>

    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
      </div>
      <div class="text-center mt30">
        <el-button type="primary" @click="toBack">案件分配完成，返回</el-button>
      </div>
    </div>
  </div>
</template>
<script setup name="TeamPoint">
import optTeam from "@/components/CaseOptTeam";
import { selectCases, specifyDivisionalData, updateCase } from "@/api/team/team";

const { proxy } = getCurrentInstance();
const route = useRoute();
const stepActive = ref(0);
const loading = ref(false);
const schedule = ref(0)
const reqQuery = ref({});
const isShowDivisionalLoad = ref('none')
const queryRes = ref({
  caseIds: [],
  caseNum: 0,
  caseMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
});

const data = reactive({
  form: {
    allocationMode: 1,
  },
  rules: {
    allocationMode: [{ required: true, message: "请选择", trigger: "change" }],
  },
});

//预览数据
const previewData = ref({
  data: [],
});
//提交数据
const submitData = ref({});
const { form, rules } = toRefs(data);

onMounted(() => {
  try {
    const { query } = JSON.parse(localStorage.getItem(`point/${route.query.time}`));
    query.pageSize = undefined;
    query.pageNum = undefined;
    let obj = JSON.parse(JSON.stringify(query));
    reqQuery.value = obj;
    selectCases(obj).then((res) => {
      queryRes.value.caseIds = res.data.arrayList;
      reqQuery.value.caseIds = res.data.arrayList;
      queryRes.value.caseNum = res.data.zongshu;
      queryRes.value.caseMoney = res.data.zongjine;
      queryRes.value.unAssignedNum = res.data.yifenpei;
      queryRes.value.assignedNum = res.data.weifenpei;
    });
  } catch (error) {
    toBack()
  }

});

//下一步
function nextstep() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let reqData = {
        teamCaseUtils: reqQuery.value,
      };
      loading.value = true;
      proxy.$refs["optteamRef"].exposeData().then((res) => {
        if (res && res.length > 0) {
          let distributions = res?.map((item) => {
            item.odvId = item.id;
            item.odvName = item.jobName;
            item.number = item.caseNum;
            return item;
          });
          reqData.distributions = distributions;
          isShowDivisionalLoad.value = 'block'
          sessionStorage.setItem(`team-case-point-loading-${route.query.time}`, 'block')
          specifyDivisionalData(reqData)
            .then((res) => {
              submitData.value = reqData;
              loading.value = false;
              stepActive.value++;
              sessionStorage.setItem(`team-case-point-no-${route.query.time}`, res.data.scheduleNo)
              proxy.$refs['divisionalLoadRef'].pollTime(res.data.scheduleNo, '0')
            })
            .catch(() => {
              loading.value = false;
            });
        } else {
          loading.value = false;
        }
      });
    }
  });
}

// 获取数据
function getDivisionalLoadData(data) {
  if (data.normal) {
    schedule.value = data.schedule;
    if (data.schedule == 100) {
      previewData.value.data = data.originalData;
      isShowDivisionalLoad.value = 'none'
      sessionStorage.setItem(`team-case-point-loading-${route.query.time}`, 'none')
    }
  } else {
    proxy.$modal.msgWarning(data.remarks || '')
    stepActive.value = 0
    sessionStorage.setItem(`team-case-point-loading-${route.query.time}`, 'none')
  }
}

watch(isShowDivisionalLoad.value, () => {
  const isBlock = sessionStorage.getItem(`team-case-point-loading-${route.query.time}`)
  isShowDivisionalLoad.value = isBlock
  if (isBlock == 'block') {
    stepActive.value = 1
    nextTick(() => {
      proxy.$refs['divisionalLoadRef']?.pollTime(sessionStorage.getItem(`team-case-point-no-${route.query.time}`))
    })
  }
}, { deep: true, immediate: true })

//上一步
function prevStep() {
  stepActive.value--;
  if (stepActive.value == 0) {
    removeLocalData()
  }
}

function removeLocalData() {
  sessionStorage.removeItem(`team-case-point-no-${route.query.time}`)
  sessionStorage.removeItem(`team-case-point-loading-${route.query.time}`)
  sessionStorage.removeItem(`team-case-point-schedule-${route.query.time}`)
}

//提交分案
function submit() {
  loading.value = true;
  updateCase(JSON.stringify(submitData.value))
    .then((res) => {
      stepActive.value++;
      removeLocalData()
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

//返回
const toBack = () => {
  removeLocalData()
  const obj = { path: "/team/teamsCases" };
  proxy.$tab.closeOpenPage(obj);
};

</script>
<style lang="scss" scoped>
.blue {
  color: #409eff;
}

.step-icon {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 0 auto;
  background-color: #3cc556;
  border-radius: 50%;

  .check-icon {
    font-size: 34px;
  }
}

.my-table {
  margin: 20px auto;
  color: #666;
  text-align: center;
  border: 1px solid #ededed;
  outline: none;
  min-width: 80%;
  border-spacing: 0px !important;

  thead {
    background-color: #f2f2f2;
    font-size: 14px;
    border: 1px solid #ededed;

    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }

  tbody {
    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }
}
</style>
