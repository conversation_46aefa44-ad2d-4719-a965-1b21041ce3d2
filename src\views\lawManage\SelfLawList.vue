<template>
    <div>
        <div v-show="!showLawProgress" class="fxn-body">
            <div class="fxn-search">
                <el-form :inline="true" :model="SearchForm" :rules="SearchRules" ref="SearchForm">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="案件ID" prop="case_id">
                                <el-input v-model="SearchForm.case_id" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="客户姓名" prop="case_name">
                                <el-input v-model="SearchForm.case_name" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="身份证" prop="case_idcard_asterisk">
                                <el-input v-model="SearchForm.case_idcard_asterisk" placeholder="请输入" maxlength="18"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="发起时间" prop="start_time">
                                <el-date-picker v-model="SearchForm.start_time" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div v-show="SearchShow">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="委托方" prop="entrust_id">
                                    <el-select filterable v-model="SearchForm.entrust_id">
                                        <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <div class="fxn-search-btn" >
                        <el-button @click="clearSearchForm">重置</el-button>
                        <el-button type="primary" @click="getLawList(1,10,true)">查 询</el-button>

                        <span class="opensearch" @click="ClickMore">
                            <span v-if="!SearchShow">展开<i class="el-icon-arrow-down"></i></span>
                            <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                        </span>
                    </div>
                </el-form>
            </div>
            <!-- Search End -->
            
            <div class="fxn-section">
                <div class="fxn-tabs">
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane v-for="(item,index) in StatusTabs" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
                    </el-tabs>
                    <div class="fxn-status">
                        <span>诉讼进度&nbsp;&nbsp;</span>
                        <el-select v-model="activeStatus" @change="getLawList(page,perPage,type)">
                            <el-option v-for="item in lawStatusWatch" :key="item.id" :label="item.title" :value="item.id"></el-option>
                        </el-select>
                    </div>
                </div>
                <!-- Tabs End -->
                <el-table v-loading="loading" :data="lawListData" :header-cell-style="{background:'#EEEFF4',color:'#888888',width: '100%'}">
                    <el-table-column prop="case_id" label="案件ID" align="center" width="80">
                        <template slot-scope="scope">
                            <router-link :to="{path:'/CaseDetail',query: {id: scope.row.case_id,noncollector:true}}" class="fxn-text-blue" tag="a" target="_blank">{{scope.row.case_id}}</router-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="诉讼进度" align="center" :formatter="suitStatusChange"></el-table-column>
                    <el-table-column prop="bill_no" label="批次号" align="center"></el-table-column>
                    <el-table-column prop="name" label="委托方" align="center"></el-table-column>
                    <el-table-column prop="case_name" label="客户姓名" align="center"></el-table-column>
                    <el-table-column prop="case_idcard_asterisk" label="身份证号" align="center"></el-table-column>
                    <el-table-column prop="new_entrust_money" label="委托金额" align="center"></el-table-column>
                    <el-table-column prop="cancel" label="状态" align="center" :formatter="cancelChange"></el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <div>
                                <el-button type="text" @click="OpenlawProgressBox(scope.row.case_id,scope.row.status,scope.row.cancel)">诉讼进度</el-button>
                            </div>
                            <div v-if="scope.row.cancel != 1 && scope.row.status < 9 || scope.row.status == 11" >
                                <el-button type="text" @click="openCancelBox(scope.row.case_id,scope.row.status)">撤销诉讼</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- Table End -->

                <div class="fxn-page">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="this.page" :page-sizes="[10, 50, 500, 1000]" :page-size="this.perPage" layout="total, sizes, prev, pager, next, jumper" :total="this.count"></el-pagination>
                </div>
                <!-- Page End -->
            </div>

            <!-- 撤销诉讼 -->
            <el-dialog title="撤销原因" :visible.sync="cancelBox" :close-on-click-modal="true">
                <el-form :model="cancelForm" :rules="cancelFormRules" ref="cancelForm">
                    <el-form-item label="原因" :label-width="formLabelWidth" prop="cancel_reason">
                        <el-input type="textarea" v-model="cancelForm.cancel_reason" rows="5"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="cancelBox = false">取 消</el-button>
                    <el-button type="primary" @click="cancelSuit">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <LawProgress v-if="showLawProgress" @func="getMsgFormSon"></LawProgress>
    </div>
</template>
<script>
import LawProgress from './LawProgress'
export default {
    components:{
        LawProgress,
    },
    data() {
        return {
            page: 1,
            perPage: 10,
            count: 0,
            loading: false,

            //查询
            SearchShow: false,
            SearchForm: {
                status: '',
                entrust_id: '',
                case_name: '',
                case_idcard_asterisk: '',
                case_id: '',
                start_time: '',
            },       
            SearchRules: {},

            //Tabs
            activeName: '0',
            StatusTabs: [
                {value: '98', label: '进行中'},
                {value: '99', label: '已完成'},
                {value: '0', label: '全部'},
            ],

            //诉讼状态
            activeStatus: 0,
            lawStatusing:[          //进行中的状态
                {id:0,title:'全部'},
                {id:1,title:'发起诉讼'},
                {id:2,title:'网约立案'},
                {id:3,title:'诉前调解'},
                {id:4,title:'正式立案'},
                {id:5,title:'受理通知'},
                {id:6,title:'开庭传票'},
                {id:7,title:'公告开庭'},
                {id:8,title:'正常开庭'},
                {id:9,title:'判决生效'},
                {id:10,title:'申请执行'},
                {id:11,title:'调解成功'},
            ],  
            lawStatused:[           //已完成的状态
                {id:0,title:'全部'},
                {id:12,title:'录入失信人名单'},
                {id:13,title:'结案'},
                {id:14,title:'二审应诉'},
                {id:666,title:'撤销诉讼'},
            ],

            entrusts: [],  //委托方（资产方）

            //诉讼列表
            lawListData: [],

            //诉讼进度
            showLawProgress: false,
            LawProgressInfo: {
                lowerPart: '',       //案件类型
                caseSuitStatus: '', //诉讼状态
                cancelStatus: '',   //是否撤销(0否1是)
                caseid: '',
            },
            formLabelWidth: "70px",
            //撤销诉讼
            cancelBox: false,
            cancelForm: {
                case_id: '',
                cancel_reason: ''
            }, 
            cancelFormRules: {
                cancel_reason: [
                    { required: true, message: '请填写撤销原因！', trigger: 'blur' }
                ]
            },


        }
    },
    computed: {
        lawStatusWatch:function() {       //诉讼状态数据变化
            let arr1 = this.lawStatusing;
            let arr2 = this.lawStatused;
            if (this.activeName == '0') {
                arr1 = [...arr1,...arr2];
                let hash = {}; 
                let resArr = arr1.reduce(function(item, next) {  //数组去重
                    hash[next.id] ? '' : hash[next.id] = true && item.push(next); 
                    return item 
                }, []) 
                return resArr
            }
            if (this.activeName == '98') {
                let resArr = this.lawStatusing
                return resArr
            }
            if (this.activeName == '99') {
                let resArr = this.lawStatused
                return resArr
            }
            
        }
    },
    methods:{
        //Tabs
        handleClick(){
            this.activeStatus = 0;
            this.getLawList(this.page,this.perPage,this.type)
        },
        //获取诉讼列表
        getLawList(page,perPage,type) {
            this.loading = true;
            this.type = type;
            page ? page : 1;
            perPage ? perPage : 10;
            let status = '';
            let cancel = '';
            

            if (this.activeStatus == 666) {
                cancel = 1 ;
            } else {
                cancel = '';
                if (this.activeStatus == 0) {
                    status = this.activeName
                } else {
                    status = this.activeStatus
                }
            }
            
            if (type) {
                this.queryForm = {
                    page: page,
                    perPage : perPage,
                    status: status,
                    cancel: cancel,
                    entrust_id: this.SearchForm.entrust_id,
                    case_name: this.SearchForm.case_name,
                    case_idcard_asterisk: this.SearchForm.case_idcard_asterisk,
                    case_id: this.SearchForm.case_id,
                    start_time: this.SearchForm.start_time ? String(this.SearchForm.start_time[0]) : '',
                    end_time: this.SearchForm.start_time ? String(this.SearchForm.start_time[1]) : ''
                }
            } else {
                this.queryForm = {
                    page: page,
                    perPage : perPage,
                    status: status,
                    cancel: cancel,
                }
            }
            this.get('/osapi/Lawsuit/suitlist',this.queryForm)
            .then( res => {
                let { code , msg ,data } = res.data;
                if ( code != 1) {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                } else {
                    this.lawListData = res.data.data.data;
                    this.page = res.data.data.page;
                    this.count = res.data.data.count;
                    this.perPage = res.data.data.perPage;
                    this.loading = false;
                }
            })
        },

        //每页显示数据量变更
        handleSizeChange: function(val) {
            this.perPage = val;
            this.getLawList(this.page,this.perPage,this.type)
        },

        //页码变更
        handleCurrentChange: function(val) {
            this.page = val;
            this.getLawList(this.page,this.perPage,this.type)
        },

        //清空查询
        clearSearchForm() {
            this.$refs['SearchForm'].resetFields();
            this.getLawList()
        },

        //查询--显示/隐藏
        ClickMore(){
            this.SearchShow = !this.SearchShow;
        },
        
        //导出诉讼列表
        exportSuit() {
            let len = this.SelectionChange.length;
            if(len == 0 || len == undefined){
                this.$message({
                    type: 'error',
                    message: '请勾选您要导出的列表!'
                })
                return false
            }
            let multis = []
            for (var i=0;i<len;i++){
                multis.push(this.SelectionChange[i].id)
            }
            //console.log(this.TextUrl +'osapi/Lawsuit/exportsuit?id=' + String(multis) + '&token=' + JSON.parse(localStorage.getItem('token')))
            window.open(this.TextUrl +'osapi/Lawsuit/exportsuit?id=' + String(multis) + '&token=' + JSON.parse(localStorage.getItem('token')))
        },

        //获取子组件传回的值
        getMsgFormSon(data) {
            this.showLawProgress = data;
            if (!data) {
                this.getLawList(this.page,this.perPage,this.type)
            }
        },

        //诉讼记录
        OpenlawProgressBox(caseid,status,cancel) {
            this.showLawProgress = true
            this.LawProgressInfo.lowerPart = 'show';
            this.LawProgressInfo.caseSuitStatus = status;
            this.LawProgressInfo.cancelStatus = cancel;
            this.LawProgressInfo.caseid = caseid;
            sessionStorage.setItem('LawInfo',JSON.stringify(this.LawProgressInfo));
            
        },

        //撤销诉讼
        openCancelBox(caseid , status) {
            this.cancelBox = true;
            this.cancelForm.case_id = caseid;
        },
        cancelSuit() {
            this.$refs['cancelForm'].validate(async valid => {
                if(valid){
                    this.post('/osapi/Lawsuit/cancelsuit',this.cancelForm)
                    .then(res => {
                        let { code , msg } = res.data;
                        if ( code != 1 ) {
                            this.$message({
                                type: 'error',
                                message: msg
                            })
                        } else {
                            this.$message({
                                type: 'success',
                                message: '撤销成功！'
                            })
                            this.getLawList();
                            this.cancelBox = false         //关闭弹框
                        }
                        //console.log(res);
                    }) 
                } else {
                    console.log('submit error');
                }
            })
        },

        //诉讼状态转换
        suitStatusChange(row ,col) {
            if (row.status == 1) {
                return '发起诉讼'
            } else if (row.status == 2){
                return '网约立案'
            } else if (row.status == 3){
                return '诉前调解'
            } else if (row.status == 4){
                return '正式立案'
            } else if (row.status == 5){
                return '受理通知'
            } else if (row.status == 6){
                return '开庭传票'
            } else if (row.status == 7){
                return '公告开庭'
            } else if (row.status == 8){
                return '正常开庭'
            } else if (row.status == 9){
                return '判决生效'
            } else if (row.status == 10){
                return '申请执行'
            } else if (row.status == 11){
                return '调解成功'
            } else if (row.status == 12){
                return '录入失信人名单'
            } else if (row.status == 13){
                return '结案'
            } else if (row.status == 14){
                return '二审应诉'
            }
        },

        //是否撤销
        cancelChange(row ,col) {
            return  row.cancel == 0 ? '正常' : '已撤销'
        },

        //案件状态
        caseStatusChange(row ,col) {
            if(row.case_status == 1){
                return '未分配'
            }else if(row.case_status == 2){
                return '已分配'
            }else if(row.case_status == 3){
                return '暂停'
            }else if(row.case_status == 4){
                return '退案'
            }else if(row.case_status == 5){
                return '留案'
            }else if(row.case_status == 6){
                return '恢复'
            }
        }
    },
    created() {
    },
    mounted() {
        //获取诉讼列表
        this.getLawList()

        //获取委托方(资产方)
        this.common.OpenEntrusts()
        .then( res => {
            this.entrusts = res.data.data
            //console.log(res)
        })
        
    },
    watch: {
        //撤销诉讼原因
        cancelBox(val) {
            if ( !val ) {
                this.$refs['cancelForm'].resetFields();
            }
        }
    }
}
</script>
<style scoped>
.fxn-detail-img-pdf{
    display: block;
}
.fxn-detail-img-pdf img {
    margin-right: .25rem;
    display: inline-block;
    cursor: pointer;
}
.fxn-detail-img-pdf span{
    color: #f66b2e !important;
}
.fxn-remark{
    font-size: 10px;
    color: #999999;
    padding-left: 10px;
}
</style>