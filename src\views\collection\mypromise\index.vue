<template>
  <div class="app-container">
    <el-form class="form-content h-50" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
      :inline="true" :rules="rules" label-width="96px">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable type="number" style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="转让方" prop="entrustingPartyId">
        <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
          :reserve-keyword="false" @focus="OwnerList" style="width: 240px">
          <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="账期" prop="accountPeriod">
        <el-input v-model="queryParams.accountPeriod" placeholder="请输入账期" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb8" style="min-height: 32px">
      <el-button plain :disabled="single" @click="opendialog(0, selectedArr)"
        v-hasPermi="['saasc:teamCase:insertRetreatMyPromise']">申请退案</el-button>
      <el-button plain :disabled="single" type="primary" @click="opendialog(1, selectedArr)"
        v-hasPermi="['saasc:teamCase:insertKeepMyPromise']">申请留案</el-button>
      <!-- <el-button plain :disabled="single" type="success" @click="toHelp(selectedArr)"
        v-hasPermi="['saasc:collection:insertAssistRecordMyPromise']">申请协案</el-button> -->
      <el-button plain :disabled="single" type="primary" @click="handleExport"
        v-hasPermi="['saasc:teamCase:exportMyPromise']">导出</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center">
            <el-tooltip v-if="scope.row.labelContent" placement="top">
              <template #content>{{ scope.row.labelContent }}</template>
              <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
            </el-tooltip>
            <span style="color:#409eff;cursor: pointer;" type="text" v-if="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)">{{
                scope.row.caseId
              }}</span>
            <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
        width="180" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible" />
      <el-table-column label="手机号码" width="120" align="center" key="clientPhone" prop="clientPhone"
        v-if="columns[3].visible" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" v-if="columns[4].visible" />
      <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[5].visible"
        width="190" />
      <el-table-column label="账期" align="center" key="accountPeriod" prop="accountPeriod" show-overflow-tooltip
        v-if="columns[6].visible" />
      <el-table-column label="承诺还款信息" align="center" show-overflow-tooltip v-if="columns[7].visible" width="250"
        key="remarks" prop="remarks" />
      <el-table-column label="分期还款期数" align="center" show-overflow-tooltip v-if="columns[8].visible">
        <template #default="scope">
          <span>{{ scope.row.promiseByStages ?? '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退案时间" align="center" key="returnCaseDate" prop="returnCaseDate" show-overflow-tooltip
        v-if="columns[9].visible" width="150" />
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button type="text" @click="toHelp([scope.row])" v-if="scope.row.button == 1"
            v-hasPermi="['saasc:collection:insertAssistRecordMyPromise']">申请协催</el-button>
          <el-button type="text" @click="opendialog(1, [scope.row])" v-if="scope.row.button == 1"
            v-hasPermi="['saasc:teamCase:insertKeepMyPromise']">申请留案</el-button>
          <span v-if="scope.row.button != 1">--</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 退案 -->
    <insertRetreat @getList="getList" ref="insertRetreatRef" />

    <!-- 申请协催 -->
    <helpUrgeVue ref="helpUrgeRef" />
  </div>
</template>

<script setup name="Mypromise">
import insertRetreat from "./dialog/insertRetreat.vue";
import { batchOptions, assetOwnerOptions } from "@/api/collection/mycase";
import { selectUrgeRecordId } from "@/api/collection/mypromise";
import helpUrgeVue from "../../collection/mycase/dialog/helpUrge.vue";

const { proxy } = getCurrentInstance();
const router = useRouter();

const showSearch = ref(false);
const loading = ref(false);
const caseList = ref([]);
const total = ref(0);
const selectLoading = ref(false);

const batchs = ref([]); //委案批次号下拉
const entrusts = ref([]); //转让方下拉

const single = ref(true); //操作栏是否可操作
const caseIds = ref([]); //选中id集合
const selectedArr = ref([]); //列表选中集合

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  caseId: undefined,
  clientName: undefined,
  clientPhone: undefined,
  clientIdcard: undefined,
  entrustingCaseBatchNum: undefined,
  entrustingPartyId: undefined,
  promiseRepaymentMoney1: undefined,
  promiseRepaymentMoney2: undefined,
  accountPeriod: undefined,
});
const rules = ref({
  clientPhone: [
    { required: false, message: "请输入手机号码", trigger: "blur" },
  ],
  clientIdcard: [
    { required: false, message: "请输入证件号码", trigger: "blur" },
  ],
});

//列显隐信息
const columns = ref([
  { "key": 0, "label": "案件ID", "visible": true },
  { "key": 1, "label": "产品类型", "visible": true },
  { "key": 2, "label": "姓名", "visible": true },
  { "key": 3, "label": "手机号码", "visible": true },
  { "key": 4, "label": "证件类型", "visible": true },
  { "key": 5, "label": "证件号码", "visible": true },
  { "key": 6, "label": "账期", "visible": true },
  { "key": 7, "label": "承诺还款信息", "visible": true },
  { "key": 8, "label": "分期还款期数", "visible": true },
  { "key": 9, "label": "退案时间", "visible": true }
]);

//获取列表
function getList() {
  loading.value = true;
  selectUrgeRecordId(queryParams.value)
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//获取批次号
function BatchList() {
  selectLoading.value = true;
  batchOptions()
    .then((res) => {
      batchs.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

//获取转让方
function OwnerList() {
  selectLoading.value = true;
  assetOwnerOptions()
    .then((res) => {
      entrusts.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    promiseRepaymentMoney1: undefined,
    promiseRepaymentMoney2: undefined,
    accountPeriod: undefined,
  };
  getList();
}

//协催
function toHelp(row) {
  let data = {
    condition: false,
    caseIds: row.map((item) => item.caseId),
  };
  proxy.$refs["helpUrgeRef"].opendialog(data);
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = queryParams.value;
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

//退案，留案操作
function opendialog(type, row) {
  let query = JSON.parse(JSON.stringify(queryParams.value))
  query.caseIds = row.map((item) => item.caseId);
  query.pageNum = undefined;
  query.pageSize = undefined;
  query.condition = false;
  proxy.$refs["insertRetreatRef"].opendialog(type, query);
}

function getQuery() {
  let query = JSON.parse(JSON.stringify(queryParams.value))
  query.caseIds = selectedArr.value.map((item) => item.caseId);
  query.pageNum = undefined;
  query.pageSize = undefined;
  query.condition = false;
  return query;
}

function handleExport() {
  let query = getQuery();
  proxy.downloadforjson(
    "/collection/exportUrgeRecordId",
    query,
    `我的承诺户_${new Date().getTime()}.xlsx`,
  );
}

</script>

<style lang="scss" scoped>
.form-content {
  .el-form-item {
    width: 30% !important;

    .el-select .el-select__tags .el-tag--info {
      max-width: 100px;
      overflow: hidden;
    }
  }
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
