<template>
    <div>
        <div class="mb10 mt10" v-if="route.path?.type == 'myCase'" style="text-align:right;margin-right:50px">
            <el-button type="primary" v-if="route.path?.type == 'myCase'" :disabled="urls.length == 0"
                @click="handleBatchDownload(`保全材料_${+new Date()}.zip`)">批量下载</el-button>
        </div>
        <el-table :data="dataList" max-height="350px">
            <el-table-column type="selection" v-if="route.path?.type == 'myCase'" width="50" align="center" />
            <el-table-column label="序号" prop="id" key="id" align="center">
                <template #default="{ $index }">
                    <div> {{ $index + 1 }} </div>
                </template>
            </el-table-column>
            <el-table-column label="保全材料" prop="firstName" key="firstName" align="center" />
            <el-table-column label="上传人员" prop="founder" key="founder" align="center" />
            <el-table-column label="操作" v-if="route.path?.type == 'myCase'">
                <template #default="{ row }">
                    <el-button type="text" link v-if="isPackage(row.previewUrl)"
                        @click="previewSigntrue(row.fileUrl)">预览</el-button>
                    <el-button type="text" link v-if="row.fileUrl"
                        @click="handleDownload(row.fileUrl, row.firstName)">下载</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script setup>
import { selectSecurity } from "@/api/caseDetail/detail";
const props = defineProps({
    urls: { type: Array, default: [] },
    previewSigntrue: { type: Function },
    handleDownload: { type: Function },
    handleBatchDownload: { type: Function },
    handleSelectionChange: { type: Function },
    isPackage: { type: Function },
})
const route = useRoute()
const total = ref(0)
const dataList = ref([])

//保全资料
getSecurityChange()
function getSecurityChange() {
    let req = { caseId: route.params.caseId };
    selectSecurity(req).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}
</script>