<template>
  <div class="app-container">
    <el-form
      :inline="true"
      :model="queryParams"
      label-position="right"
      :label-width="130"
      ref="queryRef"
    >
      <el-form-item label="委托方" prop="entrustingPartyId">
        <el-select
          v-model="entrustingPartyIdList"
          placeholder="请输入或选择委托方"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @visible-change="OwnerList"
          style="width: 240px"
        >
          <el-option
            v-for="item in entrusts"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="批次号" prop="batchNum">
        <el-select
          v-model="batchNumSelected"
          placeholder="请输入或选择批次号"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @visible-change="getbatchNumList"
          style="width: 240px"
        >
          <el-option
            v-for="item in batchNumArray"
            :key="item.label"
            :label="item.value"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="委案日期">
        <el-date-picker
          v-model="queryParams.entrustingCaseDate"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="退案日期">
        <el-date-picker
          v-model="queryParams.returnCaseDate"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="委托金额">
        <el-row style="width: 240px">
          <el-col :span="11">
            <el-input
              type="text"
              v-model="queryParams.clientMoney1"
              @blur="validatePrincipalRange"
              @input="(value) => value.replace(/[^\d]/g, '')"
              clearable
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input
              type="text"
              v-model="queryParams.clientMoney2"
              @blur="validatePrincipalRange"
              @input="(value) => value.replace(/[^\d]/g, '')"
              clearable
            />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb10 mt10" style="min-height: 26px">
      <el-button
        v-hasPermi="['case:recycle:recovery']"
        type="primary"
        @click="handleRecovery"
        >恢复案件</el-button
      >
      <el-button
        v-hasPermi="['case:recycle:delete']"
        type="warning"
        @click="handleDelete"
        >彻底销毁</el-button
      >
      <el-button
        v-hasPermi="['case:recycle:clear']"
        type="danger"
        @click="handleClear"
        >一键清空回收站</el-button
      >
    </el-row>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="caseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        label="案件ID"
        align="center"
        prop="caseId"
        :width="100"
      />
      <el-table-column label="批次号" align="center" prop="batchNum" />
      <el-table-column
        label="委托金额"
        align="center"
        prop="clientMoney"
        :width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.clientMoney) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="委托方"
        align="center"
        prop="entrustingPartyName"
      />
      <el-table-column
        label="委案日期"
        align="center"
        prop="entrustingCaseDate"
        :width="160"
      />
      <el-table-column
        label="退案日期"
        align="center"
        prop="returnCaseDate"
        :width="110"
        :formatter="formatDate"
      />
      <el-table-column label="操作人" align="center" prop="updateBy" />
      <el-table-column label="操作时间" align="center" prop="updateTime" />
      <el-table-column fixed="right" width="200" label="操作">
        <template #default="{ row }">
          <el-button
            v-hasPermi="['case:recycle:recovery']"
            type="text"
            @click="handleRecovery(row)"
            >恢复案件</el-button
          >
          <el-button
            v-hasPermi="['case:recycle:delete']"
            type="text"
            @click="handleDelete(row)"
            >彻底销毁案件</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { selectAssetOwner, selectListWithBatchNum2 } from "@/api/common/common";
import {
  caseManageList,
  recoverRecycleBin,
  deleteRecycleBin,
  clearRecycle,
} from "@/api/case/index/index";

const { proxy } = getCurrentInstance();

const entrusts = ref([]);
const entrustingPartyIdList = ref([]);
const batchNumSelected = ref([]);
const batchNumArray = ref([]);
const checkMoreName = ref([entrustingPartyIdList, batchNumSelected]);
//多选字段
const checkMoreList = ref(["entrustingPartyId", "batchNum"]);

//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    isRecycleBin: 1,
    condition: false, //是否搜索结果全选
    entrustingPartyId: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    returnCaseDate: [],
    entrustingCaseDate: [],
    batchNum: undefined,
  },
});
//需要拆分的字段
const rangfiles = ["returnCaseDate", "entrustingCaseDate"];
const { queryParams } = toRefs(data);

//表格配置数据
const loading = ref(false);
const total = ref(0);
const multipleTableRef = ref();
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
const caseList = ref([]);

//获取转让方
function OwnerList() {
  selectAssetOwner().then((res) => {
    entrusts.value = res.data;
  });
}

function getbatchNumList() {
  selectListWithBatchNum2().then((res) => {
    let data = [];
    res.data.forEach((item) => {
      data.push({
        label: item,
        value: item,
      });
    });
    batchNumArray.value = data;
  });
}

const validatePrincipalRange = () => {
  const { clientMoney1, clientMoney2 } = queryParams.value;
  // 检测输入是否是数字
  if (clientMoney1 && !Number.isFinite(Number(clientMoney1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney1 = undefined;
  }
  if (clientMoney2 && !Number.isFinite(Number(clientMoney2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney2 = undefined;
  }
  if (!clientMoney1 || !clientMoney2) return;

  const principal1 = parseFloat(clientMoney1);
  const principal2 = parseFloat(clientMoney2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney2 = undefined;
  }
};

//获取列表数据
function getList() {
  checkMoreList.value.forEach((item, index) => {
    console.log(item, index);
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });

  loading.value = true;
  caseManageList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
getList();

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    isRecycleBin: 1,
    condition: false, //是否搜索结果全选
    entrustingPartyId: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    returnCaseDate: [],
    allocatedTime: [],
    batchNum: undefined,
  };
  entrustingPartyIdList.value = [];
  batchNumSelected.value = [];
  getList();
}

//时间格式转换为日期格式
function formatDate(row) {
  return proxy.parseTime(row.returnCaseDate, `{y}-{m}-{d}`);
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//恢复案件
function handleRecovery(row) {
  let form = {};
  if (row.caseId) {
    Object.assign(form, { caseId: row.caseId });
  } else {
    Object.assign(form, { ids: caseIds.value });
  }
  proxy.$modal.confirm("该操作将案件移出回收站，是否确认！？").then(() => {
    recoverRecycleBin(form).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      }
      getList();
    });
  });
}

function handleDelete(row) {
  let form = {};
  if (row.caseId) {
    Object.assign(form, { caseId: row.caseId });
  } else {
    Object.assign(form, { ids: caseIds.value });
  }
  proxy.$modal.confirm("该操作将彻底销毁案件，是否确认！？").then(() => {
    deleteRecycleBin(form).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      }
      getList();
    });
  });
}

function handleClear() {
  proxy.$modal.confirm("该操作将清空回收站，是否确认！？").then(() => {
    clearRecycle().then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      }
      getList();
    });
  });
}
</script>

<style scoped></style>
