import request from '@/utils/request'

// 根据结佣日期（年/月）查询本机构的结佣详情表数据
export function getDetailsBrokerage(query) {
    return request({
        url: '/brokerageTeam/selectDetailsBrokerage',
        method: 'get',
        params: query
    })
}

// 根据佣金详情表id查询资产回收情况
export function getRecoveryAssetById(query) {
    return request({
        url: '/brokerageTeam/selectRecoveryAsset',
        method: 'get',
        params: query
    })
}

// 根据佣金详情表id查询机构风险奖罚设置
export function getRiskTeamById(query) {
    return request({
        url: '/brokerageTeam/selectRiskTeam',
        method: 'get',
        params: query
    })
}

// 根据佣金详情表id查询统计回收合计以及风险奖惩合计
export function getStatisticsMoneyById(query) {
    return request({
        url: '/brokerageTeam/statisticsMoney',
        method: 'get',
        params: query
    })
}

// 有异议（驳回佣金结算信息）
export function rejectObjection(data) {
    return request({
        url: '/brokerageTeam/rejectObjection',
        method: 'post',
        data: data
    })
}

// 有异议（驳回佣金结算信息）
export function confirmBrokerage(data) {
    return request({
        url: '/brokerageTeam/confirm',
        method: 'post',
        data: data
    })
}

// 导出--(查询返回文件名称)
export function exportFileName(query) {
    return request({
        url: '/brokerageTeam/exportFileName',
        method: 'get',
        params: query
    })
}
