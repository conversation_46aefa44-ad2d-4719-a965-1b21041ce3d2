<template>
  <el-dialog
    :title="title"
    :loading="loading"
    v-model="open"
    width="800px"
    :before-close="cancel"
  >
    <el-form
      :model="form"
      :loading="loading"
      :rules="rules"
      label-width="100px"
      ref="formRef"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="法院名称" prop="courtName">
            <el-input
              v-model="form.courtName"
              @input="handleInput"
              maxlength="50"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="联系电话" prop="phone">
            <el-input class="mt5" v-model="form.phone" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input
              class="mt5"
              v-model="form.address"
              @change="handleChangeAddress"
              placeholder="请输入地址"
            >
              <template #append>
                <el-button @click="BaiduMap(null, form.address, 'OutboundMap')"
                  >搜索地址</el-button
                >
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label=" ">
            <div class="mt10" v-loading="maploading" id="OutboundMap"></div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联文书" prop="ids">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-input
                  style="width: 100%"
                  v-model="filterWrit"
                  placeholder="可搜索你需要的文书"
                ></el-input>
              </el-col>
              <el-col
                :span="24"
                v-if="filterWritList.length > 0"
                class="associated-document"
              >
                <el-checkbox-group v-model="form.ids">
                  <el-checkbox
                    v-for="item in filterWritList"
                    :key="item.id"
                    :label="item.id"
                  >
                    {{ item.templateName }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-col>
              <el-col :span="24" v-else class="associated-document mt20">
                目前没有文书，请到文书模板创建文书
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submintForm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import {
  addCourt,
  updateCourt,
  getCourtById,
  listNotPaging,
} from "@/api/institutionManage/institutionManage";
const { proxy } = getCurrentInstance();
const props = defineProps({
  getList: {
    type: Function,
    required: true,
  },
});
const filterWrit = ref(undefined);
const loading = ref(false);
const open = ref(false);
const maploading = ref(false);
const classifyName = ref(undefined);
const classifyIdsList = ref([]);
const template = ref(undefined);
const title = ref("添加机构");
const data = reactive({
  form: {
    agencyType: undefined,
    courtName: undefined,
    jurisdiction: undefined,
    phone: undefined,
    address: undefined,
    ids: undefined,
    jurisdictionEconomize: undefined,
    jurisdictionMarket: undefined,
  },
  rules: {
    agencyType: [{ required: true, message: "请输入机构类型", trigger: "change" }],
    courtName: [{ required: true, message: "请输入法院名称", trigger: "blur" }],
    jurisdiction: [{ required: true, message: "请选择所属辖区", trigger: "change" }],
    phone: [
      { required: false, message: "请输入联系电话", trigger: "blur" },
      {
        pattern: /^\d{11}|^\d{3,4}-\d{8}$/,
        message: "请输入正确格式的联系电话",
        trigger: "blur",
      },
    ],
    // address: [{ required: false, validator: validateAddress, trigger: "blur" }],
    ids: [{ required: false, message: "请选择关联文书", trigger: "change" }],
  },
});
const writList = ref([]);
const { form, rules } = toRefs(data);

function handleInput(val) {
  form.value.courtName = val.replace(/\s/g, "");
}

// 输入地址
function handleChangeAddress() {
  if (!form.value.address) {
    form.value.longitudeAtitudeStart = undefined;
  }
}

// 提交
function submintForm() {
  proxy.$refs["formRef"].validate((vaild) => {
    if (vaild) {
      loading.value = true;
      nextTick(() => {
        if (loading.value) {
          const reqForm = JSON.parse(JSON.stringify(form.value));
          delete reqForm.jurisdiction;
          if (["台湾省", "澳门", "香港"].includes(reqForm.jurisdictionEconomize)) {
            reqForm.jurisdictionMarket = " ";
          }
          if (reqForm.ids?.length > 0) {
            reqForm.ids = reqForm.ids.filter((item) => item);
          }
          const reqApi = reqForm.id ? updateCourt : addCourt;
          reqApi(reqForm)
            .then(() => {
              props.getList();
              cancel();
              proxy.$modal.msgSuccess("操作成功");
            })
            .finally(() => {
              loading.value = false;
            });
        }
      });
    }
  });
}
//搜索地址校验
function validateAddress(rule, value, callback) {
  if (!value || value == "") {
    callback(new Error("请输入地址搜索选择！"));
  } else {
    if (!form.value.longitudeAtitudeStart || form.value.longitudeAtitudeStart == "") {
      callback(new Error("请点击搜索后选择地图中的详细地点！"));
    } else {
      callback();
    }
  }
}
// 打开
function openDialog(data) {
  open.value = true;
  title.value = data.title;
  BaiduMap(null, null, "OutboundMap");
  if (data.row) {
    loading.value = true;
    getCourtById({ id: data.row.id })
      .then((res) => {
        if (res.code == 200) {
          form.value = res.data;
          form.value.jurisdiction = [];
          if (res.data.jurisdictionEconomize) {
            form.value.jurisdiction.push(res.data.jurisdictionEconomize);
          }
          if (res.data.jurisdictionMarket && res.data.jurisdictionMarket.trim() != "") {
            form.value.jurisdiction.push(res.data.jurisdictionMarket);
          }
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
}
// 关闭
function cancel() {
  proxy.resetForm("formRef");
  classifyIdsList.value = [];
  filterWrit.value = undefined;
  classifyName.value = undefined;
  open.value = false;
}
const filterWritList = computed(() => {
  let filterArr = JSON.parse(JSON.stringify(writList.value));
  filterArr = Boolean(classifyName.value)
    ? filterArr.filter((item) => item.classifyLabel.includes(classifyName.value))
    : filterArr;
  filterArr = Boolean(filterWrit.value)
    ? filterArr.filter((item) => item.templateName.includes(filterWrit.value))
    : filterArr;
  return filterArr;
});

//百度地图
async function BaiduMap(Win, huji_address, div, result) {
  //Win-弹窗窗口,huji_address-地址，div-渲染容器，result-检索渲染容器
  huji_address = huji_address || "北京";
  maploading.value = true;
  await getDiv(div);
  var map = new BMap.Map(div); // 创建Map实例
  var geoc = new BMap.Geocoder({ extensions_town: true }); //地址解析对象
  map.centerAndZoom(new BMap.Point(116.331398, 39.897445), 11);
  map.centerAndZoom("北京", 11);
  map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
  var myKeys = [huji_address];
  if (result) {
    let local = new BMap.LocalSearch(map, {
      renderOptions: { map: map, panel: result },
      pageCapacity: 2,
    });
    local.searchInBounds(myKeys, map.getBounds());
  } else {
    let options = {
      onSearchComplete: function (results) {
        var opts = {
          width: 250, // 信息窗口宽度
          height: 100, // 信息窗口高度
          title: "", // 信息窗口标题
        };
        var pointArr = [];
        if (huji_address != "北京") {
          for (var i = 0; i < results.getCurrentNumPois(); i++) {
            var pointObj = results.getPoi(i);
            var pointA = new BMap.Point(pointObj.point.lng, pointObj.point.lat);
            pointArr.push(pointA);
            //var marker = new BMapGL.Marker(new BMapGL.Point(point.lng, point.lat));
            var marker = new BMap.Marker(
              new BMap.Point(pointObj.point.lng, pointObj.point.lat)
            ); // 创建点
            map.addOverlay(marker); //增加点
            marker.setAnimation(BMAP_ANIMATION_BOUNCE); //跳动的动画
            addClickHandler("地址：" + pointObj.address, marker, pointObj.title);
            //s.push(results.getPoi(i).title + ", " + results.getPoi(i).address);
          }
          map.setViewport(pointArr); //显示搜索到的所有标注
        }
        function addClickHandler(content, marker, title) {
          marker.addEventListener("click", function (e) {
            //给标注添加点击事件
            openInfo(content, e, title);
          });
        }
        function openInfo(content, e, title) {
          opts.title = title;
          var p = e.target;
          let lng_lat = [p.getPosition().lng, p.getPosition().lat];
          form.value.longitudeAtitudeStart = String(lng_lat);
          var point = new BMap.Point(p.getPosition().lng, p.getPosition().lat);
          var infoWindow = new BMap.InfoWindow(content, opts); // 创建信息窗口对象
          map.openInfoWindow(infoWindow, point); //开启信息窗口
          const lat = point.lat;
          const lng = point.lng;
          map.centerAndZoom(new BMap.Point(lat, lng), 11);
          // 创建地理编码实例, 并配置参数获取乡镇级数据
          var geoc = new BMap.Geocoder({ extensions_town: true });
          // 根据坐标得到地址描述
          geoc.getLocation(point, function (result) {
            if (result) {
              let address = infoWindow.K.title;
              const { city, province, town, district } = result.addressComponents;
              address = address.replace(province, "");
              address = address.replace(city, "");
              address = address.replace(town, "");
              form.value.address = `${province}${province == city ? "" : city}${
                district || town
              }${address}`;
            }
          });
        }
        maploading.value = false;
      },
    };
    var local = new BMap.LocalSearch(map, options);
    local.search(huji_address);
  }
}

//持续获取
function getDiv(value) {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      var div = document.getElementById(value);
      if (!div) {
        setTimeout(() => {
          getDiv(value);
        });
        return;
      }
      resolve(div);
    });
  });
}
getListNotPagingList();
function getListNotPagingList() {
  listNotPaging().then((res) => {
    writList.value = res.data;
  });
}
defineExpose({ openDialog });
</script>
<style lang="scss" scoped>
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;

  & > label {
    width: 48%;
    margin: 0;

    &:nth-of-type(2n) {
      margin-left: 4%;
    }
  }
}

#OutboundMap {
  width: 100%;
  height: 250px;
  border-radius: 8px;
}

:deep(.el-form-item--default) {
  margin-bottom: 10px;
}

:deep(.el-dialog__body) {
  max-height: 800px;
}

.associated-document {
  max-height: 15vh;
  overflow-y: auto;
  font-size: 16px;
  text-align: center;
  color: #9f9f99;
}
</style>
