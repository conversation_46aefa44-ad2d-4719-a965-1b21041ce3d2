<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container mb20" style="color: #888888">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          组织架构
        </div>
        <el-input class="mb8" v-model="filterText" placeholder="请输入关键字" />
        <el-tree ref="treeRef" class="filter-tree" :data="tree" :props="defaultProps" :expand-on-click-node="false"
          check-on-click-node :render-after-expand="false" node-key="id" @node-click="getNode"
          :filter-node-method="filterNode">
          <template #default="{ data }">
            <el-tooltip effect="light" :content="data.name" placement="right-start">
              <span class="el-tree-node__label">{{ data.name }}</span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" :label-width="100"
          class="form-content h-50" :class="{ 'h-auto': showSearch }" :rules="ruleses">
          <el-form-item label="案件ID">
            <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="UID" prop="uid">
            <el-input v-model="queryParams.uid" placeholder="请输入UID" clearable style="width: 240px"
              onkeyup="value=value.replace(/[^A-z0-9/g, '')" @keyup.enter="handleQuery"
              oninput="value=value.replace(/[^A-z0-9]/g, '')" />
          </el-form-item>
          <el-form-item label="姓名" prop="clientName">
            <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="手机号码" prop="clientPhone">
            <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable type="number"
              style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="证件号码" prop="clientIdcard">
            <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="分配时间" prop="allocatedTime">
            <el-date-picker v-model="queryParams.allocatedTime" style="width: 240px" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="转让方" prop="entrustingPartyId">
            <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
              :reserve-keyword="false" @focus="OwnerList" style="width: 240px">
              <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="户籍地" prop="clientCensusRegister">
            <el-cascader :options="areas" ref="clientCensusRegisterRef" filterable v-model="clientCensusRegisterList"
              collapse-tags-tooltip collapse-tags @change="changeOption" :reserve-keyword="false" placeholder="请输入或选择地区"
              clearable @focus="CensusRegisters" style="width: 240px" :props="{ multiple: true, value: 'label' }" />
          </el-form-item>
          <el-form-item label="退案日期">
            <el-date-picker v-model="queryParams.returnCaseDate" style="width: 240px" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="跟进日期">
            <el-date-picker v-model="queryParams.followUpAst" style="width: 240px" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="标签" prop="label">
            <el-select v-model="queryParams.label" placeholder="请输入或选择标签" clearable filterable :reserve-keyword="false"
              @focus="getTagList" style="width: 240px">
              <el-option v-for="item in tags" :key="item.code" :value="item.code" :label="item.labelContent">
                <div>
                  <el-icon :color="lableColor[item.code]" class="icon-flag">
                    <flag />
                  </el-icon>
                  {{ item.labelContent }}
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="处置人员" prop="odvName">
            <el-input v-model="queryParams.odvName" placeholder="请输入处置人员" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="资产包名称" prop="packageName">
            <el-select v-model="packageNameChecked" placeholder="请输入或选择资产包名称" clearable filterable
              :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip @visible-change="getPackageName"
              style="width: 240px">
              <el-option v-for="item in packageNameList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="剩余应还本金" style="width: 336px" prop="syYhPrincipal">
            <el-row style="width: 240px">
              <el-col :span="11">
                <el-input type="text" v-model="queryParams.syYhPrincipal1" @blur="validatePrincipalRange"
                  @input="value => value.replace(/[^\d]/g, '')" clearable />
              </el-col>
              <el-col :span="2" class="text-center">
                <span>-</span>
              </el-col>
              <el-col :span="11">
                <el-input type="text" v-model="queryParams.syYhPrincipal2" @blur="validatePrincipalRange"
                  @input="value => value.replace(/[^\d]/g, '')" clearable />
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>

        <el-row class="mb10 mt10">
          <el-button plain :disabled="single" @click="opendialog(0)"
            v-hasPermi="['saasc:teamCase:insertRetreat']">申请退案</el-button>
          <el-button plain :disabled="single" type="primary" @click="opendialog(1)"
            v-hasPermi="['saasc:teamCase:insertKeep']">申请留案</el-button>
          <el-button plain :disabled="single" type="success" @click="toStop({ type: 0, row: selectedArr })"
            v-hasPermi="['saasc:teamCase:insertStop']">申请停案</el-button>
          <el-button plain :disabled="single" type="info" @click="openLabelCase"
            v-hasPermi="['saasc:teamCase:selectMarkCase']">标记案件</el-button>
          <el-button plain :disabled="single" type="warning" @click="toPoint"
            v-hasPermi="['saasc:teamCase:updateCase']">指定分案</el-button>
          <el-button plain :disabled="single" type="danger" @click="toRules"
            v-hasPermi="['saasc:teamCase:writeRuleDivision']">规则分案</el-button>
          <el-button plain type="info" @click="openTpldivision"
            v-hasPermi="['saasc:case:importDataTeam']">模板分案</el-button>
          <el-button plain :disabled="single" type="primary" @click="handleExport"
            v-hasPermi="['saasc:teamCase:reminder']">导出沟通记录</el-button>
          <el-button plain :disabled="single" type="warning" @click="downloadCase"
            v-hasPermi="['saasc:teamCase:case']">导出案件</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-row class="mb10">
          <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
            <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
              :indeterminate="item.indeterminate" :disabled="caseList.length === 0" />
          </el-checkbox-group>
          <div class="text-flex ml20">
            <span>剩余债权总额（元）：</span>
            <span class="text-danger mr10">{{ moneyFor(statistics.totalMoney) }}</span>
            <span>剩余债权本金：</span>
            <span class="text-danger mr10">{{ moneyFor(statistics.principal) }}</span>
            <span>案件数量：</span>
            <span class="text-danger">{{ statistics.caseNum }}</span>
          </div>
          <div class="top-right-btn">
            <el-radio-group v-model="settleState" @change="settleStateChange">
              <el-radio label="1" value="1">已结清</el-radio>
              <el-radio label="0" value="0">未结清</el-radio>
              <el-radio label="all" value="all">全部</el-radio>
            </el-radio-group>
          </div>
        </el-row>
        <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
          <el-tab-pane label="待跟进" name="待跟进"> </el-tab-pane>
          <el-tab-pane label="每日跟进" name="每日跟进"> </el-tab-pane>
          <el-tab-pane label="重点跟进" name="重点跟进"> </el-tab-pane>
          <el-tab-pane label="难联跟进" name="难联跟进"> </el-tab-pane>
          <el-tab-pane label="失联中" name="失联中"> </el-tab-pane>
          <el-tab-pane label="特殊状态" name="特殊状态"> </el-tab-pane>
          <el-tab-pane label="全部" name="all"> </el-tab-pane>
        </el-tabs>

        <el-table v-loading="loading" ref="multipleTableRef" @sort-change="handleSortChange" :data="caseList"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
            <template #default="{ row, $index }">
              <div class="df-center">
                <el-tooltip v-if="row.labelContent" placement="top">
                  <template #content>{{ row.labelContent }}</template>
                  <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
                </el-tooltip>
                <el-button type="text" @click="toDetails(row.caseId, $index)"> {{ row.caseId }}</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="UID" align="center" key="uid" prop="uid" v-if="columns[1].visible" />
          <el-table-column label="调解阶段" align="center" key="disposeState" prop="disposeState" :width="90"
            v-if="columns[2].visible" />
          <el-table-column label="跟进调诉员" align="center" key="prosecutor" prop="prosecutor" :width="100"
            v-if="columns[3].visible" />
          <el-table-column label="资产包名称" align="center" key="packageName" prop="packageName" width="120px"
            v-if="columns[4].visible" show-overflow-tooltip />
          <el-table-column label="产品类型" align="center" key="productName" prop="productName" width="100px"
            v-if="columns[5].visible" show-overflow-tooltip />
          <el-table-column label="姓名" align="center" key="clientName" prop="clientName" width="90px"
            v-if="columns[6].visible" />
          <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" width="90px"
            v-if="columns[7].visible" />
          <el-table-column label="证件号码【户籍地】" align="center" key="clientIdcard" prop="clientIdcard" width="180px"
            v-if="columns[8].visible">
            <template #default="{ row }">
              <span>
                {{ `${row.clientIdcard}` }}<br />
                {{ `【${row.clientCensusRegister || "--"}】` }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" width="110px"
            v-if="columns[9].visible" />
          <el-table-column label="出生日期" align="center" key="clientBirthday" prop="clientBirthday" width="110px"
            v-if="columns[10].visible" />
          <el-table-column label="案件状态" align="center" key="caseState" prop="caseState" width="90px"
            :formatter="caseStateFor" v-if="columns[11].visible" />
          <el-table-column label="债权总金额" align="center" key="clientMoney" sortable="clientMoney" prop="clientMoney"
            width="130px" v-if="columns[12].visible">
            <template #default="{ row }">
              <span>{{ moneyFor(row.clientMoney) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余应还债权金额" width="140" align="center" key="remainingDue" prop="remainingDue"
            v-if="columns[13].visible" />
          <el-table-column label="剩余应还本金" align="center" key="syYhPrincipal" prop="syYhPrincipal" width="130"
            v-if="columns[14].visible" />
          <el-table-column label="逾期日期（末次）" sortable="overdueDays" align="center" key="overdueDays" prop="overdueDays"
            width="160px" v-if="columns[15].visible" />
          <el-table-column label="分配时间" align="center" key="allocatedTime" prop="allocatedTime" :width="160"
            v-if="columns[16].visible" show-overflow-tooltip />
          <el-table-column label="处置人员" align="center" key="odvName" prop="odvName" width="90px"
            v-if="columns[17].visible" show-overflow-tooltip />
          <el-table-column label="跟进日期" sortable="followUpAst" align="center" key="followUpAst" prop="followUpAst"
            v-if="columns[18].visible" width="160px" show-overflow-tooltip />
          <el-table-column label="跟进状态" align="center" key="followUpState" prop="followUpState" width="90px"
            v-if="columns[19].visible" show-overflow-tooltip />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <!-- 导出沟通记录 -->
    <exportDialog :query="addFieldsRange(queryParams, rangfiles)" :checkedType="checkedType[0]" ref="exportdialogRef" />
    <!-- 退案 -->
    <insertRetreat :caseNum="statistics.caseNum" @getList="getList" ref="insertRetreatRef" />

    <!-- 案件操作 -->
    <handleDialog :checkedType="checkedType[0]" :caseIds="caseIds" :query="queryParams" ref="handleDialogRef"
      @getList="getList" :destroy-on-close="true" />

    <!-- 模板分案 -->
    <tplDivision ref="tpldivisionRef" @getList="getList" />

    <!-- 标记案件 -->
    <leabelCaseVue @getList="getList" ref="leabelCaseRef" />
    <!-- 导出案件 -->
    <exportTip ref="exportTipRef" :exportType="`团队案件导出`" />
  </div>
</template>
<script setup name="TeamsCases">
import { ElMessage } from "element-plus";
import exportDialog from "./dialog/exportDialog.vue";
import insertRetreat from "./dialog/insertRetreat.vue";
import handleDialog from "./dialog/handleDialog.vue";
import leabelCaseVue from "./dialog/leabelCase.vue";
import tplDivision from "./dialog/tplDivision.vue";

import {
  getBatchNums,
  selectLabel,
  selectAssetOwner,
  getProvinces,
  getPackageList,
  exportSwtich
} from "@/api/common/common";
import { getTeamTree, selectTeamCase, selectCaseManagesMoney, exportCase } from "@/api/team/team";
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
//树结构
const tree = ref([]);
const filterText = ref("");
const defaultProps = {
  children: "children",
  label: "name",
};
const allDept = ref([{ id: "all", name: "全部" }]);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const multipleTableRef = ref();
const checkedType = ref(["本页选中"]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const caseList = ref([]);
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
const packageNameChecked = ref([]);
const checkMoreList = ref(["packageName"]);
const checkMoreName = ref([packageNameChecked]);
// 控制导出数据按钮
const exportStatus = ref(0);
//列表切换字段
const settleState = ref("all");
const activeTab = ref("all");
const clientCensusRegisterList = ref([]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    allocatedTime: undefined,
    entrustingCaseBatchNum: undefined,
    clientPhone: undefined,
    daysNotFollowed1: undefined,
    daysNotFollowed2: undefined,
    entrustingPartyId: undefined,
    clientCensusRegister: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    followUpState: undefined,
    settleState: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    followUpAst: undefined,
    area: undefined,
    label: undefined,
    stringDeptId: undefined,
    odvName: undefined,
    packageName: undefined,
    condition: false,
    syYhPrincipal1: undefined,
    syYhPrincipal2: undefined,
  },
  ruleses: {
    clientPhone: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
      // 这个只能验证手机号码
      // { pattern:/^0{0,1}(13[0-9]|15[7-9]|153|156|18[7-9])[0-9]{8}$/, message: "请输入合法手机号码", trigger: "blur" }
    ],
    clientIdcard: [{ required: false, message: "请输入证件号码", trigger: "blur" }],
  },
});
const { queryParams, ruleses } = toRefs(data);
//需要拆分的字段
const rangfiles = ["entrustingCaseDate", "returnCaseDate", "followUpAst", 'allocatedTime'];
//表单配置信息
const showSearch = ref(false);
//表单数据集合
const batchs = ref([]);
const entrusts = ref([]);
const packageNameList = ref([]);
const areas = ref([]);
const tags = ref([]);
// 列显隐信息
const columns = ref([
    { "key": 0, "label": "案件ID", "visible": true },
    { "key": 1, "label": "UID", "visible": true },
    { "key": 2, "label": "调解阶段", "visible": true },
    { "key": 3, "label": "跟进调诉员", "visible": true },
    { "key": 4, "label": "资产包名称", "visible": true },
    { "key": 5, "label": "产品类型", "visible": true },
    { "key": 6, "label": "姓名", "visible": true },
    { "key": 7, "label": "证件类型", "visible": true },
    { "key": 8, "label": "证件号码【户籍地】", "visible": true },
    { "key": 9, "label": "手机号码", "visible": true },
    { "key": 10, "label": "出生日期", "visible": true },
    { "key": 11, "label": "案件状态", "visible": true },
    { "key": 12, "label": "债权总金额", "visible": true },
    { "key": 13, "label": "剩余应还债权金额", "visible": true },
    { "key": 14, "label": "剩余应还本金", "visible": true },
    { "key": 15, "label": "逾期日期（末次）", "visible": true },
    { "key": 16, "label": "分配时间", "visible": true },
    { "key": 17, "label": "处置人员", "visible": true },
    { "key": 18, "label": "跟进日期", "visible": true },
    { "key": 19, "label": "跟进状态", "visible": true }
]);
const lableColor = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];

//获取部门案件
function getList() {
  queryParams.value.followUpState =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.settlementStatus =
    settleState.value == "all" ? undefined : settleState.value;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });
  loading.value = true;
  selectTeamCase(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//获取部门列表
function getTeamTreeData() {
  getTeamTree().then((res) => {
    let formatterDept = [...allDept.value, ...res.data];
    tree.value = formatterDept;
  });
}
getTeamTreeData();

// 设置导出按钮显示与隐藏
function getExportSwtich() {
  exportSwtich().then((res) => {
    exportStatus.value = res.data.exportStatus;
  });
}
getExportSwtich();

//户籍地
function CensusRegisters() {
  getProvinces().then((res) => {
    areas.value = res.data;
  });
}
CensusRegisters()

//获取资产包名称
function getPackageName() {
  getPackageList().then((res) => {
    packageNameList.value = res.data
  })
}

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//获取转让方
function OwnerList() {
  selectAssetOwner().then((res) => {
    entrusts.value = res.data;
  });
}

//机构过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { syYhPrincipal1, syYhPrincipal2 } = queryParams.value;
  // 检测输入是否是数字
  if (syYhPrincipal1 && !Number.isFinite(Number(syYhPrincipal1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal1 = undefined;
  }
  if (syYhPrincipal2 && !Number.isFinite(Number(syYhPrincipal2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal2 = undefined;
  }
  if (!syYhPrincipal1 || !syYhPrincipal2) return;
  const principal1 = parseFloat(syYhPrincipal1);
  const principal2 = parseFloat(syYhPrincipal2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal2 = undefined;
  }
};
// 排序
function handleSortChange({ prop, order }) {
  const orderObj = { clientMoney: 3, overdueDays: 4, followUpAst: 5 }
  const orderBy = orderObj[prop];
  queryParams.value.sortOrder = proxy.orderEnum[order]
  queryParams.value.orderBy = orderBy;
  getList();
}
//节点点击事件
function getNode(node, details) {
  queryParams.value.stringDeptId = node?.id == "all" ? undefined : node.id;
  getList();
}

//拼接字段
function changeOption() {
  if (clientCensusRegisterList.value?.length > 0) {
    let checkedMap = proxy.$refs['clientCensusRegisterRef'].getCheckedNodes();
    let checkedMapLabel = checkedMap.map(item => item.label)
    let provinceList = clientCensusRegisterList.value.map((item, index) => {
      let prolabel = ""
      if (checkedMapLabel.includes(item?.[0])) {
        prolabel = item?.[0]
      }
      return prolabel
    })
    let clientCensusRegisterFormat = Array.from(new Set(provinceList.filter(item => item?.length > 0))).toString()
    clientCensusRegisterList.value.forEach((item, index) => {
      if (provinceList.includes(item?.[0])) {
        return true
      } else {
        let text = "";
        item.forEach((v, h) => {
          text = text + v;
        })
        clientCensusRegisterFormat = clientCensusRegisterFormat + `${index == 0 && clientCensusRegisterFormat.length == 0 ? '' : (text.length > 0 ? ',' : '')}${text}`
      }
    })
    queryParams.value.clientCensusRegister = clientCensusRegisterFormat
  } else {
    queryParams.value.clientCensusRegister = undefined;
  }
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//结算类型切换
function settleStateChange() {
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/teamIndex-detail/teamCaseDetails/${caseId}`, query: { type: "caseManage" } });
}

//表格行能否选择
function checkSelectable() {
  if (checkedType.value[0] === "本页选中" || checkedType.value.length === 0) {
    return true;
  } else {
    return false;
  }
}

//案件状态 0-未分配 1-已分配 2-停催 3-留案 4-退案 5-回收案件 6-案件结清
function caseStateFor(row) {
  return ["未分配", "已分配", "停催", "留案", "退案", "回收案件", "案件结清"][
    row.caseState
  ];
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;

  if (selectedArr.value.length) {
    //选择了案件时
    checkedType.value = queryParams.value.condition ? ["搜索结果全选"] : ["本页选中"];
    checkStatus.value.map((item) => {
      //全选/半选样式变化
      if (item.label === checkedType.value[0]) {
        item.indeterminate =
          selectedArr.value.length > 0 &&
          selectedArr.value.length < caseList.value.length;
      }
    });
  } else {
    checkedType.value = [];
  }
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    allocatedTime: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    clientPhone: undefined,
    daysNotFollowed1: undefined,
    daysNotFollowed2: undefined,
    entrustingPartyId: undefined,
    clientCensusRegister: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    caseState: undefined,
    settleState: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    followUpAst1: undefined,
    followUpAst2: undefined,
    area: undefined,
    label: undefined,
    odvName: undefined,
    packageName: undefined,
    deptId: undefined,
    condition: false,
  };
  packageNameChecked.value = [];
  clientCensusRegisterList.value = [];
  getList();
}

function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    multipleTableRef.value.clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else if (checkedType.value?.[0] == "搜索结果全选") {
    nextTick(() => {
      caseList.value.length > 0 &&
        caseList.value.map((item) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
    });
  } else {
    caseList.value.length > 0 &&
      caseList.value.map((item) => {
        multipleTableRef.value.toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.condition = true;
  } else {
    queryParams.value.condition = false;
  }
}

//标签下拉
function getTagList() {
  selectLabel().then((res) => {
    tags.value = res.data;
  });
}

function openLabelCase() {
  let data = {};
  data = proxy.addFieldsRange(queryParams.value, rangfiles);
  data.pageNum = undefined;
  data.pageSize = undefined;
  if (checkedType.value[0] == "本页选中") {
    Object.assign(data, { caseIds: caseIds.value });
  } else {
    data.condition = true;
  }
  proxy.$refs["leabelCaseRef"].opendialog(data);
}

//打开模板分案
function openTpldivision() {
  proxy.$refs["tpldivisionRef"].opendialog();
}

//退案，留案操作
function opendialog(type) {
  let query = JSON.parse(JSON.stringify(queryParams.value));
  if (checkedType.value[0] == "本页选中") {
    query.caseIds = caseIds.value;
    query.condition = false;
  } else {
    query.condition = true;
  }
  query.pageNum = undefined;
  query.pageSize = undefined;
  proxy.$refs["insertRetreatRef"].opendialog(type, query);
}

//停催
function toStop(data) {
  proxy.$refs["handleDialogRef"].opendialog(data.type, data.row);
}

//搜索部门
watch(filterText, (val) => {
  proxy.$refs["treeRef"].filter(val);
});

//指定分案
function toPoint(type, row) {
  let obj = queryParams.value;
  if (checkedType.value[0] == `本页选中`) {
    obj.caseIds = caseIds.value;
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: checkedType.value[0],
    query: obj,
  };
  localStorage.setItem(`point/${time}`, JSON.stringify(objStorage));
  store.dispatch('tagsView/delCachedView', { name: "TeamPoint" })
  router.push({ path: `/teamcase/caseManage-outcase/point/23`, query: { time } });
}

//规则分案
function toRules() {
  let obj = {};
  if (checkedType.value[0] === "本页选中") {
    obj = selectedArr.value;
  } else {
    obj = queryParams.value;
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: checkedType.value[0],
    query: obj,
    queryParams: queryParams.value,
  };
  localStorage.setItem(`rules/${time}`, JSON.stringify(objStorage));
  store.dispatch('tagsView/delCachedView', { name: "TeamRules" })
  router.push({ path: `/teamcase/caseManage-outcase/rules/23`, query: { time } });
}

//tab选择
function tabChange() {
  getList();
  let type = checkedType.value[0];
  if (type === "本页选中") {
    getStaticForID();
  } else if (type === "搜索结果全选") {
    getStaticForQuery();
  } else {
    statistics.value = {
      caseNum: 0,
      totalMoney: 0,
      principal: 0,
    };
  }
}

//监听列表选择
watch([selectedArr, checkedType], (newval) => {
  nextTick(() => {
    let type = newval[1][0];
    if (type === "本页选中") {
      getStaticForID();
    } else if (type === "搜索结果全选") {
      getStaticForQuery();
    } else {
      statistics.value = {
        caseNum: 0,
        totalMoney: 0,
        principal: 0,
      };
    }
  });
});

//获取案件统计数据
function getStaticForID() {
  let form = JSON.parse(JSON.stringify(queryParams.value));
  delete form.pageNum;
  delete form.pageSize;
  Object.assign(form, { caseIds: caseIds.value });
  const reqForm = proxy.addFieldsRange(form, rangfiles)
  selectCaseManagesMoney(reqForm).then((res) => {
    statistics.value = {
      caseNum: res.data.size,
      totalMoney: res.data.money,
      principal: res.data.principal,
    };
  });
}
function getStaticForQuery() {
  let form = JSON.parse(JSON.stringify(queryParams.value));
  delete form.pageNum;
  delete form.pageSize;
  const reqForm = proxy.addFieldsRange(form, rangfiles)
  selectCaseManagesMoney(reqForm).then((res) => {
    statistics.value = {
      caseNum: res.data.size,
      totalMoney: res.data.money,
      principal: res.data.principal,
    };
  });
}

//导出沟通记录
function handleExport() {
  const condition = queryParams.value.condition;
  proxy.$refs["exportdialogRef"].opendialog(0, caseIds.value, condition);
}

//导出案件
function downloadCase() {
  proxy.$modal.loading("正在导出数据，请稍候...");
  let reqForm = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles))
  );
  reqForm.caseIds = caseIds.value;
  exportCase(reqForm).then((res) => {
    proxy.$modal.closeLoading();
    proxy.$modal.exportTip(res.data)
  }).catch((error) => {
    proxy.$modal.closeLoading();
  });
}

//监听选择发生改变
watch(caseList, (newval, preval) => {
  multipleTableRef.value.toggleAllSelection();
});

// 格式化金额
function moneyFor(num, str = '') {
  return num ? proxy.setNumberToFixed(num) : str
}

</script>
<style scoped>
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

:deep(.el-tree-node__label) {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.df-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
