<template>
    <el-dialog v-loading="loading" v-model="open" append-to-body @before-close="cancel" width="auto" title="并案号">
        <el-steps class="mt20 mb20" :active="activeStep" align-center>
            <el-step title="立案登记" />
            <el-step title="选择立案文书" />
            <el-step title="预览文书" />
        </el-steps>
        <el-form :model="form" ref="formRef" :rules="rules" label-width="110px">
            <registerCheck v-if="activeStep == 1" v-model:form="form" :rules="rules" :courtOption="courtOption" />
            <filingWrit v-if="activeStep == 2" v-model:form="form" :rules="rules" />
        </el-form>
        <preview v-if="activeStep == 3" v-model:form="form" :preivewArr="preivewArr" :rules="rules" />
        <template #footer>
            <div class="text-center">
                <el-button :loading="loading" v-if="activeStep == 1" @click="cancel">取消</el-button>
                <el-button :loading="loading" v-if="activeStep != 1" @click="preStep">上一步</el-button>
                <el-button :loading="loading" v-if="activeStep != 3" type="primary" @click="nextStep">下一步</el-button>
                <el-button :loading="loading" v-if="activeStep == 3" type="primary" @click="submit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getCourtOptions } from '@/api/common/common';
import filingWrit from './components/filingWrit';
import registerCheck from './components/registerCheck';
import preview from './components/preview';
import { batchAddLitigationDoc, documentPreview } from '@/api/collection/mycase';
import { filingCaseOnlineFiling } from '@/api/mediation/onlineFiling';
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const courtOption = ref([])
const preivewArr = ref([])
const loading = ref(false)
const activeStep = ref(1)
const data = reactive({
    form: {},
    rules: {
        filingNumber: [{ required: true, message: '请输入立案号', trigger: 'blur' }],
        courtId: [{ required: true, message: '请选择法院', trigger: 'blur' }],
        filingTime: [{ required: true, message: '请选择立案时间', trigger: 'blur' }],
        trialTime: [{ required: true, message: '请选择开庭时间', trigger: 'blur' }],
        // contractor: [{ required: true, message: '请选择承办员', trigger: 'blur' }],
        // clerk: [{ required: true, message: '请输入书记员', trigger: 'blur' }],
        result: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
        mediationRecord: [{ required: true, message: '请选择调解记录', trigger: 'blur' }],
        documentTemplateIds: [{ required: true, message: '请选择立案文书', trigger: 'change' }],
    },
})
const uid = ref(undefined)
const open = ref(false)
const { form, rules } = toRefs(data)

// 提交
function submit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const court = courtOption.value.find(item => item.code == form.value.courtId)
            const reqForm = JSON.parse(JSON.stringify(form.value))
            reqForm.courtName = court?.info
            reqForm.lawAgencyId = court?.code
            reqForm.uid = uid.value
            batchAddLitigationDoc(reqForm).then((res) => {
                if (res.code == 200) {
                    cancel()
                    props.getList && props.getList()
                    const htmlContent = `
                <div>
                     <div>已提交电子签章文书审核，可以在案件详情查看文书材料</div>   
                     <div>待审核人员审核通过，即可以查看或下载带电子签章的文书</div>   
                </div>
                `
                    ElMessageBox.alert(htmlContent, '提交审核', {
                        confirmButtonText: '确定',
                        dangerouslyUseHTMLString: true,
                    })

                }
            }).finally(() => loading.value = false)
        }
    })
}

// 上一步
function preStep() {
    activeStep.value--
}

// 下一步
const nextStep = () => {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            if (activeStep.value == 1) {
                proxy.$refs['formRef'].validate(valid => {
                    if (valid) {
                        loading.value = true
                        const reqForm = JSON.parse(JSON.stringify(form.value))
                        const court = courtOption.value.find(item => item.code == form.value.courtId) || {}
                        reqForm.court = court?.info
                        filingCaseOnlineFiling(reqForm).then(res => {
                            activeStep.value++
                        }).finally(() => loading.value = false)
                    }
                })
                return false
            }
            if (activeStep.value == 2) {
                loading.value = true
                const court = courtOption.value.find(item => item.code == form.value.courtId) || {}
                const reqForm = JSON.parse(JSON.stringify(form.value))
                reqForm.courtName = court?.info
                reqForm.lawAgencyId = court?.code
                documentPreview(reqForm).then(res => {
                    if (res.code == 200) {
                        uid.value = res.data.uid
                        preivewArr.value = res.data.pdfFilePojos
                        activeStep.value++
                    }
                }).finally(() => loading.value = false)
                return false
            }
        }
    })
}

function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data }
}
function cancel() {
    proxy.resetForm('formRef')
    form.value = {}
    open.value = false
    activeStep.value = 1
}
getCourtOptionFun()
function getCourtOptionFun() {
    getCourtOptions().then(res => {
        courtOption.value = res.data
    })
}

defineExpose({ openDialog })
</script>
<style lang="scss">
.el-step__head.is-finish {
    .el-step__icon.is-text {
        color: #fff;
        border-color: #1890ff;
        background-color: #1890ff;
        box-shadow: 0px 0px 5px 0px #999;
    }
}

.el-step__title {
    font-size: 14px;
}

.el-step__head {
    .el-step__icon.is-text {
        color: #fff;
        border-color: #aaaaaa;
        background-color: #aaaaaa;
        box-shadow: 0px 0px 5px 0px #aaaaaa;
    }
}

.el-step__title.is-process {
    font-weight: normal;
    color: #333 !important;
}
</style>