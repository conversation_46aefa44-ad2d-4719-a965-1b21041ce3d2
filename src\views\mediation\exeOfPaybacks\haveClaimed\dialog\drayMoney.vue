<template>
    <el-dialog title="法院领款" v-model="open" width="600px" append-to-body :before-close="cancel">
        <el-form :model="form" ref="formRef" :rules="rules" label-width="130px">
            <el-form-item prop="courtId" label="对应法院">
                <el-select v-model="form.courtId" style="width:320px" filterable clearable placeholder="请选择对应法院">
                    <el-option v-for="item in courtOption" :key="item.code" :label="item.info" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item prop="accountName" label="对方户名">
                <el-input v-model="form.accountName" style="width:320px" placeholder="请输入对方户名" />
            </el-form-item>
            <el-form-item prop="openingInstitution" label="对方开户机构">
                <el-input v-model="form.openingInstitution" style="width:320px" placeholder="请输入对方开户机构" />
            </el-form-item>
            <el-form-item prop="accountNumber" label="对方账号">
                <el-input v-model="form.accountNumber" style="width:320px" placeholder="请输入对方账号" />
            </el-form-item>
            <el-form-item prop="serialNumber" label="账户明细编号-交易流水号">
                <el-input v-model="form.serialNumber" style="width:320px" placeholder="请输入账户明细编号-交易流水号" />
            </el-form-item>
            <el-form-item prop="refundAmount" label="还款金额">
                <el-input v-model="form.refundAmount" style="width:320px" placeholder="请输入还款金额" />
            </el-form-item>
            <el-form-item prop="refundTime" label="领款时间">
                <el-date-picker v-model="form.refundTime" style="width:320px" placeholder="请选择领款时间"
                    value-format="YYYY-MM-DD hh:mm:ss" type="datetime" />
            </el-form-item>
            <el-form-item prop="fileUrl" label="回款凭证">
                <FileUpload v-model:fileList="fileList" uploadFileUrl='/collection/uploadPayment'
                    :fileType="['jpg', 'png']" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
                <el-button :loading="loading" @click="cancel">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getCourtOptions } from '@/api/common/common';
import { registerRefundFilingCourt } from '@/api/mediation/filingCourt';
import FileUpload from '@/components/FileUpload';
const props = defineProps({
    getList: { type: Function }
})
const fileList = ref([])
const courtOption = ref([])
const { proxy } = getCurrentInstance()
const data = reactive({
    form: {},
    rules: {
        courtId: [{ required: true, message: '请选择法院', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入对方户名', trigger: 'blur' }],
        openingInstitution: [{ required: true, message: '请输入对方开户机构', trigger: 'blur' }],
        accountNumber: [{ required: true, message: '请输入对方账号', trigger: 'blur' }],
        serialNumber: [{ required: true, message: '请输入账户明细编号-交易流水号', trigger: 'blur' }],
        refundAmount: [{ required: true, message: '请输入还款金额', trigger: 'blur' }],
        refundTime: [{ required: true, message: '请选择领款时间', trigger: 'blur' }],
        fileUrl: [{ required: true, message: '请上传回款凭证', trigger: 'blur' }],
    },
})
const open = ref(false)
const loading = ref(false)
const { form, rules } = toRefs(data)
const query = ref({})
// 提交
function submit() {
    form.value.fileUrl = fileList.value[0]?.response.data.fileUrl[0]
    form.value.fileName = fileList.value[0]?.response.data.modifyName[0]
    nextTick(() => {
        proxy.$refs['formRef'].validate((vaild) => {
            if (vaild) {
                const reqForm = JSON.parse(JSON.stringify(query.value))
                reqForm.refundRecord = form.value
                const court = courtOption.value.find(item => item.code == form.value.courtId)
                reqForm.refundRecord.court = court?.info
                reqForm.refundRecord.executiveCourt = court?.info
                reqForm.refundRecord.courtId = court?.code
                loading.value = true
                registerRefundFilingCourt(reqForm).then(res => {
                    if (res.code == 200) {
                        props.getList && props.getList()
                        proxy.$modal.msgSuccess('操作成功！')
                        cancel()
                    }
                }).finally(() => loading.value = false)
            }
        })
    })
}

const openDialog = (data) => {
    open.value = true
    query.value = { ...data.query, ...data }
}
const cancel = () => {
    proxy.resetForm('formRef')
    fileList.value = []
    form.value = {}
    open.value = false
}

// 获取机构列表
getCourts()
function getCourts() {
    getCourtOptions().then((res) => {
        courtOption.value = res.data
    })
}
defineExpose({ openDialog })
</script>
