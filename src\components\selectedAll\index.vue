<template>
  <div class="select-area">
    <el-checkbox-group v-model="checkType" :disabled="dataList.length == 0" @change="handleChangeCheck">
      <el-checkbox v-for="item in checkList" :key="item.code" :label="item.code" :indeterminate="item.indeterminate">{{
        item.info }}</el-checkbox>
    </el-checkbox-group>
    <slot name="content" />
  </div>
</template>
<script setup>
const { proxy } = getCurrentInstance()
const emit = defineEmits(['update:allQuery'])
const props = defineProps({
  dataList: { type: Array, default: [] },// 表格数据
  selectedArr: { type: Array, default: [] },// 父组件（表格）选中的数据
  isPreKeep: { type: Boolean, default: false },// 是否保留上次选中的数据
  tableRef: { type: String, default: 'multipleTableRef' },// 表格赋值的ref
  allQuery: { type: <PERSON>olean, default: false },// 搜索结果全选
  cusTableRef: { type: Object, default: null },
})
const isAll = ref(false) // 是否全选
const loading = ref(false)
const checkType = ref([])
const checkList = ref([
  { code: 0, info: '本页选中', indeterminate: false },
  { code: 1, info: '搜索结果全选', indeterminate: false },
])

// 复选框选中
function handleChangeCheck(value) {
  checkList.value.forEach(item => item.indeterminate = false)
  const typeVal = value[value.length - 1]
  checkType.value = value.length > 0 ? [typeVal] : []
  const isCheck = value.length > 0
  handleCheckedData(props.dataList, isCheck)
  isAll.value = typeVal == 1
  nextTick(() => {
    emit("update:allQuery", typeVal == 1)
  })
}

// 处理列表选中的数据
function handleCheckedData(dataList, isCheck) {
  return new Promise((resolve, reject) => {
    try {
      const useTableRef = props.cusTableRef || proxy.$parent.$refs[props.tableRef]
      useTableRef.clearSelection()
      dataList.forEach(item => {
        useTableRef.toggleRowSelection(item, isCheck)
      })
      resolve()
    } catch (error) {
      reject(error)
    }
  })
}

// 监听表格选中数据的变化
watch(() => props?.selectedArr, (newVal) => {
  const typeVal = checkType.value[checkType.value.length - 1]
  checkList.value.forEach(item => item.indeterminate = false)
  if (typeVal != 1) {
    nextTick(() => {
      if (props.dataList.length > 0) {
        let newArr = newVal
        if (props.isPreKeep) {
          const arr1 = JSON.parse(JSON.stringify(props.selectedArr))
          const arr2 = JSON.parse(JSON.stringify(props.dataList))
          const map = new Map()
          arr1.forEach(item => {
            if (!map.has(JSON.stringify(item))) {
              map.set(JSON.stringify(item), item)
            }
          })
          newArr = arr2.filter(item => map.has(JSON.stringify(item)))
        }
        checkType.value = newArr.length == props.dataList.length ? [0] : []
        checkList.value[0].indeterminate = newArr.length > 0 && newArr.length < props.dataList.length
      }
    })
  } else if (!loading.value) {
    loading.value = true
    nextTick(() => {
      loading.value && handleCheckedData(props.dataList, true).finally(() => loading.value = false)
    })
  }
}, { immediate: true, deep: true })
watch(() => props.dataList, () => {
  if (props.allQuery) {
    emit("update:allQuery", false)
    nextTick(() => {
      handleCheckedData(props.dataList, true).finally(() => emit("update:allQuery", true))
    })
  }
  nextTick(() => {
    if (props.isPreKeep && props.selectedArr.length > 0) {
      const arr1 = JSON.parse(JSON.stringify(props.selectedArr))
      const map = new Map()
      arr1.forEach(item => {
        if (!map.has(JSON.stringify(item))) {
          map.set(JSON.stringify(item), item)
        }
      })
      const newArr = []
      props.dataList.forEach(item => {
        if (map.has(JSON.stringify(item))) {
          newArr.push(item)
        }
      })
      newArr.length > 0 && handleCheckedData(newArr, true)
    }
  })
}, { deep: true })
defineExpose({ isAll })
</script>
<style lang="scss" scoped>
.select-area {
  display: flex;
  align-items: center;

  span {
    font-size: 14px;
    margin-left: 10px;

    i {
      font-size: 18px;
      font-weight: bold;
      color: #ed5565;
      font-style: normal;
    }
  }
}
</style>