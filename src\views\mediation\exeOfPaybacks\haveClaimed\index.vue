<template>
  <div class="app-container">
    <el-form :model="queryParams" :loading="loading" ref="queryRef" :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
      inline label-width="100px">
      <el-form-item prop="caseId" label="案件ID">
        <el-input v-model="queryParams.caseId" style="width: 240px" placeholder="请输入案件ID" />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input v-model="queryParams.clientName" style="width: 240px" placeholder="请输入被告" />
      </el-form-item>
      <el-form-item prop="accountName" label="对方账户">
        <el-input v-model="queryParams.accountName" style="width: 240px" placeholder="请输入对方账户" />
      </el-form-item>
      <el-form-item prop="executiveCourt" label="对应法院">
        <el-select placeholder="请选择判决法院" style="width: 240px" v-model="queryParams.executiveCourt" @focus="getclosed">
          <el-option v-for="item in courtOption" :key="item.code" :label="item.info" :value="item.info" />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额" prop="syYhPrincipal">
        <div class="range-scope" style="width:240px">
          <el-input type="text" v-model="queryParams.amount1" @blur="validatePrincipalRange"
            @input="value => value.replace(/[^\d]/g, '')" clearable />
          <span>-</span>
          <el-input type="text" v-model="queryParams.amount2" @blur="validatePrincipalRange"
            @input="value => value.replace(/[^\d]/g, '')" clearable />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area">
      <el-button v-if="checkPermi(['exeOfPaybacks:haveClaimed:register'])" :disabled="selectedArr.length == 0"
        type="primary" @click="handleOpenDialog('drayMoneyRef')">回款登记</el-button>
      <el-button v-if="checkPermi(['exeOfPaybacks:haveClaimed:download'])" :disabled="selectedArr.length == 0"
        type="primary" @click="downloadCase">批量导出</el-button>
      <el-button v-if="checkPermi(['exeOfPaybacks:haveClaimed:import'])" type="primary"
        @click="handleOpenDialog('importDataRef')">批量导入</el-button>
      <el-button v-if="checkPermi(['exeOfPaybacks:haveClaimed:sendNote'])" :disabled="selectedArr.length == 0"
        type="primary" @click="handleOpenDialog('sendMessageRef')">发送短信</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <div class="mb10 hint">
      <selectedAll :dataList="dataList" v-model:all-query="allQuery" :selected-arr="selectedArr">
        <template #content>
          <div class="text-flex ml20">
            <span>剩余债权总额：</span>
            <span class="text-danger mr10">
              {{ numFilter(statistics.totalMoney) }}
            </span>
            <span>剩余债权本金：</span>
            <span class="text-danger mr10">
              {{ numFilter(statistics.principal) }}
            </span>
            <span>案件数量：</span>
            <span class="text-danger">{{ statistics.caseNum }}</span>
          </div>
        </template>
      </selectedAll>
    </div>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="selectionChange">
      <el-table-column type="selection" :selectable="selectable" align="right" />
      <el-table-column v-if="columns[0].visible" label="案件ID" align="center" key="caseId" prop="caseId" :width="100">
        <template #default="{ row, $index }">
          <el-button :disable="!checkPermi(['exeOfPaybacks:haveClaimed:detail'])" type="text"
            @click="toDetails(row, $index)">
            {{ row.caseId }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[1].visible" label="被告" align="center" key="clientName" prop="clientName"
        :min-width="100" show-overflow-tooltip />
      <el-table-column v-if="columns[2].visible" label="手机号码" align="center" key="phone" prop="phone" :width="110">
        <template #default="{ row }">
          <div>
            <span>{{ row.phone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[3].visible" label="标的额" align="center" key="remainingDue" prop="remainingDue"
        :width="120">
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[4].visible" label="身份证号码" align="center" key="clientIdcard" prop="clientIdcard"
        :width="180" />
      <el-table-column v-if="columns[5].visible" label="户籍地" align="center" key="clientCensusRegister"
        prop="clientCensusRegister" :min-width="160" />
      <el-table-column v-if="columns[6].visible" label="对应法院" align="center" key="executiveCourt" prop="executiveCourt"
        :min-width="150" show-overflow-tooltip />
      <el-table-column v-if="columns[7].visible" label="领款状态" align="center" key="disposeStage" prop="disposeStage"
        width="120px" />
      <el-table-column v-if="columns[8].visible" label="对方户名" align="center" key="accountName" prop="accountName"
        show-overflow-tooltip />
      <el-table-column v-if="columns[9].visible" label="对方开户机构" align="center" key="openingInstitution"
        prop="openingInstitution" width="180px" />
      <el-table-column v-if="columns[10].visible" label="对方账号" align="center" key="accountNumber" prop="accountNumber"
        :min-width="130" />
      <el-table-column v-if="columns[11].visible" label="交易流水号" align="center" key="serialNumber" prop="serialNumber"
        :min-width="130" />
      <el-table-column v-if="columns[12].visible" label="还款时间" align="center" key="refundTime" prop="refundTime"
        show-overflow-tooltip :width="160" />
      <el-table-column v-if="columns[13].visible" label="跟进人员" align="center" key="follower" prop="follower" />
      <el-table-column v-if="columns[14].visible" label="最近一次跟进时间" align="center" key="followUpTime" prop="followUpTime"
        width="160px" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <drayMoney :getList="getList" ref="drayMoneyRef" />
    <importData :getList="getList" ref="importDataRef" />
  </div>
</template>

<script setup name="haveClaimed">
import { checkPermi } from "@/utils/permission";
import { formatParams } from '@/utils/common';
import drayMoney from './dialog/drayMoney';
import importData from './dialog/importData';
import { getReturnList, totalMoneyReturn } from '@/api/mediation/exeOfPaybacks';
import { pageTypeEnum, isNoEnum } from '@/utils/enum';
import { getCourtOptions } from '@/api/common/common'

const router = useRouter();
const route = useRoute();
const pageType = 'exeOfPaybacks'
const { proxy } = getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const showSearch = ref(false)
const statistics = ref({ totalMoney: 0, principal: 0, caseNum: 0 })

const rangFields = []

const { queryParams } = toRefs(data);
const dataList = ref([]);

const allQuery = ref(false)
const selectedArr = ref([])
const courtOption = ref([])
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '手机号码', visible: true },
  { key: 3, label: '标的额', visible: true },
  { key: 4, label: '身份证号码', visible: true },
  { key: 5, label: '户籍地', visible: true },
  { key: 6, label: '对应法院', visible: true },
  { key: 7, label: '领款状态', visible: true },
  { key: 8, label: '对方户名', visible: true },
  { key: 9, label: '对方开户机构', visible: true },
  { key: 10, label: '对方账号', visible: true },
  { key: 11, label: '交易流水号', visible: true },
  { key: 12, label: '还款时间', visible: true },
  { key: 13, label: '跟进人员', visible: true },
  { key: 14, label: '最近一次跟进时间', visible: true },
])
//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = []
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.type = 1
  reqForm.disposeStage = '已领款'
  reqForm.condition = allQuery.value
  getReturnList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  handleQuery();
}

// 选择
function selectionChange(selection) {
  selectedArr.value = selection; // 选中的列表
}

// 是否可选
function selectable() {
  return !allQuery.value;
}

//导出数据
function downloadCase() {
  const reqForm = getReqParams()
  proxy.downloadforjson('/execute-case/exportWithRefundRecord', reqForm, `已领款_${+new Date()}.xlsx`)
}


// 获取机构列表
getCourts()
function getCourts() {
  getCourtOptions().then((res) => {
    courtOption.value = res.data
  })
}
function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map(item => item.caseId)
  const ids = row ? [row.caseId] : selectedArr.value.map(item => item.caseId)
  const newAllQuery = row ? false : allQuery.value
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds, ids }
  const data = { query, ...row, caseIds, allQuery: newAllQuery, ids, pageType, isBatchSend: 1, isLitigationExecute: 1 }
  data.condition = newAllQuery
  data.type = 1
  data.oneStatus = '回款登记'
  proxy.$refs[refName].openDialog(data)
}
watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0, };
      reslove()
      return false
    }
    nextTick(() => {
      const reqForm = getReqParams()
      reqForm.condition = allQuery.value
      totalMoneyReturn(reqForm).then((res) => {
        statistics.value = {
          caseNum: res.data.size,
          totalMoney: res.data.money,
          principal: res.data.principal,
        };
      }).finally(() => reslove());
    })
  })
}
provide("getList", Function, true);
getList();

//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      disposeStage: '已领款',
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      type: 1,
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: '已领款' }
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}
// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.condition = allQuery.value
  reqForm.type = 1
  reqForm.sign = pageTypeEnum[pageType]
  reqForm.disposeStage = '已领款'
  return reqForm
}
const htrxCall = computed(() => store.getters.htrxCall);
</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.table-title-question {
  display: inline-block;
  margin-left: 5px;
  color: var(--theme);
}
</style>
