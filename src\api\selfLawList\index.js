import request from '@/utils/request'

export function getLawListApi(params) {
  return request({
    url: '/lawsuitStatus/lawsuit/list',
    method: 'get',
    params
  })
}


export function cancelSuitApi(data) {
  return request({
    url: '/osapi/Lawsuit/cancelsuit',
    method: 'post',
    data
  })
}


export function jgApproveRevoke(data) {
  return request({
    url: '/zws_jg_approve/jgApproveRevoke',
    method: 'post',
    data
  })
}

// 查询诉讼进度列表
export function getLawProgressListApi(params) {
  return request({
    url: '/lawsuitStatus/process/list',
    method: 'get',
    params
  })
}
