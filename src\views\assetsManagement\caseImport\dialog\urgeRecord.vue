<template>
  <el-dialog
    title="导入沟通记录"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="106px">
      <el-form-item v-if="batchNum" label="导入批次">
        {{ batchNum }}
      </el-form-item>
      <el-form-item label="模板下载">
        <el-button type="primary" link @click="downTpl('urgeRecordTpl')"
          >点击下载沟通记录模板</el-button
        >
      </el-form-item>
      <el-form-item label="沟通记录上传" prop="fileUrl">
        <el-input v-show="false" v-model="form.fileUrl"></el-input>
        <el-upload
          ref="uploadRef"
          drag
          :limit="1"
          accept=".xls, .xlsx"
          :headers="upload.headers"
          :action="uploadUrl"
          :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange"
          :before-remove="handleRemove"
          :on-success="handleFileSuccess"
          :auto-upload="false"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div>
              <el-button type="success" @click="submitFile">上传到服务器</el-button>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { urgeRecordBatch, urgeRecordByAsset } from "@/api/assets/asset/asset";

const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const batchNum = ref("");
const emit = defineEmits(["urgeRecordTpl"]);

const data = reactive({
  form: {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  },
  rules: {
    fileUrl: [{ required: true, message: "请上传文件！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
const product = ref({})
const upload = reactive({
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/file/upload",
});
const uploadUrl = computed(() => {
  return upload.url.replace("/appeal", "");
}) 
const files = ref([]); //上传成功文件列表

//打开弹窗
function opendialog(row, prod) {
  if (product) {
    product.value = prod;
  } else {
    product.value = {};
  }
  if (row.id) {
    batchNum.value = row.batchNum;
    form.value.id = row.id;
  } else {
    batchNum.value = undefined;
    form.value.id = undefined;
  }
  open.value = true;
}

//下载文件
function downTpl(type) {
  emit(type);
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 30 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过30MB!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 30 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过30MB!");
    fileList.pop();
    return false;
  }
}

/* 文件移除 */
function handleRemove(file, fileList) {
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.response.data.name;
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == name) {
        files.value.splice(i, 1);
        form.value.fileUrl = undefined;
        form.value.originalFilename = undefined;
        break;
      }
    }
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    form.value.fileUrl = data.url;
    form.value.originalFilename = file.name;
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: file.name,
      modifyName: data.name,
      fileUrl: data.url,
    };
    files.value.push(obj);
  }
};

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  };
  files.value = [];
  batchNum.value = "";
  proxy.$refs["uploadRef"].clearFiles();
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.id) {
        urgeRecordByAsset(form.value)
          .then((res) => {
            loading.value = false;
            proxy.$modal.msgSuccess("操作成功");
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        form.value.productId = product.value.productId;
        urgeRecordBatch(form.value)
          .then((res) => {
            loading.value = false;
            proxy.$modal.msgSuccess("操作成功");
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
