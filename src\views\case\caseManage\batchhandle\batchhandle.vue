<template>
  <div class="app-container">
    <!-- 案件批量操作 -->
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="上传文件"></el-step>
        <el-step title="数据匹配"></el-step>
        <el-step title="任务执行"></el-step>
        <el-step title="导入完成"></el-step>
      </el-steps>
    </div>
    <div v-show="stepActive === 0" class="step-item pt20">
      <div v-if="!matching">
        <el-form class="width450 mt20" ref="formRef">
          <el-form-item label="文件导入">
            <el-upload ref="uploadRef" multiple :file-list="files" :limit="1" accept=".xls,.xlsx"
              :headers="upload.headers" :action="upload.url" :before-upload="handleFileUploadBefore"
              :on-change="handleEditChange" :before-remove="remove" :on-success="handleFileSuccess" :auto-upload="false"
              drag>
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  <span>只能上传xls文件，且不超过10Mb</span>
                </div>
                <el-button type="success" @click="submitFile">上传到服务器</el-button>
                <el-button type="primary" plain @click="downTpl">下载模板</el-button>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <div class="text-center mt30">
          <el-button @click="toBack">取消</el-button>
          <el-button type="primary" plain @click="handle(0)">批量申请停案</el-button>
          <!-- <el-button type="primary" plain @click="handle(1)">批量申请退案</el-button> -->
          <el-button type="primary" plain @click="handle(2)">批量申请留案</el-button>
        </div>
      </div>
      <div class="pro-hint-box" v-else>
        <el-progress :percentage="100" :duration="6" :indeterminate="true" :show-text="false" :stroke-width="12" />
        <div class="pro-hint text-center mb20">数据匹配中，等待时间较长......</div>
      </div>
    </div>

    <div v-show="stepActive === 1" class="step-item pt20 mt20">
      <div class="resluts mb8">
        <span class="tit">匹配成功数量条数：</span>
        <span class="text-navy">{{ resluts.success }}</span>
        <span class="tit ml10">匹配失败数量条数：</span>
        <span class="text-danger">{{ resluts.fails }}</span>
      </div>
      <el-table :data="failsTable">
        <el-table-column label="转让方" prop="entrustingPartyName" align="center" />
        <el-table-column label="姓名" prop="clientName" align="center" />
        <el-table-column label="证件号码" prop="clientIdcard" align="center" />
        <el-table-column label="失败原因" prop="error" align="center" :show-overflow-tooltip="true" />
      </el-table>
      <div class="text-center mt20">
        <el-button type="primary" plain @click="preStep">返回，重新上传</el-button>
        <el-button type="primary" @click="nextStep" :loading="loading">跳过失败数据，下一步</el-button>
      </div>
    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <div class="pro-hint-box">
        <el-progress :percentage="100" :duration="6" :indeterminate="true" :show-text="false" :stroke-width="12" />
        <div class="pro-hint text-center mb20">数据匹配中，等待时间较长......</div>
      </div>
    </div>

    <div v-show="stepActive === 3" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
        <p>{{ resultTxt }}</p>
      </div>
      <div class="text-center mt30">
        <el-button @click="toBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="BatchHandle">
import { getToken } from "@/utils/auth";
import {
  importDataStop,
  importKeepCase,
  importDataBack,
  insertStopUrging,
  insertDataBack,
  insertKeepCase,
} from "@/api/case/index/index";

const { proxy } = getCurrentInstance();

const stepActive = ref(0);
const loading = ref(false);
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/case/upload",
});
const files = ref([]); //上传成功文件列表
const matching = ref(false); //是否匹配中
const resluts = ref({
  success: 0,
  fails: 0,
  key: undefined,
});
const failsTable = ref([]);
const reqObj = ref({});
const submitType = ref(0);
const type_arr = ["停案", "退案", "留案"];
const tips_arr = [
  // '此操作会将所导入的案件，申请停催，成功后将会退回资产端且无法进行催记作业，是否确认',
  '此操作会将所导入的案件，申请停案，成功后将会退回主账号且调解员无法进行调解作业，是否确认',
  '此操作会将导入的案件申请退回到资产端，成功后无法进行催记，是否确认？',
  '此操作会将所导入的案件，申请留案，是否确认？'
]
const type_info = ref([importDataStop, importDataBack, importKeepCase]);
const submit_info = ref([insertStopUrging, insertDataBack, insertKeepCase]);
const resultTxt = ref("");

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  console.log(file)
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
}

/* 文件移除 */
function remove(file, fileList) {
  console.log(file);
  console.log(files.value);
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.response.data.name;
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == name) {
        console.log(i);
        files.value.splice(i, 1);
        console.log(files.value);
        break;
      }
    }
  }
  files.value = fileList
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: file.name,
      modifyName: data.modifyName,
      fileUrl: data.fileUrl,
      url: data.fileUrl,
      name: file.name,
    };
    files.value.push(obj);
  }
};

//下载模板
function downTpl() {
  proxy.download("case/exports", {}, `tpl_案批量操作模板.xlsx`);
}

//批量操作
function handle(type) {
  if (!files.value.length) {
    proxy.$modal.msgWarning("请上传文件后再操作吧～");
    return false;
  }
  proxy.$modal
    .confirm(tips_arr[type])
    .then(function () {
      let req = {
        fileUrl: files.value[0].fileUrl[0],
      };
      reqObj.value = req;
      matching.value = true;
      return type_info.value[type](req);
    })
    .then((res) => {
      matching.value = false;
      resluts.value.success = res.data.arrayList.length;
      resluts.value.fails = res.data.list.length;
      resluts.value.key = res.data?.key;
      failsTable.value = res.data.list;
      submitType.value = type;
      stepActive.value = 1;
    })
    .catch(() => {
      matching.value = false;
    });
}

//上一步
function preStep() {
  stepActive.value--;
}

//下一步
function nextStep() {
  if (!resluts.value.key)
    return proxy.$modal.msgWarning("没有匹配成功的案件,请返回重新上传文件！");
  loading.value = true;
  stepActive.value = 2;
  submit_info.value[submitType.value]({ key: resluts.value.key })
    .then((res) => {
      console.log(res);
      loading.value = false;
      resultTxt.value = `已成功申请${type_arr[submitType.value]}${res?.data ?? 0}条`;
      stepActive.value = 3;
    })
    .catch(() => {
      loading.value = false;
      stepActive.value = 2;
    });
}

//取消
//返回
const toBack = () => {
  const obj = { path: "/case/caseIndex" };
  proxy.$tab.closeOpenPage(obj);
};

</script>

<style lang="scss" scoped>
.step-item {
  width: 80%;
  margin: 0 auto;
}

.width450 {
  width: 450px;
  margin: 40px auto 0px auto;
}

.pro-hint-box {
  margin-top: 60px;
}

.pro-hint {
  font-size: 14px;
  color: #888888;
  margin-top: 50px;
}

.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}
</style>
