<template>
  <el-dialog title="来电弹屏" v-model="open" width="650px" append-to-body :before-close="cancel" :modal-append-to-body="false"
    :close-on-click-modal="false">
    <div class="">客户信息</div>

    <div class="mt20 mb20">
      <el-table height="120px" :header-cell-style="{background:'#EEEFF4',color:'#888888',width: '100%'}" v-loading="loading" :data="dataList" ref="multipleTableRef" @selection-change="idhandleSelectionChange">
          <el-table-column label="编号" prop="id" align="center"></el-table-column>
          <el-table-column label="客户名称" prop="caseName" align="center"></el-table-column>
          <el-table-column label="联系电话" prop="casePhone" align="center"></el-table-column>
          <el-table-column label="产品名称" prop="productName" align="center"></el-table-column>
          <el-table-column label="委托金额" prop="entrustMoney" align="center">
            <template #default="{ row }">
              <span>{{ numFilter(row.entrustMoney) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余本金" prop="remainMoney" align="center">
            <template #default="{ row }">
              <span>{{ numFilter(row.remainMoney) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="贷款时间" prop="loanTime" align="center"></el-table-column>
          <el-table-column label="逾期时间" prop="overTime" align="center"></el-table-column>
      </el-table>

      <pagination v-show="total > 10" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>

    
    <el-form :model="form" ref="formRef">
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" maxlength="300" show-word-limit rows="4"
          placeholder="请输入内容" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button class="submitBtn" type="primary" :loading="loading" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { popOnScreens, popOnScreenRemark } from "@/api/appreciation/customerList";
const emits = defineEmits(["submitFinish"]);
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
//提交数据
const data = reactive({
  form: {
    remark: undefined,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { form, queryParams } = toRefs(data);

const total = ref(1);


const dataList = ref([]);
const taskMessage = ref(undefined);

//打开窗口
function openDialog(message) {
  console.log(message);
  open.value = true;
  taskMessage.value = {
    taskUuid : message.taskUuid,
    casePhone : message.phone
  };
  nextTick(() => {
    // const formData = new FormData();
    const query = { ...queryParams.value, ...taskMessage.value };
    popOnScreens(query).then((res) => {
      // console.log(res);
      dataList.value = res.rows;
      total.value = res.total;
    })
  })
}

const getList = () => {
  const query = { ...queryParams.value, ...taskMessage.value };
  popOnScreens(query).then((res) => {
    // console.log(res);
    dataList.value = res.rows;
    total.value = res.total;
  })
}

//提交
function submit() {
  loading.value = true;
  let id = undefined;
  if (dataList.value && dataList.value.length > 0) {
    id = dataList.value[0].id;
  }
  popOnScreenRemark({
    casePhone: taskMessage.value.casePhone,
    remark: form.value.remark,
    id: id
  }).then((res) => {
    if( res.code == 200 ) {
      // console.log(res);
      proxy.$modal.msgSuccess(res.msg);
      loading.value = false;
      cancel();
    }
  }).finally(() => {
    loading.value = false;
  });
  // emits('submitFinish', form.value)
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
      remark: undefined,
  };
}

//取消
function cancel() {
  if (loading.value) return false;
  reset();
  open.value = false;
}

defineExpose({
  openDialog,
  cancel,
});
</script>
<style lang="scss" scoped>
:deep(.el-textarea),
.dialog-footer {
  margin-right: 20px;
}
</style>