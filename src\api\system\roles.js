import request from '@/utils/request'

// 角色列表
export function getRoleList(query) {
  return request({
    url: '/settings/findRole',
    method: 'get',
    params: query
  })
}

// 角色下拉
export function getRoleOptions() {
  return request({
    url: '/settings/selectRole',
    method: 'get'
  })
}

//新增角色
export function addrole(data) {
  return request({
    url: '/settings/insertRole',
    method: 'post',
    data: data
  })
}

//编辑角色
export function editrole(data) {
  return request({
    url: '/settings/updateRole',
    method: 'put',
    data: data
  })
}

//删除角色
export function delRole(id) {
  return request({
    url: '/settings/deleteRole/' + id,
    method: 'put'
  })
}