import request from '@/utils/request'

//查询数据概览信息
export function selectStatistics(query) {
  return request({
    url: '/data/outline/selectStatistics',
    method: 'get',
    params: query
  })
}

//查询委案批次维度最新数据
export function getCensusBatchList(query) {
  return request({
    url: '/data/outline/getCensusBatchList',
    method: 'get',
    params: query
  })
}

//查询员工批次维度最新数据
export function getCensusEmployeeList(query) {
  return request({
    url: '/data/outline/getCensusEmployeeList',
    method: 'get',
    params: query
  })
}

//查询成员名称下拉框
export function getEmployee(query) {
  return request({
    url: '/data/outline/getEmployee',
    method: 'get',
    params: query
  })
}

//获取团队所有分案批次_下拉框
export function getBatchNum(query) {
  return request({
    url: '/data/outline/getBatchNum',
    method: 'get',
    params: query
  })
}




