<template>
    <div>
        <el-form-item prop="mediationRecord" label="调解记录">
            <div class="label-list">
                <el-radio-group v-model="form.mediationRecord">
                    <el-row :gutter="24">
                        <el-col :span="8" v-for="item in labelOption" :key="item.code">
                            <el-radio :value="item.code" :label="item.code">
                                {{ item.info }}
                            </el-radio>
                        </el-col>
                    </el-row>
                </el-radio-group>
            </div>
            <div class="mb20">
                <el-button icon="Plus" v-if="!isAdd" type="success" @click="add">添加标签内容</el-button>
                <el-input v-if="isAdd" v-model="labelValue" @blur="handleBlurAdd" placeholder="请输入标签内容" />
            </div>
        </el-form-item>
        <el-form-item prop="content">
            <div>沟通内容（可使用标签便捷服务）：</div>
            <el-input v-model="form.content" type="textarea" :rows="4" maxlength="300" show-word-limit
                placeholder="请输入沟通内容" />
        </el-form-item>
    </div>
</template>
<script setup>
const props = defineProps({
    form: { type: Object, default: {} },
    rules: { type: Object, default: {} },
})
const labelValue = ref(undefined)
const isAdd = ref(false)
const labelOption = ref([
    { info: "承诺还款", code: "承诺还款" },
    { info: "接通后挂断", code: "接通后挂断" },
    { info: "拒接", code: "拒接" },
    { info: "承诺还款，较困难", code: "承诺还款，较困难" },
    { info: "查无此人", code: "查无此人" },
    { info: "通话中", code: "通话中" },
    { info: "空号", code: "空号" },
    { info: "号码转让", code: "号码转让" },
    { info: "承诺分期还款", code: "承诺分期还款" },
    { info: "忙线", code: "忙线" },
    { info: "无法接通", code: "无法接通" },
    { info: "另约时间", code: "另约时间" },
    { info: "无人接听", code: "无人接听" },
    { info: "等待减免", code: "等待减免" },
    { info: "停机", code: "停机" },
    { info: "其他", code: "其他" },
    { info: "客户声称已还款", code: "客户声称已还款" },
    { info: "关机", code: "关机" },
    { info: "电话留言", code: "电话留言" }
])
const add = () => {
    isAdd.value = true
}

// 失焦添加
function handleBlurAdd() {
    isAdd.value = false
    if (labelValue.value) {
        labelOption.value.push({ code: labelValue.value, info: labelValue.value })
        nextTick(() => {
            labelValue.value = undefined
        })
    }
}
</script>
<style lang="scss" scoped>
.label-list {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}
</style>