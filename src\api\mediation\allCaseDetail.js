import request from '@/utils/request'

// 我的案件客户列表
export function getMyCaseCustomerList(query) {
  return request({
    url: '/collection/selectCaseLabelList',
    method: 'get',
    params: query
  })
}

// 我的案件客户tab 标签统计数量
export function selectCaseLabelCount() {
  return request({
    url: '/collection/selectCaseLabelCount',
    method: 'get'
  })
}

// 添加联系人
export function insertInfoContact(data) {
  return request({
    url: '/collection/insertInfoContact',
    method: 'post',
    data
  })
}

// 修改联系人
export function updateInfoContact(data) {
  return request({
    url: '/collection/updateInfoContact',
    method: 'put',
    data
  })
}

// 通过坐席号获取线路
export function getCallNetworkBySipNumber(query) {
  return request({
    url: '/settings/getCallNetworkBySipNumber',
    method: 'get',
    params: query
  })
}

// 贷款信息
export function selectCaseDetails(query) {
  return request({
    url: '/collection/selectCaseDetails',
    method: 'get',
    params: query
  })
}
//案件信息
export function selectSameBatchCases(query) {
  return request({
    url: '/collection/selectSameBatchCases',
    method: 'get',
    params: query
  })
}
// 案件信息统计
export function selectSameBatchAmountMoney(query) {
  return request({
    url: '/collection/selectSameBatchAmountMoney',
    method: 'get',
    params: query
  })
}
// 获取沟通记录
export function selectNegotiateRecordList(query) {
  return request({
    url: '/collection/selectNegotiateRecordList',
    method: 'get',
    params: query
  })
}
// 保存沟通记录
export function addNegotiateRecord(data) {
  return request({
    url: '/collection/addNegotiateRecord',
    method: 'post',
    data
  })
}

// 根据线路名称查询通话记录
export function selectListByNetworkName(query) {
  return request({
    url: '/call/record/selectListByNetworkName',
    method: 'get',
    params: query
  })
}

// 申请协助调解、诉讼、外访（approveCode： teamwork、lawsuit、outside）
export function applyHandle(data) {
  return request({
    url: '/zws_jg_approve/jgAddApprove',
    method: 'post',
    data
  })
}
