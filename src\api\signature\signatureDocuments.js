import request from '@/utils/request'

// 获取模板类型数据
export function getSignType(query) {
    return request({
        url: '/signLog/sign_log/getSignType',
        method: 'get',
        params: query
    })
}

// 模板管理-列表
export function getMessageList(query) {
    return request({
        url: '/letter/message/list',
        method: 'get',
        params: query,
    });
}

//获取签章状态
export function getProceOptions(query) {
    return request({
        url: '/letter/message/getProceOptions',
        method: 'get',
        params: query,
    });
}

//获取签章状态
export function getItemList(query) {
    return request({
        url: '/letter/item/list',
        method: 'get',
        params: query,
    });
}

// 下载签章文件
export function downloadFile(data) {
    return request({
        url: '/letter/message/export',
        method: 'post',
        data: data
    });
}

export function checkUniqueName(query) {
    return request({
        url: '/letter/message/checkUniqueName',
        method: 'get',
        params: query
    });
}

//获取签章审批流程
export function getProce(query) {
    return request({
        url: '/case/selectSingApproveProce',
        method: 'get',
        params: query
    })
}
