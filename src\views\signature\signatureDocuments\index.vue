<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-position="right"
      class="form-content h-50 mt20"
      :class="{ 'h-auto': showSearch }"
      label-width="120px"
    >
      <el-form-item label="模板类型" prop="templateId">
        <el-select
          v-model="queryParams.classifyIds"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择模板类型"
          clearable
          filterable
          :reserve-keyword="false"
          @focus="getTemplate"
          style="width: 240px"
        >
          <el-option
            v-for="item in classifyList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输模板名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="批次号" prop="batchNum">
        <el-input
          v-model="queryParams.batchNum"
          placeholder="请输入批次号"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="签章状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择签章状态"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in statusList"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="导入状态" prop="importStatus">
        <el-select
          v-model="queryParams.importStatus"
          placeholder="请输入导入状态"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 240px"
        >
          <el-option
            v-for="item in owners"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建人"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div class="text-center mb10">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >查询</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mt10 mb30">
      <el-button
        type="primary"
        class="mb10"
        v-hasPermi="['signature:signatureDocuments:newOutgoingLetter']"
        @click="addLawyer"
        >新建发函</el-button
      >
      <el-button
        type="warning"
        class="mb10"
        v-hasPermi="['signature:signatureDocuments:pdfLetter']"
        @click="importLawyer"
        >PDF发函</el-button
      >
      <right-toolbar
        :columns="columns"
        @queryTable="getList"
        v-model:showSearch="showSearch"
      ></right-toolbar>
    </el-row>

    <div class="mt10 mb10">
      <el-table v-loading="loading" :data="dataList" ref="multipleTableRef">
        <!--        <el-table-column type="selection" :selectable="checkSelectable" width="30px" align="right" />-->
        <el-table-column
          label="批次号"
          align="center"
          width="200"
          key="batchNum"
          prop="batchNum"
          v-if="columns[0].visible"
        />
        <el-table-column
          label="模板类型"
          align="center"
          key="classifyName"
          prop="classifyName"
          v-if="columns[1].visible"
        />
        <el-table-column
          label="模板名称"
          align="center"
          width="180"
          key="templateName"
          prop="templateName"
          v-if="columns[2].visible"
        />
        <el-table-column
          label="导入状态"
          align="center"
          key="importStatus"
          prop="importStatus"
          :formatter="importStatusFor"
          v-if="columns[3].visible"
        />
        <el-table-column
          label="审核状态"
          align="center"
          key="proce"
          prop="proce"
          v-if="columns[4].visible"
        >
          <template #default="scope">
            <el-popover
              placement="bottom"
              :width="600"
              :ref="`popover-${scope.$index}`"
              trigger="click"
            >
              <template #reference>
                <el-button @click="showPopover(scope.row)" type="text">{{
                  scope.row.examineState
                }}</el-button>
              </template>
              <el-table :data="gridData">
                <el-table-column width="200" property="approveTime" label="处理时间" />
                <el-table-column
                  width="100"
                  property="reviewer"
                  label="处理人"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  width="100"
                  property="approveStart"
                  :formatter="approveStartFor"
                  label="处理状态"
                />
                <el-table-column
                  width="180"
                  property="refuseReason"
                  label="原因"
                >
                <template #default="scope">
                    <Tooltip :content="scope.row.refuseReason" :length="10" />
                </template>
                </el-table-column>
              </el-table>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="签章状态"
          align="center"
          key="status"
          prop="status"
          :formatter="statusFor"
          v-if="columns[5].visible"
        />
        <el-table-column
          label="签章文件"
          align="center"
          key="quantity"
          prop="quantity"
          v-if="columns[6].visible"
        >
          <template #default="scope">
            <router-link
              class="text-primary"
              :to="`/signature/SigDocsList/${scope.row.id}`"
              >{{ scope.row.quantity }}</router-link
            >
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          key="remarks"
          prop="remarks"
          width="200px"
          v-if="columns[7].visible"
        >
          <template #default="scope">
              <Tooltip :content="scope.row.remarks" :length="10" />
          </template>
        </el-table-column>
        <el-table-column
          label="创建人"
          align="center"
          key="createBy"
          prop="createBy"
          show-overflow-tooltip
          v-if="columns[8].visible"
        />
        <el-table-column
          label="创建时间"
          align="center"
          width="250"
          key="createTime"
          prop="createTime"
          show-overflow-tooltip
          v-if="columns[9].visible"
        />
        <el-table-column fixed="right" align="center" width="250" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              v-hasPermi="['signature:signatureDocuments:downloadSignFile']"
              v-if="scope.row.status == 2"
              @click="downloadSignFile(scope.row)"
              >下载签章文件</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页器 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="signatureIndex">
import {
  getMessageList,
  downloadFile,
  getProce,
} from "@/api/signature/signatureDocuments";
import { getTemplateType } from "@/api/common/common";

//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const gridData = ref([]);
const gradLoading = ref(false);

// 列表显隐
const columns = ref([
  { key: 0, label: `批次号`, visible: true, width: 50 },
  { key: 1, label: `模版类型`, visible: true, width: 50 },
  { key: 2, label: `模版名称`, visible: true, width: 50 },
  { key: 3, label: `导入状态`, visible: true, width: 50 },
  { key: 4, label: `审核状态`, visible: true, width: 50 },
  { key: 5, label: `签章状态`, visible: true, width: 50 },
  { key: 6, label: `签章文件`, visible: true, width: 50 },
  { key: 7, label: `备注`, visible: true, width: 50 },
  { key: 8, label: `创建人`, visible: true, width: 50 },
  { key: 9, label: `创建时间`, visible: true, width: 50 },
]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);
// 搜索显隐
const showSearch = ref(false);
const store = useStore();
//数据参数
const dataList = ref([]);
const total = ref(0);
const classifyList = ref([]);
const rangFields = ["createTime"]; //范围字段
const statusList = ref([
  { code: "0", info: "失败" },
  { code: "1", info: "进行中" },
  { code: "2", info: "完成" },
]);

const owners = ref([
  { code: "0", info: "导入中" },
  { code: "1", info: "失败" },
  { code: "2", info: "成功" },
]);

//获取列表
function getList() {
  loading.value = true;
  let req = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields))
  );
  if (req.createTime1 && req.createTime2) {
    req.createTime1 = `${req.createTime1} 00:00:00`;
    req.createTime2 = `${req.createTime2} 23:59:59`;
  }
  getMessageList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取模板数据
function getTemplate() {
  const type = 0;
  // getSignType({ type }).then((res) => {
  //   classifyList.value = res.data;
  // });
  getTemplateType("model_type").then((res) => {
    classifyList.value = res.data;
  });
}

//气泡框展示
function showPopover(row) {
  gradLoading.value = true;
  let req = { id: row.id };
  getProce(req)
    .then((res) => {
      gradLoading.value = false;
      gridData.value = res.data;
    })
    .catch(() => {
      gradLoading.value = false;
    });
}

// 获取签章状态
// function getProce() {
//   // getProceOptions().then((res) => {
//   //   statusList.value = res.data;
//   // });
// }

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  getList();
}

// 导入状态
function importStatusFor(row) {
  return ["导入中", "失败", "成功"][row.importStatus];
}

// 签章状态
function statusFor(row) {
  if (!row.status) return "--";
  return ["失败", "进行中", "完成"][row.status];
}

//审核状态
function proceFor(row) {
  return ["待审核", "审核中", "已通过", "未通过"][row.proce];
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
  return ["已同意", "未同意", "待处理", "已完成"][row.approveStart];
}

// 新建发函
function addLawyer() {
  store.dispatch("tagsView/delCachedView", { name: "AddLawyer" });
  store.dispatch("tagsView/delCachedView", { name: "signatureIndex" });
  router.push("/signature/addLawyer");
}

// PDF发函
function importLawyer() {
  store.dispatch("tagsView/delCachedView", { name: "AddLawyer" });
  store.dispatch("tagsView/delCachedView", { name: "signatureIndex" });
  router.push({ path: "/signature/addLawyer", query: { isCompress: true } });
}

//下载签章文件
function downloadSignFile(row) {
  downloadFile({ batchNum: row.batchNum }).then((res) => {
    if (res && res.data && res.data[0]) {
      window.open(res.data[0].fileUrl);
    }
  });
}
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.popover-file {
  max-height: 340px;
  overflow: auto;
}

.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.text-blue {
  color: var(--theme);
}

.text-blue:hover {
  color: #79bbff;
}

.file-path {
  margin: 10px;
}

.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
