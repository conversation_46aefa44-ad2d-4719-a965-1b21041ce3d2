import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import locale from 'element-plus/lib/locale/lang/zh-cn' // 中文语言

import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive

// 防止多次点击
import preventReClick from './utils/preventReClick'


//绿色圆圈
import GreenCircle from "@/components/GreenCircle/index.vue";

// 注册指令
import plugins from './plugins' // plugins
import { download, downloadforjson, downloadforjsonGet } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { parseTime, resetForm, addDateRange, addFieldsRange, handleTree, setNumberToFixed, setNumberToFixedThree, copyUrl, antiShake, loadJs, newAddFieldsRange } from '@/utils/ruoyi'
import { formatAmountWithComma, numFilter } from "@/utils/common";

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 案件标签组件
import CaseLabel from '@/components/CaseLabel'
// 气泡组件
import Tooltip from '@/components/Tooltip'
//导出弹窗
import exportTip from "@/components/exportTip/index.vue";

import divisionalLoad from "@/components/divisionalLoad/index";
import myEditor from "@/components/myEditor/index";
// 本页选中
import selectedAll from "@/components/selectedAll/index";

// 数字输入框
import NumberInput from '@/components/InputNum'
// 拨打按钮
import callBarVue from '@/components/callBar/index.vue'
import workPhoneVue from '@/components/workPhone/index.vue'
import { orderEnum } from './utils/enum'
//Mrtc
const option = ref({
  fixedTop: 85,
  user: null,
  password: null,
});
// const webrtcSDKRef = ref(loadJs("https://amcmj_os.amcmj.com/webrtcSDK.umd.min.js"));
// console.log(webrtcSDKRef.value)
// loadJs("https://amcmj_os.amcmj.com/webrtcSDK.umd.min.js").then(() => {
//   console.log(webrtcSDK.callPhone)
//   // webrtcSDK.callPhone({phone:13266226685})
// })

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.download = download
app.config.globalProperties.downloadforjson = downloadforjson
app.config.globalProperties.downloadforjsonGet = downloadforjsonGet
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.copyUrl = copyUrl
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.addFieldsRange = addFieldsRange
app.config.globalProperties.newAddFieldsRange = newAddFieldsRange
app.config.globalProperties.setNumberToFixed = setNumberToFixed
app.config.globalProperties.setNumberToFixedThree = setNumberToFixedThree
app.config.globalProperties.orderEnum = orderEnum
app.config.globalProperties.antiShake = antiShake
app.config.globalProperties.formatAmountWithComma = formatAmountWithComma
app.config.globalProperties.numFilter = numFilter

// 全局组件挂载
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)
app.component('CaseLabel', CaseLabel)
app.component('Tooltip', Tooltip)
app.component('divisionalLoad', divisionalLoad)
app.component('myEditor', myEditor)
app.component('exportTip', exportTip)
app.component('selectedAll', selectedAll)
app.component('callBarVue', callBarVue)
app.component('GreenCircle', GreenCircle)
app.component('workPhoneVue', workPhoneVue)
app.component('NumberInput', NumberInput)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)
app.use(preventReClick);

directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
})

app.mount('#app')
