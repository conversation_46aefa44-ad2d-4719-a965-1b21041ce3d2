import requests from '@/utils/https'
//获取最近的5条消息
export function getMessageList(query) {
    return requests({
        url: '/team/getList',
        method: 'get',
        params:query
    })
}

//标记已读
export function markRead(data) {
    return requests({
        url: '/team/markRead',
        method: 'post',
        data:data
    })
}

//删除消息
export function deteleMessage(data) {
    return requests({
        url: '/team/delete',
        method: 'post',
        data:data
    })
}

//消息内容
export function getUserNotice(query) {
    return requests({
        url: '/team/getUserNotice',
        method: 'get',
        params:query
    })
}