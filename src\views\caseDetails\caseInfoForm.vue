<template>
    <formCom :form="infoBase" :form2="infoLoan" :formList="infoBaseList" />
</template>

<script setup>
import formCom from './components/formCom';

const props = defineProps({
  infoBase: {
    //案件详情信息
    type: Object,
    default: {},
  },
  infoLoan: {
    //案件详情信息
    type: Object,
    default: {},
  }
});
const infoBaseList = ref([
  { code: "clientIdType", info: "证件类型" },
  { code: "registeredAddress", info: "户籍地址" },
  { code: "clientBirthday", info: "出生日期" },
  { code: "uid", info: "UID" },
  { code: "clientSex", info: "性别" },
  { code: "maritalStatus", info: "婚姻状况" },
  { code: "education", info: "学历" },
  { code: "academicDegree", info: "学位" },
  { code: "employmentStatus", info: "就业状态" },
  { code: "residentialStatus", info: "居住状态" },
  { code: "homeAddress", info: "家庭地址" },
  { code: "homePhone", info: "家庭电话" },
  { code: "mailbox", info: "邮箱" },
  { code: "occupation", info: "职业" },
  { code: "duties", info: "职务" },
  { code: "title", info: "职称" },
  { code: "unitStartYear", info: "本单位工作起始年份" },
  { code: "placeOfWork", info: "单位名称" },
  { code: "unitIndustry", info: "单位所属行业" },
  { code: "workingAddress", info: "单位详细地址" },
  { code: "unitPostalCode", info: "单位所在地邮编" },
  { code: "unitTelephone", info: "单位电话" },
  { code: "residencePostalCode", info: "居住地邮编" },
  { code: "ycFiveLevel", info: "五级分类：", isVal: true },
  { code: "clientAge", info: "年龄" },
  { code: "qq", info: "QQ" },
  { code: "weixin", info: "微信" },
  { code: "residentialAddress", info: "居住地址" },
  { code: "securityName", info: "担保人" },
  { code: "securityIdType", info: "担保人证件类型" },
  { code: "securityIdNum", info: "担保人证件号码" },
  { code: "securityPhone", info: "担保人电话" },
  { code: "assetNo", info: "资产编号" }
])
</script>

<style></style>