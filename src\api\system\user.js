import request from '@/utils/request'

//获取员工列表 
export function getUserList(query) {
  return request({
    url: '/settings/selectEmployees',
    method: 'get',
    params: query
  })
}

//新建员工 
export function addUser(data) {
  return request({
    url: '/settings/insertEmployees',
    method: 'post',
    data: data
  })
}

//编辑员工 
export function editUser(data) {
  return request({
    url: '/settings/updateEmployees',
    method: 'put',
    data: data
  })
}

//删除员工
export function delUser(id) {
  return request({
    url: '/settings/deleteEmployees/'+id,
    method: 'put',
  })
}

//批量修改员工部门/角色
export function updateAccountStatus(data) {
  return request({
    url: '/settings/updateAccountStatus',
    method: 'put',
    data: data
  })
}

//批量重置密码
export function updatePassword(data) {
  return request({
    url: '/settings/updatePassword',
    method: 'put',
    data: data
  })
}

//导入员工
export function insertEmployeesBatch(data) {
  return request({
    url: '/settings/insertEmployeesBatch',
    method: 'post',
    data: data
  })
}

//获取用户登录日志
export function selectEmployeesLogin(query) {
  return request({
    url: `/settings/selectEmployeesLogin`,
    method: 'get',
    params:query
  })
}

//获取用户登录日志
export function selectTeamOperLog(query) {
  return request({
    url: `/sign/selectTeamOperLog`,
    method: 'get',
    params:query
  })
}

//修改密码
export function updatePassWord(data) {
  return request({
    url: `settings/updatePassWord`,
    method: 'PUT',
    data: data
  })
}

//获取坐席
export function getUnassignedSipList(query) {
  return request({
    url: `/call/getUnassignedSipList`,
    method: 'get',
    params: query
  })
}

//分配坐席
export function assignSip(data) {
  return request({
    url: `/call/assignSip`,
    method: 'post',
    data: data
  })
}

//坐席密码重置
export function updateSipPwd(data) {
  return request({
    url: `/call/updateSipPwd`,
    method: 'post',
    data: data
  })
}

//判断当前账号是否有坐席 
export function checkUserSip() {
  return request({
    url: `/call/checkUserSip`,
    method: 'get',
  })
}

//获取操作类型
export function getOperationOption() {
  return request({
    url: `/sign/operationType`,
    method: 'get',
  })
}

//获取当前团队的员工信息
// bindWorkPhone boolean 是否绑定工作手机 
export function getEmployeeInfo() {
  return request({
    url: `/create/getEmployeeInfo`,
    method: 'get',
  })
}




