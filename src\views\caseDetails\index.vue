<template>
  <div class="app-container">
    <el-row class="row-scroll" :gutter="20">
      <el-col :span="6" class="left-side">
        <div class="head-container flex">
          <el-input
            v-model="search"
            placeholder="请输入联系人/联系号码"
            clearable
            @click="clearSearch"
            style="margin-bottom: 20px; width: 235px"
          >
            <template #append>
              <el-button type="primary" @click="getContactList">
                <svg-icon icon-class="search" class="el-input__icon input-icon" />
              </el-button>
            </template>
          </el-input>

          <el-button class="mb20" type="primary" icon="Plus" plain @click="handleContact"
            >新增联系人</el-button
          >
        </div>

        <el-table
          v-loading="loading"
          :data="contactData"
          highlight-current-row
          @current-change="handleCurrentChange"
          ref="contactRef"
          max-height="360"
        >
          <el-table-column
            label="联系人/关系"
            prop="contactRelation"
            align="center"
            width="100"
          >
            <template #default="{ row }">
              <div class="wx-Name">
                {{ row.contactName }}
                <svg
                  v-if="row.appFlag === '1'"
                  t="1706507293373"
                  class="wx-Icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="4264"
                  width="32"
                  height="32"
                >
                  <path
                    d="M669.3 369.4c9.8 0 19.6 0 29.4 1.6C671 245.2 536.9 152 383.2 152 211.6 152 71 269.7 71 416.8c0 85 45.8 156.9 124.2 210.9l-31.1 93.2L273.6 667c39.2 8.2 70.3 16.3 109.5 16.3 9.8 0 19.6 0 31.1-1.6-6.5-21.3-9.8-42.5-9.8-65.4 0.1-135.7 116.2-246.9 264.9-246.9z m-168.4-85c24.5 0 39.2 16.3 39.2 39.2 0 22.9-16.3 39.2-39.2 39.2-24.5 0-47.4-16.4-47.4-39.2 0-24.5 24.6-39.2 47.4-39.2z m-216.3 73.1c-24.7 0-47.8-16.2-47.8-38.8 0-24.3 24.7-38.8 47.8-38.8s39.5 16.2 39.5 38.8c0.1 22.7-16.4 38.8-39.5 38.8z"
                    fill="#24DB5A"
                    p-id="4265"
                  ></path>
                  <path
                    d="M953.8 613c0-125.9-124.2-227.2-264.8-227.2-148.8 0-266.5 103-266.5 227.2 0 125.9 117.7 227.2 266.5 227.2 31.1 0 62.1-8.2 93.2-16.3l85 47.4-22.9-78.5c62.1-47.4 109.5-109.5 109.5-179.8z m-351.5-39.2c-14.7 0-31.1-14.7-31.1-31.1 0-14.7 16.3-31.1 31.1-31.1 22.9 0 39.2 16.3 39.2 31.1 0 16.4-14.7 31.1-39.2 31.1z m178-7.6c-14.8 0-31.3-14.6-31.3-30.7 0-14.6 16.5-30.7 31.3-30.7 23.1 0 39.5 16.2 39.5 30.7 0 16.2-16.4 30.7-39.5 30.7z"
                    fill="#24DB5A"
                    p-id="4266"
                  ></path>
                </svg>
              </div>
              <div>【{{ row.contactRelation }}】</div>
            </template>
          </el-table-column>
          <el-table-column label="联系方式" prop="contactPhone" align="center">
            <template #default="{ row }">
              <div style="cursor: pointer">
                <el-tooltip
                  :content="`${row.remarks ? row.remarks : '无备注！'}`"
                  effect="dark"
                  placement="top"
                >
                  <i
                    @click="handleContact(row)"
                    :class="`${row.phoneState == 0 ? 'greenTip' : 'reTip'}`"
                /></el-tooltip>
                {{ row.contactPhone }}
                <span v-if="isMyCase || listInfo.type == 'mycase'">
                  <callBarVue
                    :contactId="row.id"
                    :phoneState="row.phoneState"
                    :key="htrxCall"
                  />
                  <workPhoneVue :phoneNumber="row.contactPhone" :caseId="route.params.caseId" :borrower="caseInfoData.infoBase?.clientName"/>
                  <span v-if="!imgFor() && row.contactRelation == '本人'">
                    <span
                      class="send-icon ml5"
                      v-if="row.phoneState == 0"
                      @click="openSendMessage(row)"
                      ><el-icon color="#409eff" :size="16">
                        <ChatLineSquare :key="route.path" /> </el-icon
                    ></span>
                    <span class="send-icon ml5" v-if="row.phoneState != 0">
                      <el-icon class="note-icon" color="#b6b4b4" :size="18">
                        <ChatLineSquare />
                      </el-icon>
                    </span>
                  </span>
                </span>
                <el-icon v-if="row.importFlag != '1'" color="#f56c6c" @click="removeInfoContact(row.id)"><Delete /></el-icon>
              </div>
              <div>
                {{
                  `【${row.phoneState == 0 ? "有效" : "无效"} | ${
                    row.phoneLocation || "--"
                  }】`
                }}
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="mt20">
          <el-radio-group v-model="recordType" @change="getNote">
            <el-radio-button label="便签" />
            <el-radio-button label="投诉" />
          </el-radio-group>
          <div class="recordtext-style mb20" v-if="recordType == '便签'">
            便签（随手便可记录重要信息）
          </div>
          <div class="recordtext-style" v-else>投诉（记录投诉信息）</div>
          <el-input
            class="mb20 tips-textarea"
            type="textarea"
            v-model="recordValue"
            placeholder="请输入"
            maxlength="300"
            show-word-limit
          />
          <el-button type="primary" style="float: right" plain @click="saveRecord"
            >保存</el-button
          >
        </div>
      </el-col>

      <el-col :span="18">
        <el-row class="mb20 h32">
          <el-button type="primary" class="mr15" plain @click="openPayment"
            >案件回款</el-button
          >
          <Fragment v-if="isMyCase || listInfo.type == 'mycase'">
            <!-- <el-button type="info" plain @click="openhelpUrge">申请协催</el-button> -->
            <el-button
              type="success"
              v-if="authorizationStatus == 1"
              plain
              @click="openOutVisit"
              >申请外访</el-button
            >
            <el-button type="warning" plain @click="openstagesRepay">申请分期</el-button>
            <!-- <el-button type="danger" plain @click="openApplyData">申请案件资料</el-button> -->
            <el-button
              icon="Plus"
              type="primary"
              class="ml10"
              @getUrgeList="getUrgeList"
              plain
              @click="addharge()"
              >新增收费</el-button
            >
          </Fragment>
        </el-row>
        <div class="warp">
          <div class="case-title mb20">
            <div
              v-if="caseInfoData.label && caseInfoData.label.stateLabel == 1"
              class="stamp"
            >
              <caseLabel
                class="mr10"
                :code="caseInfoData.label.code"
                :text="caseInfoData.label.labelContent"
              />
              <el-tooltip content="修改标记" placement="top">
                <el-button type="text" icon="Edit" @click="editCaseLabel"></el-button>
              </el-tooltip>
            </div>
            <el-button
              v-else
              type="primary"
              plain
              round
              size="small"
              icon="Flag"
              @click="editCaseLabel"
              >标记案件</el-button
            >
            <div class="ml10" style="height: 40px; line-height: 40px;">
              <span style="font-size: 15px">案件详情</span>
              （案件ID：{{ route.params.caseId }}）
            </div>
          </div>

          <!-- 案件信息 -->
          <caseInfo
            :infoBase="caseInfoData.infoBase"
            :infoLoan="caseInfoData.infoLoan"
            :imgFor="imgFor"
            :jointDebtData="caseInfoData.caseManage"
            :infoExtra="caseInfoData.infoExtra"
            :infoPlan="caseInfoData.infoPlan"
            @getcaseDetailInfo="getcaseDetailInfo"
          />

          <div style="overflow-y: auto;">
            <caseInfoForm
              :infoBase="caseInfoData.infoBase"
              :infoLoan="caseInfoData.infoLoan" 
            />
            <!-- 共债等信息 -->
            <otherInfoVue
              :infoBase="caseInfoData.infoBase"
              :infoLoan="caseInfoData.infoLoan"
              :jointDebtData="caseInfoData.caseManage"
              :infoExtra="caseInfoData.infoExtra"
              :infoPlan="caseInfoData.infoPlan"
              :caseId="route.params.caseId"
              :count="caseInfoData.count"
              :accounts="caseInfoData.accounts"
            />

            <div v-if="isMyCase" class="mb20">
              <!-- 跟进结果（调诉诉讼记录） -->
              <el-radio-group v-model="infotype">
                <el-radio-button label="沟通记录" />
                <el-radio-button
                  label="调解登记"
                  v-if="['phoneMediation', 'mediateCarry'].includes(route.query.pageType)"
                />
                <el-radio-button
                  label="执行登记"
                  v-if="['lawsuitCarry', 'exeOfPaybacks'].includes(route.query.pageType)"
                />
                <el-radio-button
                  label="诉讼登记"
                  v-if="
                    ['onlineFiling', 'judgmentResult', 'filingCourt'].includes(
                      route.query.pageType
                    )
                  "
                />
                <el-radio-button
                  label="保全登记"
                  v-if="
                    [
                      'phoneMediation',
                      'iegalPreservation',
                      'onlineFiling',
                      'filingCourt',
                    ].includes(route.query.pageType)
                  "
                />
              </el-radio-group>
              <handleUrgeVue
                v-if="infotype == '沟通记录'"
                :contactOptions="contactData"
                :selected="currentRow"
                :caseId="route.params.caseId"
                @setCurrentRow="setCurrentRow"
                @getcaseDetailInfo="getcaseDetailInfo"
                @getUrgeList="getUrgeList"
                ref="handleUrgeRef"
              />
              <mediateRegister
                v-if="infotype == '调解登记'"
                :contactOptions="contactData"
                :selected="currentRow"
                :caseId="route.params.caseId"
                @setCurrentRow="setCurrentRow"
                @getcaseDetailInfo="getcaseDetailInfo"
                @getUrgeList="getUrgeList"
                ref="handleUrgeRef"
              />
              <executeRegister
                v-if="infotype == '执行登记'"
                :contactOptions="contactData"
                :selected="currentRow"
                :caseId="route.params.caseId"
                @setCurrentRow="setCurrentRow"
                @getcaseDetailInfo="getcaseDetailInfo"
                @getUrgeList="getUrgeList"
                ref="executeRef"
              />

              <lawsuitUrgeVue
                v-if="infotype == '诉讼登记'"
                :contactOptions="contactData"
                :selected="currentRow"
                :caseId="route.params.caseId"
                @setCurrentRow="setCurrentRow"
                @getcaseDetailInfo="getcaseDetailInfo"
                @getUrgeList="getUrgeList"
                ref="keepUrgeRef"
              />

              <keepUrgeVue
                v-if="infotype == '保全登记'"
                :contactOptions="contactData"
                :selected="currentRow"
                :caseId="route.params.caseId"
                @setCurrentRow="setCurrentRow"
                @getcaseDetailInfo="getcaseDetailInfo"
                @getUrgeList="getUrgeList"
                ref="keepUrgeRef"
              />
            </div>

            <recordsVue
              :key="route.path"
              ref="recordsRef"
              @getcaseDetailInfo="getcaseDetailInfo"
            />
          </div>


        </div>
      </el-col>
    </el-row>

    <!-- 添加/编辑联系人 -->
    <addContactVue
      :key="route.path"
      @getContactList="getContactList"
      @getcaseDetailInfo="getcaseDetailInfo"
      ref="addContactRef"
    />

    <!-- 案件回款 -->
    <paymentVue @getcaseDetailInfo="getcaseDetailInfo" ref="paymentRef" />

    <!-- 申请协催 -->
    <helpUrgeVue ref="helpUrgeRef" />

    <!-- 申请外访 -->
    <outVisitVue ref="outVisitRef" />

    <!-- 申请分期 -->
    <stagesRepay ref="stagesRepayRef" />

    <!-- 申请案件资料 -->
    <applyDataBox ref="applyDataBoxRef" />

    <!-- 标记案件 -->
    <labelCaseVue ref="labelCaseVueRef" @getList="getcaseDetailInfo" />

    <!-- 右侧悬浮按钮 -->
    <floatBarVue
      :key="route.path"
      @getUrgeList="getUrgeList"
      :isShow="isMyCase || listInfo.type == 'mycase'"
    >
      <!-- <template #opetTop>
        <div>
          <el-button type="primary" @click="openLawsuit">诉讼时效</el-button>
        </div>
      </template> -->
    </floatBarVue>

    <!-- 发送短信 -->
    <sendMessageVue
      :key="route.path"
      @getList="getList"
      @openResult="openResult"
      ref="sendMessageVueRef"
    />

    <!-- 发送结果 -->
    <sendResult ref="sendResultRef" />
    <lawsuitAging ref="lawsuitAgingRef" />

    <!-- 新增收费信息 -->
    <addChagreVue ref="addChagreVueRef" />
  </div>
</template>

<script setup>
import addChagreVue from "./dialog/addChagre";
import lawsuitUrgeVue from "./lawsuitUrge";
import keepUrgeVue from "./keepUrge";
import discontinuation from "@/assets/images/discontinuation.png";
import settle from "@/assets/images/settle.png";
import lawsuitAging from "./components/lawsuitAging";
import caseLabel from "@/components/CaseLabel/index";
import addContactVue from "./dialog/addContact";
import paymentVue from "./dialog/payment";
import helpUrgeVue from "../collection/mycase/dialog/helpUrge";
import recordsVue from "./records";
import outVisitVue from "./dialog/outVisit";
import applyDataBox from "./dialog/applyData";
import stagesRepay from "./dialog/stagesRepay";
import labelCaseVue from "./dialog/labelCase";
import caseInfo from "./caseInfo";
import otherInfoVue from "./otherInfo";
import handleUrgeVue from "./handleUrge";
import mediateRegister from "./mediateRegister";
import executeRegister from "./executeRegister";
import floatBarVue from "./floatBar";
import caseInfoForm from "./caseInfoForm.vue"
import callBarVue from "@/components/callBar/index";
import sendMessageVue from "../collection/mycase/dialog/sendMessage";
import sendResult from "../collection/mycase/dialog/sendResult.vue";
import {
  selectContact,
  selectNoteComplaint,
  editNote,
  editComplaint,
  getCaseInfo,
  updateInfoContact,
  delInfoContact
} from "@/api/caseDetail/detail";
const { proxy } = getCurrentInstance();
const route = useRoute();
const store = useStore();

const search = ref(undefined);
const loading = ref(false);
const contactData = ref([]);
const authorizationStatus = ref(0);
const currentRow = ref({}); //联系人选中

const recordType = ref("便签");
const recordValue = ref("");
const infotype = ref("沟通记录");
const caseInfoData = ref({}); //案件所有信息

const listInfo = ref({}); //包含分页信息、type：标记从哪个页面跳转过来、以此显示隐藏案件详情相关功能
const htrxCall = computed(() => store.getters.htrxCall);

onMounted(() => {
  listInfo.value = JSON.parse(localStorage.getItem(`searchInfo/${route.params.caseId}`));
});

watch(
  () => route.params,
  () => {
    if (route.params.caseId) {
      getContactList();
      getNote();
      getcaseDetailInfo();
      getUrgeList();
    }
  },
  { deep: true }
);

//获取案件信息
function getcaseDetailInfo() {
  getCaseInfo(route.params.caseId).then((res) => {
    caseInfoData.value = res.data;
  });
}
getcaseDetailInfo();
provide("getcaseDetailInfo", Function, true);

//获取案件联系人列表
function getContactList() {
  loading.value = true;
  let form = {
    caseId: route.params.caseId,
    contactNameOrPhone: search.value,
  };
  selectContact(form)
    .then((res) => {
      authorizationStatus.value = res.data.authorizationStatus;
      contactData.value = res.data.infoContacts;
      if (contactData.value.length) {
        setCurrentRow(contactData.value[0]);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
getContactList();

function getUrgeList() {
  proxy.$refs["recordsRef"]?.getReocrdList();
}
provide("getUrgeList", Function, true);

//获取便签/投诉记录
function getNote() {
  selectNoteComplaint(route.params.caseId).then((res) => {
    if (recordType.value == "便签") {
      recordValue.value = (res.data.Note && res.data.Note.noteContent) || "";
    } else {
      recordValue.value =
        (res.data.complaint && res.data.complaint.complaintContent) || "";
    }
  });
}
getNote();

//发送短信
function openSendMessage(row) {
  let query = {
    caseIds: [row.caseId],
    condition: false,
    allQuery: false,
    contactId: row.id,
  };
  let isBatchSend = row ? 0 : 1;
  proxy.$refs["sendMessageVueRef"].openDialog({ isBatchSend, query, pageType: "case" });
}

//新增收费
function addharge(row) {
  proxy.$refs["addChagreVueRef"].opendialog(row);
}
//打开短信结果
function openResult(data) {
  // proxy.$refs['sendResultRef'].opendialog(data)
}

//清除查询
function clearSearch() {
  search.value = undefined;
  getContactList();
}

//新增/编辑联系人
function handleContact(row) {
  let data = {
    caseId: route.params.caseId,
  };

  if (row.id) {
    data.phoneState = row.phoneState;
    data.id = row.id;
    data.remarks = row.remarks || "";
  }
  proxy.$refs["addContactRef"].opendialog(data);
}

//删除联系人
function removeInfoContact(id) {
  proxy.$modal.confirm(
        `是否确认删除该联系人，删除后不可恢复，是否确认`
    ).then(() => {
        delInfoContact({id}).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess('操作成功！')
          }
          getContactList();
        })
    })
}

//设置表格单选
function setCurrentRow(data) {
  if (data) {
    proxy.$refs["contactRef"]?.setCurrentRow(data);
  } else {
    proxy.$refs["contactRef"]?.setCurrentRow();
  }
}

//联系人表格单选
function handleCurrentChange(val) {
  currentRow.value = val;
}

//保存便签/投诉
function saveRecord() {
  let req = {
    caseId: route.params.caseId,
  };
  if (recordType.value == "便签") {
    req.noteContent = recordValue.value;
    editNote(req)
      .then((res) => {
        proxy.$modal.msgSuccess("保存成功！");
      })
      .finally(() => {
        getNote();
      });
  } else {
    req.complaintContent = recordValue.value;
    editComplaint(req)
      .then((res) => {
        proxy.$modal.msgSuccess("保存成功！");
      })
      .finally(() => {
        getNote();
      });
  }
}

//打开案件回款
function openPayment() {
  proxy.$refs["paymentRef"].opendialog();
}

//打开申请协催
function openhelpUrge() {
  let data = {
    condition: false,
    caseIds: [route.params.caseId],
  };
  proxy.$refs["helpUrgeRef"].opendialog(data);
}

//打开申请外访
function openOutVisit() {
  let data = {
    caseId: route.params.caseId,
  };
  proxy.$refs["outVisitRef"].opendialog(data);
}

//打开申请分期
function openstagesRepay() {
  proxy.$refs["stagesRepayRef"].opendialog(route.params.caseId);
}

//申请案件资料
function openApplyData() {
  proxy.$refs["applyDataBoxRef"].opendialog(route.params.caseId);
}

//修改案件标记
function editCaseLabel() {
  let data = {
    caseIds: [route.params.caseId],
    code: caseInfoData.value.label == null ? undefined : caseInfoData.value.label.code,
    condition: false,
    sign: route.query.twoStage,
  };
  proxy.$refs["labelCaseVueRef"].opendialog(data);
}

//返回
const toBack = () => {
  const obj = { path: `/collection/mycase-detail/caseDetails/${route.params.caseId}` };
  proxy.$tab.closeOpenPage(obj);
};

// 打开诉讼时效
function openLawsuit() {
  proxy.$refs["lawsuitAgingRef"].openDialog();
}
// 改变图片
function imgFor() {
  const imgObj = { 2: discontinuation, 6: settle };
  if (caseInfoData.value?.infoLoan) {
    const { caseState, settlementStatus } = caseInfoData.value?.infoLoan;
    const type = settlementStatus == 1 ? 6 : caseState;
    return imgObj[type];
  }
}
const isMyCase = computed(() => route.query.type == "record");
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.recordtext-style {
  font-size: 14px;
  color: rgb(191, 203, 217);
  padding: 15px 0 5px;
}

.warp {
  height: calc(100vh - 156px);
  overflow: hidden;
  //overflow-y: auto;
  display: flex;
  flex-direction: column;

  .case-title {
    height: 40px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: rgba(238, 239, 244, 0.39);
    padding: 0 20px;
    font-size: 14px;
    font-weight: 450;
    color: #515a6e;

    .stamp {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}

.reTip {
  display: inline-block;
  background: #f00;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: relative;
  z-index: 4;
  margin-right: 5px;
}

.greenTip {
  display: inline-block;
  background: #67c23a;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: relative;
  z-index: 4;
  margin-right: 5px;
}

.tips-textarea {
  :deep(.el-textarea__inner) {
    height: 150px;
  }
}

:deep(.el-dialog__body) {
  max-height: 800px;
}

.send-icon {
  position: relative;
  top: 2px;
  cursor: pointer;
}

.wx-Name {
  position: relative;
}

.fixed-operation-btn {
  display: flex;
  flex-direction: column;

  .el-button {
    width: 32px;
    height: auto;
    padding: 15px 8px;
    margin-left: 0;
    margin-bottom: 10px;
  }

  :deep(.el-button > span) {
    text-align: center;
    writing-mode: vertical-rl;
  }
}

.wx-Icon {
  position: absolute;
  /* right: 12px; */
  bottom: 0px;
  width: 20px;
}

.left-side {
  height: calc(100vh - 106px);
  overflow-y: auto;
}
</style>
