<template>
  <!-- 添加联系人 -->
  <el-dialog title="新增收费记录" v-model="open" width="600px" append-to-body :before-close="cancel">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="收费金额" prop="costAmt">
        <el-input v-model="form.costAmt" style="width: 360px" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="收费日期" prop="costDate">
        <el-date-picker v-model="form.costDate" value-format="YYYY-MM-DD" type="date" style="width: 360px"
          placeholder="请输入收费日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="款项类别" prop="costType">
        <el-select v-model="form.costType" clearable style="width: 360px" placeholder="请选择">
          <el-option v-for="(item, index) in costTypeList" :key="index" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="付款方式" prop="payMethod">
        <el-select v-model="form.payMethod" clearable placeholder="请选择" style="width: 360px">
          <el-option v-for="(item, index) in methodList" :key="index" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="收费对象" prop="feeRecipient">
        <el-input v-model="form.feeRecipient" style="width: 360px" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="入账单位" prop="accountUnit">
        <el-input v-model="form.accountUnit" style="width: 360px" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="收费回执" prop="receiptUrl">
        <div style="width:100%">
          <el-upload ref="uploadRef" :limit="1" accept=".jpg, .png" :headers="upload.headers" :action="upload.url"
            :file-list="files" :before-upload="handleFileUploadBefore" :on-change="handleEditChange"
            :before-remove="handleRemove" :on-success="handleFileSuccess" :auto-upload="false">
            <template #trigger>
              <el-button class="mr10" type="primary">选取文件</el-button>
            </template>
            <el-button class="ml10" type="success" @click="submitFile">上传到服务器</el-button>
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="form.remark" @blur="stateChange" :rows="5"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submit">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { getCostTypeOption, getPayMethodOption, addCost, editCost } from "@/api/caseDetail/detail";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(["getUrgeList"]);

const title = ref("");
const open = ref(false);
const loading = ref(false);
const costTypeList = ref([]);
const methodList = ref([])
const typeInfo = ref(0);
const infoList = ref([addCost, editCost])
const data = reactive({
  form: {
    caseId: route.params.caseId,
    remark: undefined,
    costAmt: undefined,
    costDate: undefined,
    costType: undefined,
    payMethod: undefined,
    feeRecipient: undefined,
    accountUnit: undefined,
    receiptUrl: undefined
  },
  rules: {
    costAmt: [
      { required: true, message: "请输入收费金额", trigger: "blur" },
      { pattern: /^\d{1,20}(\.[0-9]{1,4})?$/, message: '请输入1~20位数字，可以保留四位小数', trigger: "blur" }
    ],
    costDate: [{ required: true, message: "请选择收费日期", trigger: "blur" }],
    costType: [{ required: true, message: "请选择款项类别", trigger: "blur" }],
    payMethod: [{ required: true, message: "请选择付款方式", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
const files = ref([]); //上传成功文件列表
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/collection/uploadReduction",
});

//打开弹窗
function opendialog(data) {
  typeInfo.value = 0
  if (data) {
    typeInfo.value = 1
    form.value = JSON.parse(JSON.stringify(data))
    if (data.receiptUrl) {
      files.value = data.receiptUrl ? [{
        url: data.receiptUrl, name: data.receiptFileName
      }] : []
    }
  }
  getCostTypeList()
  getPaymethod()
  open.value = true;
}

//获取款项类别
function getCostTypeList() {
  getCostTypeOption().then((res) => {
    costTypeList.value = res.data;
  })
}

//获取付款方式
function getPaymethod() {
  getPayMethodOption().then((res) => {
    methodList.value = res.data;
  })
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    caseId: route.params.caseId,
    remark: undefined,
    costAmt: undefined,
    costDate: undefined,
    costType: undefined,
    payMethod: undefined,
    feeRecipient: undefined,
    accountUnit: undefined,
    receiptUrl: undefined
  };
  files.value = []
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
  const index = file.name.lastIndexOf('.')
  const fileType = file.name.slice(index, file.name.length)
  if (!['.jpg', '.png'].includes(fileType)) {
    proxy.$modal.msgWarning("请上传 .png、.jpg文件类型!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
  const index = file.name.lastIndexOf('.')
  const fileType = file.name.slice(index, file.name.length)
  if (!['.jpg', '.png'].includes(fileType)) {
    proxy.$modal.msgWarning("请上传 .png、.jpg文件类型!");
    return false;
  }
}

/* 文件移除 */
function handleRemove(file, fileList) {
  files.value = [];
  form.value.fileUrl = undefined;
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.receiptUrl = data.fileUrl[0];
    form.value.receiptFileName = data.firstName[0];
  }
};


//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = false;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = false;
      infoList.value[typeInfo.value](form.value).then(() => {
        proxy.$modal.msgSuccess("操作成功！");
        emit("getUrgeList");
        cancel();
      }).catch(() => {
        loading.value = false;
      });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>
