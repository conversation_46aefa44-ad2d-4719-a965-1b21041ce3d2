<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="82px">
        <el-form-item label="开户行名称" prop="bankName">
          <el-input
            v-model="queryParams.bankName"
            placeholder="开户行名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="账户状态" prop="accountStatus">
          <el-select
            v-model="queryParams.accountStatus"
            placeholder="请选择账户状态"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 240px"
          >
            <el-option
              v-for="item in accountList"
              :key="item.info"
              :label="item.label"
              :value="item.info"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-row class="mb8">
        <el-col :span="2.5" class="mr10">
          <el-button
            v-hasPermi="['finance:account:add']"
            type="primary"
            plain
            :disabled="loading"
            @click="addAccount(0)"
            >添加账户</el-button
          >
        </el-col>
        <el-col :span="2.5">
          <el-button
            v-hasPermi="['finance:account:export']"
            type="success"
            plain
            :disabled="loading"
            @click="exprotAccount()"
            >导出</el-button
          >
        </el-col>
      </el-row>
  
      <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
        <el-table-column
          label="开户行名称"
          align="center"
          key="bankName"
          prop="bankName"
          show-overflow-tooltip
        />
        <el-table-column
          label="账户名称"
          align="center"
          key="accountName"
          prop="accountName"
          show-overflow-tooltip
        />
        <el-table-column
          label="开户账户"
          align="center"
          key="accountNumber"
          prop="accountNumber"
          show-overflow-tooltip
        />
        <el-table-column
          label="账户状态"
          prop="accountStatus"
          key="accountStatus"
          align="center"
        >
        <template #default="{row}">
            <el-switch
              class="myswitch"
              v-model="row.accountStatus"
              active-color="#2ECC71"
              :active-value="0"
              :inactive-value="1"
              active-text="开"
              inactive-text="关"
              v-if="checkPermi(['finance:account:status'])"
              @change="stateChange(row)"
            ></el-switch>
            <span v-else>{{row.accountStatus == 0?'开启':'关闭'}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="添加人"
          align="center"
          key="createdBy"
          prop="createdBy"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          align="center"
          key="createdTime"
          prop="createdTime"
          show-overflow-tooltip
        />
        <el-table-column label="操作" fixed="right" width="180px">
          <template #default="{ row }">
            <el-button type="text" v-hasPermi="['finance:account:del']" @click="removeDate(row)">删除</el-button>
            <el-button type="text" v-hasPermi="['finance:account:edit']" @click="addAccount(1, row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加账户 -->
      <addAccountDialog @getList="getList" ref="addAccountDialogRef" />
    </div>
  </template>
  <script setup name="Account">
  import addAccountDialog from "./dialog/addAccount.vue";
  import { getAccountList, delAccount, updateAccount } from "@/api/system/account";
  import { checkPermi } from "@/utils/permission";
  //全局参数
  const router = useRouter();
  const { proxy } = getCurrentInstance();
  //请求参数
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      bankName: undefined,
      accountStatus: undefined,
    },
  });
  const accountList = ref([
    { info: 0, label: "开" },
    { info: 1, label: "关" },
  ]);
  //列表参数
  const dataList = ref([]);
  const loading = ref(false);
  const total = ref(0);
  const { queryParams } = toRefs(data);
  //请求数据
  function getList() {
    loading.value = true;
    getAccountList(queryParams.value)
      .then((res) => {
        loading.value = false;
        total.value = res.total;
        dataList.value = res.rows;
      })
      .catch(() => {
        loading.value = false;
      });
  }
  getList();
  
  //添加用户
  function addAccount(type, row) {
    let req =type == 1? JSON.parse(JSON.stringify(row)): undefined;
    proxy.$refs["addAccountDialogRef"].opendialog(type, req);
  }
  //删除数据
  function removeDate(row) {
    let req = {
      id: row.id,
    };
    proxy.$modal
      .confirm("删除后相关信息将无法恢复，是否确定删除？?")
      .then(function () {
        loading.value = true;
        return delAccount(req);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功！");
      })
      .catch((err) =>{
        loading.value = false;
      });
  }
  //改变账户状态
  function stateChange(row) {
    let req = {
      id: row.id,
      accountStatus: row.accountStatus,
      bankName: row.bankName,
      accountName: row.accountName,
      accountNumber: row.accountNumber,
    };
    updateAccount(req)
      .then((res) => {
        loading.value = false;
        proxy.$modal.msgSuccess(`${row.accountStatus == 0?'开启':'关闭'}成功`);
        getList();
      })
      .catch(() => {
        loading.value = false;
      });
  }
  
  
  //查询数据
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  
  //导出
  function exprotAccount() {
    proxy.download(
      "/teamAccount/export",
      {
        bankName: queryParams.value.bankName,
        accountStatus: queryParams.value.accountStatus,
      },
      `tpl_银行账户数据.xlsx`,
      { gateway: 'cis' }
    );
  }
  
  //重置
  function resetQuery() {
    proxy.resetForm("queryRef");
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      bankName: undefined,
      accountStatus: undefined,
    };
    getList();
  }
  
  </script>
  <style lang="scss" scoped></style>
  