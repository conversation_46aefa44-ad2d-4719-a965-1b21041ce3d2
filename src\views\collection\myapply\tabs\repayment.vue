<template>
  <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
    :inline="true" label-width="100px">
    <el-form-item label="案件ID" prop="caseId">
      <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="姓名" prop="clientName">
      <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="证件号码" prop="clientIdcard">
      <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="还款金额" style="width: 322px">
      <el-row>
        <el-col :span="10">
          <el-input type="number" v-model="queryParams.repaymentMoney1" clearable />
        </el-col>
        <el-col :span="3" class="text-center">
          <span>-</span>
        </el-col>
        <el-col :span="10">
          <el-input type="number" v-model="queryParams.repaymentMoney2" clearable />
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="转让方" prop="entrustingPartyId">
      <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
        :reserve-keyword="false" @focus="getEntrustingPartys" style="width: 240px">
        <el-option v-for="item in entrustingPartys" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="还款类型" prop="repaymentType">
      <el-select v-model="repaymentType" placeholder="请输入或选择还款类型" clearable filterable :reserve-keyword="false"
        @focus="getRepaymentTypes" style="width: 240px">
        <el-option v-for="item in repaymentTypes" :key="item.info" :label="item.info" :value="item.info" />
      </el-select>
    </el-form-item>
    <el-form-item label="债权总金额" style="width: 322px">
      <el-row>
        <el-col :span="10">
          <el-input type="number" v-model="queryParams.clientMoney1" clearable />
        </el-col>
        <el-col :span="3" class="text-center">
          <span>-</span>
        </el-col>
        <el-col :span="10">
          <el-input type="number" v-model="queryParams.clientMoney2" clearable />
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
  <div class="text-center">
    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
  </div>
  <el-row class="mb10 mt10 h32">
    <el-button v-hasPermi="['case:manage:recovery:repay']" plain v-if="activeTab == '待审核'" :disabled="single || loading"
      @click="batchRevoke()">批量撤销案件
    </el-button>

    <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
  </el-row>

  <el-row>
    <el-tabs class="mb8" type="card" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane v-for="item in examineStateTab" :key="item.key" :label="item.value" :name="item.key">
      </el-tab-pane>
      <el-tab-pane label="全部" name="全部"></el-tab-pane>
    </el-tabs>
    <el-radio-group v-model="repaymentType" @change="antiShake(handleQuery)" style="position: absolute; right: 0">
      <el-radio v-for="item in repaymentTypes" :key="item.info" :label="item.info">
        {{ item.info }}
      </el-radio>
    </el-radio-group>
  </el-row>

  <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
    <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
    <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
      <template #default="{ row }">
        <div class="df-center">
          <el-tooltip v-if="row.labelContent" placement="top">
            <template #content>{{ row.labelContent }}</template>
            <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
          </el-tooltip>
          <el-button type="text" v-if="row.button == 1" @click="toDetails(row.caseId, $index)">
            {{ row.caseId }}
          </el-button>
          <span v-if="row.button == 0">{{ row.caseId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
      show-overflow-tooltip />
    <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible" />
    <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" v-if="columns[3].visible" />
    <el-table-column label="证件号码【籍贯】" align="center" width="180" v-if="columns[4].visible">
      <template #default="{ row }">
        <span>{{ `${row.clientIdcard == null ? "--" : row.clientIdcard}` }} <br />
          {{ `【${row.clientCensusRegister == null ? "未知" : row.clientCensusRegister || "--"}】` }} </span>
      </template>
    </el-table-column>
    <el-table-column label="债权总金额" width="120" align="center" v-if="columns[5].visible" show-overflow-tooltip>
      <template #default="scope">
        <span>{{ numFilter(scope.row.clientMoney) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="委托时间" width="200" align="center" key="entrustingCaseDate" prop="entrustingCaseDate"
      v-if="columns[6].visible" show-overflow-tooltip />
    <el-table-column label="登记时间" width="180" align="center" key="createTime" prop="createTime"
      v-if="columns[7].visible" show-overflow-tooltip />
    <el-table-column label="还款金额" align="center" key="repaymentMoney" prop="repaymentMoney" v-if="columns[8].visible"
      show-overflow-tooltip>
      <template #default="scope">
        <span>{{ numFilter(scope.row.repaymentMoney) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="交易时间" width="120" align="center" key="repaymentDate" prop="repaymentDate"
      v-if="columns[9].visible" show-overflow-tooltip />
    <el-table-column label="对方户名" width="120" align="center" key="transferorName" prop="transferorName"
      v-if="columns[10].visible" show-overflow-tooltip />
    <el-table-column label="对方账号" width="120" align="center" key="accountNumber" prop="accountNumber"
      v-if="columns[11].visible" show-overflow-tooltip />
    <el-table-column label="对方开户机构" width="120" align="center" key="ycAccountAgency" prop="ycAccountAgency"
      v-if="columns[12].visible" show-overflow-tooltip />
    <el-table-column label="交易流水" width="120" align="center" key="orderNo" prop="orderNo" v-if="columns[13].visible" />
    <el-table-column label="还款类型" align="center" key="repaymentType" prop="repaymentType" v-if="columns[14].visible" />
    <el-table-column label="审核状态" align="center" key="examineState" prop="examineState"
      v-if="columns[15].visible && activeTab != '待上传凭证'">
      <template #default="scope">
        <el-button type="text" @click="selectProce(scope.row)">
          {{ scope.row.examineState }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column label="审核时间" width="150px" align="center" key="updateTime" prop="updateTime"
      v-if="columns[16].visible && activeTab != '待上传凭证'" show-overflow-tooltip />
    <el-table-column label="操作" width="100" fixed="right">
      <template #default="{ row }">
        <el-dropdown trigger="hover" @command="handlesCommand" v-if="isActionBar(row)">
          <span class="el-dropdown-link">
            操作栏
            <el-icon class="el-icon&#45;&#45;right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="row.examineState == '待审核' && checkPermi(['case:manage:recovery:repay'])"
                :command="{ data: row, func: revoke }">撤销
              </el-dropdown-item>
              <el-dropdown-item v-if="checkPermi(['collection:myapply:repayment:proof'])"
                :command="{ data: row, func: selectProof }">查看凭证
              </el-dropdown-item>
              <el-dropdown-item v-if="row.settleCertificateUrl" :command="{ data: row, func: downClearance }"
                type="text">下载结清证明
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
    @pagination="getList" />

  <el-dialog v-model="showProceDialog" title="审批进度">
    <el-table :data="proceDateList">
      <el-table-column label="处理时间" align="center" key="approveTime" prop="approveTime" show-overflow-tooltip />
      <el-table-column label="处理人" width="120" align="center" key="reviewer" prop="reviewer" show-overflow-tooltip />
      <el-table-column label="处理状态" align="center" key="approveStart" prop="approveStart"
        :formatter="approveStartFor" />
      <el-table-column label="原因" align="center" key="refuseReason" prop="refuseReason" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.refuseReason == null ? "--" : scope.row.refuseReason }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import {
  examineStates,
  repaymentModeList,
  entrustingPartyList,
  repaymentList,
  updateRepaymentRecord,
  selectApproveProceOne,
  selectRepaymentRecordById,
} from "@/api/collection/myapply.js";
import { getBatchNums } from "@/api/common/common.js";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const router = useRouter();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    repaymentMoney1: undefined,
    repaymentMoney2: undefined,
    entrustingCaseBatchNum: undefined,
    repaymentType: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
  },
});
const rangfiles = ["entrustingCaseDate", "returnCaseDate"];
const batchs = ref([]); //委案批次号列表
const entrustingPartys = ref([]); //转让方选项
const repaymentModes = ref([]); //还款渠道
const repaymentTypes = ref([]); //还款类型
const examineStateTab = ref([]); //审核状态tab
const showSearch = ref(false);
const showProceDialog = ref(false); //审批流程对话框
const proceDateList = ref([]); //审批流程表数据
const activeTab = ref("待审核");
const repaymentType = ref("全部"); //还款状态
const { queryParams } = toRefs(data);
//选中的id集合
const ids = ref([]);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

const single = ref(true); //是否可操作
// 列显隐信息
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "产品类型", visible: true },
  { key: 2, label: "姓名", visible: true },
  { key: 3, label: "证件类型", visible: true },
  { key: 4, label: "证件号码【籍贯】", visible: true },
  { key: 5, label: "债权总金额", visible: true },
  { key: 6, label: "委托时间", visible: true },
  { key: 7, label: "登记时间", visible: true },
  { key: 8, label: "还款金额", visible: true },
  { key: 9, label: "交易时间", visible: true },
  { key: 10, label: "对方户名", visible: true },
  { key: 11, label: "对方账号", visible: true },
  { key: 12, label: "对方开户机构", visible: true },
  { key: 13, label: "交易流水", visible: true },
  { key: 14, label: "还款类型", visible: true },
  { key: 15, label: "审核状态", visible: true },
  { key: 16, label: "审核时间", visible: true }
]);

//获取列表数据
function getList() {
  queryParams.value.examineState =
    activeTab.value == "全部" ? undefined : activeTab.value;

  queryParams.value.repaymentType =
    repaymentType.value == "全部" ? undefined : repaymentType.value;

  loading.value = true;
  repaymentList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取还款类型
function getRepaymentTypes() {
  repaymentTypes.value = [
    { code: "部分还款", info: "部分还款" },
    { code: "结清还款", info: "结清还款" },
    { code: "全部", info: "全部" },
  ];
}
getRepaymentTypes();

//获取审核状态
function getExamineStateList() {
  examineStates().then((res) => {
    examineStateTab.value = res.data.map(item => {
      if (item.value === "已提交,待审核") {
        item.value = "待审核";
      }
      return item;
    });
  });
}
getExamineStateList();

//获取转让方
function getEntrustingPartys() {
  entrustingPartyList().then((res) => {
    entrustingPartys.value = res.data;
  });
}

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//获取还款渠道
function getRepaymentModes() {
  repaymentModeList().then((res) => {
    repaymentModes.value = res.data;
  });
}

// 是否显示操作栏
const isActionBar = (row) => {
  return (row.examineState == '待审核' && checkPermi(['case:manage:recovery:repay'])) ||
    (checkPermi(['collection:myapply:repayment:proof']) && row.examineState !== '待上传凭证')
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    repaymentMoney1: undefined,
    repaymentMoney2: undefined,
    entrustingCaseBatchNum: undefined,
    repaymentType: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {

  const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 4: '已撤销', 5: '已退案关闭' }
  return stutasEnum[row.approveStart];
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

//表格行能否选择
function checkSelectable() {
  return true;
}

//操作栏
function handlesCommand(command) {
  command.func(command.data);
}

//撤销
function revoke(data) {
  let req = [data.id];
  revokeSubmit(req);
}

//批量撤销
function batchRevoke() {
  let req = ids.value;
  revokeSubmit(req);
}

//提交撤销
function revokeSubmit(data) {
  proxy.$modal
    .confirm("此操作将撤销选中的申请, 是否继续?")
    .then(function () {
      loading.value = true;
      return updateRepaymentRecord(data);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功！");
    }).catch(() => {
      loading.value = false;
    });
  ;
}

//下载结清证明
function downClearance(row) {
  proxy.download(
    `collection/selectRepaymentRecordUrl`,
    { id: row.id },
    `tpl_结清证明${new Date().getTime()}.pdf`
  );
}

//查看凭证
function selectProof(data) {
  let id = data.id;
  selectRepaymentRecordById(id)
    .then((res) => {
      if (res.data.repaymentProof) {
        window.open(res.data.repaymentProof, "_blank");
      } else {
        proxy.$modal.msgError("用户没有上传凭证！");
      }
    })
    .catch(() => {
    });
}

//查看审批流程
function selectProce(data) {
  showProceDialog.value = true;
  let req = { applyId: data.id };
  selectApproveProceOne(req).then((res) => {
    proceDateList.value = res.data;
  });
}

function moneyFor(num) {
  return num ? proxy.setNumberToFixed(num) : '--'
}
</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

// :deep(.el-table__header-wrapper .el-checkbox) {
//   display: none;
// }</style>
