<template>
  <div class="tpl-container">
    <div v-for="item in fieldarr" :key="item.name">
      <div class="card" v-if="item.name != 'additionalInfo'">
        <div class="title">
          <span class="tit">{{ item.title }}</span>
          <el-checkbox v-model="checkedall[item.name]" :indeterminate="isIndeterminate[item.name]"
            @change="handleCheckAllChange(item.name)">全选</el-checkbox>
        </div>
        <div class="fields">
          <el-checkbox-group v-model="checkedres[item.name]" @change="handleCheckedFieldsChange(item.name)"
            style="width: 100%">
            <el-row style="padding: 10px 20px">
              <el-col :span="6" v-for="field in item.columns" :key="field">
                <el-checkbox :label="field.label" :checked="field.choose" :disabled="field.def">{{ field.label +
                  (field.def ? "(默认)" : "") }}</el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </div>
      <div class="card" v-else>
        <div class="title flex-space">
          <span class="tit">
            <span>{{ item.title }}</span>&nbsp;
            <i>{{ `(${item?.columns?.length || 0}/50)` }}</i>
          </span>
          <el-button type="primary" icon="Plus" @click="addOtherFields(item.columns)">添加字段</el-button>
        </div>
        <div class="fields">
          <el-row style="padding: 20px 20px 0px">
            <el-col :span="6" :xs="24" v-for="(field, index) in item.columns" :key="field" style="margin-bottom: 20px">
              <el-input type="text" v-model="field.label" style="width: 180px; margin-right: 5px"></el-input>
              <el-button type="primary" link @click="removeOtherFields(index)">删除</el-button>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

const { proxy } = getCurrentInstance()
const fieldarr = inject("tplFields"); //字段信息
const checkedall = ref({}); //是否全选
const isIndeterminate = ref({}); //全选的样式变化
const checkedres = inject("checkedres"); //选择结果

//添加附加信息字段
function addOtherFields(columns) {
  if (columns.length > 49) {
    proxy.$modal.msgWarning('附加信息限制50个以内！')
    return false
  }
  let obj = { label: undefined };
  fieldarr.value.additionalInfo.columns.push(obj);
}

//删除附加信息字段
function removeOtherFields(index) {
  fieldarr.value.additionalInfo.columns.splice(index, 1);
}
watch(
  fieldarr,
  async (newval, preval) => {
    if (JSON.stringify(checkedres.value) == '{}') {
      await initcheckedall(fieldarr.value);
    }

    nextTick(() => {
      Object.keys(newval).forEach((key) => {
        //全选按钮样式变化
        const checkedCount = checkedres.value[key] ? checkedres.value[key].length : 0;
        checkedall.value[key] = checkedCount === newval[key].columns.length;
        isIndeterminate.value[key] =
          checkedCount > 0 && checkedCount < fieldarr.value[key].columns.length;
      });
    });
  },
  { deep: true }
);
watch(checkedall, (newval) => { });

//创建全选v-modal数据
function initcheckedall(value) {
  return new Promise((reslove, reject) => {
    Object.keys(value).forEach((key) => {
      if (value[key] && value[key] !== null) {
        checkedall.value[key] = false;
        isIndeterminate.value[key] = false;
        checkedres.value[key] = [];
      }
    });
    reslove(true);
  });
}

//全选操作
function handleCheckAllChange(name) {
  if (checkedall.value[name]) {
    //全选
    checkedres.value[name] = fieldarr.value[name].columns.map((item) => item.label);
  } else {
    //全不选（排除默认必选）
    let arr = [];
    fieldarr.value[name].columns.forEach((item) => {
      if (item.def) {
        arr.push(item.label);
      }
    });
    checkedres.value[name] = arr;
  }
  isIndeterminate.value[name] = false;
}

//字段选中
function handleCheckedFieldsChange(name) {
  const checkedCount = checkedres.value[name].length;
  checkedall.value[name] = checkedCount === fieldarr.value[name].columns.length;
  isIndeterminate.value[name] =
    checkedCount > 0 && checkedCount < fieldarr.value[name].columns.length;
}

</script>

<style lang="scss" scoped>
.tpl-container {
  width: 90%;
  margin: 15px auto;
  border: 1px solid #e8e8e8;
  min-height: 200px;
  border-radius: 2px;

  .title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 48px;
    background: rgba(238, 239, 244, 0.39);
    font-size: 14px;
    padding: 0 20px;
    color: #3f3f3f;

    .tit {
      margin-right: 20px;
    }
  }

  .flex-space {
    justify-content: space-between;
  }
}
</style>
