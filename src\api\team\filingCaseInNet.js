import request from '@/utils/request'
//获取机构树结构
export function getDeptTreeWithDisposeStage(params) {
  return request({
    url: '/team/DeptTreeWithDisposeStage',
    method: 'get',
    params
  })
}

// 根据条件查询机构信息
export function selectFillingList(query) {
  return request({
    url: '/team/selectFillingList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectFillingWithMoney(data) {
  return request({
    url: '/team/selectFillingWithMoney',
    method: 'post',
    data: data,
  })
}
