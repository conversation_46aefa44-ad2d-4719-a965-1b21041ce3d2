<template>
  <!-- 各个功能记录 -->
  <div>
    <el-radio-group v-model="recordType" class="mb10">
      <el-radio-button label="沟通记录" />
      <el-radio-button label="问答记录" />
      <el-radio-button
        v-for="item in networkNameDatas"
        :key="item.id"
        :label="item.networkName"
      />
    </el-radio-group>
    <div v-show="recordType === '沟通记录'">
      <gouTong />
    </div>
    <div v-if="recordType === '问答记录'">
      <caesAnswer />
    </div>
    <div v-show="recordType !== '沟通记录' && recordType !== '问答记录'">
      <lineCall :networkName="recordType" :names="networkNameDatas" />
    </div>
  </div>
</template>

<script setup>
import gouTong from './recordCom/gouTong.vue';
import caesAnswer from './recordCom/caesAnswer.vue';
import lineCall from './recordCom/lineCall.vue';
const { proxy } = getCurrentInstance();
const store = useStore();
const recordType = ref('沟通记录');

const networkNameDatas = computed(() => store.state.allCaseDetail.networkNameDatas)
</script>

<style scoped>

</style>
