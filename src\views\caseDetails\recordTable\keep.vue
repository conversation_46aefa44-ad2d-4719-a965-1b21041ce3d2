<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="记录时间" prop="updateTime" key="updateTime" align="center" />
    <el-table-column label="保全阶段" prop="saveStage" key="saveStage" align="center" />
    <el-table-column label="保全内容" prop="saveContent" key="saveContent" align="center">
      <template #default="scope">
        <div v-for="(item, index) in scope.row?.saveContent && JSON?.parse(scope.row?.saveContent)" :key="index">
          <div>
            <span>{{ item.fieldName }}:</span>
            <span class="ml10" v-if="!isArray(item.labelName || item[item.receiveParam])">
              <span v-if="!item.fileUrl">
                {{ item.labelName || item[item.receiveParam] }}
              </span>
              <Fragment type="text" v-if="isFile(item.labelName || item[item.receiveParam])">
                <el-button type="text">{{ item.labelName || item[item.receiveParam] }}</el-button>
                <el-button :loading="loading" type="text" @click="handlePreview(item.fileUrl)">预览</el-button>
                <el-button :loading="loading" type="text"
                  @click="handleDownload(item.fileUrl, item.labelName || item[item.receiveParam])">下载</el-button>
              </Fragment>
            </span>
            <span class="ml10" v-else>
              <span>{{ isArray(item.labelName || item[item.receiveParam]).join('、') || '--' }}</span>
            </span>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="记录人员" prop="createBy" key="createBy" align="center" />
  </el-table>
  <previewFile ref="previewFileRef" />
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import previewFile from '@/components/previewFile';
import { selectUrgeKeepRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  params: { type: Object, default: {} }
})
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: route.params.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm, ...props.params }
  loading.value = true;
  selectUrgeKeepRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();
// 是否文件
function isFile(val) {
  if (!val) return false
  const index = val.lastIndexOf('.')
  const fileType = index > -1 ? val.slice(index, val.length) : ''
  return ['.jpg', '.png', '.pdf'].includes(fileType)
}
// 预览
function handlePreview(url) {
  if (url) {
    const fileUrl = url
    nextTick(() => {
      window.open(fileUrl)
      // proxy.$refs['previewFileRef'].opendialog(fileUrl)
    })
  }
}

//下载操作
function downloadFile(url, name) {
  fetch(url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], {
          type: "application/pdf"
        })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      window.open(url + `?attname=${name}`, "_blank");
    });
  // window.open(url)
}
// 判断是否数组
function isArray(content) {
  if (!Array.isArray(content)) {
    return false
  }
  return content
}
//下载
function handleDownload(fileUrl, fileName) {
  // fileUrl && window.open(fileUrl);

  // ie和浏览器兼容模式会有问题，可以用下面代码调试。
  try {
    exportFile(fileUrl, fileName) // 调用方式
  } catch (err) {
    // 兼容模式下，IE
    const exportBlob = new Blob([data]);
    if (navigator.userAgent.indexOf('Trident') > -1) {
      window.navigator.msSaveBlob(data, fileName);
    } else {
      exportFile(fileUrl, fileName) // 调用方式
    }
  };
}
function exportFile(data, fileName) {
  // 地址不存在时，禁止操作
  if (!data) return;
  loading.value = true
  proxy.$modal.notify('正在下载中...')
  // 下载文件并保存到本地
  const callback = (data) => {
    // 创建a标签，使用 html5 download 属性下载，
    const link = document.createElement('a');
    // 创建url对象
    const objectUrl = window.URL.createObjectURL(new Blob([data]));
    link.style.display = 'none';
    link.href = objectUrl;
    // 自定义文件名称， fileName
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    // 适当释放url
    window.URL.revokeObjectURL(objectUrl);
  };
  // 把接口返回的url地址转换为 blob
  const xhr = new XMLHttpRequest();
  xhr.open('get', data, true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    // 返回文件流，进行下载处理
    callback(xhr.response);
    proxy.$modal.msgSuccess('操作成功！')
    loading.value = false
  };
  xhr.send(); // 不要忘记发送
};

defineExpose({ getList })
</script>

<style scoped></style>
