<template>
  <div>
    开启后，可使用外访功能<el-switch class="myswitch ml20" v-model="state.authorizationStatus" active-color="#2ECC71"
      :active-value="1" :inactive-value="0" active-text="开" inactive-text="关" @change="change" />
  </div>

</template>
<script setup>
import { changeSafeStatus } from "@/api/safetySet/index";
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);

function change() {
  changeSafeStatus(state.value)
    .then(() => { })
    .catch(() => {
      getTeamSafe();
    });
}
</script>
<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>