<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" :label-width="130">
      <el-row>
        <el-col :span="12">
          <el-form-item label="填单日期" prop="fillDate">
            <el-date-picker
              v-model="form.fillDate"
              type="date"
              :disabled="title === '查看订单' || title === '编辑订单'"
              clearable
              placeholder="请选择填单日期"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同号" prop="contractNo">
            <el-input
              v-model="form.contractNo"
              :disabled="title === '查看订单' || title === '编辑订单'"
              maxlength="50"
              clearable
              placeholder="请输入合同号"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customName">
            <el-input
              v-model="form.customName"
              :disabled="title === '查看订单' || title === '编辑订单'"
              clearable
              placeholder="请输入客户名称"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName">
            <el-select
              v-model="form.projectName"
              :disabled="title === '查看订单' || title === '编辑订单'"
              clearable
              filterable
              @focus="getproject"
              @change="changeProject"
              placeholder="请输入或搜索项目名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in projectOptions"
                :key="item.id"
                :value="item.packageName"
                >{{ item.packageName }}</el-option
              >
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调解员" prop="mediator">
            <el-select
              v-model="form.mediator"
              :disabled="title === '查看订单' || title === '编辑订单'"
              placeholder="请输入或选择调解员"
              clearable
              filterable
              :reserve-keyword="false"
              @focus="getmediator"
              style="width: 240px"
            >
              <el-option
                v-for="item in member"
                :key="item.code"
                :label="item.info"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品名称" prop="productName">
            <el-input
              v-model="form.productName"
              :disabled="title === '查看订单' || title === '编辑订单'"
              maxlength="50"
              clearable
              placeholder="请输入产品名称"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属战队" prop="belongDept">
            <tree-select
              v-model:value="form.belongDept"
              :disabled="title === '查看订单' || title === '编辑订单'"
              :options="deptOptionsCom"
              filterable
              clearable
              placeholder="请输入或选择所属部门"
              :objMap="{
                value: 'id',
                label: 'name',
                disabled: 'disabled',
                children: 'children',
              }"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案件时间" prop="caseDate">
            <el-input
              v-model="form.caseDate"
              :disabled="title === '查看订单' || title === '编辑订单'"
              clearable
              placeholder="请输入案件时间"
              style="width: 240px"
            />
            <!-- <el-date-picker
              v-model="form.caseDate"
              type="date"
              :disabled="title === '查看订单' || title === '编辑订单'"
              value-format="YYYY-MM-DD"
              clearable
              placeholder="请选择案件时间"
              style="width: 240px"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案件属性" prop="caseAttribute">
            <el-select
              v-model="form.caseAttribute"
              :disabled="title === '查看订单' || title === '编辑订单'"
              clearable
              placeholder="请选择案件属性"
              style="width: 240px"
            >
              <el-option label="新案" value="新案"></el-option>
              <el-option label="留案" value="留案"></el-option>
              <el-option label="轮换" value="轮换"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户属性" prop="customAttribute">
            <el-select
              v-model="form.customAttribute"
              :disabled="title === '查看订单'"
              clearable
              placeholder="请选择"
              style="width: 240px"
            >
              <el-option
                label="短信+工单回复"
                value="短信+工单回复"
              ></el-option>
              <el-option label="自流水" value="自流水"></el-option>
              <el-option label="电话" value="电话"></el-option>
              <el-option label="上期留案" value="上期留案"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否结清" prop="settlement">
            <el-select
              v-model="form.settlement"
              :disabled="title === '查看订单'"
              clearable
              placeholder="请选择"
              style="width: 240px"
            >
              <el-option label="一次性结清" value="一次性结清"></el-option>
              <el-option label="分期" value="分期"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="还款金额" prop="repayment">
            <NumberInput
              v-model="form.repayment"
              :disabled="title === '查看订单'"
              :decimals="2"
              placeholder="请输入(元)"
              style="width: 240px"
              @input="jisuanChuangYong"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="还款日期" prop="repaymentDate">
            <el-date-picker
              v-model="form.repaymentDate"
              :disabled="title === '查看订单' || title === '编辑订单'"
              type="date"
              value-format="YYYY-MM-DD"
              clearable
              placeholder="请选择还款日期"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="还款方式" prop="repaymentMethod">
            <el-select
              v-model="form.repaymentMethod"
              :disabled="title === '查看订单'"
              clearable
              placeholder="请选择还款方式"
              style="width: 240px"
            >
              <el-option label="APP" value="APP"></el-option>
              <el-option label="对公账户" value="对公账户"></el-option>
              <el-option label="自然流" value="自然流"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="垫资" prop="advance">
            <NumberInput
              v-model="form.advance"
              :disabled="title === '查看订单'"
              :decimals="2"
              placeholder="请输入(元)"
              style="width: 240px"
              @input="jisuanChuangYong"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="系数" prop="ratio">
            <NumberInput
              v-model="form.ratio"
              :disabled="true"
              :decimals="2"
              placeholder="选择项目名称后获取！"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="特殊创佣" prop="specialCommission">
            <NumberInput
              v-model="form.specialCommission"
              :disabled="title === '查看订单'"
              :decimals="2"
              placeholder="请输入(元)"
              style="width: 240px"
              @input="jisuanChuangYong"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创佣" prop="createCommission">
            <NumberInput
              v-model="form.createCommission"
              :disabled="true"
              :decimals="2"
              placeholder="输入垫资或特殊创佣后自动计算"
              style="width: 240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业绩归属月份" prop="performanceMonth">
            <el-date-picker
              v-model="form.performanceMonth"
              type="month"
              :disabled="title === '查看订单'"
              value-format="YYYY-MM"
              clearable
              placeholder="请选择业绩归属月份"
              style="width: 240px"
              :disabled-date="
                (time) => {
                  const year = time.getFullYear();
                  return year < 2023 || year > 2030;
                }
              "
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer" v-if="title !== '查看订单'">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >保 存</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { selectEmployees } from "@/api/common/common";
import { deptTree } from "@/api/system/dept";
import { getFmlResult } from "@/api/assetsManagement/formula";
import {
  getSolverSelect,
  getProjectSelect,
  addOrder,
  updateOrder,
  orderDetail,
} from "@/api/case/orderInfo/orderInfo";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const Timer = ref(null);
const title = ref("添加订单");
const loading = ref(false);
const open = ref(false);
const formulaId = ref(null);
const isInTimeRange = ref(true); // 项目使用日期是否在有效期内
const form = ref({
  fillDate: undefined,
  contractNo: undefined,
  customName: undefined,
  projectName: undefined,
  mediator: undefined,
  productName: undefined,
  belongDept: undefined,
  caseDate: 'T1',
  caseAttribute: "新案",
  customAttribute: "短信+工单回复",
  settlement: "一次性结清",
  repayment: undefined,
  repaymentDate: undefined,
  repaymentMethod: "APP",
  advance: undefined,
  ratio: undefined,
  specialCommission: undefined,
  createCommission: undefined,
  performanceMonth: undefined,
});
const rules = {
  fillDate: [{ required: true, message: "请选择填单日期", trigger: "blur" }],
  contractNo: [{ required: true, message: "请输入合同号", trigger: "blur" }],
  customName: [{ required: true, message: "请输入客户名称", trigger: "blur" }],
  projectName: [{ required: true, message: "请选择项目名称", trigger: "blur" }],
  mediator: [{ required: true, message: "请选择调解员", trigger: "blur" }],
  productName: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
  belongDept: [{ required: true, message: "请选择所属战队", trigger: "blur" }],
  caseDate: [{ required: true, message: "请输入案件时间", trigger: "blur" }],
  caseAttribute: [
    { required: true, message: "请选择案件属性", trigger: "blur" },
  ],
  customAttribute: [
    { required: true, message: "请选择客户属性", trigger: "blur" },
  ],
  settlement: [{ required: true, message: "请选择是否结清", trigger: "blur" }],
  repayment: [{ required: true, message: "请输入还款金额", trigger: "blur" }],
  repaymentDate: [
    { required: true, message: "请选择还款日期", trigger: "blur" },
  ],
  performanceMonth: [
    { required: true, message: "请选择业绩归属月份", trigger: "blur" },
  ],
};

const projectOptions = ref([]);
//项目名称
function getproject(type, data) {
  console.log("获取项目下拉");
  getProjectSelect().then((res) => {
    projectOptions.value = res.data;
    if (type && data) {
      for (let i = 0; i < projectOptions.value.length; i++) {
        const item = projectOptions.value[i];
        if (item.packageName === data.projectName) {
          formulaId.value = item.formulaId;
          break;
        }
      }
      // 打开编辑判断项目是否在有效期内
      changeProject(data.projectName).then(() => {
        // 计算创佣
        nextTick(() => {
          jisuanChuangYong();
        });
      });
    }
  });
}
// 项目名称选择
function changeProject(val) {
  return new Promise((resolve) => {
    const project = projectOptions.value.find(
      (item) => item.packageName === val
    );
    form.value.productName = project.productName || undefined;
    form.value.ratio = project.ratio || undefined;
    formulaId.value = project.formulaId || undefined;
    if (project.useDateType === "短期") {
      // 获取当前日期
      const now = new Date();
      console.log(now);
      // 项目开始日期和结束日期格式均为YYYY-MM-DD 判断当前日期是否在项目有效期内
      const startDate = new Date(project.useBeginDate);
      console.log(startDate);
      const endDate = new Date(project.useEndDate);
      console.log(endDate);
      // 判断当前日期是否在项目有效期内
      isInTimeRange.value = now >= startDate && now <= endDate;
    }
    resolve(true);
  });
}

// 计算创佣
function jisuanChuangYong() {
  nextTick(() => {
    if (!isInTimeRange.value) {
      // 项目不在使用期内
      form.value.createCommission = 0;
      return;
    }
    if (!formulaId.value || !form.value.repayment) {
      proxy.$modal.msg("选择项目名称及填写还款金额后,将自动计算创佣");
      return;
    }
    if (Timer.value) {
      clearTimeout(Timer.value);
      Timer.value = null;
    }
    Timer.value = setTimeout(() => {
      getFmlResult({
        formulaId: formulaId.value,
        repaymentAmount: form.value.repayment,
        advanceFunding: form.value.advance || 0,
        coefficient: form.value.ratio,
        specialCommission: form.value.specialCommission || 0,
      })
        .then((res) => {
          form.value.createCommission = res.data;
        })
        .finally(() => {
          clearTimeout(Timer.value);
          Timer.value = null;
        });
    }, 500);
  });
}

const member = ref([]);
//处置人员
function getmediator() {
  getSolverSelect().then((res) => {
    member.value = res.data;
  });
}

const deptOptionsCom = ref([]);
//部门数据处理(增加层级)
function deptOptionsHandle(data) {
  function handle(data, ranks) {
    let rank = ranks || 0;
    data.map((item) => {
      item.rank = rank;
      if (item.children && item.children.length !== 0) {
        handle(item.children, rank + 1);
      }
    });
  }
  handle(data);
  return data;
}
//获取树结构
function getDeptTree(type, data) {
  deptTree().then((res) => {
    deptOptionsCom.value = deptOptionsHandle(res.data);
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    fillDate: undefined,
    contractNo: undefined,
    customName: undefined,
    projectName: undefined,
    mediator: undefined,
    productName: undefined,
    belongDept: undefined,
    caseDate: 'T1',
    caseAttribute: "新案",
    customAttribute: "短信+工单回复",
    settlement: "一次性结清",
    repayment: undefined,
    repaymentDate: undefined,
    repaymentMethod: "APP",
    advance: undefined,
    ratio: undefined,
    specialCommission: undefined,
    createCommission: undefined,
    performanceMonth: undefined,
  };
  isInTimeRange.value = true;
}

//取消
function cancel() {
  reset();
  open.value = false;
}

function submit() {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      const reqForm = JSON.parse(JSON.stringify(form.value));
      // reqForm.belongDept = reqForm.belongDept.split(":")[1];
      if (title.value === "添加订单") {
        addOrder(reqForm)
          .then((res) => {
            proxy.$modal.msgSuccess("保存成功");
            cancel();
            emits("getList");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        updateOrder(reqForm)
          .then((res) => {
            proxy.$modal.msgSuccess("保存成功");
            cancel();
            emits("getList");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

function opendialog(type, row) {
  getmediator();
  getDeptTree();
  if (type === "add") {
    title.value = "添加订单";
    open.value = true;
    getproject();
    return;
  }
  if (type === "edit") {
    title.value = "编辑订单";
    //获取订单详情
    //TODO
  } else {
    title.value = "查看订单";
  }
  orderDetail({ id: row.id }).then((res) => {
    const { data } = res;
    for (const key in form.value) {
      form.value[key] = data[key];
    }
    form.value.id = data.id;
    open.value = true;
    // 获取项目下拉
    getproject(true, data);
  });
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
