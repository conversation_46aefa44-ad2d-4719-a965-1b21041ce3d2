<template>
  <el-dialog title="模板分案" v-model="open" append-to-body width="700px" :before-close="cancel">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item label="案件导入" class="upload-item">
        <div class="preFileurl"></div>
        <el-upload ref="uploadRef" :limit="1" accept=".xls, .xlsx" :headers="upload.headers" :action="upload.url"
          :before-upload="handleFileUploadBefore" :on-change="handleEditChange" :before-remove="remove"
          :on-success="handleFileSuccess" :auto-upload="false">
          <template #trigger>
            <el-button class="mr10" type="primary">选取文件</el-button>
          </template>
          <el-button class="ml-12" type="success" @click="submitFile">上传到服务器</el-button>
        </el-upload>
      </el-form-item>
      <div class="case-tips">
        <el-icon class="info-tip-icon" :size="16"><warning-filled /></el-icon>
        <span>1、只能上传xls文件，且不超过10Mb</span>
        <span>2、进行模板分案操作前请先下载案件信息模板，点击下载<span class="text-danger" @click="downTpl"
            style="cursor: pointer; display: inline-block">案件分配模板</span></span>
      </div>
    </el-form>
    <div class="case-tips info-tip">
      <span>导入说明：</span>
      <span>1、导入案件，导入文件表头名称需与字段名称相同、需要注意符号的中英文状态。</span>
      <span>2、模板字段必填项需存在于表头，导入数据对应项不能为空，否则无法成功导入数据。</span>
      <span>3、导入前请检查Excel表格内数值格式正确，如果格式不正确将无法导入数据</span>
      <span>4、导入数据行，若对应案件系统必填项为空，将跳过当前数据行继续向下导入</span>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="hintOpen" append-to-body width="890px" :before-close="toBack">
    <div class="text-center reset-pwd">
      <div class="step-icon">
        <el-icon class="check-icon" color="#FFFFFF">
          <check />
        </el-icon>
      </div>
      <h2>分案申请成功，等待分案审核通过</h2>
    </div>
    <div class="text-center mt30">
      <el-button @click="toBack">返回</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { templateImport } from "@/api/case/index/index";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const open = ref(false);
const loading = ref(false);
const data = reactive({
  form: {
    fileUrl: undefined,
    fileName: undefined,
  },
  rules: {
    file: [{ required: true, message: "请选择模板文件！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
const files = ref([]); //上传成功文件列表
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/case/upload",
});
const hintOpen = ref(false);

//文件移除
function remove(file, fileList) {
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.response.data.name;
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == name) {
        files.value.splice(i, 1);
        break;
      }
    }
  }
}

//打开弹窗
function opendialog() {
  open.value = true;
}

//下载模板
function downTpl() {
  proxy.download("case/export", {}, `tpl_模板分案.xlsx`);
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

// 文件上传前的处理
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
}

// 文件上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: file.name,
      modifyName: data.modifyName,
      fileUrl: data.fileUrl[0],
      fileName: data.firstName[0],
    };
    files.value.push(obj);
  }
};

//取消
function cancel() {
  proxy.resetForm("formRef");
  form.value = {
    fileUrl: undefined,
  };
  open.value = false;
}

//提交操作数据
function submit() {
  if (files.value.length === 0) {
    proxy.$modal.msgWarning("请上传文件后再操作吧～");
    return false;
  }
  form.value.fileUrl = files.value[0].fileUrl;
  form.value.fileName = files.value[0].fileName;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      templateImport(form.value)
        .then((res) => {
          loading.value = false;
          cancel();
          if (res?.data?.fileUrl) {
            proxy.$modal.confirm("将导入失败的数据下载到本地？是否继续").then(function () {
              window.open(res?.data?.fileUrl[0], "_blank");
            })
          }
          emits("getList");
          proxy.$modal.alertWarning('模板分案文件已导入成功，后台执行分案中，如需查看结果请前往导出日志页面查看！')
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

//返回
function toBack() {
  hintOpen.value = false;
  emits("getList");
}

defineExpose({
  opendialog,
});

</script>
<style>
.upload-item .el-upload-list {
  position: absolute;
  left: 4px;
  top: -5px;
  width: 270px;
}
</style>
<style lang="scss" scoped>
.ml-12 {
  margin-left: 12px !important;
}

.preFileurl {
  width: 280px;
  height: 30px;
  margin-right: 20px;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
}

.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}

.case-tips {
  font-size: 14px;
  background-color: #cce7fa;
  padding: 8px 12px;
  color: #409eff;
  margin: 0 20px;

  span {
    margin: 2px 0;
    display: block;
  }

  span:nth-child(2) {
    display: inline;
  }
}

.info-tip {
  background-color: #fdfddb;
  color: #666;
  margin-top: 10px;
  display: block;
  border: 0;
}
</style>
