<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" inline :label-width="100" class="h-50"
            :class="{ 'h-auto': showSearch }">
            <el-form-item label="资产包名称" prop="packageName">
                <el-input v-model="queryParams.packageName" placeholder="请输入资产包名称" clearable style="width: 240px"
                    @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="案件ID">
                <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
                    @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="被告" prop="clientName">
                <el-input v-model="queryParams.clientName" placeholder="请输入被告" clearable style="width: 240px"
                    @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="调解号" prop="mediationNum">
                <el-input v-model="queryParams.mediationNum" placeholder="请输入调解号" clearable style="width: 240px"
                    @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="标的额">
                <div class="range-scope" style="width: 240px">
                    <el-input type="text" v-model="queryParams.amount1" @blur="validatePrincipalRange"
                        @input="value => value.replace(/[^\d]/g, '')" clearable />
                    <span>-</span>
                    <el-input type="text" v-model="queryParams.amount2" @blur="validatePrincipalRange"
                        @input="value => value.replace(/[^\d]/g, '')" clearable />
                </div>
            </el-form-item>
        </el-form>

        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="mb10 operation-list mt10" style="min-height: 26px">
            <el-button v-if="checkPermi([setPermiss('back')])" plain :disabled="single" type="primary"
                @click="opendialog('backStayRef', 0)">退案</el-button>
            <el-button v-if="checkPermi([setPermiss('stay')])" plain :disabled="single" type="success"
                @click="opendialog('backStayRef', 1)">留案</el-button>
            <el-button v-if="checkPermi([setPermiss('stop')])" plain :disabled="single" type="info"
                @click="opendialog('stopUrgeRef', 2)">停案</el-button>
            <el-button v-if="checkPermi([setPermiss('batchImportWrit')])" :disabled="single" type="primary"
                @click="handleOpenDialog('batchImportWritVueRef')">批量生成文书</el-button>
            <el-button v-if="checkPermi([setPermiss('importLogistics')])" type="primary"
                @click="handleOpenDialog('importLogisticsRef')">导入物流单号</el-button>
            <el-button v-if="checkPermi([setPermiss('transferCase')])" :disabled="single" type="primary"
                @click="handleOpenDialog('transferCaseRef')">案件流转</el-button>
            <el-button :disabled="single" type="primary"
                v-if="route.meta.query == 2 && checkPermi([setPermiss('mediationNo')])"
                @click="handleOpenDialog('createMediationNoRef')">生成调解号</el-button>
            <el-button :disabled="single" type="primary"
                v-if="route.meta.query == 4 && checkPermi([setPermiss('link')])"
                @click="handleOpenDialog('createMediationNoRef')">司法送达链接</el-button>
            <el-button v-if="checkPermi([setPermiss('sendMsg')])" type="primary" :disabled="single"
                @click="handleOpenDialog('sendMessageRef')">发送短信</el-button>
            <el-button v-if="checkPermi([setPermiss('applyKeep')])" type="primary" :disabled="single"
                @click="handleOpenDialog('applyKeepRef')">申请保全</el-button>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
        </div>

        <selectedAll v-model:allQuery="allQuery" :selectedArr="selectedArr" :dataList="dataList" class="mb10">
            <template #content>
                <div>
                    <span class="ml10">剩余债权总额：</span>
                    <i class="danger mr10">
                        {{ proxy.setNumberToFixed(statistics.totalMoney) }}
                    </i>
                    <span>剩余债权本金：</span>
                    <i class="danger mr10">
                        {{ proxy.setNumberToFixed(statistics.principal) }}
                    </i>
                    <span>案件数量：</span>
                    <i class="danger">{{ statistics.caseNum }}</i>
                </div>
            </template>
        </selectedAll>

        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
            <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" :width="100"
                v-if="columns[0].visible">
                <template #default="{ row, $index }">
                    <el-button :disable="!checkPermi([setPermiss('detail')])" type="text"
                        @click="toDetails(row, $index)">
                        {{ row.caseId }}
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="资产包名称" align="center" key="packageName" prop="packageName" :min-width="120"
                v-if="columns[1].visible" />
            <el-table-column label="转让方" align="center" key="entrustingPartyName" prop="entrustingPartyName"
                :min-width="90" v-if="columns[2].visible" />
            <el-table-column label="被告" v-if="columns[3].visible" align="center" key="clientName" prop="clientName"
                :width="90" />
            <el-table-column label="身份证号码" align="center" key="clientIdNum" prop="clientIdNum" :width="180"
                v-if="columns[4].visible" />
            <el-table-column label="户籍地" align="center" key="clientCensusRegister" prop="clientCensusRegister"
                :min-width="160" v-if="columns[5].visible" />
            <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" :width="110"
                v-if="columns[6].visible">
                <template #default="{ row }">
                    <div>
                        <span>{{ row.clientPhone }}</span>
                        <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="标的额" align="center" key="remainingDue" prop="remainingDue" :width="120"
                v-if="columns[7].visible">
                <template #default="{ row }">
                    <span>{{ row.remainingDue ? proxy.setNumberToFixed(row.remainingDue) : `--` }}</span>
                </template>
            </el-table-column>
            <el-table-column label="调解号" align="center" key="mediationNum" prop="mediationNum" :min-width="120"
                v-if="columns[8].visible">
                <template #default="{ row }">
                    <Tooltip :content="row.mediationNum" />
                </template>
            </el-table-column>
            <el-table-column label="司法送达链接" align="center" key="publicityLink" prop="publicityLink" :min-width="120"
                v-if="columns[9].visible">
                <template #default="{ row }">
                    <Tooltip :content="row.publicityLink" />
                </template>
            </el-table-column>
            <el-table-column label="逾期日期" align="center" key="overdueStart" prop="overdueStart" :width="160"
                v-if="columns[10].visible" />
            <el-table-column label="逾期天数" align="center" key="ycOverdueDays" prop="ycOverdueDays" :width="160"
                v-if="columns[11].visible" />
            <el-table-column label="跟进人员" align="center" key="updateBy" prop="updateBy" :width="100"
                v-if="columns[12].visible" show-overflow-tooltip />
            <el-table-column label="最近一次跟进时间" align="center" key="updateTime" prop="updateTime" :width="160"
                v-if="columns[13].visible" show-overflow-tooltip />
            <el-table-column fixed="right" width="340" label="操作">
                <template #default="{ row }">
                    <el-button v-if="row.caseState != 2 && row.caseState != 4 && checkPermi([setPermiss('stay')])"
                        type="text" @click="opendialog('backStayRef', 1, row)">留案</el-button>
                    <el-button v-if="row.caseState != 2 && row.caseState != 4 && checkPermi([setPermiss('stop')])"
                        type="text" @click="opendialog('stopUrgeRef', 2, row)">停案</el-button>
                    <el-button
                        v-if="row.caseState != 2 && row.caseState != 4 && checkPermi([setPermiss('exportMaterials')])"
                        type="text" @click="handleExport(row)">导出沟通记录</el-button>
                    <el-button v-if="route.meta.query == 2 && checkPermi([setPermiss('mediationNo')])" type="text"
                        @click="handleOpenDialog('createMediationNoRef', row)">生成调解号</el-button>
                    <el-button v-if="route.meta.query == 4 && checkPermi([setPermiss('link')])" type="text"
                        @click="handleOpenDialog('createMediationNoRef', row)">司法送达链接</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 案件操作 -->
        <stopUrge ref="stopUrgeRef" @getList="getList" />
        <backStay ref="backStayRef" @getList="getList" />

        <!-- 导出沟通记录 -->
        <exportDialog :query="getReqParams()" :allQuery="allQuery" ref="exportdialogRef" />

        <!-- 标记案件 -->
        <leabelCaseVue @getList="getList" ref="leabelCaseRef" />

        <!-- 成功弹窗 -->
        <exportTip :getList="getList" ref="exportTipRef" :exportType="`案件导出`" />

        <batchImportWritVue :getList="getList" ref="batchImportWritVueRef" />

        <!-- 案件流转 -->
        <transferCase :getList="getList" ref="transferCaseRef" />
        <!-- 导入物流单号 -->
        <importLogistics :getList="getList" ref="importLogisticsRef" />
        <!-- 生成调解号 -->
        <createMediationNo :getList="getList" ref="createMediationNoRef" />
        <!-- 发送短信 -->
        <sendMessage :getList="getList" ref="sendMessageRef" />
        <!-- 申请保全 -->
        <applyKeep :getList="getList" ref="applyKeepRef" />
    </div>
</template>

<script setup name="MediateInfo-materials">
import { checkPermi } from "@/utils/permission";
import { ElMessage } from "element-plus";
import applyKeep from '@/views/mediation/dialog/applyKeep';
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import createMediationNo from '../../dialog/createMediationNo';
import transferCase from '@/views/mediation/dialog/transferCase';
import importLogistics from '@/views/mediation/dialog/importLogistics';
import batchImportWritVue from "@/views/mediation/dialog/batchImportWrit";
import stopUrge from "@/views/mediation/dialog/stopUrge";
import backStay from "@/views/mediation/dialog/backStay";
import exportDialog from "@/views/mediation/dialog/exportDialog";
import leabelCaseVue from "@/views/mediation/dialog/leabelCase";
import { formatParams } from "@/utils/common";
import { getPhoneList } from "@/api/mediation/phoneMediation";
import { pageTypeEnum } from "@/utils/enum";
import { selectMediationWithMoney } from "@/api/team/appealCase";
const htrxCall = computed(() => store.getters.htrxCall);;

//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const store = useStore();
const pageType = 'phoneMediation'
//表格配置数据
const loading = ref(false);
const total = ref(0);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const statistics = ref({
    caseNum: 0,
    totalMoney: 0,
    principal: 0,
});
const rangFields = []
//表格查询参数
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
//需要拆分的字段
const { queryParams } = toRefs(data);
//表单配置信息
const showSearch = ref(false);
const allQuery = ref(false)

const stageObj = {
    1: '待上传调解材料',
    2: '生成调解号',
    3: '司法确认材料',
    4: '司法确认送达',
    5: '下发司法确认书',
}
// 列显隐信息
const columns = ref([
    { "key": 0, "label": "案件ID", "visible": true },
    { "key": 1, "label": "资产包名称", "visible": true },
    { "key": 2, "label": "转让方", "visible": true },
    { "key": 3, "label": "被告", "visible": true },
    { "key": 4, "label": "身份证号码", "visible": true },
    { "key": 5, "label": "户籍地", "visible": true },
    { "key": 6, "label": "手机号码", "visible": true },
    { "key": 7, "label": "标的额", "visible": true },
    { "key": 8, "label": "调解号", "visible": true },
    { "key": 9, "label": "司法送达链接", "visible": true },
    { "key": 10, "label": "逾期日期", "visible": true },
    { "key": 11, "label": "逾期天数", "visible": true },
    { "key": 12, "label": "跟进人员", "visible": true },
    { "key": 13, "label": "最近一次跟进时间", "visible": true }
]);

//获取列表数据
function getList() {
    loading.value = true;
    selectedArr.value = []
    const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
    reqForm.sign = pageTypeEnum[pageType]
    reqForm.mediatedStage = stageObj[route.meta.query]
    getPhoneList(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    }).finally(() => loading.value = false);
}
provide("getList", Function, true);


function handleOpenDialog(refName, row) {
    const caseIds = row ? [row.caseId] : selectedArr.value.map(item => item.caseId)
    const newAllQuery = row ? false : allQuery.value
    const query = { ...getReqParams(), allQuery: newAllQuery, caseIds }
    const data = { query, ...row, caseIds, allQuery: newAllQuery, pageType, isBatchSend: 1, caseId: row?.caseId }
    data.condition = row ? false : allQuery.value
    data.oneStatus = '电话调解'
    proxy.$refs[refName].openDialog(data)
}

//搜索
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

// 下载pdf格式文件
function downloadPdfFormat(reqForm) {
    proxy.downloadforjson("/zip/downloadMediate", reqForm, `批量PDF文件_${new Date().getTime()}.zip`);
}
// 下载word格式文件
function downloadWordFormat(reqForm) {
    proxy.downloadforjson("/zip/downloadWordDocumentsMediate", reqForm, `批量WORD文件_${new Date().getTime()}.zip`);
}
//跳转案件详情
function toDetails(row, index) {
    let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
    let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
    delete queryChange.pageNum;
    delete queryChange.pageSize;
    let searchInfo = {
        query: {
            mediatedStage: stageObj[route.meta.query],
            manageQueryParam: queryChange, //查询参数
            pageNumber: pageNumber, //当前第几页(变动)
            pageSize: 1, //一页一条
            pageIndex: 0, //当前第一条
            caseIdCurrent: row.caseId, //当前案件id
        },
        type: "mycase",
        total: total.value, //查询到的案件总数
    };
    localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
    const query = { type: "record", pageType, twoStage: stageObj[route.meta.query] }
    router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}


//重置
function resetQuery() {
    proxy.resetForm('queryRef')
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
    };
    getList();
}

//查询案件金额
function getStaticForQuery() {
    return new Promise((reslove, reject) => {
        if (!allQuery.value && caseIds.value.length == 0) {
            statistics.value = { caseNum: 0, totalMoney: 0, principal: 0, };
            reslove()
            return false
        }
        selectMediationWithMoney(getReqParams()).then((res) => {
            statistics.value = {
                caseNum: res.data.size,
                totalMoney: res.data.money,
                principal: res.data.principal,
            };
        }).finally(() => reslove());
    })

}

//选择列表
function handleSelectionChange(selection) {
    caseIds.value = selection.map((item) => item.caseId);
    single.value = !(selection.length > 0);
    selectedArr.value = selection;
}

//表格行能否选择
function checkSelectable() {
    return !allQuery.value;
}

//导出沟通记录
function handleExport(row) {
    const reqForm = { caseIds: [row.caseId] }
    proxy.$refs["exportdialogRef"].opendialog(reqForm);
}

//打开回收案件等案件操作
function opendialog(refName, type, row) {
    const caseIds = row ? [row.caseId] : selectedArr.value.map(item => item.caseId)
    const sign = pageTypeEnum[pageType]
    const newAllQuery = row ? false : allQuery.value
    const totalSum = newAllQuery ? total.value : caseIds.length
    const req = { ...getReqParams(), caseIds, condition: newAllQuery, allQuery: newAllQuery, ids: caseIds }
    const openDialogData = { sign, req, total: totalSum, type }
    proxy.$refs[refName].openDialog(openDialogData);
}

// 获取参数
function getReqParams() {
    const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
    const reqForm = formatParams(reqParams, selectedArr, allQuery)
    reqForm.condition = allQuery.value
    reqForm.sign = pageTypeEnum[pageType]
    reqForm.mediatedStage = stageObj[route.meta.query]
    return reqForm
}

// 应还本金区间校验
const validatePrincipalRange = () => {
    const { amount1, amount2 } = queryParams.value;
    // 检测输入是否是数字
    if (amount1 && !Number.isFinite(Number(amount1))) {
        ElMessage({
            message: "请输入正确的金额！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.amount1 = undefined;
    }
    if (amount2 && !Number.isFinite(Number(amount2))) {
        ElMessage({
            message: "请输入正确的金额！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.amount2 = undefined;
    }
    if (!amount1 || !amount2) return;

    const principal1 = parseFloat(amount1);
    const principal2 = parseFloat(amount2);
    // 检查区间逻辑
    if (principal1 >= principal2) {
        ElMessage({
            message: "后面区间的值必须大于前面区间的值！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.amount2 = undefined;
    }
};

// 设置权限符
function setPermiss(val) {
    const permissBtn = {
        1: 'mediateInfo:materials:',
        2: 'mediateInfo:mediationNo:',
        3: 'mediateInfo:judicial:',
        4: 'mediateInfo:publicity:',
        5: 'mediateInfo:confirmation:',
    }
    return `${permissBtn[route.meta.query]}${val}`
}

watch(() => selectedArr.value, () => {
    nextTick(() => {
        if (!loading.value) {
            loading.value = true
            getStaticForQuery().finally(() => loading.value = false)
        }
    })
}, { immediate: true, deep: true })
watch(() => route, () => {
    resetQuery()
}, { immediate: true, deep: true })
</script>
<style lang="scss" scoped>
body {
    color: #666 !important;
}

.h-50 {
    overflow: hidden;
    height: 50px;
}

.h-auto {
    height: auto !important;
}

.text-flex {
    display: inline-flex;
    align-items: center;
    height: 32px;
    font-size: 14px;
    font-weight: 500;
}

.block {
    display: block;
    margin: 10px auto;
}

.text-flex .text-danger {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
}

.hint-item {
    font-size: 18px;
    color: #5a5e66;
    cursor: pointer;
}

.info-tip {
    border: #9fccdb;
    display: block;
}

.selected-all-content {
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 10px;
    justify-content: space-between;
}

.case-tips {
    font-size: 14px;
    background-color: #cce7fa;
    padding: 20px;
    color: #409eff;

    P {
        margin: 0;
        display: block;
    }

    p:nth-child(2) {
        display: inline;
    }
}

.form-content {
    .el-form-item {
        width: 30% !important;
    }
}

:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}

:deep(.el-cascader .el-cascader__search-input) {
    margin: 2px 0 2px 13px !important;
}
</style>