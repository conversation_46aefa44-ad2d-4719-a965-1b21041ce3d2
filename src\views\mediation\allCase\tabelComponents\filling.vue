<template>
  <div>
    <el-form
      :model="queryParams"
      :loading="loading"
      ref="queryRef"
      inline
      label-width="100px"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
    >
      <el-form-item prop="caseId" label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          style="width: 280px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input
          v-model="queryParams.clientName"
          style="width: 280px"
          placeholder="请输入被告"
        />
      </el-form-item>
      <el-form-item prop="trialLawyer" label="开庭律师">
        <el-input
          v-model="queryParams.trialLawyer"
          style="width: 280px"
          placeholder="请输入开庭律师"
        />
      </el-form-item>
      <el-form-item prop="trialTime" label="开庭时间">
        <el-date-picker
          style="width: 280px"
          v-model="queryParams.trialTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          unlink-panels
        />
      </el-form-item>
      <el-form-item prop="trialCourt" label="开庭法院">
        <el-select
          placeholder="请选开庭法院"
          style="width: 280px"
          v-model="queryParams.trialCourt"
          @focus="getclosed"
        >
          <el-option
            v-for="item in trialCourtOption"
            :label="item.info"
            :key="item.code"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 280px">
          <el-input v-model="queryParams.amount1" />
          <span>-</span>
          <el-input v-model="queryParams.amount2" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button
        :loading="loading"
        icon="Search"
        type="primary"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button
        :loading="loading"
        icon="Refresh"
        @click="antiShake(resetQuery)"
        >重置</el-button
      >
    </div>
    <div class="operation-revealing-area mb10">
      <!-- <el-button plain type="primary" @click="openCreateCaseTaskDialog" :disabled="single"
                v-hasPermi="['saasc:collection:createOutboundTasks']">创建智能外呼任务</el-button> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
      <el-tab-pane label="开庭缴费" name="开庭缴费" />
      <el-tab-pane label="预约开庭" name="预约开庭" />
      <el-tab-pane label="正式开庭" name="正式开庭" />
      <el-tab-pane label="全部" name="全部" />
    </el-tabs>

    <selectedAll
      ref="selectedAllRef"
      v-model:allQuery="queryParams.allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      :cusTableRef="proxy.$refs.multipleTableRef"
    >
      <template #content>
        <span class="case-data-list">
          案件数量：<i class="danger">{{ statistics.size || 0 }}</i>
        </span>
        <span class="case-data-list">
          初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
        </span>
        <span class="case-data-list">
          初始债权本金：<i class="danger">{{
            numFilter(statistics.principal)
          }}</i>
        </span>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      class="multiple-table"
      @selection-change="handleSelectionChange"
      :data="dataList"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="center"
        prop="caseId"
        width="120"
      >
        <template #default="{ row, $index }">
          <el-button type="text" link @click="toDetails(row, $index)">{{
            row.caseId
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="开庭状态"
        align="center"
        prop="diposeStage"
        width="120px"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="被告"
        align="center"
        prop="clientName"
        width="120px"
      />
      <el-table-column
        v-if="columns[3].visible"
        label="标的额"
        align="center"
        prop="remainingDue"
        width="120px"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[4].visible"
        label="身份证号码"
        align="center"
        prop="clientIdNum"
        width="180px"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="户籍地"
        align="center"
        prop="clientCensusRegister"
        width="160px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[6].visible"
        label="开庭律师"
        align="center"
        prop="trialLawyer"
        width="120px"
      />
      <el-table-column
        v-if="columns[7].visible"
        label="承办律师"
        align="center"
        prop="undertakingLawyer"
        width="120px"
      />
      <el-table-column
        v-if="columns[8].visible"
        label="开庭次数"
        align="center"
        prop="trialSum"
        width="120px"
      />
      <el-table-column
        v-if="columns[9].visible"
        label="开庭时间"
        align="center"
        prop="trialTime"
        width="160px"
      />
      <el-table-column
        v-if="columns[10].visible"
        label="缴费金额"
        align="center"
        prop="costAmt"
        width="120px"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.costAmt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[11].visible"
        label="开庭法院"
        align="center"
        prop="trialCourt"
        width="120px"
      />
      <el-table-column
        v-if="columns[12].visible"
        label="开庭方式"
        align="center"
        prop="trialMethod"
        width="120px"
      />
      <el-table-column
        v-if="columns[13].visible"
        label="服务内容"
        align="center"
        prop="content"
        width="120px"
      />
      <el-table-column
        v-if="columns[14].visible"
        label="跟进人员"
        align="center"
        prop="updateBy"
        width="120px"
      />
      <el-table-column
        v-if="columns[15].visible"
        label="最近一次跟进时间"
        align="center"
        prop="updateTime"
        width="160px"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />
  </div>
</template>

<script setup>
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
import { getFilingCourtList } from "@/api/mediation/filingCourt";
import {
  getSessionList,
  selectSessionWithMoney,
} from "@/api/team/filingCaseOpenCourt";
import { formatParams2 } from "@/utils/common";
import { checkUserSip } from "@/api/system/user";
import { getCourtOptions } from "@/api/common/common"; //法院下拉

//全局数据
const router = useRouter();
const { proxy } = getCurrentInstance();
const total = ref(0);
//需要拆分的字段
const rangFiles = ["trialTime"];
const selectedArr = ref([]);
const statistics = ref({
  caseNum: 0,
  money: 0,
  principal: 0,
});
const activeTab = ref("全部");

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
});
const loading = ref(false);
const dataList = ref([]);
const showSearch = ref(false);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "开庭状态", visible: true },
  { key: 2, label: "被告", visible: true },
  { key: 3, label: "标的额", visible: true },
  { key: 4, label: "身份证号码", visible: true },
  { key: 5, label: "户籍地", visible: true },
  { key: 6, label: "开庭律师", visible: true },
  { key: 7, label: "承办律师", visible: true },
  { key: 8, label: "开庭次数", visible: true },
  { key: 9, label: "开庭时间", visible: true },
  { key: 10, label: "缴费金额", visible: true },
  { key: 11, label: "开庭法院", visible: true },
  { key: 12, label: "开庭方式", visible: true },
  { key: 13, label: "服务内容", visible: true },
  { key: 14, label: "跟进人员", visible: true },
  { key: 15, label: "最近一次跟进时间", visible: true },
]);

const single = ref(true);
const caseIds = ref([]);
//法院下拉数据
const trialCourtOption = ref([]);

// 获取列表
function getList() {
  const reqForm = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles))
  );
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["开庭缴费", "预约开庭", "正式开庭"];
  reqForm.disposeStageList = String(reqForm.disposeStageList);
  reqForm.mineQuery = true;
  loading.value = true;
  getFilingCourtList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
getList();
function checkSelectable() {
  return !queryParams.value.allQuery;
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList();
  });
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList();
  });
}

//表格选择
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  single.value = !(selection.length != 0);
  caseIds.value = selection.map((item) => item.caseId);
}

//跳转案件详情
function toDetails(row, index) {
  // let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  // let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  // delete queryChange.pageNum;
  // delete queryChange.pageSize;
  // queryChange.disposeStageList = activeTab.value != '全部' ? [activeTab.value] : ['开庭缴费', '预约开庭', '正式开庭']
  // let searchInfo = {
  //     query: {
  //         manageQueryParam: queryChange, //查询参数
  //         pageNumber: pageNumber, //当前第几页(变动)
  //         pageSize: 1, //一页一条
  //         pageIndex: 0, //当前第一条
  //         caseIdCurrent: row.caseId, //当前案件id
  //     }, //查询参数
  //     type: "mycase",
  //     total: total.value, //查询到的案件总数
  // };
  // localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  // router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
  router.push({ path: `/case/allcase-detail/caseDetails/${row.caseId}` });
}
//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!queryParams.value.allQuery && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      reqForm.mineQuery = true;
      selectSessionWithMoney(reqForm)
        .then((res) => {
          statistics.value = res.data;
        })
        .finally(() => reslove());
    });
  });
}

function getReqParams() {
  const reqParams = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles))
  );
  const reqForm = formatParams2(
    reqParams,
    selectedArr,
    queryParams.value.allQuery
  );
  reqForm.condition = queryParams.value.allQuery;
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["开庭缴费", "预约开庭", "正式开庭"];
  reqForm.mineQuery = true;
  return reqForm;
}

function openCreateCaseTaskDialog() {
  checkUserSip().then((res) => {
    if (res.data?.isPreTestSip) {
      //   const query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)));
      //   query.condition = query.allQuery;
      //   if (!query.allQuery) query.caseIds = caseIds.value;
      const query = getReqParams();
      query.mineQuery = true;
      proxy.$refs["createCaseTaskRef"].openDialog(query, 1);
    } else {
      proxy.$modal.msgWarning("未分配预测式外呼坐席");
    }
  });
}

getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    trialCourtOption.value = res.data;
  });
}

watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);
defineExpose({ getList });
</script>

<style lang="scss" scoped></style>
