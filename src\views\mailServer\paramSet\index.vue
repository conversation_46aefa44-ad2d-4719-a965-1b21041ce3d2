<template>
  <div class="app-container">
    <div class="app-content">
      <div class="form-content">
        <el-form :model="queryParams" ref="queryRef" :inline="true">
          <el-form-item>
            <el-select v-model="variableNameList" clearable filterable style="width: 240px" @focus="getVariableName"
                       multiple collapse-tags collapse-tags-tooltip placeholder="请选择参数名称">
              <el-option v-for="(item, index) in variableList" :key="index" :label="item.code" :value="item.code"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryParams.status" style="width: 240px" placeholder="请选择状态">
              <el-option label="启用" :value="0"/>
              <el-option label="禁用" :value="1"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.createBy" placeholder="请输入创建人" style="width: 240px"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="mb10">
          <el-button type="primary" v-hasPermi="['lawyer:paramSet:add']" plain @click="addParams()">添加参数</el-button>
        </div>
      </div>
      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="参数名称" align="center" prop="variableName"/>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <span v-if="scope.row.status == 0" class="blue">启用</span>
            <span v-else class="red">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy"/>
        <el-table-column label="创建时间" align="center" prop="createTime"/>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text"  v-hasPermi="['lawyer:paramSet:edit']" @click="addParams(scope.row)">编辑
            </el-button>
            <el-button v-if="scope.row.status == 0" type="text"  @click="setStatus(scope.row)"
                       v-hasPermi="['lawyer:paramSet:close']">禁用
            </el-button>
            <el-button v-else type="text" v-hasPermi="['lawyer:paramSet:open']"
                       @click="setStatus(scope.row)">启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-area">
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList"/>
      </div>
      <!-- 新增类型 -->
      <addParamsBox ref="addParamsBoxRef" @getList="getList"/>
    </div>
  </div>
</template>

<script setup name="ParamSet">
import addParamsBox from "../parametric/dialog/addParams.vue";
import {
  getTemplateOptions,
} from "@/api/lawyer/template";
import {getVariableList, editVariableStatus} from "@/api/lawyer/parametric";
//props传值
const props = defineProps({
  activeTab: {
    type: String,
    required: true,
  },
});
//全局变量
const {proxy} = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    variableNames: undefined,
    createBy: undefined,
    status: undefined,
  },
});
const {queryParams} = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
//模板变量
const variableList = ref([]);
const variableNameList = ref([]);
//多选
const checkMoreList = ref([
  "variableNames",
]);
const checkMoreName = ref([
  variableNameList
]);

//获取列表
function getList() {
  loading.value = true;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
        checkMoreName.value[index].value.length === 0
            ? undefined
            : checkMoreName.value[index].value.toString();
  });
  getVariableList(queryParams.value)
      .then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
}

getList();

/** 多选框选中数据 */
function addParams(row) {
  if (row) {
    let req = JSON.parse(JSON.stringify(row));
    proxy.$refs["addParamsBoxRef"].opendialog(req);
  } else {
    proxy.$refs["addParamsBoxRef"].opendialog();
  }
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//获取变量数据
function getVariableName() {
  getTemplateOptions().then((res) => {
    variableList.value = res.data;
  });
}

getVariableName();

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    variableNames: undefined,
    createBy: undefined,
    status: undefined,
  };
  variableNameList.value = []
  getList();
}

//停用
function setStatus(row) {
  let req = {
    status: row.status == 0 ? 1 : 0,
    id: row.id
  };
  proxy.$modal
      .confirm(`是否确认${row.status == 0 ? '停用' : '启用'}？此操作将${row.status == 0 ? '停用' : '启用'}该参数，是否确认？`)
      .then(() => {
        editVariableStatus(req)
            .then((res) => {
              getList();
            })
            .catch(() => {
            });
      })
      .catch(() => {
      });
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.form-content {
  padding: 10px 20px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
