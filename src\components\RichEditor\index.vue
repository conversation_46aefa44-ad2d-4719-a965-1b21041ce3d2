<template>
  <div ref="divRef" :style="`height: ${height}px`" />
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { AiEditor } from "aieditor";
import "aieditor/dist/style.css";
const emits = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: { type: String, default: "" },
  height: { type: Number, default: 500 },
  imgUploadUrl: { type: String, default: "/message/upload" },
  toolbarKeys: {
    type: Array,
    default: () => [
      "undo",
      "redo",
      "brush",
      "eraser",
      "|",
      "heading",
      "font-family",
      "font-size",
      "|",
      "bold",
      "italic",
      "underline",
      "strike",
      "link",
      "code",
      "subscript",
      "superscript",
      "hr",
      "emoji",
      "|",
      "highlight",
      "font-color",
      "|",
      "align",
      "line-height",
      "|",
      "bullet-list",
      "ordered-list",
      "indent-decrease",
      "indent-increase",
      "break",
      "|",
      "image",
      "quote",
      "code-block",
      "table",
      "|",
      "printer",
      "fullscreen",
    ],
  },
});
const divRef = ref();
let aiEditor = null;
function insertText(text) {
  aiEditor.focus();
  aiEditor.insert(text);
}
function clear() {
  aiEditor.clear();
}
onMounted(() => {
  aiEditor = new AiEditor({
    toolbarKeys: props.toolbarKeys,
    defaultSize: 400,
    element: divRef.value,
    placeholder: "请输入内容...",
    content: props.modelValue,
    fieldName: "file",
    lineHeight:{
      values:["1.0", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7", "1.8", "1.9", "2.0"],
    },
    onChange: (aiEditor) => {
      // 将图片的样式去掉
      let htmlContent = aiEditor.getHtml();
      const imgTagArr1 = htmlContent.match(/<img[^>]*>/g);
      const imgTagArr2 = imgTagArr1?.map(
        (item) => item.match(/src="([^"]+)"/)[1]
      );
      imgTagArr1 &&
        imgTagArr1.forEach((item, index) => {
          const newImg = `<img src="${imgTagArr2[index]}" />`;
          htmlContent = htmlContent.replace(item, newImg);
        });
      if (htmlContent && htmlContent.includes("<br>")) {
        htmlContent = htmlContent.replaceAll("<br>", "<br/>");
      }
      // if(htmlContent.includes(' ')){
      //   htmlContent = htmlContent.replaceAll('\t', '&nbsp;')
      // }
      function replaceTextInHtml(html) {
        // 使用正则表达式匹配标签内的文本
        return html.replace(/>([^<]+)</g, (match, p1) => {
          // 替换空格为 &sbnp;
          const replacedText = p1.replace(/ /g, "&nbsp;");
          return ">" + replacedText + "<";
        });
      }
      htmlContent = replaceTextInHtml(htmlContent);
      emits("update:modelValue", htmlContent);
    },
    image: {
      uploadUrl: import.meta.env.VITE_APP_BASE_API + props.imgUploadUrl,
      uploadHeaders: {
        ContentType: "multipart/form-data",
        Authorization: "Bearer " + getToken(),
      },
      uploader: (file, uploadUrl, headers, formName) => {
        const formData = new FormData();
        formData.append("file", file);
        return new Promise((resolve, reject) => {
          fetch(uploadUrl, {
            method: "post",
            headers: { Accept: "application/json", ...headers },
            body: formData,
          })
            .then((resp) => resp.json())
            .then((json) => {
              resolve(json);
            })
            .catch((error) => {
              reject(error);
            });
        });
      },
      uploaderEvent: {
        onUploadBefore: (file, uploadUrl, headers) => {
          //Listen for the image upload before it happens. This method can be left without returning any content, but if it returns false, the upload will be aborted.
        },
        onSuccess: (file, res) => {
          if (res.code == 200) {
            // 上传成功，在成功函数里填入图片路径
            const { url, fileUrl } = res.data;
            aiEditor.insert(
              `<img class="rich-editor-img" src="${
                url || fileUrl
              }" style="width:auto;" />`
            );
          } else {
            // failure("上传失败");
          }
          console.log(file, res);
        },
        onFailed: (file, response) => {
          //Listen for Image Upload Failure, or if the returned JSON information is incorrect.
        },
        onError: (file, error) => {
          //Listen for Image Upload Errors, such as network timeouts, etc.
        },
      },
      bubbleMenuItems: ["AlignLeft", "AlignCenter", "AlignRight", "delete"],
    },
  });
});

onUnmounted(() => {
  aiEditor && aiEditor.destroy();
});
defineExpose({ insertText, clear });
</script>
<style>
.aie-container img {
  width: auto !important;
}
</style>
