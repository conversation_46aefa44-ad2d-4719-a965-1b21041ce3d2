import Cookies from 'js-cookie'

const TokenKey = 'appeal-Token'
const username = 'appeal-username'

// 导出一个函数getToken，用于获取Token
export function getToken() {
  return Cookies.get(TokenKey)
}

// 导出一个函数，用于设置token
export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

// 导出一个函数，用于删除Token
export function removeToken() {
  return Cookies.remove(TokenKey)
}

// 导出一个函数，用于获取用户名
export function getName() {
  return Cookies.get(username)
}
