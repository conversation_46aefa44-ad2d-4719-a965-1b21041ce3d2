<template>
    <div>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
            <el-form-item label="日期：" prop="date">
                <el-date-picker
                    v-model="queryParams.date"
                    type="month"
                    value-format="YYYY-MM"
                />
            </el-form-item>
            <el-form-item label="坐席类型：" prop="seatType">
                <el-select v-model="queryParams.seatType" placeholder="请选择坐席类型" style="width: 240px">
                    <el-option
                        v-for="item in seatTypeEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="sip账号：" prop="sipAccount">
                <el-input v-model="queryParams.sipAccount" placeholder="请输入坐席账号" clearable style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div>
            <el-button v-if="checkPermi(['agentService:consumptionRecords:export'])" type="primary" @click="antiShake(handleExport)">导出明细</el-button>
        </div>
        <div class="mt10">
            <p>坐席当月累计消费金额：{{ numFilter(amount?.totalConsumption) }}元，坐席剩余余额：{{ numFilter(amount?.balance) }}元</p>
        </div>
        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange" class="mt10">
          <el-table-column label="sip账号" align="center" prop="sipAccount" show-overflow-tooltip />
          <el-table-column label="坐席类型" align="center" prop="seatType" show-overflow-tooltip :formatter="seatTypeFormat" />
          <el-table-column label="套餐名称" align="center" prop="packageName" show-overflow-tooltip />
          <el-table-column label="套餐月租（元/月）" align="center" prop="monthlyFee" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.monthlyFee) }}
            </template>
          </el-table-column>
          <el-table-column label="套餐含免费时长（分钟）" align="center" prop="freeDuration" show-overflow-tooltip />
          <el-table-column label="总拨打时长(分钟)" align="center" prop="totalCallDuration" show-overflow-tooltip />
          <el-table-column label="单价(元/分钟)" align="center" prop="unitPrice" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.unitPrice) }}
            </template>
        </el-table-column>
          <el-table-column label="合计(元)" align="center" prop="totalCost" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.totalCost) }}
            </template>
        </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import { settleList, selectListCost } from "@/api/settlement/agentService";
import { numFilter } from "@/utils/common";
import { nextTick } from "vue";
const getCurrentYearMonth = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 确保月份是两位数
  return `${year}-${month}`;
}
const { proxy } = getCurrentInstance();
const total = ref(0);
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        date: getCurrentYearMonth(),
    },
});
const { queryParams } = toRefs(data);
const loading = ref(false);
const dataList = ref([]);
const amount = ref(undefined);
const rangeFields = [];
const selectedArr = ref([]);
const seatTypeEnum = [
    {
        value: 1,
        label: '普通坐席',
    },
    {
        value: 2,
        label: '预测试外呼坐席',
    },
]
const seatTypeFormat = (row) => {
    return seatTypeEnum.find((item) => item.value === row.seatType)?.label || '';
}

const handleQuery = () => {
    queryParams.value.pageNum = 1;
    nextTick(() => {
        getList();
    })
};

const resetQuery = () => {
    proxy.$refs["queryRef"].resetFields();
    nextTick(() => {
        getList();
    })
}

const handleSelectionChange = (selection) => {
    selectedArr.value = selection;
}

const getAmount = async (query) => {
    try {
        const res = await selectListCost(query);
        if (res.code == 200) {
            amount.value = res.data;
        }
    } catch (error) {
        console.error(error);
    }
}

const getList = async () => {
    loading.value = true;
    const query = proxy.addFieldsRange(queryParams.value, rangeFields);
    try {
        const res = await settleList(query);
        if (res.code == 200) {
            dataList.value = res.rows;
            total.value = res.total;
        }
        await getAmount(query);
    } catch (error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};
getList();

const handleExport = () => {
    const query = proxy.addFieldsRange(queryParams.value, rangeFields);
    proxy.downloadforjson(
        "/settle/export",
        query,
        `消费记录.xlsx`
    );
}

</script>

<style lang="scss" scoped></style>