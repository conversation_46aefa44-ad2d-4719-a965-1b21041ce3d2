<template>
  <div class="handle-urage caseinfo-wrap pd20">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="160px">
      <el-form-item label="一级阶段选择" prop="lawsuitState">
        <el-radio-group v-model="form.lawsuitState" @change="handleChangeState">
          <el-radio v-for="item in oneState" :key="item" :label="item">
            {{ item }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="二级阶段选择" prop="mediateFieldName">
        <el-radio-group v-model="form.mediateFieldName">
          <el-radio v-for="item in twoState" :key="item.info" :label="item.info">
            {{ item.info }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 表单配置 -->
    <formComponents ref="formComponentsRef" :key="formOption" :contactMedium="form.mediateFieldName"
      v-model:formOption="formOption" v-model:worksheetForm="worksheetForm" />
    <div class="mt20" style="margin-left:160px">
      <el-button type="primary" plain :loading="loading" @click="saveLawsuit">保存</el-button>
    </div>
  </div>
</template>

<script setup name="lawsuitUrge">
import formComponents from "@/components/formComponents/index";
import { selectContactChannel, insertComplaint } from "@/api/caseDetail/urge";
import { selectStageFrom } from "@/api/caseDetail/detail";
import { stateOnlineFilling } from "@/api/mediation/onlineFiling";
import { pageTypeEnum } from "@/utils/enum";
import { watch } from "vue";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(["setCurrentRow", "getcaseDetailInfo", "getUrgeList"]);
//表单设置
const props = defineProps({
  caseId: {
    type: [Number, String]
  }
});

const loading = ref(false);
const data = reactive({
  form: {
    registerType: 2,
    lawsuitState: pageTypeEnum[route.query.pageType],
    mediateFieldName: route.query.twoStage,
  },
  rules: {
    contactMedium: [{ required: true, message: "请选择调诉阶段", trigger: "change" }],
    lawsuitState: [{ required: true, message: "请选择一级阶段", trigger: "change" }],
    mediateFieldName: [{ required: true, message: "请选择二级阶段", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//调诉阶段
const contactChannel = ref([]);
const formOption = ref([]);
//自定义表单
const worksheetForm = ref([]);


// 选择一级阶段
function handleChangeState() {
  form.value.mediateFieldName = twoState.value ? twoState.value[0].info : ''
}

//获取调诉阶段
function getContactChannel() {
  let req = { type: 2 , caseId: route.params.caseId}
  stateOnlineFilling(req).then((res) => {
    contactChannel.value = res.data;
  });
}
getContactChannel();

//查询阶段表单
function getStageFrom() {
  let req = { stageTwoName: form.value.mediateFieldName, disposeWay: 2, };
  selectStageFrom(req).then((res) => {
    formOption.value = res.data;
  });
  worksheetForm.value = []
}

//保存催记
function saveLawsuit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      // loading.value = true;
      let req = {
        registerType: 2,
        disposeWay: 2,
        caseId: props.caseId,
        stageId: form.value.contactMedium,
        registerType: form.value.registerType,
        mediateStage: form.value.mediateFieldName,
        mediateFieldName: form.value.mediateFieldName,
      }
      proxy.$refs['formComponentsRef'].submitForm()
      Object.assign(req, worksheetForm.value)
      addFile(req).then(() => {
        insertComplaint(req).then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          emit('getUrgeList')
          reset()
        }).finally((erroe) => {
          loading.value = false;
        })
      })
    }
  })
}

// 添加字段
function addFile(form) {
  return new Promise((resolve) => {
    form.objContent.forEach(item => {
      const obj = formOption.value.find(item2 => item2?.sysDictType?.dictName == item.fieldName)
      if (obj && obj?.sysDictType?.sysDictDataList) {
        obj && obj?.sysDictType?.sysDictDataList?.forEach(item2 => {
          if (item[item.receiveParam].includes(item2.dictValue)) {
            if (item2.dictLabel) {
              // item.labelName += `${item2.dictLabel}、`
              // item.labelName = item.labelName.substr(0, item.labelName.length - 1)
              item.labelName = item.labelName.replace('undefined', '')
            }
          }
        })
      } else {
        item.labelName = item[item.receiveParam]
      }
    })
    resolve()
  })
}

//清空表单方法
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    registerType: 2,
    lawsuitState: pageTypeEnum[route.query.pageType],
    mediateFieldName: route.query.twoStage,
  };
  if (proxy.$refs["formComponentsRef"]) {
    proxy.$refs["formComponentsRef"]?.resetNewForm()
  }
}

const oneState = computed(() => [...new Set(contactChannel.value.map(item => item.code))])
const twoState = computed(() => contactChannel.value.filter(item => item.code == form.value.lawsuitState) || [])

watch(() => form.value, () => {
  nextTick(() => {
    getStageFrom()
  })
}, { immediate: true, deep: true })
</script>

<style></style>
