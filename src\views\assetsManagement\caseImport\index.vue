<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="4" :xs="24">
                <div class="head-container">
                    <el-input v-model="assetsName" placeholder="请输入名称" clearable prefix-icon="Search"
                        style="margin-bottom: 20px">
                    </el-input>
                </div>
                <div class="head-container">
                    <div class="home-style" @click="clickAll">
                        <el-icon>
                            <home-filled />
                        </el-icon>
                        <span class="ml5">全部</span>
                    </div>
                    <el-tree :data="assetsOptions" :props="{ label: 'label', children: 'children' }"
                        :expand-on-click-node="false" :filter-node-method="filterNode" :current-node-key="currentKey"
                        node-key="id" ref="assetsTreeRef" highlight-current @node-click="handleNodeClick"
                        @current-change="currentChange">
                        <template #default="{ node }">
                            <el-tooltip effect="light" :content="node.label" placement="right-start">
                                <span class="el-tree-node__label">{{ node.label }}</span>
                            </el-tooltip>
                        </template>
                    </el-tree>
                </div>
            </el-col>

            <el-col :span="20" :xs="24">
                <el-form :model="queryParams" :class="`${showSearch ? 'form-auto' : 'form-h50'}`" ref="queryRef" inline
                    label-width="82px">
                    <el-form-item label="导入批次号" prop="batchNum">
                        <el-input v-model="queryParams.batchNum" placeholder="导入批次号" clearable style="width: 260px"
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="项目名称" prop="packageNameArr">
                        <el-select v-model="packageNameArr" placeholder="请输入或选择项目名称" clearable filterable
                            :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip
                            @change="changePackageName" style="width: 260px">
                            <el-option v-for="item in backList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="委案金额" prop="entrustMoneyTotal">
                        <el-row style="width: 260px">
                            <el-col :span="11">
                                <el-input v-model="queryParams.entrustMoneyTotal1" clearable />
                            </el-col>
                            <el-col :span="2" class="text-center">
                                <span>-</span>
                            </el-col>
                            <el-col :span="11">
                                <el-input v-model="queryParams.entrustMoneyTotal2" clearable />
                            </el-col>
                        </el-row>
                    </el-form-item>
                    <el-form-item label="案件量">
                        <el-row style="width: 260px">
                            <el-col :span="11">
                                <el-input v-model="queryParams.caseNumber1" clearable />
                            </el-col>
                            <el-col :span="2" class="text-center">
                                <span>-</span>
                            </el-col>
                            <el-col :span="11">
                                <el-input v-model="queryParams.caseNumber2" clearable />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-form>
                <div class="text-center">
                    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
                    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
                </div>

                <el-row :gutter="10" class="mb8 mt10">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['assets:asset:importcase']" type="primary" plain icon="Plus"
                            :disabled="single || loading" @click="toimportCase">导入案件</el-button>
                        <el-button v-hasPermi="['assets:asset:batchImportContacts']" type="success" plain icon="Plus"
                            :disabled="single || loading" @click="openImportContact(selectedProduct)">批量导入联系人</el-button>
                        <el-button v-hasPermi="['assets:asset:importRepayplan']" type="danger" plain icon="Plus"
                            :disabled="single || loading" @click="openPlanPay(selectedProduct)">导入还款计划</el-button>
                        <el-button v-hasPermi="['assets:asset:importUrgeReacord']" type="info" plain icon="Plus"
                            :disabled="single || loading" @click="openUrgeReacord">导入沟通记录</el-button>
                    </el-col>
                </el-row>

                <div class="text-flex mb5">
                    <span>剩余债权总额：</span>
                    <span class="text-danger mr10">{{ numFilter(statistics.totalRemainingClaims || 0) }}</span>
                    <span>剩余债权本金：</span>
                    <span class="text-danger mr10">{{ numFilter(statistics.residualDebtPrincipal || 0) }}</span>
                    <span>案件数量：</span><span class="text-danger">{{ formatAmountWithComma(statistics.caseNum || 0)
                        }}</span>
                    <!-- <span>案件数量：</span><span class="text-danger">{{ statistics.caseNum || 0
                        }}</span> -->
                </div>
                <div class="operation-revealing-area mb10" style="margin-top:0px">
                    <div class="hint-flex" style="height: 32px">
                        <el-tooltip class="hint" trigger="click" effect="light" placement="bottom-start">
                            <div>
                                <svg-icon class="hint-item" icon-class="question" />
                            </div>
                            <template #content>
                                <div class="info-tip" style="width:85vw">
                                    <el-icon class="info-tip-icon" :size="16">
                                        <warning-filled />
                                    </el-icon>
                                    <div>
                                        导入案件前请先下载 【{{ selectedProduct.productName }}】 (<span
                                            class="text-danger">点击左侧选择产品</span>)案件信息模板，
                                        <el-button type="primary" link @click="caseinfoTpl"
                                            :disabled="!selectedProduct.productName" style="text-decoration: underline">
                                            点击下载案件信息模板</el-button>
                                        &nbsp;|&nbsp;
                                        <el-button type="primary" link @click="contactTplVertical"
                                            :disabled="!selectedProduct.productName"
                                            style="text-decoration: underline">点击下载联系人竖向模板</el-button>
                                        &nbsp;|&nbsp;
                                        <el-button type="primary" link @click="contactTplHorizontal"
                                            :disabled="!selectedProduct.productName"
                                            style="text-decoration: underline">点击下载联系人横向模板</el-button>
                                        &nbsp;|&nbsp;
                                        <el-button type="primary" link @click="planPayBackTpl"
                                            :disabled="!selectedProduct.productName"
                                            style="text-decoration: underline">点击下载导入还款计划模板</el-button>
                                        &nbsp;|&nbsp;
                                        <el-button type="primary" link :disabled="!selectedProduct.productName"
                                            style="text-decoration: underline"
                                            @click="urgeRecordTpl">点击下载导入催记模板</el-button>
                                    </div>
                                </div>
                            </template>
                        </el-tooltip>
                    </div>
                    <right-toolbar @queryTable="getList" :columns="columns" v-model:showSearch="showSearch" />
                </div>
                <el-table v-loading="loading" :data="dataList" @sort-change="handleSortChange">
                    <el-table-column label="批次号" prop="batchNum" key="batchNum" align="center" :width="180"
                        v-if="columns[0].visible" />
                    <el-table-column label="项目名称" prop="packageName" key="packageName" align="center" width="110px"
                        v-if="columns[1].visible" />
                    <el-table-column label="产品名称" prop="productName" key="productName" align="center" width="110px"
                        v-if="columns[2].visible" />
                    <el-table-column label="项目系数" prop="ratio" key="ratio" align="center" width="110px"
                        v-if="columns[3].visible" />
                    <el-table-column label="计算公式" prop="formulaName" key="formulaName" align="center" width="110px"
                        v-if="columns[4].visible" />
                    <el-table-column label="委托方" prop="ownerName" key="ownerName" :width="180" align="center"
                        v-if="columns[5].visible" />
                    <el-table-column label="案件量" prop="caseNumber" key="caseNumber" align="center" sortable="caseNumber"
                        width="110" v-if="columns[6].visible">
                        <template #default="{ row }">
                            {{ row.caseNumber || 0 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="委托金额(元)" prop="entrustMoneyTotal" key="entrustMoneyTotal" align="center" v-if="columns[7].visible">
                        <template #default="{ row }">
                            {{ numFilter(row.entrustMoneyTotal || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="案件回收日期" prop="recyclingDate" key="recyclingDate" align="center" width="110px" v-if="columns[8].visible" />
                    <el-table-column label="导入状态" prop="importStartInfo" key="importStartInfo" align="center" v-if="columns[9].visible" />
                    <el-table-column label="操作" align="center" fixed="right">
                        <template #default="{ row }">
                            <el-dropdown v-if="row.importStart != '0'" trigger="hover" @command="handlesCommand">
                                <span class="el-dropdown-link">
                                    操作栏
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:edit']) && row.importStart == '1'"
                                            :command="{ data: row, func: editReacord }">编辑</el-dropdown-item>
                                        <el-dropdown-item v-if="checkPermi(['assets:asset:importUrgeReacord']) &&
                                            row.importStart == '1'"
                                            :command="{ data: row, func: openUrgeReacord }">导入沟通记录</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:updateCase']) && row.importStart == '1'"
                                            :command="{ data: row, func: openUpdateCase }">更新案件</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:downExisting']) && row.importStart == '1'"
                                            :command="{ data: row, func: downExisting }">现批次案件下载</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:downCasesSource']) && row.importStart == '1'"
                                            :command="{ data: row, func: downCasesSource }">源案件下载</el-dropdown-item>
                                        <el-dropdown-item v-if="row.importStart == '2' || row.importStart == '3'"
                                            :command="{ data: row, func: faliReason }">失败原因</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:importcaseAgain']) && row.importStart != '1'"
                                            :command="{ data: row, func: toimportCase }">重新导入案件</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:importContacts']) && row.importStart == '1'"
                                            :command="{ data: row, func: openImportContact }">导入联系人</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:importRepayplan']) && row.importStart == '1'"
                                            :command="{ data: row, func: openPlanPay }">导入还款计划</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:reductionSet']) && row.importStart == '1'"
                                            :command="{ data: row, func: openReduction }">减免设置</el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="checkPermi(['assets:asset:entrustMoneySet']) && row.importStart == '1'"
                                            :command="{ data: { id: row.id, entrustMoneyId: row.entrustMoneyId }, func: openEntrustMoney }">计息方式设置</el-dropdown-item>
                                        <el-dropdown-item v-if="checkPermi(['assets:asset:toContractManage']) &&
                                            row.importStart == '1'
                                        " :command="{ data: row, func: toContractManage }">合同管理</el-dropdown-item>
                                        <!-- <el-dropdown-item v-if="checkPermi(['assets:asset:downAllocatedHistory']) &&
                                            row.importStart == '1'
                                        " :command="{ data: row, func: downAllocatedHistory }">分案历史</el-dropdown-item> -->
                                        <el-dropdown-item v-if="checkPermi(['assets:asset:del'])"
                                            :command="{ data: row.id, func: removeSet }">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>
        <!-- 减免设置 -->
        <reductionVue ref="reductionRef" @queryList="getList" />

        <!-- 计息方式 -->
        <entrustMoneyVue ref="entrustMoneyRef" @queryList="getList" />

        <!-- 导入联系人 -->
        <importContactVue ref="importContactRef" @contactTplVertical="contactTplVertical"
            @contactTplHorizontal="contactTplHorizontal" />

        <editRecord ref="editRecordRef" @queryList="getList" />
        <!-- 导入还款计划 -->
        <planPayVue ref="planPayRef" @planPayBackTpl="planPayBackTpl" />

        <!-- 导入沟通记录 -->
        <urgeRecordVue ref="urgeRecordRef" @urgeRecordTpl="urgeRecordTpl" />

        <!-- 更新案件 -->
        <updateCaseVue ref="updateCaseRef" @queryList="getList" />
    </div>
</template>

<script setup name="Asset">
import reductionVue from "./dialog/reduction.vue";
import editRecord from "./dialog/editRecord.vue";
import entrustMoneyVue from "./dialog/entrustMoney.vue";
import importContactVue from "./dialog/importContact.vue";
import planPayVue from "./dialog/planPay.vue";
import urgeRecordVue from "./dialog/urgeRecord.vue";
import updateCaseVue from "./dialog/updateCase.vue";

import { assetOwnerTree } from "@/api/assets/assetside";
import { getPackName } from "@/api/assets/asset/asset";
import { assetList, selectCount, deletedAset } from "@/api/assets/asset/asset";

import { checkPermi } from "@/utils/permission";
import { numFilter, blobToOriginalData, formatAmountWithComma } from "@/utils/common";

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();

const assetsName = ref("");
const assetsOptions = ref([]);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const showSearch = ref(false)
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        batchNum: undefined,
        entrustMoneyTotal1: undefined,
        entrustMoneyTotal2: undefined,
        caseNumber1: undefined,
        caseNumber2: undefined,
        ownerId: undefined,
        productId: undefined,
        packageNameArr: undefined,
    },
});
const backList = ref([]);
const packageNameArr = ref([]);

const { queryParams } = toRefs(data);

const currentKey = ref("owner:" + route.query.ownerId);

const statistics = ref({
    caseNum: 0,
    totalRemainingClaims: 0,
    residualDebtPrincipal: 0,
});
const single = ref(true);
const selectedProduct = ref({
    productId: undefined,
    productName: undefined,
});

//列显示/隐藏
const columns = ref([
    { key: 0, label: `批次号`, visible: true },
    { key: 1, label: `项目名称`, visible: true },
    { key: 2, label: `产品名称`, visible: true },
    { key: 3, label: `项目系数`, visible: true },
    { key: 4, label: `计算公式`, visible: true },
    { key: 5, label: `委托方`, visible: true },
    { key: 6, label: `案件量`, visible: true },
    { key: 7, label: `委托金额(元)`, visible: true },
    { key: 8, label: `案件回收日期`, visible: true },
    { key: 9, label: `导入状态`, visible: true },
]);

//获取列表
function getList() {
    loading.value = true;
    assetList(queryParams.value)
        .then((res) => {
            loading.value = false;
            dataList.value = res.rows;
            total.value = res.total;
        })
        .catch(() => {
            loading.value = false;
        });
    geStatistics();
}

//打开编辑
function editReacord(row) {
    proxy.$refs["editRecordRef"].opendialog(row);
}
//删除资产管理
function removeSet(id) {
    proxy.$modal.confirm(
        `此操作将彻底删除该批次案件及批次下的所有文件，删除后无法找回，是否确认删除？`
    ).then(() => {
        loading.value = true;
        return deletedAset(id);
    }).then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
    }).catch((err) => {
        loading.value = false;
    });
}
//获取资产包
function getPackList() {
    getPackName().then(res => {
        backList.value = res.data
    })
}
getPackList();
// 排序
function handleSortChange({ prop, order }) {
    const orderByObj = { caseNumber: 1, entrustMoneyTotal: 2 }
    queryParams.value.orderBy = orderByObj[prop]
    queryParams.value.sortOrder = proxy.orderEnum[order]
    getList()
    geStatistics()
}

//切换资产包
function changePackageName() {
    queryParams.value.packageNameArr = packageNameArr.value?.toString()
}

//获取下拉树
function getTreeselect() {
    assetOwnerTree().then((res) => {
        assetsOptions.value = res.data;
        nextTick(() => {
            if (route.query.productId) {
                queryParams.value.productId = route.query.productId;
                let id = "proud:" + route.query.productId;
                proxy.$refs["assetsTreeRef"].setCurrentKey(id);

                nextTick(() => {
                    let currentNode = proxy.$refs["assetsTreeRef"].getNode(id);
                    selectedProduct.value.productId = route.query.productId;
                    selectedProduct.value = {
                        productId: route.query.productId,
                        productName: currentNode.data.label,
                    };
                    queryParams.value.productId = route.query.productId;
                    getList();
                });
            } else {
                getList();
            }
        });
    });
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

// 点击项目
function clickAll() {
    queryParams.value.pageNum = 1;
    queryParams.value.ownerId = undefined;
    queryParams.value.productId = undefined;
    selectedProduct.value.productName = undefined;
    selectedProduct.value.productId = undefined;
    single.value = true;
    //清空选中
    proxy.$refs["assetsTreeRef"].setCurrentKey(null);
    getList();
}

/** 节点单击事件 */
function handleNodeClick(data, node, obj) {
    queryParams.value.pageNum = 1;
    let arr = data.id.split(":");
    if (arr[0] == "owner") {
        queryParams.value.ownerId = arr[1];
        queryParams.value.productId = undefined;
        single.value = true;
    } else {
        queryParams.value.productId = arr[1];
        queryParams.value.ownerId = undefined;

        selectedProduct.value.productName = data.label;
        selectedProduct.value.productId = arr[1];
        selectedProduct.value.ownerName = node.parent.data.label;
        single.value = false;
    }
    getList();
}

//节点变化
function currentChange(data, node) {
    console.log(data);
}

//筛选转让方下拉及产品
watch(assetsName, (val) => {
    proxy.$refs["assetsTreeRef"].filter(val);
});

onMounted(() => {
    getTreeselect();
});

//跳转导入案件
function toimportCase(row) {
    console.log(row, selectedProduct);
    let query = {};
    let pathid = undefined;
    if (row.id) {
        query = { rowId: row.id };
        pathid = row.id;
    } else {
        query = selectedProduct.value;
        pathid = selectedProduct.value.productId;
    }
    router.push({
        path: "/assets/asset-import/importcase/" + pathid,
        query: query,
    });
}

//统计数据
function geStatistics() {
    let req = JSON.parse(JSON.stringify(queryParams.value));
    req.pageNum = undefined;
    req.pageSize = undefined;
    selectCount(req).then((res) => {
        statistics.value = res.data;
    });
}

//搜索
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

//重置
function resetQuery() {
    const { productName, productId, ownerName, ownerId } = queryParams.value
    proxy.$refs["queryRef"].resetFields();
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        productName,
        ownerId,
        productId,
        ownerName,
        batchNum: undefined,
        entrustMoneyTotal1: undefined,
        entrustMoneyTotal2: undefined,
        caseNumber1: undefined,
        caseNumber2: undefined,
    };
    getList();
    packageNameArr.value = []
}

//案件信息模板
function caseinfoTpl() {
    proxy.download(
        "teamProduct/downloadTemplate",
        { id: selectedProduct.value.productId },
        `tpl_${selectedProduct.value.productName}.xlsx`,
        { gateway: 'cis' }
    );
}

//下载联系人竖向模板
function contactTplVertical() {
    proxy.download(
        "team/download/contactTemplateVertical",
        { id: selectedProduct.value.productId },
        `tpl_联系人竖向模板.xlsx`,
        { gateway: 'cis' }
    );
}

//下载联系人横向模板
function contactTplHorizontal() {
    proxy.download(
        "team/download/contactTemplateHorizontal",
        { id: selectedProduct.value.productId },
        `tpl_联系人横向模板.xlsx`,
        { gateway: 'cis' }
    );
}

//下载导入还款计划模板
function planPayBackTpl() {
    proxy.download(
        "team/download/downloadPlanTemplate",
        { id: selectedProduct.value.productId },
        `tpl_导入还款计划模板.xlsx`,
        { gateway: 'cis' }
    );
}

//下载导入催记模板
function urgeRecordTpl() {
    proxy.download(
        "team/download/downloadUrgeRecordTemplate",
        { id: selectedProduct.value.productId },
        `tpl_导入沟通记录模板.xlsx`,
        { gateway: 'cis' }
    );
}

//操作栏
function handlesCommand(command) {
    command.func(command.data);
}
//打开导入催记
function openUrgeReacord(row) {
    proxy.$refs["urgeRecordRef"].opendialog(row, selectedProduct.value);
}

//打开更新案件
function openUpdateCase(row) {
    proxy.$refs["updateCaseRef"].opendialog(row);
}

//现案件下载
async function downExisting(row) {
    const result = await proxy.download(
        "/team/download/existingCases",
        { id: row.id },
        `tpl_现案件${row.batchNum}.xlsx`, { gateway: 'cis' }
    );

    blobToOriginalData(result).then(array => {
        proxy.$modal.alertWarning(
            `此导出操作已成功提交，请前往导出日志查看导出结果!\n
           任务文件名称：${array.data}`
        )
    }).catch(error => {
        proxy.$modal.alertWarning(`此导出操作已成功提交，请前往导出日志查看导出结果!`)
        console.error('转换出错：', error);
    });
}

//案源下载
async function downCasesSource(row) {
    const result = await proxy.download(
        "/team/download/casesSource",
        { id: row.id },
        `tpl_案源${row.batchNum}.xlsx`, { gateway: 'cis' }
    );

    blobToOriginalData(result).then(array => {
        proxy.$modal.alertWarning(
            `此导出操作已成功提交，请前往导出日志查看导出结果!\n
           任务文件名称：${array.data}`
        )
    }).catch(error => {
        proxy.$modal.alertWarning(`此导出操作已成功提交，请前往导出日志查看导出结果!`)
        console.error('转换出错：', error);
    });
}

//失败原因
function faliReason(row) {
    router.push({
        path: "/assetsManagement/importLog",
        query: { type: "0", batch: row.batchNum },
    });
}

//合同管理
function toContractManage(row) {
    router.push({ path: "/assets/asset-btn/contract/" + row.id });
}

//下载分案历史
async function downAllocatedHistory(row) {
    const result = await proxy.download(
        "/case/download/allocatedHistory",
        { id: row.id },
        `tpl_分案历史${row.batchNum}.xlsx`
    );

    blobToOriginalData(result).then(array => {
        proxy.$modal.alertWarning(
            `此导出操作已成功提交，请前往导出日志查看导出结果!\n
           任务文件名称：${array.data}`
        )
    }).catch(error => {
        console.error('转换出错：', error);
    });
}

//打开导入联系人
function openImportContact(row) {
    proxy.$refs["importContactRef"].opendialog(row);
}

//打开导入还款计划
function openPlanPay(row) {
    proxy.$refs["planPayRef"].opendialog(row);
}

//打开减免设置
function openReduction(row) {
    proxy.$refs["reductionRef"].opendialog(row.id, row.reductionId, row.autoReduction);
}

//打开计息方式
function openEntrustMoney({ id, entrustMoneyId }) {
    proxy.$refs["entrustMoneyRef"].opendialog(id, entrustMoneyId);
}

</script>

<style lang="scss" scoped>
.home-style {
    font-size: 16px;
    color: #606266;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
}

.form-content {
    overflow: hidden;
}

.h-50 {
    height: 50px;
}

.h-auto {
    height: auto !important;
}

.text-flex {
    display: inline-flex;
    align-items: center;
    height: 32px;
    font-size: 14px;
    font-weight: 500;
}

.text-flex .text-danger {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
}

.hint-item {
    font-size: 18px;
    color: #5a5e66;
    cursor: pointer;
}

.hint-flex {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 14px;
}

:deep(.el-popper.is-light) {
    transform: translate3d(220px, 311.111px, 0px);
}

.info-tip {
    border: unset;

    span.text-primary {
        cursor: pointer;
        text-decoration: underline;
    }
}

:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}

:deep(.el-tree-node__label) {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>