<!--我的外访-->
<template>
  <el-form
    class="form-content h-50 mt20"
    :class="{ 'h-auto': showSearch }"
    :model="queryParams"
    ref="queryRef"
    :inline="true"
    label-width="68px"
  >
    <el-form-item label="案件ID" prop="caseId">
      <el-input
        v-model="queryParams.caseId"
        placeholder="请输入案件ID"
        clearable
        style="width: 240px"
        @keyup.enter="antiShake(handleQuery)"
      />
    </el-form-item>
    <el-form-item label="客户姓名" prop="clientName">
      <el-input
        v-model="queryParams.clientName"
        placeholder="请输入客户姓名"
        clearable
        style="width: 240px"
        @keyup.enter="antiShake(handleQuery)"
      />
    </el-form-item>

    <el-form-item label="申请时间" style="width: 308px">
      <el-date-picker
        v-model="queryParams.applyDate"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
    </el-form-item>
    <el-form-item label="审核时间" style="width: 308px">
      <el-date-picker
        v-model="queryParams.updateTime"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
    </el-form-item>
  </el-form>

  <div class="text-center">
    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
      >搜索</el-button
    >
    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
  </div>
  <el-row class="mb10 mt10 h32">
    <right-toolbar
      v-model:showSearch="showSearch"
      :columns="columns"
      @queryTable="getList"
    ></right-toolbar>
  </el-row>

  <el-tabs class="mb8" type="card" v-model="activeTab" @tab-click="tabChange">
    <el-tab-pane
      v-for="item in examineStateTab"
      :key="item.key"
      :label="item.value"
      :name="item.key"
    ></el-tab-pane>
  </el-tabs>

  <el-table
    v-loading="loading"
    ref="multipleTableRef"
    :data="dataList"
    @selection-change="handleSelectionChange"
  >
    <el-table-column
      type="selection"
      :selectable="checkSelectable"
      width="50"
      align="center"
    />
    <el-table-column
      label="案件ID"
      align="center"
      prop="caseId"
      v-if="columnsMap[0].visible"
      width="80"
    >
      <template #default="{ row, $index }">
        <div
          style="display: flex; align-items: center; justify-content: center"
        >
          <el-tooltip v-if="row.labelContent" placement="top">
            <template #content>{{ row.labelContent }}</template>
            <case-label
              class="ml5"
              v-if="row.label && row.label != 7"
              :code="row.label"
            />
          </el-tooltip>
          <span
            style="color: #409eff; cursor: pointer"
            type="text"
            v-if="row.button == 1"
            @click="toDetails(row.caseId, $index)"
            >{{ row.caseId }}</span
          >
          <span v-if="row.button == 0">{{ row.caseId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      label="批次号"
      align="center"
      prop="batchNum"
      v-if="columnsMap[1].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="客户姓名"
      align="center"
      prop="clientName"
      v-if="columnsMap[2].visible"
    />
    <el-table-column
      label="委托金额"
      align="center"
      prop="entrustMoney"
      v-if="columnsMap[3].visible"
    />
    <el-table-column
      label="申请时间"
      width="120"
      align="center"
      prop="applyDate"
      v-if="columnsMap[4].visible"
      :show-overflow-tooltip="true"
    >
    </el-table-column>
    <el-table-column
      label="外访地址"
      align="center"
      prop="outsideAddress"
      v-if="columnsMap[5].visible"
    >
    </el-table-column>
    <el-table-column
      label="外访次数"
      width="120"
      align="center"
      prop="outsideNum"
      v-if="columnsMap[6].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="申请人"
      width="120"
      align="center"
      prop="applicant"
      v-if="columnsMap[7].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="外访人员"
      width="120"
      align="center"
      prop="outsidePerson"
      v-if="columnsMap[8].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="外访原因"
      width="120"
      align="center"
      prop="reason"
      v-if="columnsMap[9].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="外访时间"
      width="120"
      align="center"
      prop="outsideTime"
      v-if="columnsMap[10].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="外访详情"
      align="center"
      prop="outsideContent"
      v-if="activeTab == '5' "
    />
    <el-table-column
      label="审核状态"
      v-if="columnsMap[11].visible"
      align="center"
    >
      <template #default="{ row, $index }">
        <!-- <el-popover
          placement="bottom"
          :width="500"
          :ref="`popover-${$index}`"
          trigger="click"
        >
          <template #reference>
            <el-button @click="showPopover(row)" type="text">
              {{ nowAuditStatusEnum[row.examineState] }}
            </el-button>
          </template>
          <el-table :data="gridData">
            <el-table-column
              width="200"
              property="approveTime"
              label="处理时间"
            />
            <el-table-column
              width="100"
              property="reviewer"
              label="处理人"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              width="100"
              property="approveStart"
              :formatter="approveStartFor"
              label="处理状态"
            />
            <el-table-column
              width="100"
              property="refuseReason"
              :formatter="reasonFor"
              label="原因"
            />
          </el-table>
        </el-popover> -->
      {{ nowAuditStatusEnum[row.examineState] }}
      </template>
    </el-table-column>

    <el-table-column
      label="审核时间"
      width="120"
      align="center"
      prop="examineTime"
      v-if="columnsMap[12].visible"
    />
    <el-table-column label="操作" width="120" align="center">
      <template #default="scope">
        <el-button
          type="text"
          v-if="scope.row.examineState == 0"
          @click="revokeApply(scope.row)"
          >撤销</el-button
        >
        <el-button
          type="text"
           v-if="scope.row.stateCode == 3"
          @click="handleOutsideRegistration(scope.row)"
          >外访登记
        </el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />

  <el-dialog v-model="showRecordDialog" title="协催记录">
    <el-table :data="recordDateList">
      <el-table-column
        label="时间"
        width="180"
        align="center"
        key="createTime"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="协催状态"
        align="center"
        key="state"
        prop="state"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span>
            {{ stateInfo(scope.row.state) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="协催内容"
        align="center"
        key="assistContent"
        prop="assistContent"
      />
    </el-table>
  </el-dialog>
  <outsideRegistration  v-model:visible="registrationVisible" :row = registrationRow @success="getList" />
</template>

<script setup>
import { nowAuditStatusEnum } from "@/utils/enum";
import { assistRecord,  } from "@/api/collection/myapply.js";
import { getMyApplyList, revokeApplyApi } from "@/api/approval";
import outsideRegistration from '../dialog/outsideRegistration.vue'

const { proxy } = getCurrentInstance();
const router = useRouter();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    applyDate: [],
    updateTime: [],
  },
});
const rangfiles = ["applyDate", "updateTime"];
const examineStateTab = ref([
  {
    key: "0",
    value: "已提交，待审核",
  },
  {
    key: "3",
    value: "未通过",
  },
  {
    key: "2",
    value: "已通过，待外访",
  },
  {
    key: "5",
    value: "已完成",
  },

  {
    key: "all",
    value: "全部",
  },
]); //审核状态tab
const showSearch = ref(false);
const showRecordDialog = ref(false); //审批流程对话框
const recordDateList = ref([]); //记录数据
const activeTab = ref("0");
const { queryParams } = toRefs(data);
const registrationVisible = ref(false)
const registrationRow = reactive({})
//选中的id集合
const ids = ref([]);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

const single = ref(true); //是否可操作
// 列显隐信息 - 更新为与表格完全匹配的列
const columns = ref([
  { key: 0, label: `案件ID`, visible: true },
  { key: 1, label: `批次号`, visible: true },
  { key: 2, label: `客户姓名`, visible: true },
  { key: 3, label: `委托金额`, visible: true },
  { key: 4, label: `申请时间`, visible: true },
  { key: 5, label: `外访地址`, visible: true },
  { key: 6, label: `外访次数`, visible: true },
  { key: 7, label: `申请人`, visible: true },
  { key: 8, label: `外访人员`, visible: true },
  { key: 9, label: `外访原因`, visible: true },
  { key: 10, label: `外访时间`, visible: true },
  { key: 11, label: `审核状态`, visible: true },
  { key: 12, label: `审核时间`, visible: true },
]);

// 创建columnsMap计算属性，方便通过key访问列配置
const columnsMap = computed(() => {
  const map = {};
  columns.value.forEach((column) => {
    map[column.key] = column;
  });
  return map;
});

//获取列表数据
function getList() {
  queryParams.value.stateCode =
    activeTab.value == 'all' ? undefined : activeTab.value;
  if (activeTab.value == 2) {
    queryParams.value.stateCode = 3;
  }

  loading.value = true;
  let req = {
    approveCode: "outside",
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    approveState: activeTab.value == 'all' ? undefined : activeTab.value,
    approveData: {
      ...proxy.addFieldsRange(queryParams.value, rangfiles),
    },
  };
  if (req.approveState == 5) req.approveState = 2;
  getMyApplyList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

const revokeApply = (row) => {
  let req = {
    approveCode: "outside",
    approveIds: [row.approveId],
  };
  revokeApplyApi(req).then((res) => {
    proxy.$modal.msgSuccess("撤销成功");
    getList();
  });
};

//状态码对应的状态信息
function stateInfo(state) {
  const stateObj = { 0: "待协案", 1: "持续协案", 2: "完成协案", 3: "终止协案" };
  return stateObj[state] ? stateObj[state] : "";
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    applicant: undefined,
    entrustingCaseBatchNum: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    updateTime: [],
    applyDate: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({
    path: `/collection/mycase-detail/caseDetails/${caseId}`,
    query: { type: "myCase" },
  });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
}

//表格行能否选择
function checkSelectable() {
  return true;
}

//查看记录
function selectRecord(data) {
  let req = data.id;
  assistRecord(req).then((res) => {
    recordDateList.value = res.rows;
    showRecordDialog.value = true;
  });
}

function moneyFor(num) {
  return num ? proxy.setNumberToFixed(num) : "--";
}

// 外访登记按钮
function handleOutsideRegistration(row){
  registrationVisible.value = true
  Object.assign(registrationRow,row)
}
</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

// :deep(.el-table__header-wrapper .el-checkbox) {
//   display: none;
// }
</style>
