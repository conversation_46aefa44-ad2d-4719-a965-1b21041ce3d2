<template>
  <div>
    <div class="header" ref="headerRef">
      <el-button type="info" @click="router.go(-1)">返回上一步</el-button>
      <el-button type="warning" @click="ExportSavePdf(pdfName)">
        导出PDF文件
      </el-button>
    </div>
    <div class="app-container" id="pdfCentent">
      <div class="assets">
        <div class="assets-title mb20">1、资产回收情况</div>
        <el-table
          v-loading="loading"
          class="mt10"
          :data="brokerageData.assetsList"
        >
          <el-table-column
            label="转让方"
            align="center"
            key="transferorName"
            prop="transferorName"
          />
          <el-table-column
            label="资产批次号"
            align="center"
            key="batchNum"
            prop="batchNum"
          />
          <el-table-column
            label="案件id"
            align="center"
            key="caseId"
            prop="caseId"
          />
          <el-table-column
            label="回款金额"
            align="center"
            key="repaymentMoney"
            prop="repaymentMoney"
          />
          <el-table-column
            label="实际回款时间"
            align="center"
            key="repaymentDate"
            prop="repaymentDate"
          />
          <el-table-column
            label="服务费率（%）"
            align="center"
            key="serviceRate"
            prop="serviceRate"
          />
          <el-table-column
            label="佣金"
            align="center"
            key="brokerage"
            prop="brokerage"
          />
        </el-table>
      </div>
      <div class="risk">
        <div class="title">2、机构风险奖罚</div>
        <el-table
          v-loading="loading"
          class="mt10"
          ref="multipleTableRef"
          :data="brokerageData.riskList"
        >
          <el-table-column
            label="奖罚名称"
            align="center"
            key="rewardName"
            prop="rewardName"
          ></el-table-column>
          <el-table-column
            label="属性"
            align="center"
            key="positiveNegative"
            prop="positiveNegative"
          >
            <template #default="{ row }">
              <span>
                {{ row.positiveNegative == 1 ? "+" : "-"
                }}{{ row.floatingRate }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            key="remarks"
            prop="remarks"
          >
          <template #default="{row}">
            <Tooltip :content="row.remarks" :length="50" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="total">
        <div class="title">3、总计</div>
        <div class="total-table">
          <div>
            <div>回收合计</div>
            <div>风险奖罚合计</div>
            <div>总计</div>
          </div>
          <div>
            <div>{{ brokerageData.totalRecycling }}元</div>
            <div>{{ brokerageData.totalRisk }}元</div>
            <div>{{ brokerageData.total }}元</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  getRecoveryAssetById,
  getRiskTeamById,
  getStatisticsMoneyById,
} from "@/api/case/brokerage/brokerage";
import { getCurrentInstance } from "vue";
import { ExportSavePdf } from "@/utils/htmlToPdf.js";
import { useRoute, useRouter } from "vue-router";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const brokerageData = reactive({
  assetsList: [], // 资产回收列表
  riskList: [], // 机构风险列表
  totalRecycling: undefined, // 回收合计
  totalRisk: undefined, // 风险奖罚合计
  total: undefined, // 总计
});
const pdfName = ref(undefined);
onMounted(() => {
  window.addEventListener("scroll", roll);
});
onUnmounted(() => {
  window.removeEventListener("scroll", roll);
});

// 获取列表
function getList() {
  const { designation, detailsId, total } = route.query;
  pdfName.value = designation;
  getRecoveryAssetById({ detailsId, pageNum: 1, pageSize: total }).then(
    (res) => (brokerageData.assetsList = res.rows)
  );
  getRiskTeamById({ detailsId }).then((res) => {
    brokerageData.riskList = res.rows;
  });
  getStatisticsMoneyById({ detailsId }).then((res) => {
    brokerageData.totalRecycling = res.totalRecycling;
    brokerageData.totalRisk = res.totalRisk;
    brokerageData.total = res.total;
  });
}
getList();

// 滚动事件
function roll() {
  if (document.documentElement.scrollTop >= 60) {
    proxy.$refs["headerRef"].classList.add("fixed-shadow");
  } else {
    proxy.$refs["headerRef"].classList.remove("fixed-shadow");
  }
}

</script>
<style lang="scss" scoped>
.header {
  padding: 20px;
}
.fixed-shadow {
  position: fixed;
  width: 100%;
  top: 0;
  left: 200px;
  z-index: 999;
  background-color: #fff;
  box-shadow: 1px 3px 3px 0px #f0ebeb94;
}
.total-table {
  display: flex;
  border: 1px solid #ccc;

  & > div {
    flex: 1;
    text-align: center;
    line-height: 60px;
    :first-child {
      border-right: none;
    }
    :nth-child(2) {
      border-top: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
    }
  }
  :first-child {
    border-right: 1px solid #ccc;
  }
}
.assets-title {
  font-size: 18px;
  margin-bottom: 30px;
}
.title {
  font-size: 18px;
  margin: 45px 0 30px;
}
</style>