<template>
    <div style="position: relative;">
        <el-table :data="dataList">
            <el-table-column label="档案名称" prop="firstName" key="firstName" align="center" />
            <el-table-column label="上传用户" prop="founder" key="founder" align="center" />
            <el-table-column label="上传时间" prop="creationTime" key="creationTime" align="center" />
            <el-table-column label="操作" align="center">
                <template #default="{ row }">
                    <div>
                        <el-button type="text" link @click="handlePreview(row)">
                            预览
                        </el-button>
                        <el-button type="text" link @click="toDownLoad(row)">
                            下载
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 10" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        <el-dialog title="预览" v-loading="loading" v-model="open" width="70vw" :before-close="cancel">
            <iframe class="iframe" :src="iframeSrc" />
        </el-dialog>
    </div>
</template>
<script setup>
// import { getArchivalList, getFileOnlinePreviewHost } from "@/api/details/details";
import Base64 from "@/utils/base64.js";
import { getArchivalList, getFileOnlinePreviewHost } from "@/api/caseDetail/detail";
const route = useRoute()
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
const dataList = ref([])
const total = ref(0)
const iframeSrc = ref(undefined);
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        caseId: route.params.caseId,
    }
})
const { queryParams } = toRefs(data)
//获取资产
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    getArchivalList(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}

function isWordFile(filePath) {
    // 获取文件扩展名（不区分大小写）
    const fileExtension = filePath.split('.').pop().toLowerCase();
    // 判断扩展名是否为 Word 文件的常见扩展名
    return ['doc', 'docx', 'dot', 'dotx'].includes(fileExtension);
}

// 预览
function handlePreview(row) {
    getFileOnlinePreviewHost().then((res) => {
        iframeSrc.value = `${res.data}onlinePreview?url=${encodeURIComponent(Base64.encode(row.fileUrl))}`;
        open.value = true;
    });
    // iframeSrc.value = row.fileUrl;
    // open.value = true;
}

//下载文件
function toDownLoad(row) {
    let req = { id: row.id, };
    proxy.download("/management/retrieval/getArchivalFile", req, row.firstName);
}
// 关闭弹窗
function cancel() {
    open.value = false;
}

// 开启多个案件详情切换的时候更新列表
watch(route, () => {
    nextTick(() => {
        if (route.params?.caseId) {
            queryParams.value.caseId = route.params.caseId
            getList();
        }
    })
})
</script>
<style lang="scss" scoped>
.iframe {
    min-height: 65vh;
    width: 100%;
}
</style>