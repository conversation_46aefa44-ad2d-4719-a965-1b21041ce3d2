<template>
  <div class="app-container">
    <el-form :model="queryParams" :inline="true">
      <el-form-item label="类型:">
        <el-input placeholder="请输入类型" style="width: 240px"></el-input>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          value-format="YYYY-MM-DD"
          type="daterange"
          style="width: 240px"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-from-item>
        <el-button>重置</el-button>
        <el-button type="primary">搜索</el-button>
      </el-from-item>
    </el-form>

    <el-table class="mt20" v-loading="loading" ref="tableRef" :data="tableList">
      <el-table-column label="任务名称" align="center" />
      <el-table-column label="类型" align="center" />
      <el-table-column label="数量" align="center" />
      <el-table-column label="创建时间" align="center" />
      <el-table-column label="状态" align="center" />
      <el-table-column label="操作" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Management">
const total = ref(0);

const loading = ref(false);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const tableList = ref([]);

const getList = () => {};
</script>

<style scoped></style>
