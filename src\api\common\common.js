import request from '@/utils/request'
import requests from '@/utils/https'

//下拉
//获取委案批次号
export function getBatchNums() {
    return request({
        url: '/dropDown/selectCaseManage',
        method: 'get'
    })
}


//案件状态
export function getCaseState() {
    return request({
        url: '/caseManage/case/start/getCaseState',
        method: 'get'
    })
}

//跟进状态
export function getFollowUpState() {
    return request({
        url: '/dropDown/selectDictData',
        method: 'get'
    })
}

//催收状态
export function getUrgeState() {
    return request({
        url: '/dropDown/selectDictData',
        method: 'get'
    })
}

//户籍地下拉
export function getCensusRegisters() {
    return request({
        url: '/dropDown/selectRegisteredResidence',
        method: 'get'
    })
}

//获取地区省份
export function getProvinces() {
    return request({
        url: '/dropDown/getProvinces',
        method: 'get'
    })
}

//案件标签=
export function selectLabel() {
    return request({
        url: '/dropDown/selectLabel',
        method: 'get'
    })
}

// 转让方下拉
export function selectAssetOwner() {
    return request({
        url: '/dropDown/selectAssetOwner',
        method: 'get'
    })
}
// 批次号
export function selectListWithBatchNum() {
    return request({
        url: '/dropDown/selectListWithBatchNum',
        method: 'get'
    })
}

export function selectListWithBatchNum2() {
    return request({
        url: '/dropDown/selectListWithBatchNum2',
        method: 'get'
    })
}

//协催下拉
export function getUrged() {
    return request({
        url: 'collection/selectAssistRecordType',
        method: 'get'
    })
}

//工单类型下拉
export function getOrderType() {
    return request({
        url: 'collection/selectWorkOrderType',
        method: 'get'
    })
}

//工单状态下拉
export function getOrderStatus() {
    return request({
        url: 'dropDown/workOrderStatus',
        method: 'get'
    })
}

//处置人员下拉
export function selectEmployees() {
    return request({
        url: 'dropDown/selectEmployees',
        method: 'get'
    })
}

//处置人员案件全查
export function selectCaseManageMoney() {
    return request({
        url: 'collection/selectCaseManageMoney',
        method: 'get'
    })
}

//设置水印
export function getWatermark(query) {
    return request({
        url: '/settings/watermark',
        method: 'get',
        params: query
    })
}

//获取最近的5条消息
export function getNavMessage() {
    return request({
        url: '/team/getNavMessage',
        method: 'get',
        gateway: 'message'
    })
}

//设置水印
export function exportSwtich(query) {
    return request({
        url: '/settings/exportSwtich',
        method: 'get',
        params: query
    })
}

//获取资产包
export function getPackageList(query) {
    return request({
        url: '/collector/selectProperty',
        method: 'get',
        params: query
    })
}

// 模板类型
export function getTemplateType(dictId) {
    return request({
        url: '/system/dict/data/type/' + dictId,
        method: 'get'
    })
}
// 省/市下拉查询
export function municipalGovernment(query) {
    return request({
        url: '/court/manage/getCityOptions',
        method: 'get',
        params: query
    })
}

//获取websocket地址
export function getWebSocketAddress() {
    return request({
        url: '/cache/getWebSocketAddress',
        method: 'get',
    })
}
//更新websocket地址
export function updateWebSocketAddress(data) {
    return request({
        url: '/legal/saveWebSocketAddress',
        method: 'post',
        data
    })
}
//获取百度地图密钥
export function getBaiduMapKey() {
    return request({
        url: '/cache/getBaiduMapKey',
        method: 'get',
    })
}
//更新百度地图密钥
export function updateBaiduMapKey(data) {
    return request({
        url: '/legal/saveBaiduMapKey',
        method: 'post',
        params: data
    })
}

// 查询法院下拉
export function getCourtOptions(query) {
    return request({
        url: '/court/manage/getCourtNameOptions',
        method: 'get',
        params: query
    })
}
// 获取团队成员下拉框
export function getEmplyOption(query) {
    return request({
        url: '/settings/user/getOption',
        method: 'get',
        params: query
    })
}
// 判决状态下拉框
export function getCaseStatusOption(query) {
    return request({
        url: '/judge/getCaseStatus',
        method: 'get',
        params: query
    })
}
// 获取判决文书下拉框
export function getJudgeLetterOption(query) {
    return request({
        url: '/judge/getJudgeLetter',
        method: 'get',
        params: query
    })
}
// 获取判决结果下拉框
export function getJudgeResultOption(query) {
    return request({
        url: '/judge/getJudgeResult',
        method: 'get',
        params: query
    })
}
// 客户满意度下拉框
export function getCustSatisfyOption(query) {
    return request({
        url: '/judge/getCustSatisfy',
        method: 'get',
        params: query
    })
}

// 调解阶段
export function getMediateStageTree(query) {
    return request({
        url: '/letter/item/getTreeNode',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}

// 获取审批数据列表
export function jgGetApprovalType() {
    return request({
        url: '/zws_jg_approve/jgGetApprovalType',
        method: 'get',
    })
}

// 获取机构端审批设置
export function jgGetApprovalSettings(data) {
    return request({
        url: '/zws_jg_approve/jgGetApprovalSettings',
        method: 'post',
        data: data
    })
}

// 保存机构端审批设置
export function jgSaveApproveSetUp(data) {
    return request({
        url: '/zws_jg_approve/jgSaveApproveSetUp',
        method: 'post',
        data: data
    })
}
