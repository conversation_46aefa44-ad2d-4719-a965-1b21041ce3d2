<template>
  <div class="fxn-body">
    <div class="fxn-section">
      <div class="fxn-operate">
        <el-button @click="toBack">返回列表</el-button>
      </div>
      <!-- operate end -->

      <el-table
        v-loading="loading"
        :data="lawRecordData"
        :header-cell-style="{
          background: '#EEEFF4',
          color: '#888888',
          width: '100%',
        }"
      >
        <el-table-column prop="add_time" label="时间" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.add_time, 1) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="诉讼状态"
          align="center"
          :formatter="suitStatusChange"
        ></el-table-column>
        <el-table-column
          prop="mark"
          label="说明"
          align="center"
        ></el-table-column>
        <el-table-column label="附件" align="center">
          <template #default="scope">
            <div class="fxn-detail-img-pdf">
              <span v-for="(item, index) in scope.row.imgurl" :key="index">
                <el-button
                  v-if="getCaption(item) == 'pdf'"
                  type="text"
                  size="small"
                  @click="clickImg(item)"
                  >下载</el-button
                >
                <img
                  v-else
                  :src="item"
                  alt=""
                  width="20px"
                  height="20px"
                  @click="clickImg(item)"
                />
              </span>
              <el-button
                v-if="objectLen(scope.row.imgurl)"
                type="text"
                size="small"
                @click="bulkDownload(scope.row.id)"
                >批量下载</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div
      v-if="
        LawInfo.lowerPart == 'show' &&
        LawInfo.cancelStatus != 1 &&
        LawInfo.caseSuitStatus != 12 &&
        LawInfo.caseSuitStatus != 13 &&
        LawInfo.caseSuitStatus != 14
      "
      class="wcc-law-update fxn-margin-t-20"
    >
      <el-form
        :model="addLawStatus"
        :rules="addLawStatusRules"
        ref="addLawStatusRef"
      >
        <el-form-item label="状态" :label-width="formLabelWidth" prop="status">
          <el-select v-model="addLawStatus.status" placeholder="">
            <el-option
              v-for="item in nextSuitStatus"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明" :label-width="formLabelWidth" prop="explain">
          <el-input
            type="textarea"
            v-model="addLawStatus.explain"
            rows="5"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="附件"
          :label-width="formLabelWidth"
          prop="attach_file"
        >
          <el-upload
            ref="uploadRef"
            accept="image/png,image/jpg,.pdf"
            :data="{ token }"
            :limit="5"
            :action="doUpload"
            :on-change="fileChange"
            :before-upload="beforeAvatarUpload"
            :on-success="uploadSuccess"
            :on-remove="uploadRemove"
            :on-exceed="fileExceed"
            :file-list="fileList"
            :auto-upload="false"
          >
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button
              style="margin-left: 10px"
              size="small"
              type="success"
              @click="submitUpload"
              >上传到服务器</el-button
            >
            <span class="fxn-remark"
              >附件数量最多不超过五个，支持jpg、png、pdf</span
            >
          </el-upload>
        </el-form-item>
      </el-form>
      <el-button class="wcc-margin-l-50" type="primary" @click="addSuitStatus"
        >更新进度</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { getLawProgressListApi } from "@/api/selfLawList";
import { ElMessage } from "element-plus";

// 声明emit
const emit = defineEmits(["func"]);

// 过滤器替代函数
const formatDateTime = (time, type) => {
  if (!time) return "";
  const date = new Date(time * 1000);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  if (type === 1) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else {
    return `${year}-${month}-${day}`;
  }
};

// 全局变量转为局部
let fileData = "";
let fileArray = [];

// 响应式状态
const loading = ref(false);
const LawInfo = reactive({});
const formLabelWidth = ref("50px");
const lawRecordData = ref([]);
const uploadRef = ref(null);
const addLawStatusRef = ref(null);
const hasFile = ref(0);

const addLawStatus = reactive({
  caseid: "",
  status: "",
  explain: "",
});

const addLawStatusRules = reactive({
  status: [{ required: true, message: "请选择状态", trigger: "blur" }],
});

// 诉讼状态
const lawStatus = reactive([
  { id: 0, title: "全部" },
  { id: 1, title: "发起诉讼" },
  { id: 2, title: "网约立案" },
  { id: 3, title: "诉前调解" },
  { id: 4, title: "正式立案" },
  { id: 5, title: "受理通知" },
  { id: 6, title: "开庭传票" },
  { id: 7, title: "公告开庭" },
  { id: 8, title: "正常开庭" },
  { id: 9, title: "判决生效" },
  { id: 10, title: "申请执行" },
  { id: 11, title: "调解成功" },
  { id: 12, title: "录入失信人名单" },
  { id: 13, title: "结案" },
  { id: 14, title: "二审应诉" },
]);

// 添加诉讼状态
const nextSuitStatus = ref([]);
const fileList = ref([]);
const token = ref(JSON.parse(localStorage.getItem("token") || '""'));
const TextUrl = ref(""); // 假设TextUrl定义在全局配置或通过API导入
const doUpload = ref(`${TextUrl.value}/osapi/File/upload`);

// 返回
const toBack = () => {
  emit("func", false);
};

// 诉讼记录列表
const getSuitRecordList = (caseid) => {
  loading.value = true;
  getLawProgressListApi({ case_id: caseid }).then((res) => {
    lawRecordData.value = res.rows;
    loading.value = false;
  });
};

// 诉讼状态转换
const suitStatusChange = (row, col) => {
  if (row.status == 1) {
    return "发起诉讼";
  } else if (row.status == 2) {
    return "网约立案";
  } else if (row.status == 3) {
    return "诉前调解";
  } else if (row.status == 4) {
    return "正式立案";
  } else if (row.status == 5) {
    return "受理通知";
  } else if (row.status == 6) {
    return "开庭传票";
  } else if (row.status == 7) {
    return "公告开庭";
  } else if (row.status == 8) {
    return "正常开庭";
  } else if (row.status == 9) {
    return "判决生效";
  } else if (row.status == 10) {
    return "申请执行";
  } else if (row.status == 11) {
    return "调解成功";
  } else if (row.status == 12) {
    return "录入失信人名单";
  } else if (row.status == 13) {
    return "结案";
  } else if (row.status == 14) {
    return "二审应诉";
  }
};

// 获取诉讼状态选择
const suitStatusChoseing = (caseid) => {
  fetch("/osapi/Lawsuit/selsuitstatus", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ case_id: caseid }),
  })
    .then((response) => response.json())
    .then((res) => {
      let { code, msg, data } = res;
      if (code != 1) {
        ElMessage({
          type: "error",
          message: msg,
        });
      } else {
        let arr = data.split(",");
        nextSuitStatus.value = [];
        let len = arr.length;
        for (let j = 0; j < lawStatus.length; j++) {
          for (let i = 0; i < len; i++) {
            if (lawStatus[j].id == arr[i]) {
              nextSuitStatus.value.push(lawStatus[j]);
            }
          }
        }
        if (nextSuitStatus.value.length == 1) {
          addLawStatus.status = nextSuitStatus.value[0].id;
        } else {
          addLawStatus.status = "";
        }
      }
    });
};

// 文件添加
const fileChange = (file, fileList) => {
  fileList.value = fileList;
  for (let i = 0, l = fileList.length; i < l; i++) {
    for (let j = i + 1; j < l; j++) {
      if (fileList[i].name === fileList[j].name) {
        fileList.splice(j, 1);
        j = ++i;
        ElMessage({
          type: "error",
          message: "文件已在上传列表中！",
        });
      }
    }
  }
  hasFile.value = 1;
};

// 删除上传文件
const uploadRemove = (file, fileListParam) => {
  for (let i = 0; i < fileListParam.length; i++) {
    let obj = fileListParam[i];
    if (file.uid == obj.uid) {
      fileListParam.splice(i, 1);
      i--;
    }
  }
  fileList.value = fileListParam;
  if (fileListParam.length == 0) {
    hasFile.value = 0;
  } else {
    hasFile.value = 1;
  }
};

// 文件数量验证
const fileExceed = () => {
  ElMessage({
    type: "error",
    message: "上传文件数量不能超过五个！",
  });
};

// 附件上传前的验证
const beforeAvatarUpload = (file) => {
  const extension = file.name.split(".")[1] === "jpg";
  const extension1 = file.name.split(".")[1] === "pdf";
  const extension2 = file.name.split(".")[1] === "png";
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!extension && !extension1 && !extension2) {
    ElMessage({
      message: "上传文件只能是 jpg、png、pdf格式!",
      type: "error",
    });
  }

  if (!isLt10M) {
    ElMessage({
      message: "上传文件大小不能超过 10MB!",
      type: "error",
    });
  }

  return (extension || extension1 || extension2) && isLt10M;
};

// 上传成功
const uploadSuccess = (file) => {
  fileData = file;
  if (fileData.code == 1) {
    fileArray.push(fileData.data);
  }
  ElMessage({
    message: "文件上传成功!",
    type: "success",
  });
};

// 上传文件
const submitUpload = () => {
  uploadRef.value.submit();
};

// 添加诉讼状态
const addSuitStatus = () => {
  if (fileList.value.length != 0) {
    fileArray = [];
    for (let i in fileList.value) {
      if (
        (fileList.value[i].response == undefined ||
          fileList.value[i].response == "") &&
        hasFile.value == 1
      ) {
        ElMessage({
          type: "error",
          message: "请先上传文件您选择的文件！",
        });
        return false;
      } else {
        fileArray.push(fileList.value[i].response.data);
      }
    }
  } else {
    fileArray = [];
  }

  let repay_voucher = fileArray.join(",");

  addLawStatusRef.value.validate(async (valid) => {
    if (valid) {
      fetch("/osapi/Lawsuit/addsuitstatus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          case_id: LawInfo.caseid,
          status: addLawStatus.status,
          mark: addLawStatus.explain,
          suitVouch: repay_voucher,
        }),
      })
        .then((response) => response.json())
        .then((res) => {
          let { code, msg } = res;
          if (code != 1) {
            ElMessage({
              type: "error",
              message: msg,
            });
          } else {
            ElMessage({
              type: "success",
              message: "添加成功！",
            });
            getSuitRecordList(LawInfo.caseid);
            suitStatusChoseing(LawInfo.caseid);
            uploadRef.value.clearFiles();
            fileList.value = [];
            addLawStatus.explain = "";
          }
        });
    } else {
      console.log("submit error");
    }
  });
};

// 获取指定字符后面的内容
const getCaption = (url) => {
  var index = url.lastIndexOf(".");
  url = url.substring(index + 1, url.length);
  return url;
};

// 诉讼记录附件点击
const clickImg = (item) => {
  window.open(item);
};

// 批量下载是否显示
const objectLen = (url) => {
  return url != "" && url != undefined ? url.length > 1 : false;
};

// 批量下载
const bulkDownload = (id) => {
  Proxy.downloadforjsonGet("/lawsuitStatus/download/" + id);
  //   window.open(
  //     TextUrl.value +
  //       "/lawsuitStatus/download" +
  //       id +
  //       "&token=" +
  //       JSON.parse(localStorage.getItem("token"))
  //   );
};

// 生命周期钩子
onMounted(() => {
  Object.assign(LawInfo, JSON.parse(sessionStorage.getItem("LawInfo") || "{}"));
  getSuitRecordList(LawInfo.caseid);
  suitStatusChoseing(LawInfo.caseid);
});

onBeforeUnmount(() => {
  sessionStorage.removeItem("LawInfo");
});
</script>

<style scoped>
.fxn-operate {
  padding-bottom: 1rem;
}
.wcc-law-update {
  margin: 0px auto;
  padding: 1rem;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(171, 186, 223, 0.2);
  border-radius: 0px 0px 2px 2px;
}
.wcc-margin-l-50 {
  margin-left: 50px;
}
</style>
