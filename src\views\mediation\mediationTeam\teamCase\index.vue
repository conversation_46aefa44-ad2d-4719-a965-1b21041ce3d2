<template>
  <div class="app-container">
    <el-radio-group class="mb20" v-model="activeTab">
      <el-radio-button
        v-hasPermi="['mediationTeam:appealCase:index']"
        label="0"
        name="0"
        >调解案件</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediationTeam:filingCaseInNet:index']"
        label="1"
        name="1"
        >网上立案</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediationTeam:filingCaseOpenCourt:index']"
        label="2"
        name="2"
        >立案开庭</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediationTeam:sentenceAndResult:index']"
        label="3"
        name="3"
        >判决与结果</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediationTeam:lawsuitExecution:index']"
        label="4"
        name="4"
        >诉讼执行</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediationTeam:lawsuitPreservation:index']"
        label="5"
        name="5"
        >诉讼保全</el-radio-button
      >
      <!-- <el-radio-button label="4" name="4">诉讼执行</el-radio-button>
      <el-radio-button label="5" name="5">诉讼保全</el-radio-button> -->
    </el-radio-group>
    <appealCase v-if="activeTab == '0'" />
    <filingCaseInNet v-if="activeTab == '1'" />
    <filingCaseOpenCourt v-if="activeTab == '2'" />
    <sentenceAndResult v-if="activeTab == '3'" />
    <lawsuitExecution v-if="activeTab == '4'" />
    <lawsuitPreservationVue v-if="activeTab == '5'" />
  </div>
</template>

<script setup name="TeamCase">
import appealCase from "./appealCase/index.vue";
import filingCaseInNet from "./filingCaseInNet/index.vue";
import filingCaseOpenCourt from "./filingCaseOpenCourt/index.vue";
import sentenceAndResult from "./sentenceAndResult/index.vue";
import lawsuitExecution from "./lawsuitExecution/index.vue";
import lawsuitPreservationVue from "./lawsuitPreservation/index.vue";
import { ref } from "vue";

const activeTab = ref("0");
</script>

<style scoped></style>
