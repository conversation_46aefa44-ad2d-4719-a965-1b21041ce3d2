import request from '@/utils/request'
//获取机构树结构
export function getDeptTreeWithDisposeStage(params) {
  return request({
    url: '/team/DeptTreeWithDisposeStage',
    method: 'get',
    params
  })
}

// 根据条件查询列表信息
export function getJudgeList(query) {
  return request({
    url: '/team/getJudgeList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectJudgeWithMoney(data) {
  return request({
    url: '/team/selectJudgeWithMoney',
    method: 'post',
    data: data,
  })
}
