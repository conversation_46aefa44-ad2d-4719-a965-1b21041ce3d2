<template>
  <div class="app-container">
    <div class="apply" v-if="radioType != undefined">
      <el-radio-group v-model="radioType" @change="radioTypeChange">
        <el-radio-button label="0" v-hasPermi="['case:aduit:replay']">
          回款审批
        </el-radio-button>
        <el-radio-button label="1" v-hasPermi="['case:aduit:reduct']">
          减免审批
        </el-radio-button>
        <el-radio-button label="2" v-hasPermi="['case:aduit:stage']">
          分期审批
        </el-radio-button>
        <el-radio-button label="3" v-hasPermi="['case:aduit:stay']">
          留案审批
        </el-radio-button>
        <el-radio-button label="4" v-hasPermi="['case:aduit:stop']">
          停案审批
        </el-radio-button>
        <el-radio-button label="5" v-hasPermi="['case:aduit:back']">
          退案审批
        </el-radio-button>
        <el-radio-button label="6" v-hasPermi="['case:aduit:outsize']">
          外访审批
        </el-radio-button>
        <el-radio-button label="9" v-hasPermi="['case:aduit:assist']">
          协案审批
        </el-radio-button>
        <el-radio-button label="10" v-hasPermi="['case:aduit:lawsuit']">
          诉讼审批
        </el-radio-button>
        <!-- <el-radio-button label="7" v-hasPermi="['case:aduit:data']">
          资料调取
        </el-radio-button> -->
        <!-- <el-radio-button label="8" v-hasPermi="['case:aduit:signature']">
          签章审批
        </el-radio-button> -->
      </el-radio-group>
      <replay v-if="radioType == 0 && checkPermi(['case:aduit:replay'])" />
      <reduct v-if="radioType == 1 && checkPermi(['case:aduit:reduct'])" />
      <stage v-if="radioType == 2 && checkPermi(['case:aduit:stage'])" />
      <stay v-if="radioType == 3 && checkPermi(['case:aduit:stay'])" />
      <stopVue v-if="radioType == 4 && checkPermi(['case:aduit:stop'])" />
      <back v-if="radioType == 5 && checkPermi(['case:aduit:back'])" />
      <outsize v-if="radioType == 6 && checkPermi(['case:aduit:outsize'])" />
      <dataVue v-if="radioType == 7 && checkPermi(['case:aduit:data'])" />
      <signurateVue
        v-if="radioType == 8 && checkPermi(['case:aduit:signature'])"
      />
      <assist v-if="radioType == 9 && checkPermi(['case:aduit:assist'])" />
      <lawsuit v-if="radioType == 10 && checkPermi(['case:aduit:lawsuit'])" />
    </div>
  </div>
</template>
<script setup name="Aduit">
import { checkPermi } from "@/utils/permission";
import replay from "./tabs/replay";
import reduct from "./tabs/reduct";
import stage from "./tabs/stage";
import stay from "./tabs/stay";
import stopVue from "./tabs/stop";
import back from "./tabs/back";
import outsize from "./tabs/outsize";
import dataVue from "./tabs/data";
import signurateVue from "./tabs/signurate";
import assist from "./tabs/assist";
import lawsuit from "./tabs/lawsuit";

const radioType = ref("0");

checkPermiRadio();

// 检测权限页面
function checkPermiRadio() {
  if (checkPermi(["case:aduit:replay"])) {
    radioType.value = "0";
  } else if (checkPermi(["case:aduit:reduct"])) {
    radioType.value = "1";
  } else if (checkPermi(["case:aduit:stage"])) {
    radioType.value = "2";
  } else if (checkPermi(["case:aduit:stay"])) {
    radioType.value = "3";
  } else if (checkPermi(["case:aduit:stop"])) {
    radioType.value = "4";
  } else if (checkPermi(["case:aduit:back"])) {
    radioType.value = "5";
  } else if (checkPermi(["case:aduit:outsize"])) {
    radioType.value = "6";
  } else if (checkPermi(["case:aduit:data"])) {
    radioType.value = "7";
  } else if (checkPermi(["case:aduit:signurate"])) {
    radioType.value = "8";
  } else {
    radioType.value = undefined;
  }
}
</script>
<style lang="scss" scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

.top-right-btn {
  z-index: 1;
}
</style>
