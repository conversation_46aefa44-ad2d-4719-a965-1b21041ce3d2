<template>
  <div class="flex">
    <div
      class="label-icon"
      :style="{
        color: color[code],
        fontSize: size + 'px',
        width: size + 'px',
        height: size + 'px',
      }"
    >
      <el-icon><flag /></el-icon>
    </div>
    <span v-if="text != ''" class="ml5" :style="{ color: color[code] }">{{ text }}</span>
  </div>
</template>

<script setup>
const props = defineProps({
  code: {
    //颜色
    required: true,
    type: [Number, String],
  },
  size: {
    //大小
    type: Number,
    default: 14,
  },
  text: {
    //文字
    type: String,
    default: "",
  },
});
const color = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.label-icon {
  display: inline-flex;
  align-items: center;
  span {
    display: block;
    width: auto;
  }
}
</style>
