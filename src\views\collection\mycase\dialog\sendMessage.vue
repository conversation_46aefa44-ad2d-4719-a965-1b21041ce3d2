<template>
  <el-dialog title="发送短信" v-model="open" width="700px" :before-close="cancel">
    <div class="wcc-el-steps">
      <el-steps v-if="form.isBatchSend == 1" :active="stepActive" align-center>
        <el-step title="选择模板"></el-step>
        <el-step title="校验结果"></el-step>
      </el-steps>
    </div>
    <div v-show="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="180px">
        <el-form-item label="短信模板" prop="templateId">
          <el-select v-model="form.templateId" placeholder="请输入短信模板" clearable :loading="selectLoading"
            @change="selectNotetemplate" @focus="getMessageTypeList" filterable :reserve-keyword="false"
            style="width: 380px">
            <el-option v-for="item in messageTypeList" :key="item.id" :label="item.templateContent" :value="item.id">
              <Tooltip :content="item.templateContent" :length="30" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预览模板" name="templateContent" v-loading="itemLoading">
          <span style="width: 380px">{{ form.templateContent || "--" }}</span>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="stepActive === 1" class="step-item step-item-info pt20">
      <div class="mt10 mb20">
        <el-row>
          <el-col :span="12">
            <span class="mt10 mb10">提交成功条数：<span class="text-danger">{{
              successObj.sendNum
            }}</span></span>
          </el-col>
          <el-col :span="12">
            <span class="mt10 mb10">提交失败条数：{{ successObj.unSendNum || 0 }}</span>
          </el-col>
        </el-row>
        <el-row :span="24">
          <span class="mt10">失败原因：</span>
        </el-row>
        <el-row :span="24">
          <span v-if="successObj.sendNum2 !== 0">存在{{
            successObj.sendNum2
          }}个重复发送案件，24小时内1个手机号码1个模版只能发送一条短信</span>
          <span v-if="successObj.sendNum3 !== 0">存在{{
            successObj.sendNum3
          }}个共债案件，只选择了共债的第一个案件手机号码发送</span>
          <span v-if="successObj.sendNum1 !== 0">存在{{ successObj.sendNum1 }}个已结清案件，已结清案件不能发送短信</span>
        </el-row>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-show="stepActive === 1" @click="beforeStep">上一步</el-button>
        <el-button :loading="loading" @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" :disabled="form.isBatchSend == 1 && successObj.sendNum == 0"
          v-if="form.isBatchSend == 0 || (stepActive == 1 && form.isBatchSend == 1)" @click="submit">提交</el-button>
        <el-button type="primary" v-if="form.isBatchSend == 1 && stepActive == 0" :loading="subLoading"
          @click="nextStep">下一步</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SendMessage">
import { selectAllSmsSignature, previewTemplate, sendCuiShouMessage } from "@/api/collection/mycase";
import { previewTemplatePhoneLink, sendMessagesPhoneLink, selectAllSmsTemplate, sendAppealMessageSingle } from "@/api/mediation/phoneMediation";
//全局配置
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList", "openResult"]);
const stepActive = ref(0);
//枚举数组
const messageTypeList = ref([]);
const smsTypeList = ref([
  { code: 0, info: "轻催" },
  { code: 1, info: "重催" },
]);
const autographList = ref([]);
//表单属性
const open = ref(false);
const subLoading = ref(false);
const loading = ref(false);
//表单参数
const data = reactive({
  form: {
    templateId: undefined,
    templateContent: undefined,
    templateId: undefined,
    isBatchSend: undefined,
    manageQueryParam: undefined,
  },
  rules: {
    templateId: [{ required: true, message: "请选择短信模板！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
//成功数量
const successObj = ref({
  sendNum: 0,
  caseIds: undefined,
});

// 接口类型
const reqApiType = ref('case')

// 选择短信模板
function selectNotetemplate(templateId) {
  if (templateId) {
    const notetemplate = messageTypeList.value.find(item => item.id == templateId)
    form.value.templateContent = notetemplate.templateContent
  } else {
    form.value.templateContent = undefined
  }
}

//开启
function openDialog(data) {
  open.value = true;
  loading.value = false
  subLoading.value = false
  form.value = { ...data.query, ...data }
  form.value.isBatchSend = data.isBatchSend;
  form.value.listOrDetails = data.isBatchSend;
  form.value.sign = data.query.sign;
  reqApiType.value = data.pageType
  getMessageTypeList()
}

//重置表单
function reset() {
  stepActive.value = 0
  proxy.resetForm("formRef");
  form.value = {
    templateId: undefined,
    templateContent: undefined,
    isBatchSend: undefined,
    manageQueryParam: undefined,
  };
  successObj.value = {
    sendNum: 0,
    caseIds: undefined,
  }
}
// 上一步
function beforeStep() {
  stepActive.value--
}

//取消
function cancel() {
  reset();
  loading.value = false
  subLoading.value = false
  open.value = false;
}

//获取短信签名
function getAutographList() {
  selectAllSmsSignature().then((res) => {
    autographList.value = res.data.data.autographs;
  });
}

//获取短信模板
function getMessageTypeList() {
  selectAllSmsTemplate().then((res) => {
    messageTypeList.value = res.data.templateSms;
  });
}

//下一步
function nextStep() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      let req = JSON.parse(JSON.stringify(form.value));
      if (req.query && req.query.caseId) {
        req.query.caseId = +req.query.caseId
      } else {
        delete req.caseId
      }
      subLoading.value = true;
      previewTemplatePhoneLink({ ...req, ...req.query }).then((res) => {
        successObj.value = res.data;
        if (form.value.isBatchSend == 1) {
          stepActive.value++;
        }
      }).finally(() => subLoading.value = false);
    }
  })
}

//提交
function submit() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      loading.value = true;
      const reqForm = JSON.parse(JSON.stringify(form.value))
      if (reqForm.query && reqForm.query.caseId) {
        reqForm.query.caseId = +reqForm.query.caseId
      } else {
        delete reqForm.caseId
      }
      const reqApi = reqForm.contactId ? sendAppealMessageSingle : sendMessagesPhoneLink
      reqApi({ ...reqForm, ...reqForm.query }).then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(res.msg)
          // emit("openResult", res.data)
          cancel()
        }
      }).finally(() => loading.value = false)
    }
  })

}

const templateContent = computed(() => {
  if (form.value.templateContent) {
    return `${form.value.templateContent}`
  } else {
    return '--'
  }
})

defineExpose({ openDialog });

</script>

<style scoped lang="scss">
.wcc-el-steps {
  padding-top: 0px;
}

.step-item-info {
  padding-left: 20px;
  color: #9f9f9f;
  font-size: 14px;
}
</style>
