<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="数据填写"></el-step>
        <!-- <el-step title="规则设置"></el-step> -->
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div v-show="stepActive === 0" class="step-item pt20">
      <el-row>
        <el-form class="form-content data-form" :model="form" :rules="rules" ref="formRef" label-width="120px">
            <el-form-item label="导入案件信息">
              【{{ product.ownerName }}】-- {{ product.productName }}
            </el-form-item>
            <el-form-item label="项目名称" prop="packageNameArr">
              <el-input v-model="form.packageNameArr" placeholder="请输入项目名称" clearable maxlength="20" show-word-limit
                style="width: 360px;" :disabled="isedit" />
            </el-form-item>
            <el-form-item label="委案日期" prop="commissionDate">
              <el-date-picker v-model="form.commissionDate" type="date" :disabled="isedit" style="width: 360px;"
                value-format="YYYY-MM-DD" placeholder="请选择委案日期" />
            </el-form-item>
            <el-form-item label="退案日期" prop="withdrawDate">
              <el-date-picker v-model="form.withdrawDate" type="date" :disabled="isedit" style="width: 360px;"
                value-format="YYYY-MM-DD" placeholder="请选择退案日期" />
            </el-form-item>
            <!-- <el-form-item label="债权准入日期" prop="creditorAccessDate">
              <el-date-picker v-model="form.creditorAccessDate" type="date" :disabled="isedit" style="width: 360px;"
                value-format="YYYY-MM-DD" placeholder="请选择债权准入日期" :disabledDate="disabledDate" />
            </el-form-item> -->
            <!-- <el-form-item label="回款开户账号" prop="accountId">
              <el-select style="width: 360px;" v-model="form.accountId" placeholder="请选择回款开户账号" clearable filterable
                :disabled="isedit" :reserve-keyword="false">
                <el-option v-for="item in repayAccountList" :key="item.code" :label="item.info" :value="item.code" />
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="收购成本:" prop="acquisitionCosts">
              <el-input placeholder="请输入" v-model="form.acquisitionCosts" type="number" style="width: 90%" />
              &nbsp;&nbsp;元
            </el-form-item>
            <el-form-item label="回款目标金额:" prop="targetAmount">
              <el-input placeholder="请输入" v-model="form.targetAmount" type="number" style="width: 90%" />
              &nbsp;&nbsp;元
            </el-form-item>
            <el-form-item label="回款目标周期:" prop="period" style="width: 92%">
              <el-date-picker v-model="form.period" type="daterange" start-placeholder="起止日期" end-placeholder="截止日期"
                format="YYYY/MM/DD" value-format="YYYY-MM-DD" clearable @change="handleChangeDatePicker" />
            </el-form-item>
            <el-form-item label="费率:" prop="rate">
              <el-input placeholder="请输入" v-model="form.rate" type="number" style="width: 90%" />
              &nbsp;&nbsp;%
            </el-form-item>
            <el-form-item label="预期收益:" prop="expectedRevenue">
              <el-input placeholder="请输入" v-model="form.expectedRevenue" type="number" style="width: 90%" />
              &nbsp;&nbsp;元
            </el-form-item>
            <el-form-item label="收购日期:" prop="acquisitionDate">
              <el-date-picker v-model="form.acquisitionDate" type="date" style="width: 90%" value-format="YYYY-MM-DD"
                placeholder="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="交割日期:" prop="closingDate">
              <el-date-picker v-model="form.closingDate" type="date" style="width: 90%" value-format="YYYY-MM-DD"
                placeholder="YYYY-MM-DD" />
            </el-form-item> -->
            <el-form-item label="案件上传" prop="fileUrl">
              <el-input v-show="false" v-model="form.fileUrl" />
              <el-upload ref="uploadRef" drag :limit="1" accept=".xlsx" :headers="upload.headers" :action="uploadUrl"
                :on-change="handleFileEditChange" :on-progress="handleUploadProgress" :before-remove="handleRemove"
                :on-success="handleFileSuccess" :auto-upload="false">
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    <span class="mr10">支持扩展名：.xlsx</span>
                    <el-button type="primary" link @click="downTpl">点击下载Excel格式模板（支持5万条数据）</el-button>
                  </div>
                  <div class="text-danger" style="font-size: 12px">
                    注：可在列表或导入日志中查看导入状态
                  </div>
                  <div>
                    <el-button type="success" @click="submitFile">
                      上传到服务器
                    </el-button>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          <!-- <el-col :span="24">
            <el-form-item label="甲乙方债权合同及相关凭证" prop="contractIds">
              <el-upload drag :limit="5" accept=".rar,.zip,.doc,.docx,.pdf" :headers="upload.headers"
                :action="upload.curl" :before-upload="handleFileUploadBefore" :on-change="handleEditChange"
                :before-remove="handleContractRemove" :on-success="handleContractFileSuccess" :file-list="fileList"
                :disabled="isedit">
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    <span class="mr10">支持格式：.rar .zip .doc .docx .pdf
                      ，单个文件不能超过20MB，最多上传5个文件</span>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col> -->
        </el-form>
      </el-row>
      <div class="text-center mt20">
        <el-button @click="toBack">取消</el-button>
        <el-button type="primary" plain @click="nextStep">下一步</el-button>
      </div>
    </div>

    <div v-show="stepActive === 1" class="step-item pt20">
      <div v-show="!importing">
        <el-form class="form-content" :model="form1" :rules="rules1" ref="form1Ref" label-width="106px">
          <el-form-item label="导入案件信息">
            【{{ product.ownerName }}】--{{ product.productName }}
          </el-form-item>
          <el-form-item label="开启自动减免" prop="openAuotReductionSetup" v-if="form1.openAuotReductionSetup">
            <el-radio-group v-model="form1.openAuotReductionSetup" :disabled="isedit">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
            （非必填）
          </el-form-item>
          <el-form-item label="自动减免公式" v-if="form1.openAuotReductionSetup" prop="entrustMoneySetupId">
            <el-select v-model="form1.reductionSetupId" clearable filterable :reserve-keyword="false" :disabled="isedit"
              placeholder="请选择" style="width: 240px" @change="changeReductionSetupId()">
              <el-option v-for="item in reductionFormulas" :key="item.code" :label="item.info"
                :value="item.code"></el-option>
            </el-select>
            <span class="ml20" v-if="form1.reductionSetupId">{{
              returnText
            }}</span>
          </el-form-item>
        </el-form>
        <div class="text-center mt20">
          <el-button @click="preStep">上一步</el-button>
          <el-button type="primary" :loading="loading" plain @click="submitImport">提交导案</el-button>
        </div>
      </div>
      <div v-show="importing">
        <div class="text-center reset-pwd">
          <div v-loading="importing" class="step-icon loading"></div>
          <h2>
            案件导入中,可关闭该页面，查看是否导入成功请至<span @click="toImportLog" class="text-primary">导入日志</span>页面查看！
          </h2>
        </div>
        <div class="text-center mt30">
          <el-button @click="toBack">关闭</el-button>
        </div>
      </div>
    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>
          案件导入已完成，查看案件请至<span @click="tozccasemanage" class="text-primary">案件导入</span>页面查看！
        </h2>
      </div>
      <div class="text-center mt30">
        <el-button @click="toBack">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="ImportCase">
import { getToken } from "@/utils/auth";
import { entrustMoneyOptions, reductionOptions } from "@/api/system/tactic";
import {
  importCaseInfo,
  getAssetDetail,
  anewImportCaseInfo,
  getImportBatchNum,
  getReductionInfo,
  getRepayAccountList
} from "@/api/assets/asset/asset";
import { contractList } from "@/api/assets/asset/contract";

const { proxy } = getCurrentInstance();
const route = useRoute();
const store = useStore();
const product = ref({});
const returnText = ref("");
const stepActive = ref(0);
const loading = ref(false);
const entrustFormulas = ref([]); //债权总金额公式
const reductionFormulas = ref([]); //减免公式
const importing = ref(false); // 是否导入中
const fileList = ref([]); //合同文件列表
const fileCaseList = ref([]);
const repayAccountList = ref([]);
const isedit = computed(() => {
  return route.query.rowId ? true : false;
});
onMounted(() => {
  if (route.query.rowId) {
    contractList({ assetManageId: route.query.rowId }).then((res) => {
      let filearr = [];
      if (res.rows && res.rows.length > 0) {
        res.rows.map((item) => {
          let obj = {
            name: item.fileName,
            url: item.fileUrl,
          };
          filearr.push(obj);
        });
        fileList.value = filearr;
      }
    });
    getAssetDetail(route.query.rowId).then((res) => {
      let data = (product.value = res.data);
      form.value = {
        productId: data.productId,
        importBatchNum: data.batchNum,
        // creditorAccessDate: data.creditorAccessDate,
        acquisitionCosts: data.acquisitionCosts,
        targetAmount: data.targetAmount,
        period: [data.beginPeriod, data.endPeriod],
        beginPeriod: data.beginPeriod,
        endPeriod: data.endPeriod,
        rate: data.rate,
        expectedRevenue: data.expectedRevenue,
        acquisitionDate: data.acquisitionDate,
        closingDate: data.closingDate,
        packageNameArr: data.packageName,
        commissionDate: data.commissionDate,
        withdrawDate: data.withdrawDate,
        // accountId: data.accountId && data.accountId.toString(),
      };
      form1.value = {
        entrustMoneySetupId: data.entrustMoneyId
          ? String(data.entrustMoneyId)
          : "",
        openAuotReductionSetup: data.autoReduction == "0" ? true : false,
        reductionSetupId: data.reductionId ? String(data.reductionId) : "",
      };
    });
  } else {
    product.value = route.query;
    form.value.productId = route.query.productId;
  }
});
const data = reactive({
  form: {
    productId: undefined,
    importBatchNum: undefined,
    // creditorAccessDate: undefined,
    fileUrl: undefined,
    contractIds: [],
    acquisitionCosts: undefined,
    targetAmount: undefined,
    period: undefined,
    rate: undefined,
    expectedRevenue: undefined,
    beginPeriod: undefined,
    endPeriod: undefined,
    acquisitionDate: undefined,
    commissionDate: undefined,
    withdrawDate: undefined,
    packageNameArr: undefined,
    // accountId: undefined,
    closingDate: undefined,
    creditorAnnouncementDate: undefined,
    creditorAnnouncement: undefined,
    signingDate: undefined,
    protocolNumber: undefined,
    protocol: undefined,
  },
  rules: {
    // importBatchNum: [
    //   { required: true, message: "请输入导入批次号", trigger: "blur" },
    // ],
    fileUrl: [{ required: true, message: "请上传案件文件！", trigger: "blur" }],
    closingDate: [
      { required: true, message: "请选择交割日期", trigger: "change" },
    ],
    commissionDate: [
      { required: true, message: "请选择委案日期", trigger: "change" },
    ],
    withdrawDate: [
      { required: true, message: "请选择退案日期", trigger: "change" },
    ],
    packageNameArr: [
      { required: true, message: "请输入项目名称", trigger: "change" },
    ],
    // accountId: [
    //   { required: true, message: "请选择回款开户账号", trigger: "change" },
    // ],
    // creditorAccessDate: [
    //   { required: true, message: "请选择债权准入日期", trigger: "change" }
    // ]
  },
  form1: {
    entrustMoneySetupId: undefined,
    openAuotReductionSetup: false,
    reductionSetupId: undefined,
  },
});
const { form, rules, form1, rules1 } = toRefs(data);

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: (import.meta.env.VITE_APP_BASE_API + "/file/upload"),
  //合同上传地址
  curl: import.meta.env.VITE_APP_BASE_API + "/caseManage/asset/contract/upload",
});
const uploadUrl = computed(() => {
  return upload.url.replace("/appeal", "");
})
const files = ref([]); //委案合同上传成功列表

//限制日期选择范围
function disabledDate(time) {
  return time.getTime() >= new Date().getTime();
}

// 选择日期
function handleChangeDatePicker(date) {
  form.value.beginPeriod = date[0];
  form.value.endPeriod = date[1];
}

//获取回款账号
function getRepayAccount() {
  getRepayAccountList().then(res => {
    repayAccountList.value = res.data
  })
}
getRepayAccount();

function downTpl() {
  proxy.download(
    "teamProduct/downloadTemplate",
    { id: form.value.productId },
    `tpl_${product.value.productName}.xlsx`,
    { gateway: 'cis' }
  );
}

function changeReductionSetupId() {
  getReductionInfo({ reductionId: form1.value.reductionSetupId }).then(
    (res) => {
      returnText.value = res.msg;
    }
  );
}

//上传案件文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    return false;
  }
};

function handleFileEditChange(file, fileList) {
  if (fileList?.length > 1) {
    proxy.$modal.msgWarning("只能上传一个文件");
    fileList.pop();
    return false;
  }
}

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    fileList.pop();
    return false;
  }
  if (fileList?.length > 1) {
    proxy.$modal.msgWarning("只能上传一个文件");
    fileList.pop();
    return false;
  }
}

//案件上传文件列表变化
function handleCaseEditChange(file, fileList) {
  if (file.name.indexOf(".xlsx") == -1 && file.name.indexOf(".vsb") == -1) {
    proxy.$modal.msgWarning("请上传.xlsx或.vsb格式文件！");
    fileList.pop();
    return false;
  }
  if (fileList.length > 1) {
    proxy.$modal.msgWarning("只能上传一个文件！");
    fileList.pop();
    return false;
  }
}

//文件上传中
function handleUploadProgress(evt, UploadFile, UploadFiles) {
  console.log(UploadFile);
}

/* 案件上传文件移除 */
function handleRemove(file, fileList) {
  form.value.fileUrl = "";
}

/* 案件上传文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.fileUrl = data.url;
    form.value.originalFilename = file.name;
  }
};

//委案合同上传文件移除
function handleContractRemove(file, fileList) {
  if (file.response && file.response.code === 200) {
    let id = file.response.data;
    let index = form.value.contractIds.indexOf(id);
    form.value.contractIds.splice(index, 1);
  }
}

/* 委案合同上传文件上传成功处理 */
const handleContractFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.contractIds.push(response.data);
  }
};

//下一步
function nextStep() {
  if (stepActive.value === 0) {
    //第一步
    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        stepActive.value++;
      }
    });
  }
}

//上一步
function preStep() {
  stepActive.value--;
}

//提交导案信息
function submitImport() {
  loading.value = true;
  try {
    if (route.query.rowId) {
      //重新导入
      let req = {
        id: route.query.rowId,
        fileUrl: form.value.fileUrl,
        originalFilename: form.value.originalFilename,
      };
      // req.uuid = store.getters.uuid;
      importing.value = true;
      anewImportCaseInfo(req)
        .then((res) => {
          stepActive.value++;
        })
        .finally(() => {
          loading.value = false;
          importing.value = false;
        });
    } else {
      let req = JSON.parse(JSON.stringify(form.value));
      let req1 = JSON.parse(JSON.stringify(form1.value));

      Object.keys(req1).forEach((item) => {
        if (req1[item] == "" || req1[item] == []) {
          req1[item] = undefined;
        }
      });
      Object.assign(req, req1);
      importing.value = true;
      // req.uuid = store.getters.uuid;
      // req.openAccount = repayAccountList.value.find(item => item.code == req.accountId)?.info
      importCaseInfo(req)
        .then((res) => {
          stepActive.value++;
        })
        .finally(() => {
          loading.value = false;
          importing.value = false;
        });
    }
  } catch (error) {
    loading.value = false;
  }
}

//跳转导入日志
function toImportLog() {
  const obj = { path: "/assetsManagement/importLog" };
  proxy.$tab.closeOpenPage(obj);
}

//跳转资产案件库
function tozccasemanage() {
  const obj = { path: "/assetsManagement/caseImport" };
  proxy.$tab.closeOpenPage(obj);
}

//返回
const toBack = () => {
  const obj = { path: "/assetsManagement/caseImport" };
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style lang="scss" scoped>
.step-item {
  width: 90%;
  margin: 0 auto;
}

.form-content {
  width: 40%;
  margin: 0 auto;
}

.data-form {
  display: flex;
  flex-wrap: wrap;
}

.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .text-primary {
    cursor: pointer;
    text-decoration: underline;
  }

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  .step-icon.loading {
    background-color: #ffffff;
    // .is-loading{
    //     font-size:34px;
    //     color: #3CC556;
    // }
    width: 150px;
    height: 150px;
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
    margin-top: 10px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}

:deep(.el-form-item__label) {
  font-weight: 700;
}
</style>
