<template>
    <el-dialog title="批量导入" v-model="open" width="550px" :before-close="cancel" append-to-body>
        <el-form :model="form" ref="formRef" :rules="rules" label-width="140px" inline>
            <el-form-item label="下载导入模板">
                <el-button :loading="loading" type="primary" @click="handleDownloadTpl">下载模板</el-button>
            </el-form-item>
            <el-form-item label="上传填好的信息表" prop="fileUrl">
                <FileUpload drag v-model:fileList="fileList" :fileType="['xls', 'xlsx']" gateway="sign"
                    uploadFileUrl="/letter/message/upload" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-right">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { importDataReturn } from '@/api/mediation/exeOfPaybacks';
import FileUpload from '@/components/FileUpload';
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const data = reactive({
    form: {
        fileUrl: undefined
    },
    rules: {
        fileUrl: [{ required: true, message: '请上传文件', trigger: 'bluer' }]
    },
})
const fileList = ref([])
const { form, rules } = toRefs(data)
function submit() {
    form.value.fileUrl = fileList.value[0].response.data.fileUrl[0]
    form.value.fileName = fileList.value[0]?.response.data.modifyName[0]
    nextTick(() => {
        proxy.$refs['formRef'].validate(valid => {
            if (valid) {
                try {
                    loading.value = true
                    const reqForm = JSON.parse(JSON.stringify(form.value))
                    importDataReturn(reqForm).then((res) => {
                        if (res.code == 200) {
                            proxy.$modal.msgSuccess('操作成功')
                            cancel()
                        }
                    }).finally(() => loading.value = false)
                } catch (error) {
                    loading.value = false
                }
            }
        })
    })
}
function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data }
}
function cancel() {
    form.value = {}
    fileList.value = []
    open.value = false
}

// 下载模板
function handleDownloadTpl() {
    proxy.download("/execute-case/exportTemplate", {}, `tpl_回款登记导入模板.xlsx`);
}

defineExpose({ openDialog })
</script>