<template>
  <div ref="DetailFloatBarRef" class="float-bar">
    <slot name="opetTop" />
    <Fragment v-if="props.isShow">
      <el-button type="primary" :disabled="queryinfo.pageNumber === 1" plain @click="prevCase">上个案件</el-button>
      <el-button type="primary" :disabled="queryinfo.pageNumber === total" plain @click="nextCase">下个案件</el-button>
      <el-button v-if="operationText == '做调诉记录'" type="primary" plain @click="toHandleUrage">
        {{ operationText }}</el-button>
      <el-button v-else type="primary" plain @click="toTopPart">{{ operationText }}</el-button>
    </Fragment>
    <slot name="opetBttom" />
  </div>
</template>

<script setup>
import {
  selectCaseManagePrevious,
  selectCaseManageNext,
} from "@/api/caseDetail/floatBar";
const props = defineProps({ isShow: { type: Boolean, default: true } })
const emit = defineEmits(["getUrgeList"]);
const { proxy } = getCurrentInstance();
const route = useRoute();
const type = ref();
const queryinfo = ref({});
const operationText = ref("做调诉记录");
const total = ref(0);
const scroll = ref(0);
onMounted(() => {
  let storage = JSON.parse(localStorage.getItem(`searchInfo/${route.params.caseId}`));
  type.value = storage?.type;
  queryinfo.value = storage?.query;
  total.value = storage?.total;
  nextTick(() => {
    // 获取DOM元素
    let dragDiv = proxy.$refs["DetailFloatBarRef"];
    // 缓存 clientX clientY 的对象: 用于判断是点击事件还是移动事件
    let clientOffset = {};
    // 绑定鼠标按下事件
    dragDiv.addEventListener(
      "mousedown",
      (event) => {
        let offsetX = dragDiv.getBoundingClientRect().left; // 获取当前的x轴距离
        let offsetY = dragDiv.getBoundingClientRect().top; // 获取当前的y轴距离
        let innerX = event.clientX - offsetX; // 获取鼠标在方块内的x轴距
        let innerY = event.clientY - offsetY; // 获取鼠标在方块内的y轴距
        // 缓存 clientX clientY
        clientOffset.clientX = event.clientX;
        clientOffset.clientY = event.clientY;
        // 鼠标移动的时候不停的修改div的left和top值
        document.onmousemove = function (event) {
          dragDiv.style.left = event.clientX - innerX + "px";
          dragDiv.style.top = event.clientY - innerY + "px";
          // dragDiv 距离顶部的距离
          let dragDivTop = window.innerHeight - dragDiv.getBoundingClientRect().height;
          // dragDiv 距离左部的距离
          let dragDivLeft = window.innerWidth - dragDiv.getBoundingClientRect().width;
          // 边界判断处理
          // 1、设置左右不能动
          dragDiv.style.left = dragDivLeft - 26 + "px";
          // 2、超出顶部处理
          if (dragDiv.getBoundingClientRect().top <= 0) {
            dragDiv.style.top = "0px";
          }
          // 3、超出底部处理
          if (dragDiv.getBoundingClientRect().top >= dragDivTop) {
            dragDiv.style.top = dragDivTop + "px";
          }
        };
        // 鼠标抬起时，清除绑定在文档上的mousemove和mouseup事件；否则鼠标抬起后还可以继续拖拽方块
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      },
      false
    );

    // 绑定鼠标松开事件
    dragDiv.addEventListener("mouseup", (event) => {
      let clientX = event.clientX;
      let clientY = event.clientY;
      if (clientX === clientOffset.clientX && clientY === clientOffset.clientY) {
        console.log("click 事件");
      } else {
        console.log("drag 事件");
      }
    });
  });
});

//数据组装
function handledata(data, info) {
  let storage = {
    query: {
      ...data.manageQueryParam,
      caseIdCurrent: data.caseIdCurrent,
      manageQueryParam: data.manageQueryParam,
      pageIndex: 0,
      pageSize: 1,
      pageNumber: data.pageNumber,
    },
    total: data.count,
    type: info?.type,
  };
  localStorage.setItem(`searchInfo/${data.caseIdCurrent}`, JSON.stringify(storage));
}
//上一个案件
function prevCase() {
  proxy.$modal.loading("正在获取下一个案件信息，请稍候...");
  let info = JSON.parse(localStorage.getItem(`searchInfo/${route.params.caseId}`));
  selectCaseManagePrevious(info?.query).then((res) => {
    let { data } = res;
    handledata(data, info);
    const query = route.query
    const obj = { path: `/collection/mycase-detail/caseDetails/${data.caseIdCurrent}`, query };
    info.query.caseIdCurrent != data.caseIdCurrent && proxy.$tab.closeOpenPage(obj);
    setTimeout(() => {
      proxy.$modal.closeLoading();
    }, 1000)
  });
}

//下一个案件
function nextCase() {
  proxy.$modal.loading("正在获取下一个案件信息，请稍候...");
  let info = JSON.parse(localStorage.getItem(`searchInfo/${route.params.caseId}`));
  selectCaseManageNext(info?.query).then((res) => {
    let { data } = res;
    handledata(data, info);
    const query = route.query
    const obj = { path: `/collection/mycase-detail/caseDetails/${data.caseIdCurrent}`, query };
    info.query.caseIdCurrent != data.caseIdCurrent && proxy.$tab.closeOpenPage(obj);
    setTimeout(() => {
      proxy.$modal.closeLoading();
    }, 1000)
  });
}

//跳转锚点
function toHandleUrage() {
  console.log(document.querySelector(".handle-urage"));
  document
    .querySelector(".handle-urage")
    .scrollIntoView({ behavior: "auto", block: "center", inline: "nearest" });
  operationText.value = "回到顶部";
}

//回到顶部
function toTopPart() {
  document
    .querySelector(".caseinfo-wrap")
    .scrollIntoView({ behavior: "auto", block: "center", inline: "nearest" });
  operationText.value = "做调诉记录";
}

</script>

<style lang="scss" scoped>
.float-bar {
  position: fixed;
  right: 20px;
  top: 40%;
  width: 52px;
  z-index: 1000;
  height: auto;
  display: flex;
  flex-direction: column;
  cursor: move;
  padding: 10px;
  border-radius: 4px;
}

.float-bar:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
  width: 32px;
  height: auto;
  padding: 15px 8px;
  margin-left: 0;
  margin-bottom: 10px;
}

:deep(.el-button > span) {
  text-align: center;
  writing-mode: vertical-rl;
}
</style>
