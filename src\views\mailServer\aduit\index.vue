<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :class="{ 'form-h50': !showSearch }" label-width="100px" inline>
      <el-form-item label="发函批次号">
        <el-input v-model="queryParams.batchNum" placeholder="请输入发函批次号" style="width: 280px" />
      </el-form-item>
      <el-form-item label="模板名称">
        <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" style="width: 280px" />
      </el-form-item>
      <el-form-item label="发函人">
        <el-input v-model="queryParams.createBy" placeholder="请输入发函人" style="width: 280px" />
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="queryParams.status" @focus="getProce" placeholder="请选择审核状态" style="width: 280px">
          <el-option v-for="(item, index) in statusList" :key="index" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="模板类型">
        <el-cascader v-model="classifyIdsList" style="width: 280px" placeholder="请选择模板类型" :options="classifyList" />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="queryParams.createTime" type="daterange" start-placeholder="请选择创建时间" end-placeholder=""
          value-format="YYYY-MM-DD" style="width: 280px" />
      </el-form-item>
      <el-form-item label="处理时间">
        <el-date-picker v-model="queryParams.updateTime" type="daterange" start-placeholder="请选择处理时间" end-placeholder=""
          value-format="YYYY-MM-DD" style="width: 280px" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"> 查询 </el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area mb10 mt10">
      <el-button type="primary" :disabled="dataList.length == 0 || single" v-hasPermi="['lawyer:aduitItem:pass']" plain
        @click="pass()">通过</el-button>
      <el-button type="success" :disabled="dataList.length == 0 || single" v-hasPermi="['lawyer:aduitItem:unpass']"
        plain @click="unpass()">不通过</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发函批次号" align="center" prop="batchNum" v-if="columns[0].visible" />
      <el-table-column label="审核状态" align="center" prop="proce" :formatter="row=>auditStatusEnum[row.proce]" v-if="columns[1].visible" />
      <el-table-column label="模板类型" align="center" prop="classifyLabel" v-if="columns[2].visible" />
      <el-table-column label="模板名称" align="center" prop="templateName" v-if="columns[3].visible" />
      <el-table-column label="发函人" align="center" prop="createBy" v-if="columns[4].visible" />
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[5].visible" />
      <el-table-column label="处理时间" align="center" prop="updateTime" v-if="columns[6].visible">
        <template #default="{ row }">
          <div>
            {{ row.status != 0 ? row.updateTime : "" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="函件量" align="center" prop="quantity" v-if="columns[7].visible">
        <template #default="scope">
          <el-button type="text" @click="getLayerDetails(scope.row)">{{
            scope.row.quantity
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-area">
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 新增类型 -->
    <unpassBox ref="unpassBoxRef" @getList="getList" />
  </div>
</template>

<script setup name="LawAduit">
import unpassBox from "./dialog/unpass.vue";
import { getProceOptions } from "@/api/lawyer/lawyer";
import { getMessageList, messagePass } from "@/api/lawyer/aduit";
import { getClassifyList } from "@/api/lawyer/template";
import { auditStatusEnum } from "@/utils/enum";

//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const statusList = ref([]);
const single = ref(true);
const ids = ref([]);
const classifyList = ref([]);
const classifyIdsList = ref([]);
const showSearch = ref(false);
//多选字段
const checkMoreList = ref(["classifyIds"]);
const checkMoreName = ref([classifyIdsList]);
//拆分字段
const rangfiles = ["createTime", "updateTime"];
// 列显隐信息
const columns = ref([
  { key: 0, label: `发函批次号`, visible: true },
  { key: 1, label: `审批状态`, visible: true },
  { key: 2, label: `模板类型`, visible: true },
  { key: 3, label: `模板名称`, visible: true },
  { key: 4, label: `发函人`, visible: true },
  { key: 5, label: `创建时间`, visible: true },
  { key: 6, label: `处理时间`, visible: true },
  { key: 7, label: `函件量`, visible: true },
]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    templateName: undefined,
    createBy: undefined,
    classifyName: undefined,
    createTime: [],
    updateTime: [],
    status: undefined,
  },
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
//获取列表
function getList() {
  loading.value = true;
  if (queryParams.value.status == 99) queryParams.value.status = undefined;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString().replace(",", "/");
  });
  getMessageList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取分类数据
function getProce() {
  getProceOptions().then((res) => {
    statusList.value = res.data;
    statusList.value.push({
      code: 99,
      info: "全部",
    });
  });
}
getProce();

//获取分类数据
function getClassify() {
  getClassifyList().then((res) => {
    classifyList.value = res.data;
  });
}
getClassify();

/** 多选框选中数据 */
function addLayer(row) {
  router.push("/addLayer");
}

//获取函件量
function getLayerDetails(row) {
  router.push(`/lawyer/aduitDetails/${row.id}`);
}

function statusFor(row) {
  return ["待审核", "审核中", "已审核"][row.status];
}

//查询操作
async function handleQuery() {
  queryParams.value.pageNum = 1;
  await getList();
  queryParams.value.status =
    queryParams.value.status == undefined ? 99 : queryParams.value.status;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  let signStatusList = selection.map((item) => item.approveStart);
  //判断通过不通过按钮状态
  let statusFlag = false;
  if (signStatusList.length > 0) {
    signStatusList.forEach((item, index) => {
      if ([1, 2, 3].includes(item)) {
        single.value = true;
        statusFlag = true;
      }
    });
    if (!statusFlag) {
      single.value = false;
    }
  } else {
    single.value = true;
  }
}

//通过
function pass() {
  let req = { ids: ids.value };
  proxy.$modal.confirm("是否确认审核？此操作将处理通过，是否确认？")
    .then(function () {
      messagePass(req).then((res) => {
        proxy.$modal.msgSuccess(res.msg);
        getList();
      });
    })
}

//不通过
function unpass() {
  proxy.$refs["unpassBoxRef"].opendialog(ids.value);
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    templateName: undefined,
    createBy: undefined,
    classifyName: undefined,
    createTime: [],
    updateTime: [],
    status: undefined,
  };
  classifyIdsList.value = [];
  getList();
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.query-reset-btn {
  margin-left: 40px;
}

.height32 {
  height: 32px;
}

.form-content {
  overflow: hidden;
}



.h-auto {
  height: auto !important;
}
</style>
