<template>
  <div>
    <el-form
      :model="queryParams"
      :loading="loading"
      ref="queryRef"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
      inline
      label-width="110px"
    >
      <el-form-item prop="caseId" label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          style="width: 240px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input
          v-model="queryParams.clientName"
          style="width: 240px"
          placeholder="请输入被告"
        />
      </el-form-item>
      <el-form-item prop="court" label="保全法院">
        <el-select
          placeholder="请选择保全法院"
          style="width: 240px"
          v-model="queryParams.court"
          @focus="getclosed"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="startDate" label="保全申请时间">
        <el-date-picker
          style="width: 240px"
          v-model="queryParams.startDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          unlink-panels
        />
      </el-form-item>
      <el-form-item label="申请保全金额">
        <div class="range-scope" style="width: 240px">
          <el-input v-model="queryParams.applyAmount1" />
          <span>-</span>
          <el-input v-model="queryParams.applyAmount2" />
        </div>
      </el-form-item>
      <el-form-item label="实际保全金额">
        <div class="range-scope" style="width: 240px">
          <el-input v-model="queryParams.actualAmount1" />
          <span>-</span>
          <el-input v-model="queryParams.actualAmount2" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button
        :loading="loading"
        icon="Search"
        type="primary"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button
        :loading="loading"
        icon="Refresh"
        @click="antiShake(resetQuery)"
        >重置</el-button
      >
    </div>
    <div class="operation-revealing-area mb10">
      <!-- <el-button plain type="primary" @click="openCreateCaseTaskDialog" :disabled="single"
        v-hasPermi="['saasc:collection:createOutboundTasks']">创建智能外呼任务</el-button> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
      <el-tab-pane label="材料提交" name="材料提交" />
      <el-tab-pane label="待缴纳保证金" name="待缴纳保证金" />
      <el-tab-pane label="已缴纳保证金" name="已缴纳保证金" />
      <el-tab-pane label="已完成财产保全" name="已财产保全" />
      <el-tab-pane label="全部" name="全部" />
    </el-tabs>
    <selectedAll
      v-model:allQuery="queryParams.allQuery"
      :cusTableRef="proxy.$refs.multipleTableRef"
      :selectedArr="selectedArr"
      :dataList="dataList"
    >
      <template #content>
        <div class="text-flex ml20">
          <span class="case-data-list">
            案件数量：<i class="danger">{{ statistics.caseNum || 0 }}</i>
          </span>
          <span class="case-data-list">
            标的额：<i class="danger">{{ numFilter(statistics.totalMoney) }}</i>
          </span>
        </div>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      class="multiple-table"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        width="120"
      >
        <template #default="{ row, $index }">
          <el-button type="text" @click="toDetails(row, $index)">{{
            row.caseId
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="被告"
        align="center"
        key="clientName"
        prop="clientName"
        :width="90"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="手机号码"
        align="center"
        key="clientPhone"
        prop="clientPhone"
        :width="120"
      />
      <el-table-column
        v-if="columns[3].visible"
        label="身份证号码"
        align="center"
        key="clientIdcard"
        prop="clientIdcard"
        :width="180"
      />
      <el-table-column
        v-if="columns[4].visible"
        label="户籍地"
        align="center"
        key="clientCensusRegister"
        prop="clientCensusRegister"
        show-overflow-tooltip
        :width="160"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="保全状态"
        align="center"
        key="saveStage"
        prop="saveStage"
        :width="130"
      />
      <el-table-column
        v-if="columns[6].visible"
        label="申请保全金额"
        align="center"
        key="freezeAmount"
        prop="freezeAmount"
        :width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.freezeAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[7].visible"
        label="实际保全金额"
        align="center"
        key="actualFreezeAmount"
        prop="actualFreezeAmount"
        :width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.actualFreezeAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[8].visible"
        label="保全标的物"
        align="center"
        key="freezeAssets"
        prop="freezeAssets"
        :width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.freezeAssets) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[9].visible"
        label="保全法院"
        align="center"
        key="court"
        prop="court"
        :width="140"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[10].visible"
        label="保全期限开始时间"
        align="center"
        key="startFreeze"
        prop="startFreeze"
        :width="160"
      />
      <el-table-column
        v-if="columns[11].visible"
        label="保全期限结束时间"
        align="center"
        key="endFreeze"
        prop="endFreeze"
        :width="160"
      />
      <el-table-column
        v-if="columns[12].visible"
        label="跟进人员"
        align="center"
        key="follower"
        prop="follower"
      />
      <el-table-column
        v-if="columns[13].visible"
        label="最近一次跟进时间"
        align="center"
        key="followUpTime"
        prop="followUpTime"
        :width="160"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />
  </div>
</template>

<script setup>
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
import { getCourtOptions } from "@/api/common/common";
import { getFreezeList } from "@/api/mediation/iegalPreservation";
import { selectFreezeList, selectFreezeWithMoney } from "@/api/team/attachment";
import { formatParams2 } from "@/utils/common";
import { ElMessage } from "element-plus";
import { checkUserSip } from "@/api/system/user";

//全局数据
const router = useRouter();
const { proxy } = getCurrentInstance();
const activeTab = ref("全部");
const total = ref(0);
//需要拆分的字段
const rangfiles = ["startDate"];
const courtOptions = ref([]);
const selectedArr = ref([]);
const dataList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
const loading = ref(false);
const saveOption = ref([
  { code: 0, info: "材料提交" },
  { code: 1, info: "缴纳保证金" },
  { code: 2, info: "已缴纳保证金" },
  { code: 3, info: "已财产保全" },
]);
const showSearch = ref(false);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "被告", visible: true },
  { key: 2, label: "手机号码", visible: true },
  { key: 3, label: "身份证号码", visible: true },
  { key: 4, label: "户籍地", visible: true },
  { key: 5, label: "保全状态", visible: true },
  { key: 6, label: "申请保全金额", visible: true },
  { key: 7, label: "实际保全金额", visible: true },
  { key: 8, label: "保全标的物", visible: true },
  { key: 9, label: "保全法院", visible: true },
  { key: 10, label: "保全期限开始时间", visible: true },
  { key: 11, label: "保全期限结束时间", visible: true },
  { key: 12, label: "跟进人员", visible: true },
  { key: 13, label: "最近一次跟进时间", visible: true },
]);

const single = ref(true);
const caseIds = ref([]);

// 格式化金额
function moneyFor(num, str = "") {
  return num ? proxy.setNumberToFixed(num) : str;
}
getList();
function getList() {
  return new Promise((resolve) => {
    const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles);
    reqForm.saveStageList =
      activeTab.value != "全部"
        ? [activeTab.value]
        : ["材料提交", "待缴纳保证金", "已缴纳保证金", "已财产保全"];
    reqForm.saveStageList = String(reqForm.saveStageList);
    reqForm.mineQuery = true;
    if (reqForm.freezeTime1 && reqForm.freezeTime2) {
      reqForm.startFreeze = reqForm.freezeTime1;
      reqForm.endFreeze = reqForm.freezeTime2;
    }
    if (reqForm.startDate1 && reqForm.startDate2) {
      reqForm.startDate1 = `${reqForm.startDate1} 00:00:00`;
      reqForm.startDate2 = `${reqForm.startDate2} 23:59:59`;
    }
    loading.value = true;
    getFreezeList(reqForm)
      .then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        resolve();
        loading.value = false;
      });
  });
}
function checkSelectable() {
  return !queryParams.value.allQuery;
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList();
  });
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList();
  });
}

getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}
//跳转案件详情
function toDetails(row, index) {
  // let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  // let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  // delete queryChange.pageNum;
  // delete queryChange.pageSize;
  // queryChange.saveStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已财产保全']
  // let searchInfo = {
  //   query: {
  //       manageQueryParam: queryChange, //查询参数
  //       pageNumber: pageNumber, //当前第几页(变动)
  //       pageSize: 1, //一页一条
  //       pageIndex: 0, //当前第一条
  //       caseIdCurrent: row.caseId, //当前案件id,
  //   }, //查询参数
  //   type: "mycase",
  //   total: total.value, //查询到的案件总数
  // };
  // localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  // router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
  router.push({ path: `/case/allcase-detail/caseDetails/${row.caseId}` });
}
//选择列表
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  single.value = !(selection.length != 0);
  caseIds.value = selection.map((item) => item.caseId);
}
// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangfiles);
  const reqForm = formatParams2(
    reqParams,
    selectedArr,
    queryParams.value.allQuery
  );
  if (reqForm.freezeTime1 && reqForm.freezeTime2) {
    reqForm.startFreeze = reqForm.freezeTime1;
    reqForm.endFreeze = reqForm.freezeTime2;
  }
  if (reqForm.startDate1 && reqForm.startDate2) {
    reqForm.startDate1 = `${reqForm.startDate1} 00:00:00`;
    reqForm.startDate2 = `${reqForm.startDate2} 23:59:59`;
  }
  reqForm.saveStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["材料提交", "待缴纳保证金", "已缴纳保证金", "已财产保全"];
  reqForm.condition = queryParams.value.allQuery;
  reqForm.saveStage = activeTab.value == "全部" ? undefined : activeTab.value;
  reqForm.mineQuery = true;
  return reqForm;
}
//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!queryParams.value.allQuery && selectedArr.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      reqForm.mineQuery = true;
      selectFreezeWithMoney(reqForm)
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.principal,
          };
        })
        .finally(() => reslove());
    });
  });
}
function openCreateCaseTaskDialog() {
  checkUserSip().then((res) => {
    if (res.data?.isPreTestSip) {
      // const query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)));
      // query.condition = query.allQuery;
      // if (!query.allQuery) query.caseIds = caseIds.value;
      const query = getReqParams();
      query.mineQuery = true;
      proxy.$refs["createCaseTaskRef"].openDialog(query, 1);
    } else {
      proxy.$modal.msgWarning("未分配预测式外呼坐席");
    }
  });
}
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);
defineExpose({ getList });
</script>

<style lang="scss" scoped></style>
