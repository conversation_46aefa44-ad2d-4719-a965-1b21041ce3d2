<template>
  <div>
    <el-popover placement="top" v-if="content?.length > length" :width="width" trigger="hover">
      <template #reference>
        <span>
          {{ content?.length > length ? `${content.substring(0, length)}...` : content }}
        </span>
      </template>
       <p :style="`max-width: ${width}px`">{{ content }}</p>
    </el-popover>
    <span v-else>{{ content }}</span>
  </div>
</template>
<script setup name="Tooltip">
defineProps({
  content: { type: String, default: "" },
  length: { type: Number, default: 8 },
  width: { type: Number, default: 300 },
});
</script>
