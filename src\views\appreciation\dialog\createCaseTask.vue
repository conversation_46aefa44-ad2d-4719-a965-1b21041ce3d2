<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="创建任务"></el-step>
        <el-step title="任务配置"></el-step>
        <el-step :title="form.type == 1 ? '任务分配' : '挂机短信'"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>

    <div v-if="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="150px">
        <el-form-item label="可创建任务案件量：">
          {{ caseNumber }}个（注：停催的案件不可以进行呼叫，已过滤！）
        </el-form-item>
        <el-form-item label="呼叫对象：" prop="callRecipient">
          <el-radio-group v-model="form.callRecipient">
            <el-radio :label="1">本人</el-radio>
            <el-radio :label="2">全部号码</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务名称：" prop="taskName">
          <el-input
            v-model="form.taskName"
            placeholder="输入任务名称"
            style="width: 400px"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="任务类型：" prop="type">
          <el-radio-group v-model="form.type">
            <!-- <el-radio :label="1">预测式外呼</el-radio> -->
            <el-radio :label="2">AI语音通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="text-center mt20">
        <el-button @click="cancel">取消</el-button>
        <el-button class="ml10" type="primary" plain @click="nextstep()"
          >下一步</el-button
        >
      </div>
    </div>

    <div v-show="stepActive === 1" class="step-item pt20">
      <el-form
        :model="configForm"
        :rules="configRules"
        ref="configFormRef"
        label-width="150px"
      >
        <el-form-item
          v-if="form.type == 2"
          label="话术模板："
          prop="voiceTplUuid"
        >
          <el-select
            style="width: 100%"
            v-model="configForm.voiceTplUuid"
            placeholder="请选择模板"
            @focus="changeAiDataT"
          >
            <el-option
              v-for="(item, index) in templateList"
              :key="index"
              :label="item.tempName"
              :value="item.voiceTplUuid"
            />
          </el-select>
          <div v-if="templateList.length > 0 && configForm.voiceTplUuid">
            <div>模板内容：</div>
            <div>
              {{
                templateList.find(
                  (item) => item.voiceTplUuid == configForm.voiceTplUuid
                )?.tempContent
              }}
            </div>
          </div>
        </el-form-item>
        <el-form-item label="执行时间：" prop="executionTime">
          <el-checkbox-group v-model="configForm.executionTime">
            <el-row>
              <el-col :span="6" v-for="(item, index) in weekList" :key="item">
                <el-checkbox :label="index + 1" :value="index + 1">{{
                  item
                }}</el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="任务时间段：" prop="taskTime">
          <el-date-picker
            v-model="configForm.taskTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="外呼时段：" prop="executionCallTime">
          <div>
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
            <span class="fxn-text-red" style="margin-left: 15px"
              >注：不选任何时段，表示选中全天24小时</span
            >
          </div>

          <SelectTwentyFour
            ref="SelectTwentyFourRef"
            :timeArr="defaultExecutionCallTime"
            @input="updateExecutionCallTime"
          />
        </el-form-item>
        <el-form-item label="重呼次数：" prop="recallCount">
          <el-input-number
            :min="0"
            :max="3"
            v-model="configForm.recallCount"
            @change="initCount"
          />
          <span class="text-margin">次</span>
        </el-form-item>
        <el-form-item
          label="重呼间隔："
          prop="recallMinute"
          v-if="configForm.recallCount != 0"
        >
          <el-input-number
            :min="5"
            :max="120"
            v-model="configForm.recallMinute"
          />
          <span class="text-margin">分钟</span>
        </el-form-item>
        <el-form-item v-if="form.type == 1" label="是否弹屏：" prop="isScreen">
          <el-radio-group v-model="configForm.isScreen">
            <el-radio :label="1">弹屏</el-radio>
            <el-radio :label="2">不弹屏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.type == 1"
          label="单次批量外呼数量："
          prop="singleCallNumber"
        >
          <el-input-number
            :min="1"
            :max="30"
            v-model="configForm.singleCallNumber"
          />
          <span class="text-margin">次</span>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="configForm.remark"
            type="textarea"
            :rows="5"
            show-word-limit
            maxlength="300"
          ></el-input>
        </el-form-item>
      </el-form>

      <div class="text-center mt20">
        <el-button class="ml10" type="primary" plain @click="backstep"
          >上一步</el-button
        >
        <el-button @click="cancel">取消</el-button>
        <el-button
          class="ml10"
          type="primary"
          plain
          @click="nextstep()"
          :loading="loading"
          >下一步</el-button
        >
      </div>
    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <el-form
        :model="allocationForm"
        :rules="allocationRules"
        ref="allocationFormRef"
        label-width="150px"
      >
        <template v-if="form.type == 2">
          <el-form-item label="是否发送挂机短信：" prop="isSendMessage">
            <el-radio-group v-model="allocationForm.isSendMessage">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="allocationForm.isSendMessage == 1"
            label="短信模板："
            prop="voiceTplUuid"
          >
            <el-select
              style="width: 100%"
              v-model="allocationForm.voiceTplUuid"
              placeholder="请选择短信模板"
            >
              <template #empty>
                <div
                  class="text-center"
                  style="color: #999; font-size: 14px; padding: 10px 0px"
                >
                  暂无数据，请联系管理员添加！
                </div>
              </template>
              <el-option
                v-for="(item, index) in noteList"
                :key="index"
                :label="item.messageSign"
                :value="item.voiceTplUuid"
              />
            </el-select>
            <div v-if="templateList.length > 0 && allocationForm.voiceTplUuid">
              <div>模板内容：</div>
              <div>
                {{
                  templateList.find(
                    (item) => item.voiceTplUuid == allocationForm.voiceTplUuid
                  )?.messageContent
                }}
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="allocationForm.isSendMessage == 1"
            prop="sendArr"
            label="选择发送对象："
          >
            <div style="display: flex; flex-direction: column">
              <div>
                <el-checkbox
                  v-model="checkTime"
                  :indeterminate="isIndeterminateObj"
                  @change="changeCheckAll"
                  >全部</el-checkbox
                >
              </div>
              <div>
                <el-checkbox-group v-model="allocationForm.sendArr">
                  <el-checkbox
                    v-for="(item, index) in sendObjectList"
                    :key="index"
                    :label="item.code"
                  >
                    {{ item.info }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="任务分配：" prop="taskAllocationPersonnelId">
            <el-cascader
              :options="taskAllocationTreeList"
              ref="taskAllocationRef"
              :disabled="requestType == 1"
              v-model="allocationForm.taskAllocationPersonnelId"
              filterable
              collapse-tags
              collapse-tags-tooltip
              clearable
              @change="changeOption"
              style="width: 240px"
              placeholder="请选择"
              :props="{ multiple: true, value: 'id', label: 'name' }"
            />
          </el-form-item>
          <el-form-item
            label="接听设置："
            prop="answerSettings"
            v-show="requestType != 1"
          >
            <el-radio-group v-model="allocationForm.answerSettings">
              <el-radio :label="1">已分配案件只允许归属坐席接听</el-radio>
              <el-radio :label="2">随机接听</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
      </el-form>

      <div class="text-center mt20">
        <el-button class="ml10" type="primary" plain @click="backstep"
          >上一步</el-button
        >
        <el-button @click="cancel">取消</el-button>
        <el-button
          v-if="form.type == 2"
          class="ml10"
          type="primary"
          plain
          @click="nextstep(true)"
          :loading="submitLoading"
          >提交任务</el-button
        >
        <el-button
          class="ml10"
          type="primary"
          plain
          @click="nextstep()"
          :loading="submitLoading"
          >提交并执行</el-button
        >
      </div>
    </div>

    <div v-if="stepActive === 3" class="step-item pt20">
      <div class="text-center mt20">
        <template v-if="form.type == 1">
          <div v-if="createTaskState == 1" class="mb20">
            任务已创建成功并执行中可前往预测式外呼任务管理页面查看任务详情
          </div>
          <div v-else class="mb20">
            任务已创建成功，但该任务中无坐席置闲，无法外呼，请先将坐席置闲后，任务即将开启；
            可前往预测式外呼任务管理页面查看任务详情
          </div>
        </template>
        <template v-else>
          <div v-if="executeStatus">
            <div
              style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
              "
            >
              <div>
                <el-icon color="#409EFF" :size="30"><CircleCheck /></el-icon>
              </div>
              <div>
                <div>任务已创建成功并执行中</div>
                <div>可前往AI语音任务管理页面查看任务详情</div>
              </div>
            </div>
            <div
              style="
                width: 100%;
                display: grid;
                grid-template-columns: 33.33% 33.33% 33.33%;
                gap: 20px;
                margin-top: 30px;
              "
            >
              <div>
                <span>执行时间：</span>
                <span>{{ changeDateWeek(configForm).join(",") }}</span>
              </div>
              <div>
                <span>任务时段：</span>
                <span>{{ configForm.taskTime.join(" ") }}</span>
              </div>
              <div>
                <span>外呼时段：</span>
                <span>{{ configForm.executionCallTime }}</span>
              </div>
              <div>
                <span>重呼次数：</span>
                <span>{{ configForm.recallCount }}</span>
              </div>
              <div>
                <span>重呼间隔：</span>
                <span>{{ configForm.recallMinute }}</span>
              </div>
              <div></div>
            </div>
          </div>
          <div v-else>
            <div
              style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
              "
            >
              <div>
                <el-icon color="#409EFF" :size="30"><CircleCheck /></el-icon>
              </div>
              <div>
                <div>任务已创建成功，暂未执行</div>
                <div>可前往AI语音任务管理页面查看任务详情</div>
              </div>
            </div>
          </div>
        </template>

        <el-button
          style="margin-top: 20px"
          class="ml10"
          type="primary"
          plain
          @click="cancel"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import SelectTwentyFour from "@/components/SelectTwentyFour";
const { proxy } = getCurrentInstance();
import { DeptTreeType } from "@/api/appreciation/customerList";
import {
  verifyCaseList,
  caseSubmitTaskData,
  selectCaseNumber,
  AiVoiceCaseSubmitTaskData,
  AiVoiceCaseSubmitTaskDataAndExecute,
  teamAiVoiceCaseSubmitTaskData,
  teamAiVoiceCaseSubmitTaskDataAndExecute,
  myAiVoiceCaseSubmitTaskData,
  myAiVoiceCaseSubmitTaskDataAndExecute,
} from "@/api/appreciation/case.js";
import {
  myVerifyCaseList,
  myCaseSubmitTaskData,
  mySelectCaseNumber,
} from "@/api/appreciation/case.js";
import {
  teamVerifyCaseList,
  teamCaseSubmitTaskData,
  teamSelectCaseNumber,
} from "@/api/appreciation/case.js";
import { getAiVoiceTplList, aiExecuteTask } from "@/api/appreciation/voiceTask";
import { nextTick } from "vue";

const requestType = ref(0);

const outboundList = ref([{ timeValue: ["09:00", "23:00"] }]);

const title = ref("新建任务");
const open = ref(false);
//当前操作进度
const stepActive = ref(0);
const weekString = ref([]);

const timeList = ref([
  { code: 0, info: "每天执行" },
  { code: 1, info: "周一至周五执行" },
  { code: 2, info: "自定义时间" },
]);
const sendArr = ref([]);
const checkTime = ref(false);

const executeStatus = ref(false);

const sendObjectList = ref([
  { code: 1, info: "接通" },
  { code: 2, info: "未接通" },
]);

const weekList = [
  "星期一",
  "星期二",
  "星期三",
  "星期四",
  "星期五",
  "星期六",
  "星期日",
];
const taskAllocationTreeList = ref([]);
const emit = defineEmits(["close"]);

const loading = ref(false);
const submitLoading = ref(false);

const data = reactive({
  form: {
    taskName: undefined,
    callRecipient: 1,
    type: 2,
  },
  configForm: {
    executionTime: undefined,
    executionCallTime: "",
    recallCount: 0,
    recallMinute: 5,
    isScreen: 1,
    singleCallNumber: 1,
    remark: undefined,
    taskTime: [],
  },
  allocationForm: {
    taskAllocationPersonnelId: [],
    answerSettings: 1,
    isSendMessage: 1,
    sendArr: [],
  },
  rules: {
    taskName: [
      { required: true, message: "任务名称不能为空", trigger: "blur" },
    ],
    callRecipient: [
      { required: true, message: "请选择呼叫对象", trigger: "blur" },
    ],
  },
  configRules: {
    executionTime: [
      { required: true, message: "请选择执行时间", trigger: "blur" },
    ],
    recallMinute: [
      { required: true, message: "请选择重呼间隔", trigger: "blur" },
    ],
    voiceTplUuid: [
      { required: true, message: "请选择话术模板", trigger: "blur" },
    ],
    taskTime: [
      { required: true, message: "请选择任务时间段", trigger: "change" },
    ],
  },
  allocationRules: {
    taskAllocationPersonnelId: [
      { required: true, message: "请选择任务分配", trigger: "change" },
    ],
    isSendMessage: [
      { required: true, message: "是否发送挂机短信", trigger: "change" },
    ],
    answerSettings: [
      { required: true, message: "请选择接听设置", trigger: "change" },
    ],
    voiceTplUuid: [
      { required: true, message: "请选择短信模板", trigger: "change" },
    ],
    sendArr: [
      { required: true, message: "请选择选择发送对象", trigger: "change" },
    ],
  },
});

const {
  form,
  rules,
  configForm,
  configRules,
  allocationForm,
  allocationRules,
} = toRefs(data);

const noteList = ref([]);

const isIndeterminateObj = computed(() => {
  if (allocationForm.value.sendArr.length > 0) {
    if (allocationForm.value.sendArr.length == sendObjectList.value.length) {
      checkTime.value = true;
      return false;
    } else {
      return true;
    }
  } else {
    return false;
  }
});

const changeCheckAll = (value) => {
  if (value) {
    allocationForm.value.sendArr = sendObjectList.value.map(
      (item) => item.code
    );
  } else {
    allocationForm.value.sendArr = [];
  }
};

// 0 案件管理
// 1 我的案件
// 2 团队案件

const verifyCaseListRequest = {
  0: verifyCaseList,
  1: myVerifyCaseList,
  2: teamVerifyCaseList,
};

const selectCaseNumberRequest = {
  0: selectCaseNumber,
  1: mySelectCaseNumber,
  2: teamSelectCaseNumber,
};

// 提交执行预测是外呼
const caseSubmitTaskDataRequest = {
  0: caseSubmitTaskData,
  1: myCaseSubmitTaskData,
  2: teamCaseSubmitTaskData,
};

// 提交执行Ai语音
const caseSubmitAiDataRequest = {
  0: AiVoiceCaseSubmitTaskDataAndExecute,
  1: myAiVoiceCaseSubmitTaskDataAndExecute,
  2: teamAiVoiceCaseSubmitTaskDataAndExecute,
};

// 提交不执行Ai语音
const caseSubmitNoRunAiDataRequest = {
  0: AiVoiceCaseSubmitTaskData,
  1: myAiVoiceCaseSubmitTaskData,
  2: teamAiVoiceCaseSubmitTaskData,
};

const queryParams = ref({});

const createTaskState = ref(undefined);

const caseNumber = ref(0);

const templateList = ref([]);

const changeAiDataT = () => {
  // 获取话术模板
  getAiVoiceTplList().then((res) => {
    templateList.value = res.data;
  });
};

//默认时间段
const defaultExecutionCallTime = ref([]);
const checkAll = ref(false);
const isIndeterminate = computed(() => {
  if (!configForm.value.executionCallTime == "") return false;
  const isAll =
    configForm.value.executionCallTime === "08:00-23:59" ||
    configForm.value.executionCallTime?.split(",").length === 16;
  if (isAll) {
    checkAll.value = true;
  } else {
    checkAll.value = false;
  }
  return (
    configForm.value.executionCallTime !== "08:00-23:59" &&
    configForm.value.executionCallTime?.split(",").length !== 16 &&
    configForm.value.executionCallTime !== ""
  );
});

const initCount = (currentValue, oldValue) => {
  if (oldValue == 0) {
    configForm.value.recallMinute = 5;
  }
};

const updateExecutionCallTime = (newTime) => {
  configForm.value.executionCallTime = newTime;
};

const handleCheckAllChange = (val) => {
  if (val) {
    proxy.$refs.SelectTwentyFourRef.selectAll();
  } else {
    proxy.$refs.SelectTwentyFourRef.clear();
  }
};

const getDeptTreeType = () => {
  function filterDepartments(departments) {
    const result = [];

    for (const dept of departments) {
      // 递归处理子项
      if (dept.children) {
        dept.children = filterDepartments(dept.children);

        // 只保留有有效子项的部门
        if (dept.children.length > 0) {
          result.push(dept);
        }
      }

      // 处理没有子项的情况
      if (!dept.id.includes("Dept:")) {
        result.push(dept);
      }
    }

    return result;
  }
  DeptTreeType().then((res) => {
    if (res.code == 200) {
      nextTick(() => {
        const result = filterDepartments(res.data);
        taskAllocationTreeList.value = result;
      });
    } else {
      proxy.$modal.msgWarning(res.msg);
    }
  });
};
getDeptTreeType();

const changeOption = (val) => {
  let checkedMap = proxy.$refs["taskAllocationRef"].getCheckedNodes();
};

//打开弹窗
const openDialog = (query, type) => {
  open.value = true;
  requestType.value = type;
  queryParams.value = query;
  queryParams.value.pageNum && delete queryParams.value.pageNum;
  queryParams.value.pageSize && delete queryParams.value.pageSize;
  selectCaseNumberRequest[requestType.value](queryParams.value).then((res) => {
    caseNumber.value = res.data;
  });
};

const checkForm = () => {
  if (caseNumber.value == 0) {
    proxy.$modal.msgWarning("可创建任务案件量为0");
    return;
  }
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      stepActive.value += 1;
    }
  });
};

const checkConfigForm = () => {
  proxy.$refs["configFormRef"].validate((valid) => {
    if (valid) {
      const filterArr = templateList.value.filter(
        (item) => item.voiceTplUuid == configForm.value.voiceTplUuid
      );
      const everyBoo = filterArr.every((item) => item.messageContent);
      if (everyBoo) {
        noteList.value = filterArr;
      } else {
        noteList.value = [];
      }
      nextTick(async () => {
        loading.value = true;
        await verifyCaseListRequest[requestType.value](queryParams.value)
          .then((res) => {
            if (res.code == 200) {
              loading.value = false;
              allocationForm.value.taskAllocationPersonnelId =
                res.data.map(String);
              if (configForm.value.recallCount == 0) {
                configForm.value.recallMinute = undefined;
              }
              if (!configForm.value.executionCallTime) {
                configForm.value.executionCallTime = "00:00-23:59";
              }
              stepActive.value += 1;
            } else {
              proxy.$modal.msgWarning(res.msg);
            }
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  });
};

const checkAllocationForm = (status) => {
  proxy.$refs["allocationFormRef"].validate((valid) => {
    if (valid) {
      let idList = new Set();
      if (typeof allocationForm.value.taskAllocationPersonnelId == "string") {
        allocationForm.value.taskAllocationPersonnelId =
          allocationForm.value.taskAllocationPersonnelId.split(",");
      }
      allocationForm.value.taskAllocationPersonnelId &&
        allocationForm.value.taskAllocationPersonnelId?.forEach((item) => {
          const sign = "Dept:";
          console.log(item.indexOf(sign));
          if (Array.isArray(item)) {
            item.forEach((child) => {
              if (child.indexOf(sign) == -1) {
                idList.add(child);
              }
            });
          } else {
            if (item?.indexOf(sign) == -1) {
              idList.add(item);
            }
          }
        });

      let sipList = [];
      // 递归获取
      const recursion = (list, idsArray) => {
        list.forEach((item) => {
          if (idsArray.includes(item.id)) {
            sipList.push(item.sipAccountNumber);
          }
          if (item.children) {
            recursion(item.children, idsArray);
          }
        });
      };
      recursion(taskAllocationTreeList.value, Array.from(idList));
      // allocationForm.value.taskAllocationPersonnelId = Array.from(idList).join(",");
      // allocationForm.value.taskAllocationPersonnelSeating = sipList.join(",");
      // console.log(allocationForm.value);
      const intelligenceTask = {
        ...form.value,
        ...allocationForm.value,
        ...configForm.value,
        number: 1,
      };

      intelligenceTask.taskAllocationPersonnelId = Array.from(idList).join(",");
      intelligenceTask.taskAllocationPersonnelSeating = sipList.join(",");
      intelligenceTask.executionTime = configForm.value.executionTime.join(",");
      if (form.value.type == 2) {
        const teInfo = templateList.value.find(
          (item) => item.voiceTplUuid == intelligenceTask.voiceTplUuid
        );
        intelligenceTask.dialogueTemplateName = teInfo.tempName;
        intelligenceTask.dialogueTemplateContent = teInfo.tempContent;
        intelligenceTask.robotCount = teInfo.concurrency || undefined;
        intelligenceTask.hookArgs = teInfo.args;
        intelligenceTask.voiceTplUuid = teInfo.voiceTplUuid;
        if (allocationForm.value.isSendMessage == 1) {
          intelligenceTask.messageTemplateContent = teInfo.messageContent;
          intelligenceTask.messageTemplateName = teInfo.messageSign;
          if (intelligenceTask.sendArr.length > 0) {
            intelligenceTask.senderTarget = intelligenceTask.sendArr.join(",");
          }
        }
      }
      const req = { ...queryParams.value, intelligenceTask };
      req.number = 1;
      if (form.value.type == 2) {
        req.aiVoiceTask = intelligenceTask;
        delete req.intelligenceTask;

        if (req.aiVoiceTask.taskTime.length > 0) {
          req.aiVoiceTask.taskStartTime = req.aiVoiceTask.taskTime[0];
          req.aiVoiceTask.taskEndTime = req.aiVoiceTask.taskTime[1];
        }
      }
      submitLoading.value = true;
      if (status == true) {
        caseSubmitNoRunAiDataRequest[requestType.value](req)
          .then((res) => {
            console.log(res);

            if (res.code == 200) {
              stepActive.value += 1;
              createTaskState.value = res.data;
              executeStatus.value = false;
            } else {
              proxy.$modal.msgWarning(res.msg);
            }
          })
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        if (form.value.type == 1) {
          caseSubmitTaskDataRequest[requestType.value](req)
            .then((res) => {
              if (res.code == 200) {
                stepActive.value += 1;
                createTaskState.value = res.data;
              } else {
                proxy.$modal.msgWarning(res.msg);
              }
            })
            .finally(() => {
              submitLoading.value = false;
              executeStatus.value = false;
            });
        } else {
          caseSubmitAiDataRequest[requestType.value](req)
            .then((res) => {
              if (res.code == 200) {
                stepActive.value += 1;
                createTaskState.value = res.data;
                executeStatus.value = true;
              } else {
                proxy.$modal.msgWarning(res.msg);
              }
            })
            .finally(() => {
              submitLoading.value = false;
            });
        }
      }
    }
  });
};

//校验名单，下一步
const nextstep = (status) => {
  const checkedMap = {
    0: checkForm,
    1: checkConfigForm,
    2: status ? checkAllocationForm(status) : checkAllocationForm,
  };
  checkedMap[stepActive.value]();
};

//取消
function cancel() {
  form.value.taskName = "";
  form.value.type = 1;
  configForm.value.executionTime = undefined;
  proxy.$refs.SelectTwentyFourRef.clear();
  configForm.value.executionCallTime = "";
  configForm.value.recallCount = 0;
  configForm.value.recallMinute = 5;
  configForm.value.isScreen = 1;
  configForm.value.singleCallNumber = 1;
  configForm.value.remark = undefined;
  allocationForm.value.taskAllocationPersonnelId = [];
  allocationForm.value.sendArr = [];
  allocationForm.value.answerSettings = 1;
  outboundList.value = [{ timeValue: ["09:00", "23:00"] }];
  proxy.$refs["configFormRef"].resetFields();
  proxy.$refs["allocationFormRef"].resetFields();
  open.value = false;
  stepActive.value = 0;
  caseNumber.value = 0;
  noteList.value = [];
  loading.value = false;
  submitLoading.value = false;
  executeStatus.value = false;
  checkTime.value = false;
  emit("close");
}

const addOutboundList = () => {
  outboundList.value.push({ timeValue: [] });
};

const removeOutboundList = (index) => {
  outboundList.value.splice(index, 1);
};

const changeDateWeek = (row) => {
  let dateTime = [];
  let enumerate = {
    1: "星期一",
    2: "星期二",
    3: "星期三",
    4: "星期四",
    5: "星期五",
    6: "星期六",
    7: "星期日",
  };
  if (row.executionTime.length > 2 && row.executionTime.includes(",")) {
    let newArr = row.executionTime.split(",");
    newArr.forEach((item) => {
      dateTime.push(enumerate[item]);
    });
  } else {
    dateTime.push(enumerate[row.executionTime]);
  }
  return dateTime;
};

const backstep = () => {
  stepActive.value -= 1;
  switch (stepActive.value) {
    case 0:
      proxy.$refs.SelectTwentyFourRef.clear();
      proxy.$refs["configFormRef"].resetFields();
      break;
    case 1:
      configForm.value.executionTime = configForm.value.executionTime
        .split(",")
        .map(Number);
      proxy.$refs["allocationFormRef"].resetFields();
      break;
  }
};

defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.wcc-el-steps {
  padding-top: 0px;
}

.upload-attachment {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
}

.text-margin {
  margin-left: 20px;
}

.fxn-text-red {
  color: #fb4848;
}

:deep(.el-step__line) {
  left: 65% !important;
  right: -35% !important;
}

:deep(.el-cascader__tags) {
  input {
    margin-left: 10px;
  }
}
</style>
