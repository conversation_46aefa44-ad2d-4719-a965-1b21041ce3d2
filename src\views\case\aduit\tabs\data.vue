<template>
    <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams"
        label-position="right" :label-width="100" ref="queryRef" :inline="true">
        <el-form-item label="案件ID" prop="caseId">
            <el-input style="width: 336px" v-model="queryParams.caseId" placeholder="请输入案件ID" clearable
                @keyup.enter="antiShake(handleQuery)" />
        </el-form-item>
        <el-form-item label="姓名" prop="clientName">
            <el-input style="width: 336px" v-model="queryParams.clientName" placeholder="请输入姓名" clearable
                @keyup.enter="antiShake(handleQuery)" />
        </el-form-item>
        <el-form-item label="证件号码" prop="clientIdcard">
            <el-input style="width: 336px" v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable
                @keyup.enter="antiShake(handleQuery)" />
        </el-form-item>
        <el-form-item label="申请时间" style="width: 336px">
            <el-date-picker style="width: 336px" v-model="queryParams.applyDate" value-format="YYYY-MM-DD"
                type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="申请人" prop="applicant">
            <el-input style="width: 336px" v-model="queryParams.applicant" placeholder="请输入申请人" clearable
                @keyup.enter="antiShake(handleQuery)" />
        </el-form-item>
    </el-form>

    <div class="text-center">
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb40 mt10">
        <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="handle-div mt20 mb10">
        <el-button v-if="activeTab === '2'" type="primary" :disabled="loading || ids.length == 0" plain
            @click="handle(0)" v-hasPermi="['case:aduit:pass:data']">通过</el-button>
        <el-button v-if="activeTab === '2'" type="primary" :disabled="loading || ids.length == 0" plain
            @click="handle(1)" v-hasPermi="['case:aduit:unpass:data']">不通过</el-button>
    </div>

    <selectedAll ref="selectedAllRef" :selectedArr="selectedArr" :dataList="caseList">
        <template #content>
            <span class="case-data-list">案件数量：<i class="danger">{{ caseNum }}</i></span>
        </template>
    </selectedAll>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
        <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
    </el-tabs>
    <el-table v-loading="loading" class="multiple-table" ref="multipleTableRef" :data="caseList"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" :selectable="checkSelectable" align="center" />
        <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible"
            :width="columns[0].width">
            <template #default="{ row, $index }">
                <div class="df-center">
                    <el-tooltip v-if="row.labelContent" placement="top">
                        <template #content>{{ row.labelContent }}</template>
                        <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
                    </el-tooltip>
                    <el-button type="text" v-if="row.button == 1" @click="toDetails(row.caseId, $index)">
                        {{ row.caseId }}
                    </el-button>
                    <span v-if="row.button == 0">{{ row.caseId }}</span>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
            :width="columns[2].width" />
        <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible"
            :width="columns[3].width" />
        <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" :width="columns[4].width"
            v-if="columns[3].visible" />
        <el-table-column label="处理状态" align="center" key="approveStart" prop="approveStart" :formatter="approveStartFor"
            v-if="columns[4].visible" :width="columns[5].width" />
        <el-table-column label="处理时间" align="center" key="updateTime" prop="updateTime" v-if="columns[5].visible"
            :width="columns[6].width">
        </el-table-column>
        <el-table-column label="审核状态" v-if="columns[6].visible" align="center">
            <template #default="scope">
                <el-popover placement="bottom" :width="500" :ref="`popover-${scope.$index}`" trigger="click">
                    <template #reference>
                        <el-button @click="showPopover(scope.row)" type="text">
                            {{ scope.row.examineState }}
                        </el-button>
                    </template>
                    <el-table :data="gridData">
                        <el-table-column width="200" property="approveTime" label="处理时间" />
                        <el-table-column width="100" property="reviewer" label="处理人" :show-overflow-tooltip="true" />
                        <el-table-column width="100" property="approveStart" :formatter="approveStartFor"
                            label="处理状态" />
                        <el-table-column width="100" property="refuseReason" :formatter="reasonFor" label="原因" />
                    </el-table>
                </el-popover>
            </template>
        </el-table-column>
        <el-table-column label="申请人" align="center" v-if="columns[7].visible" :width="columns[7].width">
            <template #default="scope">
                <span>{{ scope.row.applicant }}</span>
            </template>
        </el-table-column>
        <el-table-column label="申请时间" align="center" v-if="columns[8].visible" :width="columns[8].width">
            <template #default="scope">
                <span>{{ scope.row.applyDate }}</span>
            </template>
        </el-table-column>
        <el-table-column label="申请原因" align="center" v-if="columns[9].visible">
            <template #default="scope">
                <Tooltip :content="scope.row.reason" :length="15" />
            </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
                <el-button v-if="row.approveStart == 2" type="text" @click="handle(0, row)"
                    v-hasPermi="['case:aduit:pass:data']">通过</el-button>
                <el-button v-if="row.approveStart == 2" type="text" @click="handle(1, row)"
                    v-hasPermi="['case:aduit:unpass:data']">不通过</el-button>
                <el-popover placement="top" :width="500" :ref="`popover-${$index}`" trigger="click"
                    v-if="row.examineState == '已通过' && checkPermi(['case:aduit:check'])"
                    :disabled="(row.examineState == '已通过' && row.fileExpiration == 1) || row.button == 0">
                    <template #reference>
                        <el-button type="text" v-hasPermi="['case:aduit:check']"
                            :disabled="(row.examineState == '已通过' && row.fileExpiration == 1) || row.button == 0">
                            查看资料 </el-button>
                    </template>
                    <span>文件地址：</span>
                    <span class="file-path text-blue" @click="setLink(row.path)" :key="index">
                        {{ row.path }}
                    </span>
                </el-popover>
                <span v-if="[1, 3].includes(row.approveStart)">--</span>
            </template>
        </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 不通过弹窗 -->
    <unpassBox ref="unpassBoxRef" @notPass="notPass" />
</template>
<script setup name="Aduit">
import {
    dataRetrievalApproval,
    selectApproveProceEight,
    selectRetrievalRecord,
} from "@/api/case/aduit/aduit";
import unpassBox from "../dialog/unpass.vue";
import { checkPermi } from "@/utils/permission";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表单配置信息
const showSearch = ref(false);
//表格配置数据
const loading = ref(false);
const total = ref(0);
//表格数据
const caseList = ref([]);
const gridData = ref([]);
const ids = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合

const tabList = ref([
    { code: '2', info: '待处理' },
    { code: '0', info: '已同意' },
    { code: '1', info: '未同意' },
    { code: 'all', info: '全部' },
])
const activeTab = ref("2");
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 50,
        approveStarts: undefined,
        caseId: undefined,
        clientName: undefined,
        clientIdcard: undefined,
        examineState: undefined,
        state: undefined,
        applyDate: [],
    },
});
const { queryParams } = toRefs(data);

//列显示/隐藏
const columns = ref([
    { key: 0, label: `案件ID`, visible: true, width: 80 },
    { key: 1, label: `产品类型`, visible: true, width: 150 },
    { key: 2, label: `姓名`, visible: true, width: 100 },
    { key: 3, label: `证件号码`, visible: true, width: 280 },
    { key: 4, label: `处理状态`, visible: true, width: 100 },
    { key: 5, label: `处理时间`, visible: true, width: 150 },
    { key: 6, label: `审核状态`, visible: true, width: 150 },
    { key: 7, label: `申请人`, visible: true, width: 100 },
    { key: 8, label: `申请时间`, visible: true, width: 100 },
    { key: 9, label: `申请原因`, visible: true, width: 180 },
]);
const rangfiles = ["applyDate"];

//获取列表
function getList() {
    loading.value = true;
    queryParams.value.approveStarts =
        activeTab.value == "all" ? undefined : activeTab.value;
    selectRetrievalRecord(proxy.addFieldsRange(queryParams.value, rangfiles))
        .then((res) => {
            caseList.value = res.rows;
            total.value = res.total;
            loading.value = false;
        })
        .catch((err) => {
            loading.value = false;
            console.log("报错")
            console, log(err)
        });
}
getList();

//重置
function resetQuery() {
    proxy.$refs["queryRef"].resetFields();
    queryParams.value = {
        pageNum: 1,
        pageSize: 50,
        approveStarts: undefined,
        caseId: undefined,
        clientName: undefined,
        clientIdcard: undefined,
        applyDate: [],
    };
    getList();
}

//点击复制
function setLink(text) {
    proxy.copyUrl(text);
    window.open(text);
    proxy.$modal.msgSuccess("复制成功！");
}

//跳转案件详情
function toDetails(row, index) {
    const caseId = row.caseId
    let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
    queryChange.pageNum = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
    queryChange.pageSize = 1;
    let searchInfo = {
        query: queryChange,
        type: "caseAudit",
    };
    localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
    router.push({ path: `/case/aduit-detail/aduitDetails/${caseId}`, query: {query: { type: "caseAudit" } } });
}

//搜索
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

//选择列表
function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = !(selection.length > 0);
    selectedArr.value = selection;
}

//tab切换
function tabChange() {
    getList();
}

//气泡框展示
function showPopover(row) {
    let req = { applyId: row.id };
    selectApproveProceEight(req)
        .then((res) => {
            gridData.value = res.data;
        })
        .catch(() => {
            loading.value = false;
        });
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
    const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 5: '已退案关闭' }
    return stutasEnum[row.approveStart];
}

//通过原因
function reasonFor(row) {
    return row.refuseReason ? row.refuseReason : `--`;
}

//审核操作
function handle(type, row) {
    let req = { ids: row ? [row.id] : ids.value };
    req.approveStart = type;
    if (req.ids.length === 0) return proxy.$modal.msgWarning("请选择操作的案件！");
    if (0 === type) {
        //通过
        proxy.$modal
            .confirm("此操作将通过选中的申请, 是否继续?")
            .then(function () {
                loading.value = true;
                return dataRetrievalApproval(req);
            })
            .then(() => {
                getList(queryParams.value);
                proxy.$modal.msgSuccess("操作成功！");
            })
            .catch((err) => { loading.value = false; })
    } else {
        proxy.$refs["unpassBoxRef"].opendialog(req);
    }
}

//提交审核不通过
function notPass(notPassData) {
    let req = JSON.parse(JSON.stringify(notPassData));
    dataRetrievalApproval(req)
        .then(() => {
            proxy.$refs["unpassBoxRef"].loadingChage();
            proxy.$modal.msgSuccess("操作成功！");
            getList();
            proxy.$refs["unpassBoxRef"].cancel();
        })
        .catch(() => {
            loading.value = false;
        });
}

// 表格是否可以选择
function checkSelectable() {
    return !proxy.$refs['selectedAllRef']?.isAll
}
const caseNum = computed(() => {
    return proxy.$refs['selectedAllRef']?.isAll ? total.value : selectedArr.value.length
})
provide("notPass", Function, true);
</script>
<style lang="scss" scoped>
.h-50 {
    overflow: hidden;
    height: 50px;
}

.h-auto {
    height: auto !important;
}

.text-flex {
    display: inline-flex;
    align-items: center;
    height: 32px;
    font-size: 14px;
    font-weight: 500;
}

.text-flex .text-danger {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
}

.form-content {
    .el-form-item {
        width: 30% !important;
    }
}

.top-right-btn {
    z-index: 1;
}
</style>