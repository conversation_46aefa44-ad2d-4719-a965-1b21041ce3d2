<template>
  <el-dialog title="申请停案" v-model="open" append-to-body @close="cancel">
    <table class="my-table" v-loading="loading">
      <thead>
        <tr class="text-left">
          <td colspan="4">案件信息</td>
        </tr>
      </thead>
      <tbody class="text-center">
        <tr>
          <td>案件总量</td>
          <td>总金额</td>
          <td>已分配</td>
          <td>未分配</td>
        </tr>
        <tr>
          <td>{{ data.caseNum }}</td>
          <td>{{ numFilter(data.totalMoney) }}</td>
          <td>{{ data.assignedNum }}</td>
          <td>{{ data.unAssignedNum }}</td>
        </tr>
      </tbody>
    </table>
    <!-- <div class="hint text-danger mt10">{{ hint }}</div> -->

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { insertStop, selectCases } from "@/api/team/team";
const props = defineProps({
  checkedType: {
    //本页选中，搜索结果选中
    type: String,
  },
  query: {
    //查询条件
    type: Object,
  },
  caseIds: {
    type: Array,
  },
});
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const handletype = ref(undefined);
const open = ref(false);
const hint = ref("");
const loading = ref(false);
const subloading = ref(false);
const selected = ref([]);
const data = ref({
  caseNum: 0,
  totalMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
  caseId: [],
});

//打开弹窗
async function opendialog(type, row) {
  handletype.value = type;
  const hint_arr = [
    "此操作会将案件状态申请为停催，资产端审核通过后，案件会退回资产端, 是否继续",
  ];
  hint.value = hint_arr[type];
  open.value = true;
  loading.value = true;
  selected.value = row;
  await handledata().then((res) => {
    loading.value = false;
  });
}

//数据处理
function handledata() {
  return new Promise((reslove, reject) => {
    if (props.checkedType === "本页选中" || !props.checkedType) {
      let unAssignedNum = []; //未分配
      let assignedNum = []; //已分配
      data.value.caseNum = selected.value.length; //案件总量
      selected.value.map((item) => {
        data.value.totalMoney += item.clientMoney * 10000;
        if (item.caseState === "0") {
          unAssignedNum.push(item);
        } else if (item.caseState === "1") {
          assignedNum.push(item);
        }
      });
      data.value.totalMoney = data.value.totalMoney / 10000;
      data.value.unAssignedNum = unAssignedNum.length; //未分配量
      data.value.assignedNum = assignedNum.length; //已分配量
      data.value.caseId = props.caseIds; //案件id集合
      reslove(true);
    } else {
      let query = JSON.parse(JSON.stringify(props.query));
      delete query.pageNum;
      delete query.pageSize;
      selectCases(query)
        .then((res) => {
          data.value.caseIds = res.data.arrayList;
          data.value.caseNum = res.data.zongshu;
          data.value.totalMoney = res.data.zongjine;
          data.value.assignedNum = res.data.yifenpei;
          data.value.unAssignedNum = res.data.weifenpei;
          reslove(true);
        })
        .catch(() => {
          reslove(false);
        });
    }
  });
}

//取消
function cancel() {
  data.value = {
    caseNum: 0,
    totalMoney: 0,
    assignedNum: 0,
    unAssignedNum: 0,
    caseId: [],
  };
  open.value = false;
}

//提交操作数据
function submit() {
  let i = handletype.value;
  subloading.value = true;
  if (props.checkedType === "本页选中" || !props.checkedType) {
    Object.assign(props.query, { condition: false, caseIds: props.caseIds });
  } else {
    Object.assign(props.query, { condition: true });
  }
  let query = JSON.parse(JSON.stringify(props.query));
  query.ids = props.caseIds;
  insertStop(query)
    .then((res) => {
      proxy.$modal.msgSuccess(res.msg);
      subloading.value = false;
      emits("getList");
      cancel();
    })
    .catch(() => {
      subloading.value = false;
    });
}

defineExpose({
  opendialog,
});

</script>

<style lang="scss" scoped>
.mar0 {
  margin: 0;
  margin-bottom: 20px !important;
}
.hint {
  font-size: 14px;
}
.my-table {
  width: 100%;
  color: #666;
  text-align: center;
  border: 1px solid #ededed;
  outline: none;
  border-spacing: 0px !important;
  thead {
    background-color: #f2f2f2;
    font-size: 14px;
    border: 1px solid #ededed;
    tr {
      height: 40px;
      td {
        border: 1px solid #ededed;
      }
    }
  }
  tbody {
    tr {
      height: 40px;
      td {
        border: 1px solid #ededed;
      }
    }
  }
}
:deep(.el-textarea textarea) {
  height: 70px !important;
}
</style>
