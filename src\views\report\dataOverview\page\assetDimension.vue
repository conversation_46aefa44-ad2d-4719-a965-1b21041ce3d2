<template>
  <div class="asset-dimension">
    <div class="dimension-title">
      <div class="dot"></div>
      <span class="title-icon">委案批次维度</span>
    </div>
    <el-form
      :model="queryParams"
      class="form-content"
      ref="queryRef"
      :inline="true"
      label-width="120px"
    >
      <el-form-item class="mar0 ml20" label="委案批次号" prop="batchNumList">
        <el-select
          v-model="queryParams.batchNumList"
          placeholder="请输入或选择委案批次号"
          clearable
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="3"
          :reserve-keyword="false"
          style="width: 260px"
          @focus="getCommissionBatch"
        >
          <el-option
            v-for="item in assetBatchList"
            :key="item.info"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column
        label="委案批次号"
        :width="180"
        align="center"
        key="entrustingBatchNum"
        prop="entrustingBatchNum"
      >
        <template #default="{ row }">
          {{ row.entrustingBatchNum ? row.entrustingBatchNum : "--" }}
        </template>
      </el-table-column>
      <el-table-column
        label="初始本金规模"
        prop="residualPrincipal"
        key="residualPrincipal"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="初始债权"
        prop="entrustMoney"
        key="entrustMoney"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="回收金额"
        prop="repaymentMoney"
        key="repaymentMoney"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="回收率"
        prop="recoveryRate"
        key="recoveryRate"
        align="center"
      >
        <template #default="{ row }">
          {{ row.recoveryRate ? `${row.recoveryRate}%` : "0%" }}
        </template>
      </el-table-column>
      <el-table-column
        label="回款目标"
        prop="recoveryTarget"
        key="recoveryTarget"
        align="center"
      >
        <template #default="{ row }">
          {{
            row.recoveryTarget == null
              ? "--"
              : row.recoveryTarget
              ? row.recoveryTarget
              : "--"
          }}
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-box">
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :autoScroll="false"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
import { getCensusBatchList, getBatchNum } from "@/api/report/dataOverview.js";
const assetBatchList = ref([]);
const { proxy } = getCurrentInstance();
//表格参数
const total = ref(0);
const loading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  batchNumList: undefined,
  transferorArr: undefined,
});
//表单参数
const dataList = ref([]);

//查询参数
function getList() {
  loading.value = true;
  let req = JSON.parse(JSON.stringify(queryParams.value));
  if (req.batchNumList && Array.isArray(req.batchNumList)) {
    req.batchNum = req.batchNumList.toString();
  }
  getCensusBatchList(req)
      .then((res) => {
        loading.value = false;
        total.value = res.total;
        dataList.value = res.rows || [];
      })
      .catch(() => {
        loading.value = false;
      });
}
provide("getList", Function, true);
getList();

// 获取委案批次号
function getCommissionBatch() {
  getBatchNum().then((res) => {
    assetBatchList.value = res.data || [];
  });
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    assetBatch: undefined,
    transferor: undefined,
  };
  getList();
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

</script>

<style scoped lang="scss">
.pagination-box {
  position: relative;
  :deep(.pagination-container .el-pagination) {
    right: 20px;
    bottom: 20px;
  }
}
.asset-dimension {
  margin-top: 40px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.2), 0 0px 3px 0 rgba(0, 0, 0, 0.19);
  .dimension-title {
    background-color: #d5d7db;
    color: #3f3f3f;
    margin: 10px 0px 20px;
    height: 60px;
    font-size: 24px;
    line-height: 60px;
    font-weight: 700;
    .dot {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #409eff;
      margin: 0px 10px 0px 20px;
    }
  }
  .form-content {
    margin: 10px 0px;
  }
  .recoery-box {
    width: 75%;
    height: 20px;
    display: inline-block;
    position: relative;
    top: 5px;
    .recoery-back {
      height: 10px;
      width: 100%;
      margin-top: 5px;
      background-color: #e2e2e2;
      border-radius: 5px;
      position: absolute;
      overflow: hidden;
    }
    .recoery-sch {
      height: 10px;
      background-color: #409eff;
      position: absolute;
    }
  }
  .recoery-data {
    width: 25%;
    display: inline-block;
  }
}
</style>
