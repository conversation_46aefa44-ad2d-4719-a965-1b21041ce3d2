import request from '@/utils/request'

// 列表
export function callNetworkList(query) {
  return request({
    url: '/settings/callNetworkList',
    method: 'get',
    params: query,
  })
}

// 新增
export function callNetworkAdd(data) {
  return request({
    url: '/settings/insertCallNetwork',
    method: 'post',
    data,
  })
}

// 修改
export function callNetworkEdit(data) {
  return request({
    url: '/settings/updateCallNetwork',
    method: 'put',
    data,
  })
}

// 删除
export function callNetworkDel(id) {
  return request({
    url: '/settings/deleteCallNetwork/' + id,
    method: 'delete'
  })
}

