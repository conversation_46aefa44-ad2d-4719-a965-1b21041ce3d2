import request from '@/utils/request'

// 获取系统消息列表
export function selectMessageCenter(query) {
    return request({
      url: '/message/selectMessageCenter',
      method: 'get',
      params:query
    })
  }

// 根据登陆人id查询该机构所有员工信息
export function selectUserByCreateId(query) {
  return request({
    url: '/message/selectUserByCreateId',
    method: 'get',
    params:query
  })
}

// 写入发布消息
export function insertMessageCenter(data) {
    return request({
      url: '/message/insertMessageCenter',
      method: 'post',
      data:data
    })
  }

//修改发布消息
export function updateMessageCenter(data) {
    return request({
      url: '/message/updateMessageCenter',
      method: 'PUT',
      data:data
    })
  }
  
//删除消息内容
export function deleteMessageCenter(data) {
  return request({
    url: '/message/deleteMessageCenter',
    method: 'PUT',
    params:data
  })
}