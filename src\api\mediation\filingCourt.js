import request from '@/utils/request'

// 立案开庭 - 查询
export function getFilingCourtList(query) {
    return request({
        url: '/session/list',
        method: 'get',
        params: query
    })
}
// 立案开庭 - 预约开庭
export function insertFilingCourt(data) {
    return request({
        url: '/session/insert',
        method: 'post',
        data
    })
}
// 立案开庭 - 批量导入
export function importFilingCourt(data) {
    return request({
        url: '/session/importData',
        method: 'post',
        data
    })
}
// 立案开庭 - 立案开庭批量发送生成短信模板短信
export function previewTemplateFilingCourt(data) {
    return request({
        url: '/session/previewCourtTemplate',
        method: 'post',
        data
    })
}
// 立案开庭 - 立案开庭批量发送短信
export function sendMessagesFilingCourt(data) {
    return request({
        url: '/session/sendLawInfoMessages',
        method: 'post',
        data
    })
}
// 立案开庭 - 开庭缴费(批量)
export function addCostFilingCourt(data) {
    return request({
        url: '/cost/record/addList',
        method: 'post',
        data
    })
}
// 立案开庭 - 开庭缴费(批量)
export function registerRefundFilingCourt(data) {
    return request({
        url: '/execute-case/batchRegisterRefund',
        method: 'post',
        data
    })
}