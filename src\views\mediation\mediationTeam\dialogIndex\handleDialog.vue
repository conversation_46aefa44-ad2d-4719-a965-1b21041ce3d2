<template>
  <el-dialog title="提示" v-model="open" append-to-body @close="cancel">
    <table class="my-table" v-loading="loading">
      <thead>
        <tr class="text-left">
          <td colspan="4">案件信息</td>
        </tr>
      </thead>
      <tbody class="text-center">
        <tr>
          <td>案件总量</td>
          <td>总金额</td>
          <td v-show="handletype != 4">已分配</td>
          <td v-show="handletype != 4">未分配</td>
        </tr>
        <tr>
          <td>{{ data.caseNum }}</td>
          <td>{{ numFilter(data.totalMoney) }}</td>
          <td v-show="handletype != 4">{{ data.assignedNum }}</td>
          <td v-show="handletype != 4">{{ data.unAssignedNum }}</td>
        </tr>
      </tbody>
    </table>
    <el-form v-if="handletype == 1" :model="form" class="mt10" label-position="top" :rules="rules" :reason="reason"
      ref="formRef">
      <el-form-item class="mar0" label="申请原因">
        <el-input style="height: 70px" v-model="form.caseReason" maxlength="300" show-word-limit type="textarea" />
      </el-form-item>
    </el-form>
    <el-form v-if="handletype == 2 && props.stay" :model="stayForm" class="mt10" :rules="stayRules" ref="stayFormRef">
      <el-form-item label="新的退案时间：" prop="returnCaseDate">
        <el-date-picker v-model="stayForm.returnCaseDate" type="date" placeholder="" format="YYYY-MM-DD"
          :disabledDate="disabledDate" value-format="YYYY-MM-DD" :editable="false" />
      </el-form-item>
      <el-form-item label="申请原因：" prop="reason">
        <el-input v-model="stayForm.reason" type="textarea" maxlength="300" show-word-limit placeholder="请输入" />
      </el-form-item>
    </el-form>
    <!-- <div class="hint text-danger mt10">{{ hint }}</div> -->

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  reclaimCase,
  insertRetreat,
  insertKeep,
  insertStop,
  recoverCase,
  strategyAllocationQuery,
  queryAllIdRestore
} from "@/api/mediation/appealCase";
const props = defineProps({
  checkedType: {
    //本页选中，搜索结果选中
    type: String,
  },
  query: {
    //查询条件
    type: Object,
  },
  stay: {
    type: Boolean,
    default: true
  }
});
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const reqarr = ref([reclaimCase, insertRetreat, insertKeep, insertStop, recoverCase]);
const getMessage = ref([strategyAllocationQuery, queryAllIdRestore]);
const handletype = ref(undefined);
const open = ref(false);
const hint = ref("");
const loading = ref(false);
const subloading = ref(false);
const selected = ref([]);
const data = ref({
  caseNum: 0,
  totalMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
  caseId: [],
});
const reason = reactive({
  form: {
    caseReason: undefined,
  },
  rules: {
    caseReason: [{ required: true, message: `请输入申请原因！`, trigger: "blur" }],
  },
});
const { form, rules } = toRefs(reason);
const stayForm = ref({
  returnCaseDate: undefined,
  reason: undefined
})
const stayRules = ref({
  returnCaseDate: [{ required: true, message: "请输入新的退案日期", trigger: "blur" }],
})

function disabledDate(time) {
  return time < Date.now(); // 禁止选择今天之前的日期
}

//打开弹窗
async function openDialog(type, row, condition) {
  handletype.value = type;
  const hint_arr = [
    "提交案件回收，当前选择案件将会从业务员身上回收，变成未分配状态。你还要继续吗？",
    "此操作将会对案件进行退案, 是否继续?",
    "此操作将会对案件进行留案, 是否继续?",
    // "此操作会将案件状态申请为停催，资产端审核通过后，案件会退回资产端, 是否继续?",
    "此操作会将案件状态申请为停案",
    "此操作将会把停催的案件恢复成资产未分配状态, 是否继续?",
  ];
  hint.value = hint_arr[type];
  open.value = true;
  loading.value = true;
  selected.value = row;
  props.query.caseIds = row.map(item => item.caseId);
  if (condition != undefined) {
    props.query.condition = condition
  }
  await handledata().then((res) => {
    loading.value = false;
  });
}

//数据处理
function handledata() {
  return new Promise((reslove, reject) => {
    // console.log(props.query);
    let query = JSON.parse(JSON.stringify(props.query));
    delete query.pageNum;
    delete query.pageSize;
    // if (query.condition) delete query.ids;
    if (query.condition) delete query.caseIds;
    query.allQuery = query.condition;
    const message = handletype.value != 4 ? getMessage.value[0] : getMessage.value[1];
    message(query).then((res) => {
      data.value.caseIds = res.data.arrayList;
      data.value.caseNum = handletype.value != 4 ? res.data.zongshu : res.data.caseNum;
      data.value.totalMoney = handletype.value != 4 ? res.data.zongjine : res.data.totalMoney;
      data.value.assignedNum = res.data?.yifenpei;
      data.value.unAssignedNum = res.data?.weifenpei;
      reslove(true);
    });
  });
}

//取消
function cancel() {
  data.value = {
    caseNum: 0,
    totalMoney: 0,
    assignedNum: 0,
    unAssignedNum: 0,
    caseId: [],
  };
  open.value = false;
  reason.form.caseReason = undefined;
}

//提交操作数据
function submit() {
  let i = handletype.value;
  let query = JSON.parse(JSON.stringify(props.query));
  if (query.condition) delete query.caseIds;
  query.allQuery = query.condition
  if (handletype.value == 2) {
    if (props.stay) {
      proxy.$refs.stayFormRef.validate((valid) => {
        if (!valid) {
          return;
        } else {
          subloading.value = true;
          Object.assign(query, stayForm.value);
          reqarr.value[i](query)
            .then((res) => {
              proxy.$modal.msgSuccess(res.msg);
              subloading.value = false;
              emits("getList");
              cancel();
            })
            .catch(() => {
              subloading.value = false;
            });
        }
      })
    } else {
      subloading.value = true;
      reqarr.value[i](query)
        .then((res) => {
          proxy.$modal.msgSuccess(res.msg);
          subloading.value = false;
          emits("getList");
          cancel();
        })
        .catch(() => {
          subloading.value = false;
        });
    }
  } else {
    subloading.value = true;
    if (handletype.value == 1) {
      Object.assign(query, { reason: reason.form?.caseReason })
    }
    reqarr.value[i](query)
      .then((res) => {
        proxy.$modal.msgSuccess(res.msg);
        subloading.value = false;
        emits("getList");
        cancel();
      })
      .catch(() => {
        subloading.value = false;
      });
  }

}

defineExpose({
  openDialog,
});

</script>

<style lang="scss" scoped>
.mar0 {
  margin: 0;
  margin-bottom: 20px !important;
}

.hint {
  font-size: 14px;
}

.my-table {
  width: 100%;
  color: #666;
  text-align: center;
  border: 1px solid #ededed;
  outline: none;
  border-spacing: 0px !important;

  thead {
    background-color: #f2f2f2;
    font-size: 14px;
    border: 1px solid #ededed;

    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }

  tbody {
    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }
}

:deep(.el-textarea textarea) {
  height: 70px !important;
}
</style>
