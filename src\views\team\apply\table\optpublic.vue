<template>
  <div class="stages">
    <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams"
      label-position="right" :label-width="100" ref="queryRef" :inline="true">
      <el-form-item label="案件ID">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 328px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 328px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 328px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input v-model="queryParams.applicant" placeholder="请输入申请人" clearable style="width: 328px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="审核时间" style="width: 336px">
        <el-date-picker v-model="queryParams.examineTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="申请时间" style="width: 336px">
        <el-date-picker v-model="queryParams.applyDate" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="退案时间" style="width: 336px">
        <el-date-picker v-model="queryParams.returnCaseDate" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb40 mt10">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="top-right-btn">
      <el-radio-group class="mr20" v-model="settleState" @change="settleStateChange">
        <el-radio label="部分还款">部分还款</el-radio>
        <el-radio label="结清还款">结清还款</el-radio>
        <el-radio label="all">全部</el-radio>
      </el-radio-group>
    </div>

    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane :label="`${tab[0].label}(${tab[0].count || 0})`" name="待审核">
      </el-tab-pane>
      <el-tab-pane :label="`${tab[1].label}(${tab[1].count || 0})`" name="审核中">
      </el-tab-pane>
      <el-tab-pane :label="`${tab[2].label}(${tab[2].count || 0})`" name="未通过">
      </el-tab-pane>
      <el-tab-pane :label="`${tab[3].label}(${tab[3].count || 0})`" name="已通过">
      </el-tab-pane>
      <el-tab-pane :label="`${tab[4].label}(${tab[4].count || 0})`" name="已撤销">
      </el-tab-pane>
      <el-tab-pane :label="`${tab[5].label}(${tab[5].count || 0})`" name="all"> </el-tab-pane>
    </el-tabs>

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" width="120" v-show="columns[0].visible">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center">
            <el-tooltip v-show="scope.row.labelContent" placement="top">
              <template #content>{{ scope.row.labelContent }}</template>
              <case-label class="ml5" v-show="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
            </el-tooltip>
            <span style="color:#409eff;cursor: pointer;" type="text" v-show="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)">{{
                scope.row.caseId
              }}</span>
            <span v-show="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="处置人员" align="center" key="odvName" prop="odvName" width="120"
        v-show="columns[1].visible" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" width="120"
        v-show="columns[2].visible" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" width="120"
        v-show="columns[3].visible" />
      <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" width="180"
        v-show="columns[4].visible">
      </el-table-column>
      <el-table-column label="退案日期" align="center" key="returnCaseDate" prop="returnCaseDate" width="120"
        v-show="columns[5].visible" show-overflow-tooltip />
      <el-table-column label="审核状态" align="center" width="120" v-show="columns[6].visible">
        <template #default="scope">
          <el-popover placement="bottom" :width="500" :ref="`popover-${scope.$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(scope.row)" type="text">{{
                scope.row.examineState
              }}</el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column width="200" property="approveTime" label="处理时间" />
              <el-table-column width="100" property="reviewer" label="处理人" show-overflow-tooltip />
              <el-table-column width="100" property="approveStart" :formatter="approveStartFor" label="处理状态" />
              <el-table-column width="100" property="refuseReason" :formatter="reasonFor" label="原因"
                show-overflow-tooltip />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" key="examineTime" prop="examineTime" width="120"
        v-show="columns[7].visible" />
      <el-table-column label="申请人" align="center" key="applicant" prop="applicant" width="120"
        v-show="columns[8].visible" />
      <el-table-column label="申请时间" align="center" key="applyDate" prop="applyDate" width="160"
        v-show="columns[9].visible" />
      <el-table-column label="申请原因" align="center" width="140" v-show="columns[10].visible">
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.reason }}</p>
            </template>
            <div>
              <span>{{
                scope.row.reason?.length > 11
                  ? `${scope.row.reason?.substring(0, 11)}...`
                  : scope.row.reason
              }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button v-show="scope.row.examineState == `待审核`"
            v-hasPermi="[`team:apply:remove:${['stay', 'stop', 'back'][Number(props.radioType) - 3]}`]" type="text"
            @click="toBackOut([scope.row])">撤销申请</el-button>
          <el-popover placement="bottom" :width="300" :ref="`popover-${scope.$index}`" trigger="click"
            :content="unPassReason" v-show="scope.row.examineState == `未通过`">
            <template #reference>
              <el-button v-show="scope.row.examineState == `未通过`"
                v-hasPermi="[`team:apply:check:${['stay', 'stop', 'back'][Number(props.radioType) - 3]}`]"
                @click="getUnPassReason(scope.row)" type="text">查看不通过原因</el-button>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList()" />
  </div>
</template>
<script setup>
import {
  selectApplyRecordKeepCase,
  selectApplyRecordStopUrging,
  selectApplyRecordWithdrawal,
  selectApplyRecordIdStopNumber,
  selectApplyRecordIdKeepNumber,
  selectApplyRecordIdWithdrawalNumber,
} from "@/api/team/apply";
import { getBatchNums } from "@/api/common/common";
import {
  selectApproveProceFour,
  selectApproveProceFive,
  selectApproveProceSix,
} from "@/api/case/aduit/aduit";
import {
  staycaseRevoke,
  stopurgingRevoke,
  sendbackRevoke,
} from "@/api/collection/myapply";
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const props = defineProps({
  radioType: {
    type: String,
    default: 3,
  }
});
//表格配置数据
const loading = ref(false);
const total = ref(0);
// 列显隐信息
const columns = ref([
  { "key": 0, "label": "案件ID", "visible": true },
  { "key": 1, "label": "处置人员", "visible": true },
  { "key": 2, "label": "姓名", "visible": true },
  { "key": 3, "label": "证件类型", "visible": true },
  { "key": 4, "label": "证件号码", "visible": true },
  { "key": 5, "label": "退案日期", "visible": true },
  { "key": 6, "label": "审核状态", "visible": true },
  { "key": 7, "label": "审核时间", "visible": true },
  { "key": 8, "label": "申请人", "visible": true },
  { "key": 9, "label": "申请时间", "visible": true },
  { "key": 10, "label": "申请原因", "visible": true }
]);
//表单配置信息
const showSearch = ref(false);
const tab = ref([
  { label: "已提交,待审核", id: '待审核', count: 0 },
  { label: "审核中", id: '审核中', count: 0 },
  { label: "未通过", id: '未通过', count: 0 },
  { label: "已通过", id: '已通过', count: 0 },
  { label: "已撤销", id: '已撤销', count: 0 },
  { label: "全部", id: '全部', count: 0 },
]);
//列表切换字段
const settleState = ref("all");
const activeTab = ref("all");
//表格数据
const caseList = ref([]);
const gridData = ref([]);
const unPassReason = ref("");
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    applicant: undefined,
    examineState: undefined,
    stringDeptId: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    examineTime: [],
    applyDate: [],
  },
});
//需要拆分的字段
const rangfiles = ["entrustingCaseDate", "returnCaseDate", "applyDate", 'examineTime'];
const tableInfo = ref([
  "",
  "",
  "",
  selectApplyRecordKeepCase,
  selectApplyRecordStopUrging,
  selectApplyRecordWithdrawal,
]);
//提交接口
const revocationInfo = ref([
  "",
  "",
  "",
  staycaseRevoke,
  stopurgingRevoke,
  sendbackRevoke,
]);
const stateDetailsInfo = ref([
  "",
  "",
  "",
  selectApproveProceFour,
  selectApproveProceFive,
  selectApproveProceSix,
]);
//获取申请状态数量
const stateNnumberInfo = ref([
  "",
  "",
  "",
  selectApplyRecordIdKeepNumber,
  selectApplyRecordIdStopNumber,
  selectApplyRecordIdWithdrawalNumber,
]);
const { queryParams } = toRefs(data);
//表单数据集合
const batchs = ref([]);
//多选信息
const entrustingCaseBatchNumList = ref([]);
//多选字段
const checkMoreList = ref(["entrustingCaseBatchNum"]);
const checkMoreName = ref([entrustingCaseBatchNumList]);

//获取列表
function getList(deptId) {
  queryParams.value.stringDeptId = deptId;
  queryParams.value.examineState = activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.repaymentType =
    settleState.value == "all" ? undefined : settleState.value;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });
  loading.value = true;
  tableInfo.value[parseInt(props.radioType)](
    proxy.addFieldsRange(queryParams.value, rangfiles)
  )
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
  getStateNumber();
}
provide("getList", Function, true);
getList();

//获取状态数量
function getStateNumber() {
  let req = proxy.addFieldsRange(queryParams.value, rangfiles);
  stateNnumberInfo.value[parseInt(props.radioType)](
    req
  )
    .then((res) => {
      let sum = 0;
      tab.value.forEach((item) => item.count = 0);
      if (res.data.length != 0) {
        tab.value.forEach((item) => {
          res?.data.forEach((items) => {
            if (item.id == items.examineStates) {
              item.count = items.number;
            }
          });
          // if (item.id != "全部") {
          //   sum += item.count;
          // }
        });
        res?.data.forEach((item) => {
          sum += item.number;
        });
        tab.value[tab.value.length - 1].count = sum;
      }
    })
    .catch(() => {
      loading.value = false;
    });
}

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//获取未通过原因
function getUnPassReason(row) {
  let req = {
    applyId: row.id,
  };
  stateDetailsInfo.value[parseInt(props.radioType)](req)
    .then((res) => {
      res?.data.forEach((item, index) => {
        if (item.approveStart === 1) {
          unPassReason.value = item.refuseReason ?? "";
        }
      });
    })
    .catch(() => {
      loading.value = false;
    });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    applicant: undefined,
    state: undefined,
    deptId: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    applyDate: [],
  };
  checkMoreName.value.forEach((item, index) => {
    item.value.length = 0;
  });
  getList();
}

//结算类型切换
function settleStateChange() {
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/teamIndex-detail/teamCaseDetails/${caseId}`, query: { type: "caseManage" } });
}

//tab切换
function tabChange() {
  getList();
}

//撤销回款
function toBackOut(row) {
  let req = row?.map((item, index) => item.id);
  proxy.$modal
    .confirm("撤销申请后，此申请将不会被提交, 你还要继续吗?")
    .then(function () {
      loading.value = true;
      return revocationInfo.value[parseInt(props.radioType)](req);
    })
    .then(() => {
      getList(queryParams.value.deptId);
      proxy.$modal.msgSuccess("操作成功！");
    })
    .catch((err) => { loading.value = false; })
}

//气泡框展示
function showPopover(row) {
  let req = {
    applyId: row.id,
  };
  stateDetailsInfo.value[parseInt(props.radioType)](req)
    .then((res) => {
      gridData.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {

  const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 4: '已撤销', 5: '已退案关闭' }
  return stutasEnum[row.approveStart];
}

//通过原因
function reasonFor(row) {
  return row.refuseReason ? row.refuseReason : `--`;
}

defineExpose({
  getList
})
</script>
<style lang="scss" scoped>
.form-content {
  .el-form-item {
    width: 30% !important;

    .el-select .el-select__tags .el-tag--info {
      max-width: 100px;
      overflow: hidden;
    }
  }
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.wrap {
  min-height: 300px;
  padding-bottom: 20px;
}

.filter-tree {
  height: 70%;
  overflow: auto;
}

.top-right-btn {
  z-index: 1;
}
</style>
