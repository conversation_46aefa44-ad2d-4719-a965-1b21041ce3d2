<template>
  <div>
    <el-form
      :model="queryParams"
      :loading="loading"
      ref="queryRef"
      inline
      label-width="auto"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
    >
      <el-form-item prop="packageName" label="资产包名称">
        <el-input
          v-model="queryParams.packageName"
          style="width: 280px"
          placeholder="请输入资产包名称"
        />
      </el-form-item>
      <el-form-item prop="caseId" label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          style="width: 280px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="filingNumber" label="立案号">
        <el-input
          v-model="queryParams.filingNumber"
          style="width: 280px"
          placeholder="请输入立案号"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input
          v-model="queryParams.clientName"
          style="width: 280px"
          placeholder="请输入被告"
        />
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 280px">
          <el-input v-model="queryParams.amount1" />
          <span>-</span>
          <el-input v-model="queryParams.amount2" />
        </div>
      </el-form-item>
      <el-form-item prop="isFreeze" label="是否诉保">
        <el-select
          placeholder="请选是否诉保"
          v-model="queryParams.isFreeze"
          style="width: 280px"
        >
          <el-option label="全部" value="" />
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button
        :loading="loading"
        icon="Search"
        type="primary"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button
        :loading="loading"
        icon="Refresh"
        @click="antiShake(resetQuery)"
        >重置</el-button
      >
    </div>
    <div class="operation-revealing-area mb10">
      <!-- <el-button plain type="primary" @click="openCreateCaseTaskDialog" :disabled="single"
                v-hasPermi="['saasc:collection:createOutboundTasks']">创建智能外呼任务</el-button> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
      <el-tab-pane label="未提交立案" name="未提交立案" />
      <el-tab-pane label="审核中" name="审核中案件" />
      <el-tab-pane label="立案不通过" name="立案不通过" />
      <el-tab-pane label="接受调解" name="接受调解" />
      <el-tab-pane label="立案通过" name="已网上立案" />
      <el-tab-pane label="全部" name="全部" />
    </el-tabs>
    <selectedAll
      ref="selectedAllRef"
      v-model:allQuery="queryParams.allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      :cusTableRef="proxy.$refs.multipleTableRef"
    >
      <template #content>
        <span class="case-data-list">
          案件数量：<i class="danger">{{ statistics.size || 0 }}</i>
        </span>
        <span class="case-data-list">
          初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
        </span>
        <span class="case-data-list">
          初始债权本金：<i class="danger">{{
            numFilter(statistics.principal)
          }}</i>
        </span>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      class="multiple-table"
      @selection-change="handleSelectionChange"
      :data="dataList"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="center"
        prop="caseId"
        width="120"
      >
        <template #default="{ row, $index }">
          <el-button type="text" link @click="toDetails(row, $index)">{{
            row.caseId
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="资产包名称"
        align="center"
        prop="packageName"
        min-width="120px"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="转让方"
        align="center"
        prop="entrustingPartyName"
        min-width="120px"
      />
      <el-table-column
        v-if="columns[3].visible"
        label="立案状态"
        align="center"
        prop="disposeStage"
        width="120px"
      />
      <el-table-column
        v-if="columns[4].visible"
        label="被告"
        align="center"
        prop="clientName"
        width="120px"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="身份证号码"
        align="center"
        prop="clientIdcard"
        width="180px"
      />
      <el-table-column
        v-if="columns[6].visible"
        label="户籍地"
        align="center"
        prop="clientCensusRegister"
        width="160px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[7].visible"
        label="立案号"
        align="center"
        prop="filingNumber"
        width="120px"
      />
      <el-table-column
        v-if="columns[8].visible"
        label="立案时间"
        align="center"
        prop="filingTime"
        width="160px"
      />
      <el-table-column
        v-if="columns[9].visible"
        label="标的额"
        align="center"
        prop="remainingDue"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[10].visible"
        label="是否诉保"
        align="center"
        prop="isFreeze"
      />
      <el-table-column
        v-if="columns[11].visible"
        label="跟进人员"
        align="center"
        prop="follower"
        width="100px"
      />
      <el-table-column
        v-if="columns[12].visible"
        label="最近一次跟进时间"
        align="center"
        prop="followUpTime"
        width="160px"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />
  </div>
</template>

<script setup>
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
import { getOnlineFilingList } from "@/api/mediation/onlineFiling";
import {
  selectFillingList,
  selectFillingWithMoney,
} from "@/api/team/filingCaseInNet";
import { formatParams2 } from "@/utils/common";
import { checkUserSip } from "@/api/system/user";

//全局数据
const router = useRouter();
const { proxy } = getCurrentInstance();

const activeTab = ref("全部");
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
});
const statistics = ref({
  caseNum: 0,
  money: 0,
  principal: 0,
});
const total = ref(0);
//需要拆分的字段
const rangFiles = [];
const selectedArr = ref([]);
const loading = ref(false);
const dataList = ref([]);
const showSearch = ref(false);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "资产包名称", visible: true },
  { key: 2, label: "转让方", visible: true },
  { key: 3, label: "立案状态", visible: true },
  { key: 4, label: "被告", visible: true },
  { key: 5, label: "身份证号码", visible: true },
  { key: 6, label: "户籍地", visible: true },
  { key: 7, label: "立案号", visible: true },
  { key: 8, label: "立案时间", visible: true },
  { key: 9, label: "标的额", visible: true },
  { key: 10, label: "是否诉保", visible: true },
  { key: 11, label: "跟进人员", visible: true },
  { key: 12, label: "最近一次跟进时间", visible: true },
]);

const single = ref(true);
const caseIds = ref([]);

// 获取列表
function getList() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFiles);
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["未提交立案", "审核中案件", "立案不通过", "接受调解", "已网上立案"];
  reqForm.disposeStageList = String(reqForm.disposeStageList);
  reqForm.mineQuery = true;
  loading.value = true;
  getOnlineFilingList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList();
  });
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList();
  });
}

//表格选择
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  single.value = !(selection.length != 0);
  caseIds.value = selection.map((item) => item.caseId);
}

// 表格是否可以选择
function checkSelectable() {
  return !queryParams.value.allQuery;
}

//跳转案件详情
function toDetails(row, index) {
  // let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  // let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  // delete queryChange.pageNum;
  // delete queryChange.pageSize;
  // queryChange.disposeStageList = activeTab.value != '全部' ? [activeTab.value] : ['未提交立案', '审核中案件', '立案不通过', '接受调解', '已网上立案']
  // let searchInfo = {
  //     query: {
  //         manageQueryParam: queryChange, //查询参数
  //         pageNumber: pageNumber, //当前第几页(变动)
  //         pageSize: 1, //一页一条
  //         pageIndex: 0, //当前第一条
  //         caseIdCurrent: row.caseId, //当前案件id
  //     }, //查询参数
  //     type: "mycase",
  //     total: total.value, //查询到的案件总数
  // };
  // localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  // router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
  router.push({ path: `/case/allcase-detail/caseDetails/${row.caseId}` });
}
//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!queryParams.value.allQuery && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      reqForm.mineQuery = true;
      selectFillingWithMoney(reqForm)
        .then((res) => {
          statistics.value = res.data;
        })
        .finally(() => reslove());
    });
  });
}
function getReqParams() {
  const reqParams = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles))
  );
  const reqForm = formatParams2(
    reqParams,
    selectedArr,
    queryParams.value.allQuery
  );
  reqForm.condition = queryParams.value.allQuery;
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["未提交立案", "审核中案件", "立案不通过", "接受调解", "已网上立案"];
  reqForm.mineQuery = true;
  return reqForm;
}

function openCreateCaseTaskDialog() {
  checkUserSip().then((res) => {
    if (res.data?.isPreTestSip) {
      //   const query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)));
      //   query.condition = query.allQuery;
      //   if (!query.allQuery) query.caseIds = caseIds.value;
      const query = getReqParams();
      query.mineQuery = true;
      proxy.$refs["createCaseTaskRef"].openDialog(query, 1);
    } else {
      proxy.$modal.msgWarning("未分配预测式外呼坐席");
    }
  });
}

watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);
defineExpose({ getList });
</script>

<style lang="scss" scoped></style>
