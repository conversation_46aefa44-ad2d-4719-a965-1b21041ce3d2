<!--留案-->
<template>
  <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
    :inline="true" label-width="100px">
    <el-form-item label="案件ID" prop="caseId">
      <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="姓名" prop="clientName">
      <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="证件号码" prop="clientIdcard">
      <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="申请人" prop="applicant">
      <el-input v-model="queryParams.applicant" placeholder="请输入申请人" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="审核时间" style="width: 308px">
      <el-date-picker v-model="queryParams.updateTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
    <el-form-item label="申请时间" style="width: 308px">
      <el-date-picker v-model="queryParams.applyDate" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
    <el-form-item label="退案日期" style="width: 308px">
      <el-date-picker v-model="queryParams.returnCaseDate" value-format="YYYY-MM-DD" type="daterange"
        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
  </el-form>

  <div class="text-center">
    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
  </div>
  <el-row class="mb10 mt10 h32">
    <el-button v-hasPermi="['case:manage:recovery:stay']" plain v-if="activeTab == '待审核'" :disabled="single || loading"
      @click="batchRevoke()">批量撤销申请
    </el-button>

    <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
  </el-row>

  <el-tabs class="mb8" type="card" v-model="activeTab" @tab-click="tabChange">
    <el-tab-pane v-for="item in examineStateTab" :key="item.key" :label="item.value" :name="item.key"></el-tab-pane>
    <el-tab-pane label="全部" name="全部"></el-tab-pane>
  </el-tabs>

  <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
    <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
    <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
      <template #default="scope">
        <div style="display: flex; align-items: center; justify-content: center">
          <el-tooltip v-if="scope.row.labelContent" placement="top">
            <template #content>{{ scope.row.labelContent }}</template>
            <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
          </el-tooltip>
          <span style="color:#409eff;cursor: pointer;" type="text" v-if="scope.row.button == 1"
            @click="toDetails(scope.row.caseId, scope.$index)">{{
              scope.row.caseId
            }}</span>
          <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
      :show-overflow-tooltip="true" />
    <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible" />
    <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" v-if="columns[3].visible" />
    <el-table-column label="证件号码【籍贯】" width="180" align="center" v-if="columns[4].visible">
      <template #default="scope">
        <span>{{
          `${scope.row.clientIdcard == null ? "--" : scope.row.clientIdcard}` }}
          <br />
          {{ `【${scope.row.clientCensusRegister == null
            ? "未知"
            : scope.row.clientCensusRegister || "--"
            }】`
          }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="退案日期" width="120" align="center" key="returnCaseDate" prop="returnCaseDate"
      v-if="columns[5].visible" :show-overflow-tooltip="true" />
    <el-table-column label="留案时间" align="center" key="stayCaseTime" prop="stayCaseTime" v-if="columns[6].visible"
      :show-overflow-tooltip="true" />
    <el-table-column label="审核状态" align="center" key="examineState" prop="examineState" v-if="columns[7].visible">
      <template #default="scope">
        <el-button type="text" @click="selectProce(scope.row)">
          {{ scope.row.examineState }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column label="审核时间" width="150px" align="center" key="examineTime" prop="examineTime"
      v-if="columns[8].visible" :show-overflow-tooltip="true" />
    <el-table-column label="申请人" align="center" key="applicant" prop="applicant" v-if="columns[9].visible"
      :show-overflow-tooltip="true" />
    <el-table-column label="申请时间" width="120" align="center" key="applyDate" prop="applyDate" v-if="columns[10].visible"
      :show-overflow-tooltip="true" />
    <el-table-column label="申请原因" align="center" key="reason" prop="reason" v-if="columns[11].visible">
      <template #default="scope">
        <el-tooltip placement="top">
          <template #content>
            <p style="max-width: 300px">{{ scope.row.reason }}</p>
          </template>
          <div>
            <span>{{
              scope.row.reason?.length > 15
                ? `${scope.row.reason?.substring(0, 15)}...`
                : scope.row.reason
            }}</span>
          </div>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="100" fixed="right">
      <template #default="scope">
        <el-button type="text" v-if="
          scope.row.examineState == '待审核'
        " @click="revoke(scope.row)" v-hasPermi="['case:manage:recovery:stay']">
          撤销
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
    @pagination="getList" />

  <el-dialog v-model="showProceDialog" title="审批进度">
    <el-table :data="proceDateList">
      <el-table-column label="处理时间" align="center" key="approveTime" prop="approveTime" :show-overflow-tooltip="true" />
      <el-table-column label="处理人" width="120" align="center" key="reviewer" prop="reviewer"
        :show-overflow-tooltip="true" />
      <el-table-column label="处理状态" align="center" key="approveStart" prop="approveStart"
        :formatter="approveStartFor" />
      <el-table-column label="原因" align="center" key="refuseReason" prop="refuseReason" :show-overflow-tooltip="true">
        <template #default="scope">
          {{ scope.row.refuseReason == null ? "--" : scope.row.refuseReason }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import {
  examineStates,
  staycaseList,
  staycaseRevoke,
  staycaseProce,
} from "@/api/collection/myapply.js";
import { getBatchNums } from "@/api/common/common.js";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const router = useRouter();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    applicant: undefined,
    entrustingCaseBatchNum: undefined,
    updateTime: [],
    applyDate: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
  },
});
const rangfiles = ["updateTime", "applyDate", "entrustingCaseDate", "returnCaseDate"];
const batchs = ref([]); //委案批次号列表
const entrustingPartys = ref([]); //转让方选项
const repaymentModes = ref([]); //还款渠道
const repaymentTypes = ref([]); //还款类型
const examineStateTab = ref([]); //审核状态tab
const showSearch = ref(false);
const showProceDialog = ref(false); //审批流程对话框
const proceDateList = ref([]); //审批流程表数据
const activeTab = ref("待审核");
const repaymentType = ref("全部"); //还款状态
const { queryParams } = toRefs(data);
//选中的id集合
const ids = ref([]);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

const single = ref(true); //是否可操作
// 列显隐信息
const columns = ref([
  { "key": 0, "label": "案件ID", "visible": true },
  { "key": 1, "label": "产品类型", "visible": true },
  { "key": 2, "label": "姓名", "visible": true },
  { "key": 3, "label": "证件类型", "visible": true },
  { "key": 4, "label": "证件号码【籍贯】", "visible": true },
  { "key": 5, "label": "退案日期", "visible": true },
  { "key": 6, "label": "留案时间", "visible": true },
  { "key": 7, "label": "审核状态", "visible": true },
  { "key": 8, "label": "审核时间", "visible": true },
  { "key": 9, "label": "申请人", "visible": true },
  { "key": 10, "label": "申请时间", "visible": true },
  { "key": 11, "label": "申请原因", "visible": true }
]);

//获取列表数据
function getList() {
  queryParams.value.examineState =
    activeTab.value == "全部" ? undefined : activeTab.value;

  loading.value = true;
  staycaseList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取审核状态
function getExamineStateList() {
  examineStates().then((res) => {
    examineStateTab.value = res.data;
  });
}
getExamineStateList();

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    applicant: undefined,
    entrustingCaseBatchNum: undefined,
    updateTime: [],
    applyDate: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {

  const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 4: '已撤销', 5: '已退案关闭' }
  return stutasEnum[row.approveStart];
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
}

//表格行能否选择
function checkSelectable() {
  return true;
}

//操作栏
function handlesCommand(command) {
  command.func(command.data);
}

//撤销
function revoke(data) {
  let req = [data.id];
  revokeSubmit(req);
}

//批量撤销
function batchRevoke() {
  let req = ids.value;
  revokeSubmit(req);
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}



//提交撤销
function revokeSubmit(data) {
  proxy.$modal
    .confirm("此操作将撤销选中的申请, 是否继续?")
    .then(function () {
      loading.value = true;
      return staycaseRevoke(data);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功！");
    }).catch(() => {
      loading.value = false;
    });
}

//查看审批流程
function selectProce(data) {
  showProceDialog.value = true;
  let req = { applyId: data.id };
  staycaseProce(req).then((res) => {
    proceDateList.value = res.data;
  });
}

</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

// :deep(.el-table__header-wrapper .el-checkbox) {
//   display: none;
// }</style>
