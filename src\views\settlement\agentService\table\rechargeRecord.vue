<template>
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
        <el-form-item label="操作日期：" prop="operationDate">
            <el-date-picker
                v-model="queryParams.operationDate"
                value-format="YYYY-MM-DD"
                type="daterange"
                style="width: 240px"
            />
        </el-form-item>
    </el-form>
    <div class="text-center">
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div>
        <el-button v-if="checkPermi(['agentService:rechargeRecord:export'])" type="primary" @click="antiShake(handleExport)">导出明细</el-button>
    </div>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange" class="mt10">
        <el-table-column label="充值前金额（元）" align="center" prop="beforeAmount" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.beforeAmount) }}
            </template>
        </el-table-column>
        <el-table-column label="充值金额（元）" align="center" prop="billAmount" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.billAmount) }}
            </template>
        </el-table-column>
        <el-table-column label="充值后金额（元）" align="center" prop="afterAmount" show-overflow-tooltip>
            <template #default="{ row }">
                {{ numFilter(row.afterAmount) }}
            </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="operateDate" show-overflow-tooltip />
        <el-table-column label="备注" align="center" prop="notes" show-overflow-tooltip />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import { billList } from "@/api/settlement/agentService";
const { proxy } = getCurrentInstance();
const initData = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 当前月份
  const day = String(date.getDate()).padStart(2, '0'); // 当前日期
  const firstDay = `${year}-${month}-01`;
  const currentDate = `${year}-${month}-${day}`;
  return [firstDay, currentDate];
}
const total = ref(0);
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        operationDate: initData()
    },
});
const { queryParams } = toRefs(data);
const loading = ref(false);
const dataList = ref([]);
const rangeFields = [];
const selectedArr = ref([]);

const handleQuery = () => {
    queryParams.value.pageNum = 1;
    nextTick(() => {
        getList();
    })
};

const resetQuery = () => {
    proxy.$refs["queryRef"].resetFields();
    nextTick(() => {
        getList();
    })
}

const handleSelectionChange = (selection) => {
    selectedArr.value = selection;
}

const getList = async () => { 
    loading.value = true;
    const query = proxy.addFieldsRange(queryParams.value, rangeFields);
    if (query.operationDate && query.operationDate.length > 0) {
        query.beginOperateDate = query.operationDate[0] + ' 00:00:00'
        query.endOperateDate = query.operationDate[1] + ' 23:59:59'
        delete query.operationDate
    }
    try {
        const res = await billList(query);
        if (res.code == 200) {
            dataList.value = res.rows;
            total.value = res.total;
        }
    } catch(error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};
getList();

const handleExport = () => {
    const query = proxy.addFieldsRange(queryParams.value, rangeFields);
    if (query.operationDate && query.operationDate.length > 0) {
        query.beginOperateDate = query.operationDate[0] + ' 00:00:00'
        query.endOperateDate = query.operationDate[1] + ' 23:59:59'
        delete query.operationDate
    }
    proxy.downloadforjson(
        "/bill/export",
        query,
        `充值记录.xlsx`
    );
}
</script>

<style lang="scss" scoped></style>