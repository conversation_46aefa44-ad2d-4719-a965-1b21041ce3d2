<template>
  <el-dialog title="案件回款" v-model="open" :before-close="cancel" width="650px" append-to-body>
    <div style="min-height: 185px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="160px">
        <el-form-item label="还款类型" prop="repaymentType">
          <el-radio-group v-model="form.repaymentType">
            <el-radio label="结清还款">结清还款</el-radio>
            <el-radio label="部分还款">部分还款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="还款渠道" prop="repaymentMode">
          <el-select v-model="form.repaymentMode" @change="repaymentModeChoose" placeholder="请选择">
            <el-option v-for="item in repayOptions" :key="item.code" :label="item.info"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('转账人姓名') > -1" label="转账人姓名" prop="transferorName">
          <el-input v-model="form.transferorName" placeholder="请输入转账人姓名  " />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('对方开户机构') > -1" label="对方开户机构  " prop="ycAccountAgency">
          <el-input v-model="form.ycAccountAgency" placeholder="请输入对方开户机构  " />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('转账人账号') > -1" label="转账人账号" prop="bankCardNumber">
          <el-input v-model="form.bankCardNumber" onkeyup="value=value.replace(/[^0-9\*]|\s/g, '')"
            oninput="value=value.replace(/[^0-9\*]|\s/g, '')" placeholder="请输入转账人账号" />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('支付宝账号') > -1" label="支付宝账号" prop="alipayAccount">
          <el-input v-model="form.alipayAccount" placeholder="请输入支付宝账号" />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('交易流水号') > -1" label="交易流水号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入交易流水号" />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('贷方发生额/元(收入)') > -1" label="还款金额" prop="repaymentMoney">
          <el-input type="number" v-model="form.repaymentMoney" placeholder="请输入贷方发生额/元(收入)" />
        </el-form-item>
        <el-form-item v-if="mustfield.indexOf('交易时间') > -1" label="交易时间" prop="repaymentTime">
          <el-date-picker v-model="form.repaymentTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择交易时间" />
        </el-form-item>
        <el-form-item label="回款凭证" v-if="form?.voucherAppUpload == 0" prop="repaymentProof">
          <el-input v-show="false" v-model="form.repaymentProof" />
          <el-upload ref="uploadRef" :limit="1" accept=".jpg,.png" :headers="upload.headers" :action="upload.url"
            :file-list="fileList" :before-upload="handleFileUploadBefore" :before-remove="remove"
            :on-success="handleFileSuccess" :auto-upload="false">
            <template #trigger>
              <el-button type="primary">选取文件</el-button>
            </template>
            <el-button class="ml10" type="success" @click="submitFile">上传到服务器</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {
  selectRepaymentSetup,
  getRegisterPayment,
  insertPayment,
} from "@/api/caseDetail/payment";
const { proxy } = getCurrentInstance();
const route = useRoute();
const emit = defineEmits(["getcaseDetailInfo"]);

const open = ref(false);
const loading = ref(false);
const data = reactive({
  form: { voucherAppUpload: 0 },
  rules: {
    repaymentType: [{ required: true, message: "请选择还款类型！", trigger: "blur" }],
    repaymentMode: [{ required: true, message: "请选择还款渠道！", trigger: "change" }],
    transferorName: [
      { required: true, message: "请输入转账人姓名！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "请不要输入空格",
        trigger: "blur",
      },
    ],
    bankCardNumber: [
      { required: true, message: "请输入转账人账号！", trigger: "blur" },
      {
        required: true,
        pattern: /^[0-9\*]+$/,
        message: "请输入数字和*的符号",
        trigger: "blur",
      },
    ],
    ycAccountAgency: [
      { required: true, message: "请输入对方开户机构！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "请不要输入空格",
        trigger: "blur",
      },
    ],
    alipayAccount: [
      { required: true, message: "请输入支付宝账号！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "请不要输入空格",
        trigger: "blur",
      },
    ],
    orderNo: [
      { required: true, message: "请输入交易流水号！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "请不要输入空格",
        trigger: "blur",
      },
    ],
    repaymentMoney: [
      { required: true, message: "请输入贷方发生额/元(收入)！", trigger: "blur" },
      {
        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
        message: "请输⼊正确的格式,可保留两位⼩数",
      },
    ],
    repaymentTime: [{ required: true, message: "请选择交易时间！", trigger: "change" }],
    repaymentProof: [{ required: true, message: "请上传回款凭证！", trigger: "change" }],
    voucherAppUpload: [
      { required: true, message: "请选择凭证小程序上传！", trigger: "blur" },
    ],
  },
});
const { form, rules } = toRefs(data);

const repayOptions = ref([]); //还款渠道下拉
const mustfield = ref([]); //还款渠道的必填字段
const fileList = ref([])
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/collection/uploadPayment",
});

//打开弹窗
function opendialog() {
  reset();
  selectRepaymentSetup().then((res) => {
    repayOptions.value = res.data;
    open.value = true;
  });
}

//还款渠道选择，获取必填字段
function repaymentModeChoose(val) {
  let notClear = ["repaymentMode", "repaymentType", "voucherAppUpload"]; //不清空字段
  Object.keys(form.value).map((key) => {
    if (notClear.indexOf(key) === -1) {
      form.value[key] = undefined;
    }
  });
  proxy.$refs["uploadRef"].clearFiles();

  // let id = undefined;
  // for (let i = 0; i < repayOptions.value.length; i++) {
  //   const item = repayOptions.value[i];
  //   if (item.repaymentMethod == val) {
  //     id = item.id;
  //     break;
  //   }
  // }
  getRegisterPayment(val).then((res) => {
      mustfield.value = res.data;
  });
}

//文件上传前
function handleFileUploadBefore(file) {
  if (file?.type != "image/png" && file?.type != "image/jpg" && file?.type != "image/jpeg") {
    proxy.$modal.msgWarning("请上传png或jpg格式文件！");
    return false;
  }
}

//文件移除
function remove(file,uploadFiles) {
  form.value.repaymentProof = undefined;
  fileList.value = uploadFiles
}

//上传成功时
function handleFileSuccess(response, file, uploadFiles) {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.repaymentProof = data.fileUrl[0];
    fileList.value = uploadFiles
  }
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      form.value.repaymentMoney = parseFloat(form.value.repaymentMoney);
      form.value.caseId = route.params.caseId;
      const reqForm = JSON.parse(JSON.stringify(form.value));
      const isVoucherAppUpload = mustfield.value.some((item) =>
        item.includes("凭证小程序上传")
      );
      if (!isVoucherAppUpload) {
        delete reqForm.voucherAppUpload;
      }
      insertPayment(reqForm)
        .then(() => {
          proxy.$modal.msgSuccess("操作成功!");
          emit("getcaseDetailInfo");
          cancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    repaymentType: undefined,
    repaymentMode: undefined,
    transferorName: undefined,
    accountNumberFront: undefined,
    accountNumberBehind: undefined,
    alipayAccount: undefined,
    orderNo: undefined,
    repaymentMoney: undefined,
    repaymentTime: undefined,
    repaymentProof: undefined,
    voucherAppUpload: 0,
  };
  mustfield.value = [];
  fileList.value = []
  proxy.$refs["uploadRef"] && proxy.$refs["uploadRef"].clearFiles();
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.note {
  font-size: 13px;
  color: #2a82e4;
  line-height: 20px;
}

:deep(.el-upload-list__item .el-progress__text) {
  top: -20px;
}

:deep(.el-upload-list__item-name) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 70%;
}

:deep(.el-upload-list__item .el-progress__text) {
  text-align: right;
  width: 65px;

  span {
    width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
  }
}

:deep(.el-upload-list__item),
:deep(.el-upload-list) {
  max-width: 400px;
}
</style>
