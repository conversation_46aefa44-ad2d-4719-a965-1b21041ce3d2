import request from '@/utils/request'

//获取列表
// export function getOftenTpl(query) {
//     return request({
//         url: '/asset/currencyTemplates/list',
//         method: 'get',
//         params: query
//     })
// }
export function getOftenTpl(query) {
    return request({
        url: '/teamTemplates/list',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//获取常用模板options
export function getOftenTplOptions() {
    return request({
        url: '/teamTemplates/getCurrencyTemplates',
        method: 'get',
		gateway: 'cis'
    })
}

//修改状态
export function updateState(data) {
    return request({
        url: '/teamTemplates/updateState',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//获取模板信息
// export function getTplinfo(id) {
//     return request({
//         url: '/asset/currencyTemplates/get',
//         method: 'get',
//         params: {id: id}
//     })
// }
export function getTplinfo(id) {
    return request({
        url: '/teamTemplates/get',
        method: 'get',
        params: {id: id},
		gateway: 'cis'
    })
}

//添加模板
// export function addOftenTpl(data) {
//     return request({
//         url: '/asset/currencyTemplates/add',
//         method: 'post',
//         data: data
//     })
// }
export function addOftenTpl(data) {
    return request({
        url: '/teamTemplates/add',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}


//编辑模板
// export function editOftenTpl(data) {
//     return request({
//         url: '/asset/currencyTemplates/edit',
//         method: 'post',
//         data: data
//     })
// }
export function editOftenTpl(data) {
    return request({
        url: '/teamTemplates/edit',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

