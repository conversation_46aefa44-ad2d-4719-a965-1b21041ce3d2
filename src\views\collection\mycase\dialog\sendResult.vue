<template>
  <el-dialog
    title="发送结果"
    v-model="open"
    width="500px"
    append-to-body
    :before-close="cancel"
  >
    <div class="send-result">
      <div class="success">
        <span class="tip-icon"
          ><el-icon :size="24" color="#d81e06"><WarningFilled /></el-icon
        ></span>
        <span class="tip-text">本次已提交发送：{{ result?.success }}条</span>
      </div>
      <div class="error">
        提交发送失败：{{ Number(result?.invalid || 0) + Number(result?.exceeded || 0) }}条
      </div>
      <div class="error-resaon" v-if="result?.invalid > 0 || result?.exceeded > 0">
        失败原因：<span v-if="result?.exceeded > 0">{{
          `其中${result?.exceeded}条已超过今日发送次数`
        }}</span
        ><span v-if="result?.invalid > 0 && result?.exceeded > 0">,</span
        ><span v-if="result?.invalid > 0">{{
          `其中${result?.invalid}条手机号码无效；`
        }}</span>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button @click="cancel">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
const { proxy } = getCurrentInstance();
const router = useRouter();
const open = ref(false);
const result = ref({
  success: 0,
  invalid: 0,
  reason: undefined,
});

// 打开
function opendialog(data) {
  open.value = true;
  result.value = data;
}

// 关闭
function cancel() {
  proxy.resetForm("formRef");
  open.value = false;
  result.value = {
    success: 0,
    invalid: 0,
    reason: undefined,
  };
}

defineExpose({ opendialog });

</script>
<style lang="scss" scoped>
.send-result {
  .success,
  .error,
  .error-resaon {
    line-height: 24px;
    font-size: 16px;
  }
  .error,
  .error-resaon {
    margin-left: 34px;
    line-height: 30px;
  }
  .tip-text {
    margin-left: 10px;
    vertical-align: top;
  }
}
</style>
