import request from '@/utils/request'

//部门审批设置列表
export function selectApprovalSettings(query) {
  return request({
    url: '/settings/selectApprovalSettings',
    method: 'get',
  })
}

//部门审批角色
export function selectRole(query) {
  return request({
    url: '/settings/selectRoles',
    method: 'get',
    params:query,
  })
}

//部门员工查询
export function selectEmployeesRole(query) {
  return request({
    url: '/settings/selectEmployeesRoles',
    method: 'get',
    params:query,
  })
}

//修改新增删除
export function updateApprovalSteps(data) {
  return request({
    url: '/settings/updateApprovalSteps',
    method: 'post',
    data: data
  })
}

//查询审批具体数据
export function selectApprovalSteps(query) {
  return request({
    url: '/settings/selectApprovalSteps',
    method: 'get',
    params:query,
  })
}


