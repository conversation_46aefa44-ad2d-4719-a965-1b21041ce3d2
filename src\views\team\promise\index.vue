<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container mb20" style="color:#888888;">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          组织架构
        </div>
        <el-input class="mb8" v-model="filterText" placeholder="请输入关键字" />
        <el-tree ref="treeRef" class="filter-tree" :data="tree" :props="defaultProps" :expand-on-click-node="false"
          check-on-click-node :render-after-expand="false" node-key="id" @node-click="getNode"
          :filter-node-method="filterNode">
          <template #default="{ data }">
            <el-tooltip effect="light" :content="data.name" placement="right-start">
              <span class="el-tree-node__label">{{ data.name }}</span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" :label-width="100"
          :rules="rules" class="form-content h-50" :class="{ 'h-auto': showSearch }">
          <el-form-item label="案件ID">
            <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="姓名" prop="clientName">
            <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="手机号码" prop="clientPhone">
            <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable type="number"
              style="width: 240px" @keyup.enter="antiShake(handleQuery)" @input="handleInput('clientPhone', $event)" />
          </el-form-item>
          <el-form-item label="证件号码" prop="clientIdcard">
            <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" @input="handleInput('clientIdcard', $event)" />
          </el-form-item>
          <el-form-item label="转让方" prop="entrustingPartyId">
            <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
              :reserve-keyword="false" @focus="OwnerList" style="width: 240px">
              <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="承诺金额" style="width: 260px">
            <el-row>
              <el-col :span="11" style="width: 120px">
                <el-input type="number" v-model="queryParams.promiseRepaymentMoney1" clearable />
              </el-col>
              <el-col :span="2" class="text-center">
                <span>-</span>
              </el-col>
              <el-col :span="11" style="width: 120px">
                <el-input type="number" v-model="queryParams.promiseRepaymentMoney2" clearable />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="账期">
            <el-input v-model="queryParams.accountPeriod" placeholder="请输入账期" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item label="处置人员">
            <el-input v-model="queryParams.odvName" placeholder="请输入处置人员" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <el-row class="mb10 mt10">
          <el-button plain :disabled="single" @click="opendialog(0, selectedArr)"
            v-hasPermi="['saasc:teamCase:insertRetreatTeamPromise']">申请退案</el-button>
          <el-button plain :disabled="single" type="primary" @click="opendialog(1, selectedArr)"
            v-hasPermi="['saasc:teamCase:insertKeepTeamPromise']">申请留案</el-button>
          <!-- <el-button plain :disabled="single" type="success" @click="toHelp(selectedArr)"
            v-hasPermi="['saasc:collection:insertAssistRecordTeamPromise']">申请协案</el-button> -->
          <el-button plain :disabled="single" type="primary" @click="handleExport"
            v-hasPermi="['saasc:teamCase:exportTeamPromise']">导出</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" ref="multipleTableRef" :data="caseList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" :width="120"
            v-if="columns[0].visible">
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: center">
                <el-tooltip v-if="scope.row.labelContent" placement="top">
                  <template #content>{{ scope.row.labelContent }}</template>
                  <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
                </el-tooltip>
                <span style="color:#409eff;cursor: pointer;" type="text" v-if="scope.row.button == 1"
                  @click="toDetails(scope.row.caseId, scope.$index)">{{
                    scope.row.caseId
                  }}</span>
                <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="处置人员" align="center" key="odvName" prop="odvName" :width="110"
            v-if="columns[1].visible" />
          <el-table-column label="姓名" align="center" key="clientName" prop="clientName" :width="120"
            v-if="columns[2].visible" />
          <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" :width="120"
            v-if="columns[3].visible" />
          <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" :width="120"
            v-if="columns[4].visible" />
          <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" :width="180"
            v-if="columns[5].visible" />
          <el-table-column label="账期" align="center" key="accountPeriod" prop="accountPeriod" :width="120"
            v-if="columns[6].visible" />
          <el-table-column label="承诺还款信息" align="center" key="remarks" prop="remarks" :width="180"
            v-if="columns[7].visible" />
          <el-table-column label="转让方" align="center" key="entrustingPartyName" prop="entrustingPartyName" :width="120"
            v-if="columns[8].visible" />
          <el-table-column fixed="right" width="250" label="操作">
            <template #default="scope">
              <!-- <el-button type="text" @click="toHelp([scope.row])" v-if="scope.row.button == 1"
                v-hasPermi="['saasc:collection:insertAssistRecordTeamPromise']">申请协案</el-button> -->
              <el-button type="text" @click="opendialog(1, [scope.row])" v-if="scope.row.button == 1"
                v-hasPermi="['saasc:teamCase:insertKeepTeamPromise']">申请留案</el-button>
              <span v-if="scope.row.button != 1">--</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <!-- 退案 -->
    <insertRetreat @getList="getList" ref="insertRetreatRef" />

    <!-- 申请协催 -->
    <helpUrgeVue ref="helpUrgeRef" />
  </div>
</template>
<script setup name="Teampromise">
import insertRetreat from "./dialog/insertRetreat.vue";
import {
  getBatchNums,
  selectAssetOwner,
} from "@/api/common/common";
import { getTeamTree } from "@/api/team/team";
import { selectUrgeRecordId } from "@/api/team/promise";
import helpUrgeVue from "../../collection/mycase/dialog/helpUrge.vue";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//树结构
const tree = ref([]);
const filterText = ref('');
const defaultProps = {
  children: "children",
  label: "name",
};
const allDept = ref([
  { id: "all", name: "全部" }
])
//表单配置信息
const showSearch = ref(false);
//表单数据集合
const batchs = ref([]);
const entrusts = ref([]);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
const multipleTableRef = ref();
//表格数据
const caseList = ref([]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    promiseRepaymentMoney1: undefined,
    promiseRepaymentMoney2: undefined,
    clientPhone: undefined,
    accountPeriod: undefined,
    odvName: undefined,
    stringDeptId: undefined,
  },
  rules: {
    clientPhone: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
    ],
    clientIdcard: [
      { required: false, message: "请输入证件号码", trigger: "blur" },
    ],
  },
});
const { queryParams, rules } = toRefs(data);
// 列显隐信息
const columns = ref([
  { key: 0, label: `案件ID`, visible: true },
  { key: 1, label: `处置人员`, visible: true },
  { key: 2, label: `姓名`, visible: true },
  { key: 3, label: `手机号码`, visible: true },
  { key: 4, label: `证件号码`, visible: true },
  { key: 5, label: `账期`, visible: true },
  { key: 6, label: `承诺还款信息`, visible: true },
  { key: 7, label: `转让方`, visible: true },
  { key: 8, label: `证件类型`, visible: true },
]);

function handleInput(key, value) {
  if (value == '') {
    queryParams.value[key] = undefined;
  }
}

//获取数据
function getList() {
  loading.value = true;
  selectUrgeRecordId(queryParams.value)
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//获取部门列表
function getTeamTreeData() {
  getTeamTree().then((res) => {
    let formatterDept = [...allDept.value, ...res.data];
    tree.value = formatterDept;
  });
}
getTeamTreeData();

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//获取转让方
function OwnerList() {
  selectAssetOwner().then((res) => {
    entrusts.value = res.data;
  });
}

//节点点击事件
function getNode(node, details) {
  queryParams.value.stringDeptId = node?.id == "all" ? undefined : node.id;
  getList();
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//机构过滤
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.indexOf(value) !== -1
}
//搜索部门
watch(filterText, (val) => {
  proxy.$refs["treeRef"].filter(val);
})

//表格行能否选择
function checkSelectable() {
  return true;
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    promiseRepaymentMoney1: undefined,
    promiseRepaymentMoney2: undefined,
    clientPhone: undefined,
    odvName: undefined,
    accountPeriod: undefined,
  };
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = JSON.parse(JSON.stringify(queryParams.value));
  queryChange.pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/teamIndex-detail/teamCaseDetails/${caseId}`, query: { type: "caseManage" } });
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//退案，留案操作
function opendialog(type, row) {
  let query = JSON.parse(JSON.stringify(queryParams.value))
  query.caseIds = row.map((item) => item.caseId);
  query.pageNum = undefined;
  query.pageSize = undefined;
  query.condition = false;
  proxy.$refs["insertRetreatRef"].opendialog(type, query);
}

//协催
function toHelp(row) {
  let data = {
    condition: false,
    caseIds: row.map((item) => item.caseId),
  };
  proxy.$refs["helpUrgeRef"].opendialog(data);
}

function getQuery() {
  let query = JSON.parse(JSON.stringify(queryParams.value))
  query.caseIds = selectedArr.value.map((item) => item.caseId);
  query.pageNum = undefined;
  query.pageSize = undefined;
  query.condition = false;
  return query;
}

function handleExport() {
  let query = getQuery();
  proxy.downloadforjson(
    "/teamCase/exportUrgeRecordId",
    query,
    `团队承诺户_${new Date().getTime()}.xlsx`,
  );
}

//监听选择发生改变
watch(caseList, (newval, preval) => {
  multipleTableRef.value.toggleAllSelection();
});

</script>
<style lang="scss" scoped>
.form-content {
  .el-form-item {
    width: 30% !important;

    .el-select .el-select__tags .el-tag--info {
      max-width: 100px;
      overflow: hidden;
    }
  }
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

:deep(.el-tree-node__label) {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
