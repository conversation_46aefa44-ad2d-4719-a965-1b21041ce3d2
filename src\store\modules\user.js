import { login, logout, getInfo, signGetInfo, smsLogin } from '@/api/login'
import { getToken, setToken, removeToken, getName } from '@/utils/auth'
import defAva from '@/assets/logo/logo.png'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(res => {
          if (!res.data?.securityVerification && res.data.access_token) {
            setToken(res.data.access_token)
            commit('SET_TOKEN', res.data)
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    //验证码登录
    SmsLogin({ commit }, userInfo) {
      const uid = userInfo.uid
      const code = userInfo.smsCode
      return new Promise((resolve, reject) => {
        smsLogin({ code, uid }).then(res => {
          let data = res.data
          setToken(data.access_token)
          commit('SET_TOKEN', data.access_token)
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        commit('SET_ROLES', ["admin"]);
        commit('SET_PERMISSIONS', ["*:*:*"]);
        resolve(true)
        const avatar = !user.avatar || user.avatar == "" ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;
        signGetInfo().then(res => {
          commit('SET_PERMISSIONS', res.permissions);
        });
        //   const user = res.user
        //   if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
        //     commit('SET_ROLES', res.roles)
        //     commit('SET_PERMISSIONS', res.permissions)
        //   } else {
        //     commit('SET_ROLES', ['ROLE_DEFAULT'])
        //   }
        const username = getName();
        commit('SET_NAME', username)
        commit('SET_AVATAR', avatar)
        //   resolve(res)
        // }).catch(error => {
        //   reject(error)
        // })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        resolve()
        // logout(state.token).then(() => {
        //   commit('SET_TOKEN', '')
        //   commit('SET_ROLES', [])
        //   commit('SET_PERMISSIONS', [])
        //   removeToken()
        //   resolve()
        // }).catch(error => {
        //   reject(error)
        // })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
