import request from "@/utils/request";

// 团队审批列表
export const getApprovalList = (data) => {
  return request({
    url: "/zws_jg_approve/queryJgApproveList",
    method: "post",
    data,
  });
};

// 同意
export const agreeApproval = (data) => {
  return request({
    url: "/zws_jg_approve/jgApprovePass",
    method: "post",
    data,
  });
};

// 不同意
export const notPassApproval = (data) => {
  return request({
    url: "/zws_jg_approve/jgApproveNotPass",
    method: "post",
    data,
  });
};

// 我的申请列表
export const getMyApplyList = (data) => {
  return request({
    url: "/zws_jg_approve/queryJgMyApplyList",
    method: "post",
    data,
  });
};
// 团队申请列表
export const getTeamApplyList = (data) => {
  return request({
    url: "/zws_jg_approve/queryJgTeamApplyList",
    method: "post",
    data,
  });
};

// 撤销申请
export const revokeApplyApi = (data) => {
  return request({
    url: "/zws_jg_approve/jgApproveRevoke",
    method: "post",
    data,
  });
};