import request from '@/utils/request';

//机构回款
export function selectRepaymentRecordId(query) {
    return request({
        url: '/teamCase/selectRepaymentRecordId',
        method: 'get',
        params: query
    })
  }

//机构减免
export function selectReductionRecordId(query) {
    return request({
        url: '/teamCase/selectReductionRecordId',
        method: 'get',
        params: query
    })
  }

  //机构分期
export function selectStagingRecordId(query) {
    return request({
        url: '/teamCase/selectStagingRecordId',
        method: 'get',
        params: query
    })
  }

//机构留案
export function selectApplyRecordKeepCase(query) {
    return request({
        url: '/teamCase/selectApplyRecordKeepCase',
        method: 'get',
        params: query
    })
  }

  //机构停催
export function selectApplyRecordStopUrging(query) {
    return request({
        url: '/teamCase/selectApplyRecordStopUrging',
        method: 'get',
        params: query
    })
  }

//机构退案
export function selectApplyRecordWithdrawal(query) {
    return request({
        url: '/teamCase/selectApplyRecordWithdrawal',
        method: 'get',
        params: query
    })
  }

  //机构外访
export function selectOutsideRecordId(query) {
    return request({
        url: '/teamCase/selectOutsideRecordId',
        method: 'get',
        params: query
    })
  }

  //机构协催
  export function selectAssistRecordId(query) {
    return request({
        url: '/teamCase/selectAssistRecordId',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (回款各状态数量)
  export function selectRepaymentRecordIdNumber(query) {
    return request({
        url: '/teamCase/selectRepaymentRecordIdNumber',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (减免各状态数量)
  export function selectReductionRecordIdNumber(query) {
    return request({
        url: '/teamCase/selectReductionRecordIdNumber',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (分期各状态数量)
  export function selectStagingRecordIdNumber(query) {
    return request({
        url: '/teamCase/selectStagingRecordIdNumber',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (停催各状态数量)
  export function selectApplyRecordIdStopNumber(query) {
    return request({
        url: '/teamCase/selectApplyRecordIdStopNumber',
        method: 'get',
        params: query
    })
  }

   //根据字段条件查询 (留案各状态数量)
   export function selectApplyRecordIdKeepNumber(query) {
    return request({
        url: '/teamCase/selectApplyRecordIdKeepNumber',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (退案各状态数量)
  export function selectApplyRecordIdWithdrawalNumber(query) {
    return request({
        url: '/teamCase/selectApplyRecordIdWithdrawalNumber',
        method: 'get',
        params: query
    })
  }

   //根据字段条件查询 (外访各状态数量)
  export function selectOutsideRecordIdNumber(query) {
    return request({
        url: '/teamCase/selectOutsideRecordIdNumber',
        method: 'get',
        params: query
    })
  }

  //根据id查询资料调取记录
  export function selectRetrievalRecordId(query) {
    return request({
        url: '/teamCase/selectRetrievalRecordId',
        method: 'get',
        params: query
    })
  }

  //根据字段条件查询 (资料各状态数量)
  export function selectRetrievalRecordIdNumber(query) {
    return request({
        url: '/teamCase/selectRetrievalRecordIdNumber',
        method: 'get',
        params: query
    })
  }


