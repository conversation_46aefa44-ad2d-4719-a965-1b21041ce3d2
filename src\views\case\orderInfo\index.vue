<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-position="right"
      :label-width="130"
      class="form-content h-50"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="案件号">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件号"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="客户姓名" prop="customName">
        <el-input
          v-model="queryParams.customName"
          placeholder="请输入客户姓名"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="所属战队/部门" prop="belongDept">
        <tree-select
          v-model:value="queryParams.belongDept"
          :options="deptOptionsCom"
          placeholder="请选择所属战队/部门"
          :objMap="{
            value: 'id',
            label: 'name',
            disabled: 'disabled',
            children: 'children',
          }"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="出单日期" prop="fillDate">
        <el-date-picker
          v-model="queryParams.fillDate"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="调解员名称" prop="mediator">
        <el-input
          v-model="queryParams.mediator"
          placeholder="请输入调解员名称"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="是否结清" prop="settlement">
        <el-select
          v-model="queryParams.settlement"
          clearable
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="一次性结清" value="一次性结清"></el-option>
          <el-option label="分期" value="分期"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户属性" prop="customAttribute">
        <el-select
          v-model="queryParams.customAttribute"
          clearable
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="短信+工单回复" value="短信+工单回复"></el-option>
          <el-option label="自流水" value="自流水"></el-option>
          <el-option label="电话" value="电话"></el-option>
          <el-option label="上期留案" value="上期留案"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row :gutter="10" class="mt10 mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['case:orderInfo:add']"
          type="primary"
          plain
          icon="Plus"
          @click="openHandleOrder('add', null)"
          >添加订单</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['case:orderInfo:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column
        label="填单日期"
        align="center"
        key="fillDate"
        prop="fillDate"
        v-if="columns[0].visible"
        width="110"
      />
      <el-table-column
        label="合同号"
        align="center"
        key="contractNo"
        prop="contractNo"
        v-if="columns[1].visible"
        width="110"
      />
      <el-table-column
        label="客户名称"
        align="center"
        key="customName"
        prop="customName"
        v-if="columns[2].visible"
      />
      <el-table-column
        label="调解员"
        align="center"
        key="mediator"
        prop="mediator"
        v-if="columns[3].visible"
      />
      <el-table-column
        label="所属战队"
        align="center"
        key="belongDeptName"
        prop="belongDeptName"
        v-if="columns[4].visible"
      />
      <el-table-column
        label="项目名称"
        align="center"
        key="projectName"
        prop="projectName"
        v-if="columns[5].visible"
        width="110"
      />
      <el-table-column
        label="产品名称"
        align="center"
        key="productName"
        prop="productName"
        v-if="columns[6].visible"
        width="110"
      />
      <el-table-column
        label="案件时间"
        align="center"
        key="caseDate"
        prop="caseDate"
        v-if="columns[7].visible"
        width="110"
      />
      <el-table-column
        label="客户属性"
        align="center"
        key="customAttribute"
        prop="customAttribute"
        v-if="columns[8].visible"
      />
      <el-table-column
        label="还款方式"
        align="center"
        key="repaymentMethod"
        prop="repaymentMethod"
        v-if="columns[9].visible"
      />
      <el-table-column
        label="是否结清"
        align="center"
        key="settlement"
        prop="settlement"
        v-if="columns[10].visible"
        width="120"
      />
      <el-table-column
        label="已还款金额"
        align="center"
        key="repayment"
        prop="repayment"
        width="100"
        v-if="columns[11].visible"
      />
      <el-table-column
        label="还款日期"
        align="center"
        key="repaymentDate"
        prop="repaymentDate"
        v-if="columns[12].visible"
        width="110"
      />
      <el-table-column
        label="垫资"
        align="center"
        key="advance"
        prop="advance"
        v-if="columns[13].visible"
      />
      <el-table-column
        label="系数"
        align="center"
        key="ratio"
        prop="ratio"
        :formatter="(row) => { return row.ratio || '--' }"
        v-if="columns[14].visible"
      />
      <el-table-column
        label="创佣"
        align="center"
        key="createCommission"
        prop="createCommission"
        :formatter="(row) => { return row.createCommission || '--' }"
        v-if="columns[15].visible"
      />
      <el-table-column
        label="特殊创佣"
        align="center"
        key="specialCommission"
        prop="specialCommission"
        v-if="columns[16].visible"
      />
      <el-table-column
        label="业绩归属月份"
        align="center"
        key="performanceMonth"
        prop="performanceMonth"
        width="110"
        v-if="columns[17].visible"
      />
      <el-table-column label="操作" align="center" fixed="right" width="150">
        <template #default="{ row }">
          <el-button
            v-hasPermi="['case:orderInfo:locklock']"
            type="text"
            @click="openHandleOrder('view', row)"
          >
            查看
          </el-button>
          <el-button
            v-hasPermi="['case:orderInfo:edit']"
            type="text"
            @click="openHandleOrder('edit', row)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['case:orderInfo:del']"
            type="text"
            @click="handleDeleteOrder(row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/查看/编辑订单 -->
    <handleOrder ref="handleOrderRef" @getList="getList" />
  </div>
</template>

<script setup name="OrderInfo">
import { deptTree } from "@/api/system/dept";
import {
  orderList,
  orderDetail,
  deleteOrder,
} from "@/api/case/orderInfo/orderInfo";
import handleOrder from "./dialog/handleOrder.vue";

const { proxy } = getCurrentInstance();

const showSearch = ref(false);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    customName: undefined,
    belongDept: undefined,
    fillDate: undefined,
    mediator: undefined,
    settlement: undefined,
    customAttribute: undefined,
  },
});
const reqForm = ref({});
const { queryParams } = toRefs(data);

const deptOptionsCom = ref([]);

const columns = ref([
  { key: 0, label: "填单日期", visible: true },
  { key: 1, label: "合同号", visible: true },
  { key: 2, label: "客户名称", visible: true },
  { key: 3, label: "调解员", visible: true },
  { key: 4, label: "所属战队", visible: true },
  { key: 5, label: "项目名称", visible: true },
  { key: 6, label: "产品名称", visible: true },
  { key: 7, label: "案件时间", visible: true },
  { key: 8, label: "客户属性", visible: true },
  { key: 9, label: "还款方式", visible: true },
  { key: 10, label: "是否结清", visible: true },
  { key: 11, label: "已还款金额", visible: true },
  { key: 12, label: "还款日期", visible: true },
  { key: 13, label: "垫资", visible: true },
  { key: 14, label: "系数", visible: true },
  { key: 15, label: "创佣", visible: true },
  { key: 16, label: "特殊创佣", visible: true },
  { key: 17, label: "业绩归属月份", visible: true },
]);
const dataList = ref([]);
const total = ref(0);
const loading = ref(false);
const multiple = ref(true);

//部门数据处理(增加层级)
function deptOptionsHandle(data) {
  function handle(data, ranks) {
    let rank = ranks || 0;
    data.map((item) => {
      item.rank = rank;
      if (item.children && item.children.length !== 0) {
        handle(item.children, rank + 1);
      }
    });
  }
  handle(data);
  return data;
}

//获取树结构
function getDeptTree() {
  deptTree().then((res) => {
    deptOptionsCom.value = deptOptionsHandle(res.data);
  });
}
getDeptTree();

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
function getList() {
  loading.value = true;
  reqForm.value = JSON.parse(JSON.stringify(queryParams.value));
  if (reqForm.value.fillDate) {
    Object.assign(reqForm.value, {
      fillDateBegin: reqForm.value.fillDate[0],
      fillDateEnd: reqForm.value.fillDate[1],
    });
    delete reqForm.value.fillDate;
  }
  if (reqForm.value.belongDept) {
    const deptId = reqForm.value.belongDept.split(":")[1];
    Object.assign(reqForm.value, {
      belongDept: deptId || undefined,
    });
    
  }
  orderList(reqForm.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    customName: undefined,
    belongDept: undefined,
    addTime: undefined,
    mediator: undefined,
    settlement: undefined,
    customAttribute: undefined,
  };
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  multiple.value = selection.length === 0;
}

// 添加/查看/编辑订单
function openHandleOrder(type, row) {
  proxy.$refs.handleOrderRef.opendialog(type, row);
}

// 删除订单
function handleDeleteOrder(id) {
  proxy.$modal
    .confirm(`确定要删除该订单吗？`)
    .then(() => {
      return deleteOrder([id]);
    })
    .then(() => {
      proxy.$modal.msgSuccess("删除成功");
      getList();
    });
}

// 导出
function handleExport() {
  proxy.downloadforjsonGet(
    "/projectOrderInfo/export",
    reqForm.value,
    `订单信息列表.xlsx`
  );
}
</script>

<style scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
