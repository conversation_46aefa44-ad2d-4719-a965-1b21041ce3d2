<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="side-edge">
        <div class="head-container mb10 pl20">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          团队判决排名
        </div>
        <div class="dept-list">
          <div class="dept-item" v-for="dept in allDept" :key="dept.id">
            <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
              {{ `${dept.name}(${dept.caseNum || 0})` }}
            </div>
          </div>
        </div>
      </el-col>

      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" :loading="loading" ref="queryRef"
          :class="`${showSearch ? 'form-auto' : 'form-h50'}`" inline label-width="110px">
          <el-form-item prop="caseId" label="案件ID">
            <el-input v-model="queryParams.caseId" style="width: 240px" placeholder="请输入案件ID" />
          </el-form-item>
          <el-form-item prop="clientName" label="被告">
            <el-input v-model="queryParams.clientName" style="width: 240px" placeholder="请输入被告" />
          </el-form-item>
          <el-form-item prop="court" label="保全法院">
            <el-select placeholder="请选择保全法院" style="width: 240px" v-model="queryParams.court" @focus="getclosed">
              <el-option v-for="item in courtOptions" :key="item.code" :label="item.info" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item prop="startDate" label="保全申请时间">
            <el-date-picker style="width: 240px" v-model="queryParams.startDate" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" unlink-panels />
          </el-form-item>
          <el-form-item label="申请保全金额">
            <div class="range-scope" style="width:240px">
              <el-input v-model="queryParams.applyAmount1" />
              <span>-</span>
              <el-input v-model="queryParams.applyAmount2" />
            </div>
          </el-form-item>
          <el-form-item label="实际保全金额">
            <div class="range-scope" style="width:240px">
              <el-input v-model="queryParams.actualAmount1" />
              <span>-</span>
              <el-input v-model="queryParams.actualAmount2" />
            </div>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <el-tabs v-model="activeTab" @tab-click="antiShake(tabChange)">
          <el-tab-pane label="材料提交" name="材料提交" />
          <el-tab-pane label="待缴纳保证金" name="待缴纳保证金" />
          <el-tab-pane label="已缴纳保证金" name="已缴纳保证金" />
          <el-tab-pane label="已完成财产保全" name="已财产保全" />
          <el-tab-pane label="全部" name="全部" />
        </el-tabs>
        <selectedAll v-model:allQuery="queryParams.allQuery" :cusTableRef="proxy.$refs.multipleTableRef"
          :selectedArr="selectedArr" :dataList="dataList">
          <template #content>
            <div class="text-flex ml20">
              <span class="case-data-list">
                案件数量：<i class="danger">{{ statistics.caseNum || 0 }}</i>
              </span>
              <span class="case-data-list">
                标的额：<i class="danger">{{ moneyFor(statistics.totalMoney) || 0 }}</i>
              </span>
            </div>
          </template>
        </selectedAll>
        <el-table v-loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" :data="dataList">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column v-if="columns[0].visible" label="案件ID" align="center" key="caseId" prop="caseId" width="120">
            <template #default="{ row, $index }">
              <el-button type="text" @click="toDetails(row, $index)">{{ row.caseId }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="被告" align="center" key="clientName" prop="clientName"
            :width="90" />
          <el-table-column v-if="columns[2].visible" label="身份证号码" align="center" key="clientIdcard" prop="clientIdcard"
            :width="180" />
          <el-table-column v-if="columns[3].visible" label="户籍地" align="center" key="clientCensusRegister"
            prop="clientCensusRegister" show-overflow-tooltip :width="160" />
          <el-table-column v-if="columns[4].visible" label="手机号码" align="center" key="clientPhone" prop="clientPhone"
            :width="120" />
          <el-table-column v-if="columns[5].visible" label="保全状态" align="center" key="saveStage" prop="saveStage"
            :width="130" />
          <el-table-column v-if="columns[6].visible" label="申请保全金额" align="center" key="freezeAmount"
            prop="freezeAmount" :width="120" />
          <el-table-column v-if="columns[7].visible" label="实际保全金额" align="center" key="actualFreezeAmount"
            prop="actualFreezeAmount" :width="120" />
          <el-table-column v-if="columns[8].visible" label="保全标的物" align="center" key="freezeAssets" prop="freezeAssets"
            :width="120" />
          <el-table-column v-if="columns[9].visible" label="保全法院" align="center" key="court" prop="court" :width="140"
            show-overflow-tooltip />
          <el-table-column v-if="columns[10].visible" label="保全期限开始时间" align="center" key="startFreeze"
            prop="startFreeze" :width="160" />
          <el-table-column v-if="columns[11].visible" label="保全期限结束时间" align="center" key="endFreeze" prop="endFreeze"
            :width="160" />
          <el-table-column v-if="columns[12].visible" label="跟进人员" align="center" key="follower" prop="follower" />
          <el-table-column v-if="columns[13].visible" label="最近一次跟进时间" align="center" key="followUpTime"
            prop="followUpTime" :width="160" show-overflow-tooltip />
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="SentenceAndResult">
import { getCourtOptions } from '@/api/common/common';
import { formatParams2 } from '@/utils/common';
import {
  DeptTreeWithStage,
  getStageList,
  selectStageWithMoney,
} from "@/api/team/lawsuitPreservation";

const { proxy } = getCurrentInstance();
const router = useRouter();

//左侧团队树排名
const allDept = ref([]);
const showSearch = ref(false)
const activeDept = ref(undefined)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const trialCourtOption = ref([]);

const activeTab = ref("全部");
const dataList = ref([]);
const rangFiles = ['startDate'];
const loading = ref(false);
const total = ref(0);
const selectedArr = ref([]);
const ids = ref([]);
const allQuery = ref(false);
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '身份证号码', visible: true },
  { key: 3, label: '户籍地', visible: true },
  { key: 4, label: '手机号码', visible: true },
  { key: 5, label: '保全状态', visible: true },
  { key: 6, label: '申请保全金额', visible: true },
  { key: 7, label: '实际保全金额', visible: true },
  { key: 8, label: '保全标的物', visible: true },
  { key: 9, label: '保全法院', visible: true },
  { key: 10, label: '保全期限开始时间', visible: true },
  { key: 11, label: '保全期限结束时间', visible: true },
  { key: 12, label: '跟进人员', visible: true },
  { key: 13, label: '最近一次跟进时间', visible: true },
])

// 获取列表
function getList() {
  return new Promise((resolve) => {
    const reqForm = proxy.addFieldsRange(queryParams.value, rangFiles)
    reqForm.saveStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已财产保全']
    reqForm.saveStageList = String(reqForm.saveStageList)
    reqForm.mineQuery = false
    if (reqForm.freezeTime1 && reqForm.freezeTime2) {
      reqForm.startFreeze = reqForm.freezeTime1
      reqForm.endFreeze = reqForm.freezeTime2
    }
    if (reqForm.startDate1 && reqForm.startDate2) {
      reqForm.startDate1 = `${reqForm.startDate1} 00:00:00`;
      reqForm.startDate2 = `${reqForm.startDate2} 23:59:59`;
    }
    loading.value = true;
    getStageList(reqForm).then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    }).finally(() => {
      resolve()
      loading.value = false;
    });
  })
}
getList();
//获取团队排名
function getTeamTreeData() {
  let smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已财产保全']
  smallStageList = String(smallStageList)
  DeptTreeWithStage({ smallStageList }).then((res) => {
    allDept.value = res.data
  });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
  queryParams.value.stringDeptId = val
  activeDept.value = val
  queryParams.value.pageNum = 1
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}
// 获取机构列表
getCourts();
function getCourts() {
  getCourtOptions().then((res) => {
    trialCourtOption.value = res.data;
  });
}

// 搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// 重置
function resetQuery() {
  activeDept.value = undefined
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0, };
      reslove()
      return false
    }
    selectStageWithMoney(getReqParams()).then((res) => {
      statistics.value = res.data;
    }).finally(() => reslove());
  })
}

// tab筛选
function tabChange() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//表格选择
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.caseId);
  selectedArr.value = selection;
}

// 表格是否可以选择
function checkSelectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  queryChange.saveStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已财产保全']
  let searchInfo = {
    query: {
        manageQueryParam: queryChange, //查询参数
        pageNumber: pageNumber, //当前第几页(变动)
        pageSize: 1, //一页一条
        pageIndex: 0, //当前第一条
        caseIdCurrent: row.caseId, //当前案件id,
    }, //查询参数
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
}
function getReqParams() {
  const reqParams = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)))
  const reqForm = formatParams2(reqParams, selectedArr, queryParams.value.allQuery)
  reqForm.condition = queryParams.value.allQuery
  reqForm.disposeStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已财产保全']
  reqForm.mineQuery = false
  // reqForm.disposeStageList = String(reqForm.disposeStageList)
  return reqForm
}
watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })

// 格式化金额
function moneyFor(num, str = '') {
  return num ? proxy.setNumberToFixed(num) : str
}
</script>

<style scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>


<style lang="scss" scoped>
.side-edge {
  height: calc(100vh - 125px);
  padding: 0 !important;
  border-right: 2px solid #eee;
}

.head-container {
  color: #333;
  font-weight: bold;
}

.dept-list {
  color: #5a5a5a;
  cursor: pointer;
  height: 80vh;
  overflow: auto;

  .active {
    border-right: 2px solid #60b2ff;
    background-color: #e6f7ff;
  }

  .dept-item {
    width: 100%;
    line-height: 44px;
    padding-left: 20px;
  }

  .employ-item {
    width: 100%;
    line-height: 44px;
    height: 44px;
    padding-left: 20px;
  }
}
</style>