<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="4" :xs="24" class="side-edge">
                <div class="head-container mb10 pl20">
                    <svg-icon class="mr5" icon-class="user" color="#888888" />
                    团队判决排名
                </div>
                <div class="dept-list">
                    <div class="dept-item" v-for="dept in allDept" :key="dept.id">
                        <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
                            {{ `${dept.name}(${dept.caseNum || 0})` }}
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :span="20" :xs="24">
                <el-form :model="queryParams" :loading="loading" ref="queryRef" inline label-width="100px"
                    :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
                    <el-form-item prop="caseId" label="案件ID">
                        <el-input v-model="queryParams.caseId" style="width: 280px" placeholder="请输入案件ID" />
                    </el-form-item>
                    <el-form-item prop="clientName" label="被告">
                        <el-input v-model="queryParams.clientName" style="width: 280px" placeholder="请输入被告" />
                    </el-form-item>
                    <el-form-item prop="saveStage" label="保全阶段">
                        <el-select placeholder="请选保全阶段" style="width: 280px" v-model="queryParams.saveStage">
                            <el-option v-for="item in saveOption" :key="item.code" :label="item.info"
                                :value="item.info" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="freezeTime" label="保全期限时间">
                        <el-date-picker style="width: 280px" v-model="queryParams.freezeTime" value-format="YYYY-MM-DD"
                            type="daterange" range-separator="" start-placeholder="开始时间" end-placeholder="结束时间"
                            unlink-panels />
                    </el-form-item>
                    <el-form-item prop="court" label="保全法院">
                        <el-select placeholder="请选保全法院" style="width: 280px" v-model="queryParams.court"
                            @focus="getCourtOptionFun">
                            <el-option v-for="item in courtOption" :key="item.code" :label="item.info"
                                :value="item.info" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="保全金额">
                        <div class="range-scope" style="width: 280px">
                            <el-input v-model="queryParams.actualAmount1" @blur="validatePrincipalRange" />
                            <span>-</span>
                            <el-input v-model="queryParams.actualAmount2" @blur="validatePrincipalRange" />
                        </div>
                    </el-form-item>
                </el-form>
                <div class="text-center">
                    <el-button :loading="loading" icon="Search" type="primary"
                        @click="antiShake(handleQuery)">搜索</el-button>
                    <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
                </div>
                <div class="operation-revealing-area">
                    <el-button :disabled="selectedArr.length == 0" :loading="loading" type="primary"
                        v-if="checkPermi(['mediationTeam:attachment:download'])"
                        @click="handleDownload">批量导出</el-button>
                    <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
                </div>
                <el-tabs v-model="activeTab" @tab-click="tabChange">
                    <el-tab-pane label="材料提交" name="材料提交" />
                    <el-tab-pane label="待缴纳保证金" name="待缴纳保证金" />
                    <el-tab-pane label="已缴纳保证金" name="已缴纳保证金" />
                    <el-tab-pane label="已完成财产保全" name="已完成财产保全" />
                    <el-tab-pane label="全部" name="全部" />
                </el-tabs>
                <selectedAll v-model:allQuery="allQuery" :cusTableRef="proxy.$refs.multipleTableRef"
                    :selectedArr="selectedArr" :dataList="dataList">
                    <template #content>
                        <div class="text-flex ml20">
                            <span>案件数量：</span>
                            <span class="text-danger mr10">{{ statistics.caseNum || 0 }} </span>
                            <span>标的额：</span>
                            <span class="text-danger mr10">{{ numFilter(statistics.totalMoney) }}</span>
                            <!-- <span>判决金额：</span>
                <span class="text-danger ">{{ moneyFor(statistics.principal) || 0 }}</span> -->
                        </div>
                    </template>
                </selectedAll>
                <el-table v-loading="loading" ref="multipleTableRef" :data="dataList"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
                    <el-table-column v-if="columns[0].visible" label="案件ID" align="center" key="caseId" prop="caseId"
                        width="80">
                        <template #default="{ row, $index }">
                            <el-button type="text" @click="toDetails(row, $index)">{{ row.caseId }}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="columns[1].visible" label="被告" align="center" key="clientName"
                        prop="clientName" :width="90" />
                    <el-table-column v-if="columns[2].visible" label="手机号码" align="center" key="clientPhone"
                        prop="clientPhone" :width="120" />
                    <el-table-column v-if="columns[3].visible" label="身份证号码" align="center" key="clientIdcard"
                        prop="clientIdcard" :width="180" />
                    <el-table-column v-if="columns[4].visible" label="户籍地" align="center" key="clientCensusRegister"
                        prop="clientCensusRegister" show-overflow-tooltip :width="160" />
                    <el-table-column v-if="columns[5].visible" label="保全状态" align="center" key="saveStage"
                        prop="saveStage" :width="130" />
                    <el-table-column v-if="columns[6].visible" label="申请保全金额" align="center" key="freezeAmount"
                        prop="freezeAmount" :width="120" >
                        <template #default="{ row }">
                            <span>{{ numFilter(row.freezeAmount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="columns[7].visible" label="实际保全金额" align="center" key="actualFreezeAmount"
                        prop="actualFreezeAmount" :width="120" >
                        <template #default="{ row }">
                            <span>{{ numFilter(row.actualFreezeAmount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="columns[8].visible" label="保全标的物" align="center" key="freezeAssets"
                        prop="freezeAssets" :width="120" >
                        <template #default="{ row }">
                            <span>{{ numFilter(row.freezeAssets) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="columns[9].visible" label="保全法院" align="center" key="court" prop="court"
                        :width="140" show-overflow-tooltip />
                    <el-table-column v-if="columns[10].visible" label="保全期限开始时间" align="center" key="startFreeze"
                        prop="startFreeze" :width="160" />
                    <el-table-column v-if="columns[11].visible" label="保全期限结束时间" align="center" key="endFreeze"
                        prop="endFreeze" :width="160" />
                    <el-table-column v-if="columns[12].visible" label="跟进人员" align="center" key="follower"
                        prop="follower" />
                    <el-table-column v-if="columns[13].visible" label="最近一次跟进时间" align="center" key="followUpTime"
                        prop="followUpTime" :width="160" show-overflow-tooltip />
                </el-table>
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>

    </div>
</template>
<script setup name="TeamsCases">
import { checkPermi } from "@/utils/permission";
import { ElMessage } from "element-plus";
import { getCourtOptions } from "@/api/common/common";
import { deptTreeWithSaveStage, selectFreezeList, selectFreezeWithMoney } from "@/api/team/attachment";
import { formatParams } from "@/utils/common";
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
const allDept = ref([]);
const activeDept = ref(undefined);
const courtOption = ref([]);
const saveOption = ref([
    { code: 0, info: '材料提交' },
    { code: 1, info: '缴纳保证金' },
    { code: 2, info: '已缴纳保证金' },
    { code: 3, info: '已完成财产保全' },
]);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const allQuery = ref(false);
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const statistics = ref({
    caseNum: 0,
    totalMoney: 0,
    principal: 0,
});
const activeTab = ref("全部");
//表格查询参数
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
const showSearch = ref(false)
const columns = ref([
    { key: 0, label: '案件ID', visible: true },
    { key: 1, label: '被告', visible: true },
    { key: 2, label: '手机号码', visible: true },
    { key: 3, label: '身份证号码', visible: true },
    { key: 4, label: '户籍地', visible: true },
    { key: 5, label: '保全状态', visible: true },
    { key: 6, label: '申请保全金额', visible: true },
    { key: 7, label: '实际保全金额', visible: true },
    { key: 8, label: '保全标的物', visible: true },
    { key: 9, label: '保全法院', visible: true },
    { key: 10, label: '保全期限开始时间', visible: true },
    { key: 11, label: '保全期限结束时间', visible: true },
    { key: 12, label: '跟进人员', visible: true },
    { key: 13, label: '最近一次跟进时间', visible: true },
])
const { queryParams } = toRefs(data);
//需要拆分的字段
const rangfiles = ['freezeTime'];
//获取部门案件
function getList() {
    return new Promise((resolve) => {
        const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
        reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已完成财产保全']
        reqForm.smallStageList = String(reqForm.smallStageList)
        if (reqForm.freezeTime1 && reqForm.freezeTime2) {
            reqForm.startFreeze = reqForm.freezeTime1
            reqForm.endFreeze = reqForm.freezeTime2
        }
        loading.value = true;
        selectFreezeList(reqForm).then((res) => {
            dataList.value = res.rows;
            total.value = res.total;
        }).finally(() => {
            resolve()
            loading.value = false;
        });
    })
}
getList();

// 切换tab
function tabChange() {
    queryParams.value.pageNum = 1
    nextTick(() => {
        getList()
        getTeamTreeData()
    })
}

//获取部门列表
function getTeamTreeData() {
    const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
    reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已完成财产保全']
    reqForm.smallStageList = String(reqForm.smallStageList)
    delete reqForm.pageNum
    delete reqForm.pageSize
    deptTreeWithSaveStage(reqForm).then((res) => {
        allDept.value = res.data
    });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
    queryParams.value.stringDeptId = val
    activeDept.value = val
    queryParams.value.pageNum = 1
    nextTick(() => {
        getList()
        getTeamTreeData()
    })
}
// 应还本金区间校验
const validatePrincipalRange = () => {
    const { actualAmount1, actualAmount2 } = queryParams.value;
    // 检测输入是否是数字
    if (actualAmount1 && !Number.isFinite(Number(actualAmount1))) {
        ElMessage({
            message: "请输入正确的金额！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.actualAmount1 = undefined;
    }
    if (actualAmount2 && !Number.isFinite(Number(actualAmount2))) {
        ElMessage({
            message: "请输入正确的金额！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.actualAmount2 = undefined;
    }
    if (!actualAmount1 || !actualAmount2) return;
    const principal1 = parseFloat(actualAmount1);
    const principal2 = parseFloat(actualAmount2);
    // 检查区间逻辑
    if (principal1 >= principal2) {
        ElMessage({
            message: "后面区间的值必须大于前面区间的值！",
            type: "warning",
            grouping: true,
            repeatNum: 1,
            duration: 1000,
        });
        queryParams.value.actualAmount2 = undefined;
    }
};
//搜索
function handleQuery() {
    queryParams.value.pageNum = 1;
    nextTick(() => {
        getList()
        getTeamTreeData()
    })
}

//跳转案件详情
function toDetails(row, index) {
    let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
    queryChange.pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
    queryChange.pageSize = 1;
    let searchInfo = {
        query: queryChange, //查询参数
        type: "caseManage",
    };
    localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
    router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "caseManage" } });
}

//表格行能否选择
function checkSelectable() {
    return !allQuery.value;
}

//查询案件金额
function getStaticForQuery() {
    return new Promise((reslove, reject) => {
        if (!allQuery.value && selectedArr.value.length == 0) {
            statistics.value = { caseNum: 0, totalMoney: 0, principal: 0, };
            reslove()
            return false
        }
        selectFreezeWithMoney(getReqParams()).then((res) => {
            statistics.value = {
                caseNum: res.data.size,
                totalMoney: res.data.money,
                principal: res.data.principal,
            };
        }).finally(() => reslove());
    })
}
//重置
function resetQuery() {
    activeDept.value = undefined
    proxy.$refs["queryRef"].resetFields();
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
    };
    nextTick(() => {
        getList()
        getTeamTreeData()
    })
}
//选择列表
function handleSelectionChange(selection) {
    selectedArr.value = selection;
}
// 导出
function handleDownload() {
    const reqForm = getReqParams()
    proxy.downloadforjson('/team/exportFreezeCase', reqForm, `诉讼保全_${+new Date()}.xlsx`)
}
// 格式化金额
function moneyFor(num, str = '') {
    return num ? proxy.setNumberToFixed(num) : str
}
// 获取法院
function getCourtOptionFun() {
    getCourtOptions().then(res => {
        courtOption.value = res.data
    })
}
// 获取参数
function getReqParams() {
    const reqParams = proxy.addFieldsRange(queryParams.value, rangfiles)
    const reqForm = formatParams(reqParams, selectedArr, allQuery)
    if (reqForm.freezeTime1 && reqForm.freezeTime2) {
        reqForm.startFreeze = reqForm.freezeTime1
        reqForm.endFreeze = reqForm.freezeTime2
    }
    reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['材料提交', '待缴纳保证金', '已缴纳保证金', '已完成财产保全']
    reqForm.condition = allQuery.value
    reqForm.saveStage = activeTab.value == '全部' ? undefined : activeTab.value
    return reqForm
}
watch(() => selectedArr.value, () => {
    nextTick(() => {
        if (!loading.value) {
            loading.value = true
            getStaticForQuery().finally(() => loading.value = false)
        }
    })
}, { immediate: true, deep: true })
</script>
<style scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}
</style>


<style lang="scss" scoped>
.side-edge {
    height: calc(100vh - 125px);
    padding: 0 !important;
    border-right: 2px solid #eee;
}

.head-container {
    color: #333;
    font-weight: bold;
}

.dept-list {
    color: #5a5a5a;
    cursor: pointer;
    height: 80vh;
    overflow: auto;

    .active {
        border-right: 2px solid #60b2ff;
        background-color: #e6f7ff;
    }

    .dept-item {
        width: 100%;
        line-height: 44px;
        padding-left: 20px;
    }

    .employ-item {
        width: 100%;
        line-height: 44px;
        height: 44px;
        padding-left: 20px;
    }
}
</style>