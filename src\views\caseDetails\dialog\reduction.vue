<template>
  <el-dialog title="申请减免" v-model="open" :before-close="cancel" width="650px" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="150px">
      <el-form-item label="剩余应还债权金额：">{{ numFilter(form.remainingDue) }}元</el-form-item>
      <el-form-item label="减免后应还金额" prop="amountAfterDeduction">
        <el-input type="number" v-model="form.amountAfterDeduction" placeholder="请输入减免后应还金额" style="width: 240px" />
      </el-form-item>
      <el-form-item label="减免后还款日" prop="afterReductionDate">
        <el-date-picker v-model="form.afterReductionDate"
                        type="date" placeholder="请选择减免后还款日"
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledDate"
                        style="width: 240px" />
      </el-form-item>
      <el-form-item label="上传凭证" prop="reductionFile">
        <el-input v-show="false" v-model="form.reductionFile" />
        <!-- jpg，png，.xls,.docx,.doc,pdf； -->
        <el-upload ref="uploadRef" multiple :limit="3" accept=".jpg, .png, .xls, .docx, .doc, .pdf"
          :headers="upload.headers" :action="upload.url" :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange" :before-remove="remove" :on-success="handleFileSuccess"
          :auto-upload="false">
          <template #trigger>
            <el-button class="mr10" type="primary">选取文件</el-button>
          </template>
          <el-button class="ml10" type="success" @click="submitFile">上传到服务器</el-button>
          <template #tip>
            <div class="el-upload__tip">
              上传的凭证格式为：jpg，png，.xls,.docx,.doc,pdf；文件数量限制3个
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="原因" prop="reason">
        <el-input v-model="form.reason" type="textarea" placeholder="请输入原因" :rows="4" maxlength="300" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { insertReduction } from "@/api/caseDetail/detail";
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance();

const getcaseDetailInfo = inject("getcaseDetailInfo");
const emit = defineEmits(["getDetails"]);

const open = ref(false);
const loading = ref(false);
const data = reactive({
  form: {},
  rules: {
    amountAfterDeduction: [
      { required: true, message: "请输入减免后应还金额！", trigger: "blur" },
      { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' },
    ],
    afterReductionDate: [
      { required: true, message: "请选择减免后还款日！", trigger: "change" },
    ],
    reason: [{ required: true, message: "请输入申请原因！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/collection/uploadReduction",
});
const files = ref([]);

const  disabledDate = (time) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
}

//打开弹窗
function opendialog(caseId, remainingDue) {
  reset();
  form.value.caseId = caseId;
  form.value.remainingDue = remainingDue;
  open.value = true;
}

//文件上传前的处理
const handleFileUploadBefore = (file) => {
};

//文件列表变化
function handleEditChange(file, fileList) {
}

//文件移除
function remove(file, fileList) {
  let modifyName = "";
  if (file.response && file.response.code === 200) {
    modifyName = file.response.data.modifyName[0];
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == modifyName) {
        files.value.splice(i, 1);
        break;
      }
    }
  }
}

//文件上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    // 找到上传失败的文件在 fileList 中的索引
    const index = fileList.findIndex(item => item.uid === file.uid);
    // 如果找到了索引，就从 fileList 中移除这个文件
    if (index !== -1) {
      fileList.splice(index, 1);
    }
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: data.firstName[0],
      modifyName: data.modifyName[0],
      fileUrl: data.fileUrl[0],
    };
    files.value.push(obj);
  }
};

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    amountAfterDeduction: undefined,
    reductionFile: undefined,
    reason: undefined,
    afterReductionDate: undefined,
  };
}

//提交
function submit() {
  loading.value = false;
  form.value.reductionFile = files.value;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      insertReduction(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess("申请提交成功！");
          getcaseDetailInfo();
          emit("getDetails");
          cancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});

</script>

<style scoped>
:deep(.el-upload-list__item-name) {
  white-space: break-spaces;
  width: 70%;
}
</style>
