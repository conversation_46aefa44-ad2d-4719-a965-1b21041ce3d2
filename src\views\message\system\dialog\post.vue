<template>
  <div class="app-container">
    <el-form
      :model="form"
      label-position="right"
      :label-width="120"
      :rules="rules"
      ref="formRef"
    >
      <el-form-item label="系统推送消息" prop="messageType">
        <el-radio-group v-model="form.messageType" size="default">
          <el-radio
            v-for="item in messageTypeList"
            :key="item.label"
            :label="item.value"
            clearable
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="推送方式" prop="pushMode">
        <el-radio-group v-model="form.pushMode" size="default">
          <el-radio
            v-for="item in pushModeList"
            :key="item.label"
            :label="item.value"
            clearable
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="推送时间"
        v-if="form.pushMode == 2"
        :rules="rules.pushTimeStr"
        prop="pushTimeStr"
      >
        <el-date-picker
          v-model="form.pushTimeStr"
          type="datetime"
          placeholder="请选择时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          :disabledDate="disabledDate"
          clearable
        />
      </el-form-item>
      <el-form-item label="推送人群" prop="pushCrowdName">
        <el-radio-group v-model="form.pushCrowdName">
          <el-radio
            v-for="item in pushCrowdNameList"
            :key="item.is_settle"
            :label="item.is_settle"
            :indeterminate="item.indeterminate"
            @change="selectPushData"
            clearable
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="推送人员" v-if="form.pushCrowdName == '1'" prop="pushCrowdId">
         <el-transfer
          :titles="['未选中', '选中']"
          v-model="pushCrowdData"
          :data="pushValue"
        ></el-transfer>
      </el-form-item>
      <el-form-item label="提醒方式" prop="reminderMode">
        <el-checkbox-group v-model="form.reminderMode">
          <el-checkbox
            v-for="item in reminderModeList"
            :key="item.is_settle"
            :label="item.is_settle"
            :disabled="item.disabled"
            :indeterminate="item.indeterminate"
            clearable
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="消息标题" :rules="rules.messageTitle" prop="messageTitle">
        <el-input
          v-model="form.messageTitle"
          placeholder="请输入消息标题"
          style="width: 1200px"
          maxlength="128"
          clearable
        />
      </el-form-item>
      <el-form-item label="消息内容" :rules="rules.messageContent" prop="messageContent">
        <div style="width:1200px">
            <myEditor v-model:modelValue="form.messageContent" :imgUploadUrl="upload.url" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center mt20">
      <el-button @click="toBack">取消</el-button>
      <el-button type="primary" :loading="loading" plain @click="submit"
        >发布消息</el-button
      >
    </div>
  </div>
</template>
<script setup name="PostMessage">
import myEditor from "@/components/myEditor";
import { getToken } from "@/utils/auth";
import { insertMessageCenter, selectUserByCreateId } from "@/api/message/system";
//全局配置
const { proxy } = getCurrentInstance();
const router = useRouter();
//表单配置
const loading = ref(false);
//消息类型
const messageTypeList = ref([
  { label: "系统通知", value: 1 },
  { label: "公告/公示", value: 2 },
  { label: "知识库", value: 3 },
]);
//推送人群
const pushCrowdNameList = ref([
  { label: "所有人", is_settle: "*", indeterminate: true },
  { label: "推送人员", is_settle: "1", indeterminate: false },
]);
//推送方式
const pushModeList = ref([
  { label: "实时推送", value: 1 },
  { label: "定时推送", value: 2 },
]);
//通知类型
const reminderModeList = ref([
  { label: "红点", is_settle: '红点', indeterminate: false, disabled: true },
  { label: "弹窗", is_settle: '弹窗', indeterminate: false, disabled: false },
]);
//集合数据
const pushValue = ref([])
//选中的集合
const pushCrowdData = ref([]);
//表单配置参数
const data = reactive({
  form: {
    messageType: 1,
    pushMode: 1,
    pushTimeStr: undefined,
    pushCrowdId: "*",
    pushCrowdName: "*",
    reminderMode: ['红点'],
    messageTitle: undefined,
    messageContent: undefined,
  },
  rules: {
    pushTimeStr: [{ required: true, message: "请选择推送时间！", trigger: "change" }],
    messageTitle: [
      {
        required: true,
        message: "请输入消息标题，长度控制在128个字符以内！",
        trigger: "change",
      },
    ],
    messageContent: [{ required: true, message: "请输入消息内容！", trigger: "change" }],
    pushCrowdId: [{ required: true, message: "请输入消息内容！", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//上传
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/message/upload",
});

//返回
const toBack = () => {
  router.go(-1)
};

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
        loading.value =true;
        let req = JSON.parse(JSON.stringify(form.value));
        req.reminderMode = req.reminderMode.toString();
        if(form.value.pushCrowdName == "1"){
          req.pushCrowdId = [],req.pushCrowdName = [];
          for (let item in pushCrowdData.value) {
            let msgId = pushCrowdData.value[parseInt(item)];
            req.pushCrowdId.push(msgId)
            pushValue.value.forEach(item => {if(item.key == msgId)  req.pushCrowdName.push(item.label)});
          }
          req.pushCrowdId = req.pushCrowdId.toString();
          req.pushCrowdName = req.pushCrowdName.toString()
        }
        insertMessageCenter(req).then((res) => {
            loading.value = false;
            proxy.$modal.msgSuccess("操作成功！");
            toBack();
        })
        .catch(() => {
            loading.value = false;
        });
    }
  });
}

//查询推送数据
function selectPushData(){
  if(form.value.pushCrowdName == "*"){
    form.value.pushCrowdId = "*"
  }else{
    pushValue.value = [];
    selectUserByCreateId().then((res) => {
      res.data?.forEach(item => {
        let myData = {};
        myData.key = item.id;
        myData.label = item.employeeName;
        pushValue.value.push(myData);
      });
    })
    .catch(() => {});
  }
}

//判断时间
function disabledDate(date){
  return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
}

</script>
<style lang="scss" scoped></style>
