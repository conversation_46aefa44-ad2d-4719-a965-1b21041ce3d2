<template>
    <el-table
      :data="fileList"
      style="width: 100%"
      :show-header="true"
      :border="false"
    >
      <el-table-column
        prop="fileName"
        label="文件名"
        min-width="300"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.fileName || row.name || row.originalFilename }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="text"
            @click="handleDownload(row)"
            style="color: #409eff; text-decoration: none;"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  }
})

// Data
const open = ref(false)
const fileList = ref([])

// Methods
function openDialog(files = []) {
  // 处理文件数据，从URL中提取文件名
  fileList.value = files.map((url, index) => {
    // 从URL中提取文件名
    const fileName = url.split('/').pop() || `文件${index + 1}`
    
    return {
      fileName: fileName,
      url: url,
    }
  })
  
  open.value = true
}

function cancel() {
  open.value = false
  fileList.value = []
}

function handleDownload(file) {
  // 根据文件对象的结构来获取下载URL
  const downloadUrl = file.url 
  const fileName = file.fileName || '未知文件'
  
  if (downloadUrl) {
    // 方式1: 直接打开链接下载
    window.open(downloadUrl)

  } else {
    proxy.$modal.msgWarning('文件下载地址不存在')
  }
}

// Expose
defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table--border) {
  border: none;
}
</style>
