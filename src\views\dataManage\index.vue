<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" class="form-content h-50 mt20"
      :class="{ 'h-auto': showSearch }" label-width="120px">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable
          onkeyup="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')"
          oninput="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')" style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="转让方" prop="entrustingPartyId">
        <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
          :reserve-keyword="false" @focus="OwnerList" style="width: 240px">
          <el-option v-for="item in owners" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="借据号" prop="contractNo">
        <el-input v-model="queryParams.contractNo" placeholder="请输入借据号" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="批次号" prop="batchNum">
        <el-input v-model="queryParams.batchNum" placeholder="请输入批次号" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
    </el-form>
    <div class="text-center mb10">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb30">
      <right-toolbar :columns="columns" @queryTable="getList" v-model:showSearch="showSearch"></right-toolbar>
    </el-row>

    <div class="mt10">
      <!-- :disabled="idList.length == 0" -->
      <el-button type="primary" class="mb10" 
        @click="handleBatchAssetsDialog">批量上传资料</el-button>
      <div>
        <!-- <el-checkbox-group v-model="checkedType" @change="checkedTypeChange" :disabled="dataList.length === 0">
          <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
            :indeterminate="item.indeterminate" />
        </el-checkbox-group> -->
      </div>
      <el-table v-loading="loading" :data="dataList" ref="multipleTableRef" @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" /> -->
        <el-table-column label="案件ID" align="left" key="id" prop="id" v-if="columns[0].visible">
          <template #default="{ row }">
            <el-button type="text" @click="toDetails(row)">{{ row.caseId }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="借据号" align="center" prop="contractNo" v-if="columns[1].visible" />
        <el-table-column label="转让方" align="center" prop="entrustingPartyName" v-if="columns[2].visible" />
        <el-table-column label="产品类型" align="center" key="productName" prop="productName" show-overflow-tooltip
          v-if="columns[3].visible" />
        <el-table-column label="姓名" align="center" key="clientName" prop="clientName" show-overflow-tooltip
          v-if="columns[4].visible" />
        <el-table-column label="证件号码" align="center" prop="clientIdcard" v-if="columns[5].visible" />
        <el-table-column label="批次号" align="center" prop="batchNum" v-if="columns[6].visible" />
        <el-table-column label="委案机构" align="center" prop="outsourcingTeamName" v-if="columns[7].visible" />
        <el-table-column label="文件地址" align="center" prop="fileInformation" v-if="columns[8].visible">
          <template #default="{ row, $index }">
            <el-popover placement="top" :width="500" :ref="`popover-${$index}`" trigger="click" v-if="row.fileInformation?.length > 0 &&
              checkPermi(['datamanage:data:check'])
            ">
              <template #reference>
                <el-button type="text" link>查看</el-button>
              </template>
              <div class="popover-file">
                <div class="ml10">文件地址：</div>
                <div class="file-path" v-for="(item, index) in row.fileInformation" :key="index">
                  {{ item }}
                </div>
              </div>
            </el-popover>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="250" label="操作">
          <template #default="{ row }">
            <el-button type="text" v-hasPermi="['datamanage:data:repair']" v-if="row.fileInformation.length > 0" link
              @click="repairData(row, 1)">修复资料</el-button>
            <el-button type="text" v-hasPermi="['datamanage:data:import']" link
              @click="repairData(row, 0)">上传资料</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 修复资料 -->
    <repairDataVue ref="repairDataRef" @getList="getList" />

    <!-- 批量上传资料 -->
    <batchData ref="batchDataRef" @getList="getList" />
  </div>
</template>

<script setup name="DataManage">
import { dataManageList } from "@/api/assets/dataManage";
import repairDataVue from "./dialog/repairData.vue";
import batchData from "./dialog/batchData.vue";
import { getOwners } from "@/api/assets/casemanage.js";
import { checkPermi } from "@/utils/permission";
import { nextTick } from "vue";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 50 },
  { key: 1, label: `借据号`, visible: true, width: 100 },
  { key: 2, label: `转让方`, visible: true, width: 200 },
  { key: 3, label: `产品类型`, visible: true, width: 100 },
  { key: 4, label: `姓名`, visible: true, width: 180 },
  { key: 5, label: `证件号码`, visible: true, width: 300 },
  { key: 6, label: `批次号`, visible: true, width: 100 },
  { key: 7, label: `委案机构`, visible: true, width: 150 },
  { key: 8, label: `文件地址`, visible: true, width: 50 },
]);
const checkedType = ref([]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingPartyId: undefined,
    contractNo: undefined,
    whetherSearch: false, // 搜索结果全选
  },
});
const { queryParams } = toRefs(data);
const showSearch = ref(false);
const owners = ref([]);
const selectedArr = ref([]);
//数据参数
const dataList = ref([]);
// 选中的案件ID
const idList = ref([]);
const total = ref(0);
//获取列表
function getList() {
  loading.value = true;
  dataManageList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

// 打开批量上传资料弹窗
function handleBatchAssetsDialog() {
  const params = {};
  for (const key in queryParams.value) {
    if (queryParams.value.hasOwnProperty.call(queryParams.value, key)) {
      if (queryParams.value[key] != undefined) {
        params[key] = queryParams.value[key];
      }
    }
  }
  proxy.$refs["batchDataRef"].opendialog({
    idList: queryParams.value.whetherSearch
      ? []
      : JSON.parse(JSON.stringify(idList.value)),
    queryParams: params,
  });
}

// 跳转详情
function toDetails(row) {
  let searchInfo = {
    query: {
      pageNum : 1,
      pageSize : 1
    },
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/caseIndex-detail/manageDetails/${row.caseId}`, query: { type: "caseManage" } });
}
//选择列表
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  if (checkedType.value[0] != "搜索结果全选") {
    if (selectedArr.value.length == dataList.value?.length) {
      checkedType.value[0] = "本页选中";
    } else {
      checkedType.value = [];
    }
  }
}

//选择的转让方
//获取转让方
function OwnerList() {
  getOwners().then((res) => {
    owners.value = res.data;
  });
}

//查询操作
function handleQuery() {
  idList.value = []
  selectedArr.value = []
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    ...queryParams.value,
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingPartyId: undefined,
    contractNo: undefined,
  };
  idList.value = []
  selectedArr.value = []
  getList();
}

//上传资料
function repairData(row, type) {
  let req = JSON.parse(JSON.stringify(row));
  proxy.$refs["repairDataRef"].opendialog(req, type);
}

//全选类型
function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    proxy.$refs["multipleTableRef"].clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else {
    dataList.value.length > 0 &&
      dataList.value.map((item) => {
        proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.whetherSearch = true;
  } else {
    queryParams.value.whetherSearch = false;
  }
}
//表格行能否选择
function checkSelectable() {
  return !queryParams.value.whetherSearch;
}
//表格选中
function selectTable() {
  return new Promise((reslove, reject) => {
    try {
      dataList.value.map((item, index) => {
        proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
      });
      reslove(true);
    } catch (error) {
      reject(error);
    }
  });
}
watch(dataList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (queryParams.value.whetherSearch) {
      queryParams.value.whetherSearch = false;
      nextTick(() => {
        selectTable()
          .then((res) => { })
          .finally(() => {
            queryParams.value.whetherSearch = true;
          });
      });
    } else {
      nextTick(() => {
        dataList.value?.forEach((item, index) => {
          if (idList.value?.includes(item.caseId)) {
            proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
          }
        });
      });
    }
  }
});

watch(selectedArr, (newval) => {
  nextTick(() => {
    let select = newval;
    let pageidList = select.map((item) => item.caseId).filter((item) => item);
    //添加选择的数据
    let caseTableidList = dataList.value.map((item) => item.caseId);
    idList.value = idList.value.filter((item) => !caseTableidList.includes(item));
    idList.value = [...new Set([...idList.value, ...pageidList])];
  });
});
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.popover-file {
  max-height: 340px;
  overflow: auto;
}

.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.text-blue {
  color: var(--theme);
}

.text-blue:hover {
  color: #79bbff;
}

.file-path {
  margin: 10px;
}

.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
