<template>
  <!-- 添加直播活动 -->
  <el-dialog :title="title" width="600px" v-model="open" @close="cancel">
    <el-form
      @submit.native.prevent
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-width="120"
    >
      <el-form-item label="类型名称" prop="classifyName">
        <el-input
          v-model="form.classifyName"
          placeholder="请输入名称，不能超过20个字；"
          style="width: 400px"
          type="text"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="二级类型名称">
        <el-button icon="Plus" size="small" circle @click="showInput" />
        <el-input
          v-if="inputVisible"
          ref="InputRef"
          class="ml10"
          v-model="inputValue"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
          placeholder="请输入标签内容"
          style="width: 136px"
        />
        <el-tag
          class="mr10 ml10 mb5"
          v-for="(tag, index) in dynamicTags"
          :key="tag"
          closable
          effect="dark"
          type="warning"
          :disable-transitions="false"
          @close="handleClose(index)"
          v-on:dblclick="editclassifyLabel(index, tag)"
        >
          {{ tag }}
        </el-tag>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="AddType">
import { addClassify, editClassify } from "@/api/writ/parametric";
//获取全局变量
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const title = ref("创建类型");
//接口名称
const typeInfo = ref([addClassify, editClassify]);
const type = ref();
//开启标志
const open = ref(false);
//标签
const dynamicTags = ref([]);
const inputValue = ref("");
const inputVisible = ref(false);
//提交
const data = reactive({
  form: {
    classifyName: undefined,
    classifyLabel: "",
    status: 0,
  },
  rules: {
    classifyName: [
      { required: true, message: "请输入类型名称！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "不能输入空格",
        trigger: "blur",
      },
    ],
    classifyLabel: [{ required: true, message: "请输入标签内容！", trigger: "blur" }],
    status: [{ required: true, message: "请选择状态！", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//加载信息
const loading = ref(false);

//检测类型名称
function checkClassifyName() {
  if (form.value.classifyName && form.value.classifyName.length > 0) {
    let req = {
      id: type.value == 1 ? route.params.id : undefined,
      classifyName: form.value.classifyName,
    };
    checkUniqueName(req).then((res) => {
      if (res.data == 1) {
        form.value.classifyName = "";
        proxy.$modal.msgError(res.data.msg);
      }
    });
  }
}
//开启弹窗
function opendialog(row) {
  if (row) {
    form.value = row;
    type.value = 1;
    title.value = "编辑类型";
    if (row.classifyLabel && row.classifyLabel.length > 0) {
      dynamicTags.value = row.classifyLabel.split(";");
    }
  } else {
    title.value = "创建类型";
    type.value = 0;
  }
  open.value = true;
}

//删除标签
function handleClose(index) {
  dynamicTags.value.splice(index, 1);
}

//添加标签
function handleInputConfirm() {
  if (inputValue.value.length == 0) return (inputVisible.value = false);
  if (dynamicTags.value.includes(inputValue.value))
    return proxy.$modal.msgError("标签内容不能重复");
  if (inputValue.value.length > 20) {
    inputValue.value = e.target.value.slice(0, 20);
    return proxy.$modal.msgError("标签内容长度不能超过20个字");
  }
  inputVisible.value = false;
  dynamicTags.value.push(inputValue.value);
  //添加到表单
  let str = inputValue.value;
  let classifyLabel = form.value.classifyLabel;
  if (classifyLabel && classifyLabel != "") {
    let i = classifyLabel.indexOf(inputValue.value);
    if (i > -1) {
      if (i === 0) {
        //在头部
        return false;
      }
      if (i > 0) {
        if (classifyLabel.indexOf(";" + inputValue.value + ";") > -1) {
          //在中部
          return false;
        } else {
          //在尾部
          let j = classifyLabel.lastIndexOf(";" + inputValue.value);
          if (j + (";" + inputValue.value).length === classifyLabel.length) {
            return false;
          }
        }
      }
    }
    str = ";" + str;
  }
  form.value.classifyLabel += str;
  inputValue.value = "";
}

//显示添加标签输入框
function showInput() {
  inputVisible.value = true;
  inputValue.value = "";
  nextTick(() => {
    proxy.$refs["InputRef"].focus();
  });
}

//双击编辑
function editclassifyLabel(index, label) {
  handleClose(index);
  showInput();
  inputValue.value = label;
}

//提交信息
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = JSON.parse(JSON.stringify(form.value));
      req.classifyLabel = dynamicTags.value.toString();
      if (req.classifyLabel.length > 0) {
        req.classifyLabel = req.classifyLabel.replace(/\,/g, ";");
      }
      typeInfo.value[type.value](req)
        .then((res) => {
          proxy.$modal.msgSuccess(`设置成功！`);
          cancel();
          emits("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
  dynamicTags.value = [];
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    classifyName: undefined,
    classifyLabel: "",
    status: 0,
  };
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped></style>
