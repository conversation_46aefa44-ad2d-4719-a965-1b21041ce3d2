<template>
  <div>
    <el-form :model="queryParams" :class="{ 'form-h50': !showSearch }" ref="queryRef" inline label-width="auto">
      <el-form-item prop="caseId" label="案件ID">
        <el-input v-model="queryParams.caseId" onkeyup="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')"
          oninput="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')" placeholder="请输入案件ID" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="clientName" label="客户姓名">
        <el-input v-model="queryParams.clientName" placeholder="请输入客户姓名" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="clientIdcard" label="身份证">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入身份证" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="registrar" label="申请人">
        <el-input v-model="queryParams.registrar" placeholder="请输入申请人" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="approveTime" label="处理时间">
        <el-date-picker v-model="queryParams.approveTime" style="width: 240px" value-format="YYYY-MM-DD"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item prop="createTime" label="申请时间">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item prop="returnCaseDate" label="退案日期">
        <el-date-picker v-model="queryParams.returnCaseDate" style="width: 240px" value-format="YYYY-MM-DD"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item prop="examineState" label="审核状态">
        <el-select v-model="queryParams.examineState" multiple collapse-tags collapse-tags-tooltip
          placeholder="请输入或选择审核状态" clearable filterable :loading="selectLoading" style="width: 240px">
          <el-option v-for="item in ExamineOptions" :key="item.info" :label="item.info" :value="item.info" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area">
      <el-button v-if="checkPermi(['assets:caseApprove:allocatPass']) && activeTab == '2'" type="success" plain
        :loading="loading" :disabled="selectedArr.length == 0" @click="handlePass()">通过</el-button>
      <el-button v-if="checkPermi(['assets:caseApprove:allocatNotPass']) && activeTab == '2'" type="warning" plain
        :loading="loading" :disabled="selectedArr.length == 0" @click="handleUnpass()">不通过</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="handleQuery">
      <el-tab-pane label="待处理" name="2" />
      <el-tab-pane label="已同意" name="0" />
      <el-tab-pane label="未同意" name="1" />
      <el-tab-pane label="全部" name="" />
    </el-tabs>
    <div class="table-area">
      <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="44px" />
        <el-table-column v-if="columns[0].visible" align="center" prop="caseId" label="案件ID">
          <template #default="{ row }">
            <el-button type="text" @click="toDetails(row)">{{ row.caseId }}</el-button>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[1].visible" align="center" prop="caseState" label="案件状态" />
        <el-table-column v-if="columns[2].visible" align="center" prop="productName" label="产品名称" />
        <el-table-column v-if="columns[3].visible" align="center" prop="clientName" label="客户姓名" />
        <el-table-column v-if="columns[4].visible" align="center" prop="" label="身份证【户籍地】" width="180">
          <template #default="{ row }">
            <div> {{ row.clientIdcard }} </div>
            <div> {{ `【${row.clientCensusRegister || '--'}】` }} </div>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[5].visible" align="center" prop="" label="委托金额" />
        <el-table-column v-if="columns[6].visible" align="center" prop="registrar" label="申请人" />
        <el-table-column v-if="columns[7].visible" align="center" prop="createTime" label="申请时间" />
        <el-table-column v-if="columns[8].visible" align="center" prop="repaymentMoney" label="还款金额" />
        <el-table-column v-if="columns[9].visible" align="center" prop="repaymentMode" label="还款方式" />
        <el-table-column v-if="columns[10].visible" align="center" prop="repaymentType" label="还款类型" />
        <el-table-column v-if="columns[11].visible" align="center" prop="amountFinalDate" label="还款日期" />
        <el-table-column v-if="columns[12].visible" align="center" prop="state" label="处理状态" />
        <el-table-column v-if="columns[13].visible" align="center" prop="approveTime" label="处理时间" />
        <el-table-column v-if="columns[14].visible" align="center" prop="examineState" label="申核状态">
          <template #default="{ row, $index }">
            <el-popover placement="bottom" :width="550" :ref="`popover-${$index}`" trigger="click">
              <template #reference>
                <el-button @click="showPopover(row)" type="text">
                  {{ row.state }}
                </el-button>
              </template>
              <el-table :data="gridData">
                <el-table-column width="200" property="approveTime" label="处理时间" />
                <el-table-column property="reviewer" label="处理人" show-overflow-tooltip />
                <el-table-column property="approveStartInfo" label="处理状态" />
                <el-table-column width="150" property="refuseReason" label="原因" v-if="row.state == '未通过'" />
              </el-table>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template #default="{ row }">
            <div>
              <el-button :loading="loading" type="text" @click="handlePass(row)" link
                :disabled="loading || row.authority == 0"
                v-if="row.approveStart == null && checkPermi(['assets:caseApprove:allocatPass']) && row.allocationUpshot !== 1">通过</el-button>
              <el-button :loading="loading" type="text" @click="handleUnpass(row)" link
                :disabled="loading || row.authority == 0"
                v-if="row.approveStart == null && checkPermi(['assets:caseApprove:allocatNotPass']) && row.allocationUpshot !== 1">不通过</el-button>
              <el-button :loading="loading" :disabled="loading" @click="handleCheckVoucher(row)" type="text"
                link>查看凭证</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 不通过 -->
    <el-dialog title="回款审核" v-model="open" width="600px" append-to-body :before-close="cancel"
      :close-on-click-modal="false">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="68px">
        <el-form-item label="类型" prop="notPassType">
          <el-select v-model="form.notPassType" placeholder="请选择" style="width: 300px">
            <el-option v-for="item in notPassTypes" :key="item.code" :label="item.info" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核原因" prop="reason">
          <el-input type="textarea" show-word-limit maxlength="300" v-model="form.reason" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 图片预览 -->
    <el-image-viewer ref="imageView" v-if="showImageViewer" hide-on-click-modal :url-list="[url]" @close="close" />
  </div>
</template>
<script setup>
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const router = useRouter()
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    uid: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    batchNum: undefined,
    registrar: undefined,
    approveTime: [],
    createTime: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
    examineState: undefined,
    voucherAppUpload: undefined,
    packageNameList: undefined,
  }
})
const activeTab = ref('')
const dataList = ref([])
const total = ref(0)
const loading = ref(false)
const showSearch = ref(false)
const columns = ref([
  { "key": 0, "label": "案件ID", "visible": true },
  { "key": 1, "label": "案件状态", "visible": true },
  { "key": 2, "label": "产品名称", "visible": true },
  { "key": 3, "label": "客户姓名", "visible": true },
  { "key": 4, "label": "身份证【户籍地】", "visible": true },
  { "key": 5, "label": "委托金额", "visible": true },
  { "key": 6, "label": "申请人", "visible": true },
  { "key": 7, "label": "申请时间", "visible": true },
  { "key": 8, "label": "还款金额", "visible": true },
  { "key": 9, "label": "还款方式", "visible": true },
  { "key": 10, "label": "还款类型", "visible": true },
  { "key": 11, "label": "还款日期", "visible": true },
  { "key": 12, "label": "处理状态", "visible": true },
  { "key": 13, "label": "处理时间", "visible": true },
  { "key": 14, "label": "申核状态", "visible": true }
])
const gridData = ref([])
const selectedArr = ref([])
const { queryParams } = toRefs(data)
const checkedType = ref([]);


const settleState = ref("全部");

// 回款审批
const open = ref(false);
const subloading = ref(false);
const notPassTypes = ref([]);
const notPassData = reactive({
  form: {},
  rules: {
    notPassType: [{ required: true, message: "请选择类型", trigger: "change" }],
  },
});
const { form, rules } = toRefs(notPassData);

const rangFields = ["approveTime", "createTime", "entrustingCaseDate", "returnCaseDate"]; //范围字段

// 图片预览
const url = ref([]);
const showImageViewer = ref(false);

function getList() {
  loading.value = true;
  queryParams.value.approveState = activeTab.value == "全部" ? undefined : activeTab.value;
  queryParams.value.repaymentType = settleState.value == "全部" ? undefined : settleState.value;
  let req = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields)));
  req.approveTime2 = req.approveTime2?.replace(/\d{2}:\d{2}:\d{2}$/, "23:59:59");
  req.createTime2 = req.createTime2?.replace(/\d{2}:\d{2}:\d{2}$/, "23:59:59");
  repaymentList(req)
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
// getList();

// 通过
function handlePass(row) {

}
// 点击不通过
function handleUnpass(row) {
  reset();
  getNotPassType()
    .then((res) => {
      notPassTypes.value = res.data;
    })
    .catch(() => {
      return false;
    });
  if (row.id) {
    //单个
    form.value.ids = [row.id];
  } else {
    //批量
    if (selectedArr.value.length === 0) {
      proxy.$modal.msgWarning("请选择案件后再进行操作～");
      loading.value = false;
      return false;
    }
    if (checkedType.value[0] == "本页选中" || checkedType.value.length === 0) {
      //本页选中
      form.value.ids = ids.value;
      form.value.queryParam = {
        allQuery: false,
      };
    } else {
      //搜索结果全选
      form.value.ids = undefined;
      form.value.queryParam = JSON.parse(JSON.stringify(queryParams.value));
      form.value.queryParam.allQuery = allQuery.value;
    }
  }
  open.value = true;
}

// 提交不通过
function submit() {
  subloading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      repaymentNotPass(form.value)
        .then((res) => {
          subloading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          getList(queryParams.value);
          cancel();
        })
        .catch(() => {
          subloading.value = false;
        });
    } else {
      subloading.value = false;
    }
  });
}

// 查看凭证
function handleCheckVoucher(row) {
  if (row.repaymentProof) {
    url.value = row.repaymentProof;
    showImageViewer.value = true;
  } else {
    proxy.$modal.msgError("该案件没有凭证");
  }
}
// 关闭查看凭证
const close = () => {
  showImageViewer.value = false;
};

// 跳转详情
function toDetails(row) {
  const path = `/assets/caseManage-details/caseDetails/${row.caseId}`
  router.push({ path })
}

//搜索
function handleQuery() {
  selectedArr.value = []
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  handleQuery();
}
// 选择表格数据
function handleSelectionChange(selection) {

}

//气泡框展示
function showPopover(row) {
  let req = {
    id: row.id,
  };
  getProce(req)
    .then((res) => {
      gridData.value = res.rows;
    })
    .catch(() => {
      loading.value = false;
    });
}

//获取案件详情
async function getListCount() {
  let req = JSON.parse(JSON.stringify(queryParams.value));
  req.allQuery = allQuery.value;
  if (!allQuery.value) {
    req.ids = ids.value;
  }
  delete req.pageNum;
  delete req.pageSize;
  return new Promise((reslove, reject) => {
    repaymentListCount(req)
      .then((res) => {
        statistics.value.payMoney = res.data;
        reslove(true);
      })
      .catch(() => {
        statistics.value.payMoney = 0;
        reject();
      });
  });
}

watch([selectedArr, checkedType], (newval) => {
  nextTick(async () => {
    let type = newval[1][0] || checkedType.value[0];
    let select = newval[0];
    let pageIds = select.map((item) => item.id).filter((item) => item);
    //添加选择的数据
    let caseTableIds = caseList.value.map((item) => item.id);
    ids.value = ids.value.filter((item) => !caseTableIds.includes(item));
    ids.value = [...new Set([...ids.value, ...pageIds])];
    single.value = !(ids.value?.length > 0);
    if (type === "搜索结果全选") {
      statistics.value.caseNum = total.value;
      getListCount();
    } else if (type === "本页选中" && ids.value?.length > 0) {
      statistics.value.caseNum = ids.value?.length;
      await getListCount();
    } else {
      setTimeout(() => {
        if (ids.value?.length > 0) {
          statistics.value.caseNum = ids.value?.length;
          getListCount();
        } else {
          nextTick(() => {
            statistics.value = {
              caseNum: 0,
              payMoney: 0,
            };
          });
        }
      });
    }
  });
});
</script>