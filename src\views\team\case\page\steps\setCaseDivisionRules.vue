<template>
    <div>
        <div class="table">
            <div class="search-result">
                <div class="table-thead">搜索结果</div>
                <div class="table-body">
                    <div class="table-column">
                        <div class="table-column-content table-column-label">可分配案件量</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.caseNum }}</div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">总金额</div>
                        <div class="table-column-content table-column-value">
                            {{ numFilter(queryRes?.caseMoney) }}
                        </div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">已分配</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.unAssignedNum }}</div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">未分配</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.assignedNum }}</div>
                    </div>
                </div>
            </div>
            <div class="division-rule">
                <div class="table-thead">分案规则</div>
                <div class="table-body">
                    <el-form :model="form" :rules="rules" ref="formRef">
                        <el-form-item label="分案规则">
                            <el-checkbox-group v-model="form.divisionalPrinciple">
                                <el-checkbox v-for="item in checkStatus" :key="item.is_settle" :label="item.is_settle"
                                    :indeterminate="item.indeterminate">{{ item.label }}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <div class="explain">
                            <p>共债分案：将所有未分配的共债案件单独列出来，统一进行分配;</p>
                            <p>隔月分案：上个月至今分过的案件，本月不重复分给同一催员，且本月分过案件不可再分;</p>
                            <p>同时勾选：共债优先，为共债的案件系统会优先分配给同一催员，仅支持所选择的案件里的所有共债进行分配；隔月分案不重复分配至同一催员;</p>
                        </div>
                        <el-form-item label="分案范围" prop="divisionScope">
                            <el-radio-group v-model="form.divisionScope" size="default">
                                <el-radio v-for="item in scopeStatus" :key="item.label" :label="item.value">
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <div class="explain">
                            <p>全部:分配当前搜索结果的全部可分案件</p>
                            <p>未分配：只分配当前搜索结果案件状态为未分配的可分案</p>
                        </div>
                        <el-form-item label="分配模式" prop="divisionalMode">
                            <el-radio-group v-model="form.divisionalMode" size="default">
                                <el-radio v-for="item in modeList" :key="item.value" :label="item.value">
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="分配方式" prop="divisionMethod">
                            <el-radio-group v-model="form.divisionMethod" size="default">
                                <el-radio v-for="item in mannerList" :key="item.value" :label="item.value">
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <div class="explain">
                            <p>分配模式=综合时不会出现分配方式选择，系统会以案件数量优先进行分配；分配模式和分配方式是联动组合操作</p>
                            <p>例如：</p>
                            <p>分配模式=按案件数量分配，分配方式=数值，则最终分配给处置人员的案件数量是按案件数量来分配；</p>
                            <p>分配模式=按案件数量分配，分配方式=百分比，则最终分配给处置人员的案件数量是按百分比例来分配，例如要将案件分配给处置人员A 40%，处置人员B 60%，A和B两人总比例累计是100%；</p>
                        </div>
                    </el-form>
                </div>
            </div>

        </div>
        <div class="text-center mt20">
            <el-button :loading="loading" @click="props.toBack">返回</el-button>
            <el-button :loading="loading" type="primary" plain @click="nextstep">下一步,选择处置人员</el-button>
        </div>
    </div>
</template>
<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
    toBack: { type: Function },
    nextstep: { type: Function },
    loading: { type: Boolean },
    form: { type: Object },
    queryRes: { type: Object },
    rules: { type: Object },
    checkStatus: { type: Array },
    scopeStatus: { type: Array },
    modeList: { type: Array },
    mannerList: { type: Array },
    stepActive: { type: [String, Number] },
})
// 下一步
function nextstep() {
    proxy.$refs["formRef"].validate((valid) => {
        if (valid && props.stepActive == 0) {
            props.nextstep()
        }
    })

}

</script>
<style lang="scss" scoped>
.table {
    border: 1px solid #ededed;

    .table-thead {
        height: 50px;
        color: #666;
        line-height: 50px;
        text-align: center;
        background-color: #f2f2f2;
    }

    .table-body {
        display: flex;
        color: #666;

        .table-column {
            flex: 1;
            text-align: center;

            .table-column-label {
                border-bottom: 1px solid #ededed;
            }

            .table-column-label,
            .table-column-value {
                border-right: 1px solid #ededed;

                &:last-of-type(1) {
                    border-right: none;
                }
            }

            .table-column-content {
                height: 44px;
                line-height: 44px;
            }
        }
    }

    .division-rule {
        .table-body {
            padding: 10px 20px;

            .explain {
                margin-left: 30px;
                font-size: 14px;
                color: #409eff;
            }
        }
    }
}
</style>