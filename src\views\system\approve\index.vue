<template>
  <div class="app-container">
    <el-table :data="dataList">
      <el-table-column
        label="审批类型名称"
        prop="approveName"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            type="text"
            v-hasPermi="['system:approve:set']"
            @click="openset(scope.row)"
            >设置</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="title"
      v-model="open"
      width="950px"
      append-to-body
      :before-close="cancel"
    >
      <div class="dispostWin">
        <div class="item">
          <div class="icon">1</div>
          <div class="item-tit">发起人</div>
          <div class="item-content">所有人</div>
        </div>
        <div
          style="display: inline-block"
          v-for="(item, index) in form.approvalSteps"
          :key="index"
        >
          <div v-if="item.approvalId != `0`" class="item-line"></div>
          <div v-if="item.approvalId != `0`" class="item">
            <div class="icon">{{ index + 2 }}</div>
            <div class="item-tit">{{ CNnumber[index] }}级审批</div>
            <div :title="item.approvers || `请配置`" class="item-content">
              {{ item.approvers || "请配置" }}
            </div>
          </div>
        </div>
      </div>
      <el-row :gutter="10" class="mb10 mt10">
        <el-col :span="2">
          <el-button
            :disabled="form.approvalSteps.length > 10"
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
      </el-row>
      <el-form
        :model="form"
        :rules="rules"
        label-position="top"
        ref="verifyRef"
      >
        <el-table :data="form.approvalSteps" max-height="400px">
          <el-table-column label="步骤" width="55px" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="审批角色">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`approvalSteps.${$index}.approvalId`"
                :rules="rules.approvalId"
                v-if="row.approvalId != `0`"
              >
                <el-select
                  v-model="row.approvalId"
                  @change="roleChange(row.approvalId, $index)"
                >
                  <el-option
                    v-for="item in verifyroles"
                    :key="item.id"
                    :label="item.roleName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="row.approvalId == `0` && approveCode != 7">
                <el-select :disabled="true" v-model="disableSelect">
                  <el-option key="1" label="资产端" value="1"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="审批人">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`approvalSteps.${$index}.approverIds`"
                :rules="rules.approverIds"
                v-if="row.approvalId != `0`"
              >
                <el-select
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  v-model="row.approverIds"
                  @focus="roleChange(row.approvalId, $index)"
                  @change="approvalChange(row.approverIds, $index)"
                  :disabled="!row.approvalId"
                >
                  <el-option
                    v-for="item in row.verifymans"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="row.approvalId == `0` && approveCode != 7">
                <el-select :disabled="true" v-model="disableSelect">
                  <el-option key="1" label="资产端" value="1"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column>
            <template #default="{ row, $index }">
              <el-form-item :prop="`approvalSteps.${$index}.approveMethod`">
                <div class="modeMain">
                  <span
                    class="modeBox"
                    :class="row.approvalId == '0' ? 'disabel' : 'modeBox'"
                    :style="{
                      color: row.approveMethod == 0 ? '#409eff' : '',
                    }"
                    @click="changeMode(row, 0)"
                    >且</span
                  >
                  <span
                    class="modeBox"
                    :class="row.approvalId == '0' ? 'disabel' : 'modeBox'"
                    :style="{
                      color: row.approveMethod == 1 ? '#409eff' : '',
                    }"
                    @click="changeMode(row, 1)"
                    >或</span
                  >
                </div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row, $index }">
              <el-button
                v-if="row.approvalId != '0'"
                type="text"
                :disabled="$index == 0"
                @click="moveUp($index, row)"
                >上移</el-button
              >
              <el-button
                v-if="row.approvalId != '0'"
                type="text"
                :disabled="$index == form.approvalSteps.length - 2"
                @click="moveDown($index, row)"
                >下移</el-button
              >
              <el-button
                v-if="row.approvalId != '0'"
                type="text"
                @click="remove(row, $index)"
                >删除</el-button
              >
              <span v-if="row.approvalId == '0' && approveCode != 7">--</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submit"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Approve">
import {
  selectApprovalSteps,
  selectRole,
  selectEmployeesRole,
  updateApprovalSteps,
  selectApprovalSettings,
} from "@/api/system/approve";
import {
  jgGetApprovalType,
  jgGetApprovalSettings,
  jgSaveApproveSetUp,
} from "@/api/common/common";
const dataList = ref([]);
const route = useRoute();
const { proxy } = getCurrentInstance();
const approveCode = ref(); //审批类型

const title = ref("");
const open = ref(false);
const loading = ref(false);
const deleIds = ref([]);
const data = reactive({
  form: {
    approvalSteps: [],
    remarks: "",
    approveCode: undefined,
  },
  rules: {
    approvalId: [
      { required: true, message: "请选择审批角色", trigger: "blur" },
    ],
    approverIds: [{ required: true, message: "请选择审批人", trigger: "blur" }],
    approveMethod: [{ required: true, message: "请选择", trigger: "blur" }],
    remarks: [{ required: true, message: "请输入审批说明", trigger: "blur" }],
  },
});

const { form, rules } = toRefs(data);

//审批角色
const verifyroles = ref([]);

//审批人
const verifymans = ref([]);

//创建人id
const createId = ref(0);

//级数
const CNnumber = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];

//列表固定数据
const tableLastColumns = ref({
  id: 0,
  approvalId: `0`,
  approvalRole: "资产端",
  approver: "资产端",
  approveMethod: 0,
});

const disableSelect = ref("1");

//获取列表
function getList() {
  loading.value = true;
  jgGetApprovalType()
    .then((res) => {
      dataList.value = res.data;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//打开设置
function openset(row) {
  createId.value = row.createId;
  form.value.remarks = row.remarks;
  approveCode.value = row.approveCode;
  form.value.approveCode = row.approveCode;
  let req = {
    approveCode: row.approveCode,
  };
  Promise.all([
    selectRole({ createId: row.createId }),
    selectEmployeesRole({ createId: row.createId }),
    jgGetApprovalSettings(req),
    selectEmployeesRole({ theRole: "调诉端主账号" }),
  ])
    .then((res) => {
      verifyroles.value = res[0].data;
      verifymans.value = res[1].data;
      if (res[2].data.length == 0) {
        let obj = {
          approvalId: res[0].data.find(
            (item) => item.roleName == "调诉端主账号"
          ).id,
          approverIds: [res[3].data[0].id],
          approver: res[3].data[0].name,
          roleName: "调诉端主账号",
          approveMethod: 0,
        };
        form.value.approvalSteps.unshift(obj);
      }
      if (res[2].data.length > 0) {
        let newArr = res[2].data
          .map((item) => item.jgApproveSetupInfoDtoList)
          .filter((item) => item.roleId != 0);
        newArr.forEach((item, index) => {
          if (item.length > 0) {
            var req = {
              approverIds: item.map((item) => item.userId),
              approvalId: item[0].roleId,
              roleName: res[0].data.find((item2) => item2.id == item[0].roleId)
                ?.roleName,
            };
            form.value.approvalSteps.push(req);
          }
        });
        res[2].data.forEach((item1) => {
          form.value.approvalSteps.forEach((item2) => {
            if (
              item2.approverIds.includes(
                item1.jgApproveSetupInfoDtoList[0].userId
              )
            ) {
              item2.approveMethod = item1.approveMethod;
            }
          });
        });
      }
      form.value.approvalSteps.map((item) => {
        item.verifymans = res[1].data;
        return item;
      });
      if (approveCode.value != 7) {
        form.value.approvalSteps.push(tableLastColumns.value);
      }
      form.value.approvalSteps.forEach((item1, index1) => {
        if (item1.verifymans && item1.verifymans.length > 0) {
          item1.approvers = [];
          item1.verifymans.forEach((item2) => {
            if (item1.approverIds && item1.approverIds.includes(item2.id)) {
              item1.approvers.push({
                name: item2.name,
              });
            }
          });
          item1.approvers = item1.approvers.map((item) => item.name).join(" ");
        }
      });
      title.value = row.name;
      open.value = true;
    })
    .catch((err) => {
      console.log(err);
    });
}

//新增审批流程
function handleAdd() {
  const obj = { approvalId: "", approverId: "", approveMethod: 0 };
  if (approveCode.value != 7) {
    form.value.approvalSteps.pop();
  }
  form.value.approvalSteps.push(obj);
  if (approveCode.value != 7) {
    form.value.approvalSteps.push(tableLastColumns.value);
  }
}

function roleChange(roleid, index) {
  let obj = {};
  for (let i = 0; i < verifyroles.value.length; i++) {
    const item = verifyroles.value[i];
    if (item.id == roleid) {
      obj.theRole = item.roleName;
      form.value.approvalSteps[index].approvalRole = item.roleName;
      form.value.approvalSteps[index].approverId = undefined;
      break;
    }
  }
  selectEmployeesRole(obj)
    .then((res) => {
      const selectedIds = form.value.approvalSteps
        .filter((step, idx) => idx !== index) // 排除当前行
        .flatMap((step) => step.approverIds || []);
      form.value.approvalSteps[index].verifymans = res.data.map((man) => ({
        ...man,
        disabled: selectedIds.includes(man.id),
      }));
    })
    .catch(() => {
      form.value.approvalSteps[index].verifymans = [];
    });
}

//获取审批人
function approvalChange(approverIds, index) {
  form.value.approvalSteps.forEach((item1, index1) => {
    if (index == index1) {
      item1.approvers = [];
      item1.verifymans.forEach((item2) => {
        if (approverIds.includes(item2.id)) {
          item1.approvers.push({
            name: item2.name,
          });
        }
      });
    }
  });
  form.value.approvalSteps[index].approvers = form.value.approvalSteps[
    index
  ].approvers
    .map((item) => item.name)
    .join(" ");
}

//删除审批流程
function remove(row, index) {
  if (row.id) {
    deleIds.value.push(row.id);
  }
  form.value.approvalSteps.splice(index, 1);
}

function changeMode(row, type) {
  row.approveMethod = type;
}

//上移
function moveUp(index, row) {
  let upData = form.value.approvalSteps[index - 1];
  form.value.approvalSteps.splice(index - 1, 1);
  form.value.approvalSteps.splice(index, 0, upData);
}

//下移
function moveDown(index, row) {
  let downData = form.value.approvalSteps[index + 1];
  form.value.approvalSteps.splice(index + 1, 1);
  form.value.approvalSteps.splice(index, 0, downData);
}

//提交
function submit() {
  if (
    (approveCode.value != 7 && form.value.approvalSteps.length == 1) ||
    (approveCode.value == 7 && form.value.approvalSteps.length == 0)
  ) {
    proxy.$modal.msgWarning("请至少选中一个审批人！");
    return false;
  }
  nextTick(() => {
    loading.value = true;
    proxy.$refs["verifyRef"].validate((valid) => {
      if (valid) {
        let req_form = JSON.parse(JSON.stringify(form.value));
        if (deleIds.value.length) {
          //删除的id集合
          req_form.ids = deleIds.value;
        }
        req_form.approvalSteps.map((item, index) => {
          item.sort = index + 1;
          item.approveCode = approveCode.value;
          delete item.verifymans;
        });
        let req = {
          approveCode: approveCode.value,
          reason: form.value.remarks,
          jgApproveSetupNodeDtoList: [],
        };
        req_form.approvalSteps.forEach((item, index) => {
          if (item.approverIds && item.approverIds.length > 0) {
            item.userInfo = [];
            item.approverIds.forEach((item2, index) => {
              let req = {
                approveCode: item.approveCode,
                roleId: item.approvalId,
                userId: item2,
                sort: index,
                infoSort: index,
                operationType:
                  verifyroles.value.find((item3) => item3.id == item.approvalId)
                    .roleName == "调诉端主账号"
                    ? 0
                    : 1,
              };
              item.userInfo.push(req);
            });
          }
        });
        let changeMap = req_form.approvalSteps.map((item) => item.userInfo);
        if (req_form.approvalSteps.length > 0) {
          for (let i = 0; i < req_form.approvalSteps.length; i++) {
            if (req_form.approvalSteps[i].approvalId != 0) {
              let changeValue = {
                approveCode: approveCode.value,
                approvePlatform:
                  req_form.approvalSteps[i].approvalId == 0 ? "ZC" : "JG",
                approveMethod: req_form.approvalSteps[i].approveMethod,
                approveObjectType: 1,
                platformId: 0,
                platformSort: i + 1,
                jgApproveSetupInfoDtoList: changeMap[i],
              };
              req.jgApproveSetupNodeDtoList.push(changeValue);
            }
          }
          let newMap1 = req.jgApproveSetupNodeDtoList.filter(
            (item) => item.approvePlatform == "JG"
          );
          let newMap2 = req.jgApproveSetupNodeDtoList.filter(
            (item) => item.approvePlatform == "ZC"
          );
          for (let i = 0; i < newMap1.length; i++) {
            newMap1[i].platformSort = i;
          }
          for (let i = 0; i < newMap2.length; i++) {
            newMap2[i].platformSort = i;
          }
          req.jgApproveSetupNodeDtoList = [...newMap1, ...newMap2];
        }
        jgSaveApproveSetUp(req)
          .then((res) => {
            proxy.$modal.msgSuccess("设置成功！");
            cancel();
            getList();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  });
}

//取消配置
function cancel() {
  proxy.resetForm("verifyRef");
  open.value = false;
  form.value = {
    approvalSteps: [],
    remarks: "",
    approveCode: undefined,
  };
  deleIds.value = [];
}
</script>

<style lang="scss" scoped>
.dispostWin {
  width: 100%;
  text-align: center;
  overflow: hidden;
}

.dispostWin .item {
  line-height: 20px;
  display: inline-block;
}

.dispostWin .item .icon {
  margin: 0 auto 10px;
  width: 30px;
  color: #ffffff;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: #409eff;
}

.item-tit {
  color: #3f3f3f;
  font-size: 14px;
}
.item-content {
  font-size: 12px;
  color: #409eff;
  width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.dispostWin .item-line {
  margin-bottom: 60px;
  display: inline-block;
  width: 100px;
  height: 1px;
  background: #e8e8e8;
}
.modeMain {
  display: flex;
  gap: 20px;
  .modeBox {
    font-size: 14px;
    cursor: pointer;
  }

  .disabel {
    pointer-events: none;
    cursor: not-allowed;
    color: #c0c4cc; // 禁用态颜色
    background: #f5f7fa; // 可选：禁用态背景
  }
}
</style>
