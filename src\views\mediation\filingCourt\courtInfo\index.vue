<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      :label-width="110"
      class="h-50"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="被告" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入被告"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="开庭律师" prop="trialLawyer">
        <el-input
          v-model="queryParams.trialLawyer"
          placeholder="请输入开庭律师"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="开庭时间">
        <el-date-picker
          v-model="queryParams.trialTime"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="开庭法院" prop="trialCourt">
        <el-select
          v-model="queryParams.trialCourt"
          placeholder="请输入或选择开庭法院"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额" prop="syYhPrincipal">
        <div class="range-scope" style="width: 240px">
          <el-input
            type="text"
            v-model="queryParams.amount1"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
          <span>-</span>
          <el-input
            type="text"
            v-model="queryParams.amount2"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
        </div>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="mb10 mt10 operation-list" style="min-height: 26px">
      <el-button
        v-if="checkPermi([setPermiss('download')])"
        :disabled="single"
        type="primary"
        @click="downloadCase"
        >导出数据</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('payFees')]) && route.meta.query == 1"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('payFeesRef')"
        >缴费</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('appoint')]) && route.meta.query == 2"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('appointCourtRef')"
        >预约开庭</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('transfer')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('transferCaseRef')"
        >案件流转</el-button
      >
      <!-- <el-button
        v-if="checkPermi([setPermiss('keep')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('applyKeepRef')"
        >申请保全</el-button
      > -->
      <el-button
        v-if="checkPermi([setPermiss('createWirt')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('batchImportWritRef')"
        >批量生成文书</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('register')]) && route.meta.query != 3"
        type="primary"
        @click="handleOpenDialog('importRegisterRef')"
        >批量登记</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('sendNote')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('sendMessageRef')"
        >发送短信</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </div>
    <selectedAll
      v-model:allQuery="allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      class="mb10"
    >
      <template #content>
        <div>
          <span class="ml10">剩余债权总额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.totalMoney) }}
          </i>
          <span>剩余债权本金：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.principal) }}
          </i>
          <span>案件数量：</span>
          <i class="danger">{{ statistics.caseNum }}</i>
        </div>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="110"
        v-if="columns[0].visible"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <GreenCircle v-if="row.isFreeze == 1" :data="row" />
            <el-button
              :disable="!checkPermi([setPermiss('detail')])"
              type="text"
              @click="toDetails(row, $index)"
            >
              {{ row.caseId }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        key="disposeStage"
        prop="disposeStage"
        :width="100"
        v-if="columns[1].visible"
      />
      <el-table-column
        label="被告"
        align="center"
        key="clientName"
        prop="clientName"
        :min-width="90"
        v-if="columns[2].visible"
      />
      <el-table-column
        label="手机号码"
        align="center"
        key="clientPhone"
        prop="clientPhone"
        v-if="columns[3].visible"
        :width="110"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.clientPhone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
            <workPhoneVue :phoneNumber="row.clientPhone" :caseId="row.caseId" :borrower="row.clientName"/>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="标的额"
        align="center"
        key="remainingDue"
        prop="remainingDue"
        :width="120"
        v-if="columns[4].visible"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="身份证号码"
        align="center"
        key="clientIdNum"
        prop="clientIdNum"
        :width="180"
        v-if="columns[5].visible"
      />
      <el-table-column
        label="户籍地"
        align="center"
        key="clientCensusRegister"
        prop="clientCensusRegister"
        v-if="columns[6].visible"
        :min-width="160"
      />
      <el-table-column
        label="开庭律师"
        align="center"
        key="trialLawyer"
        prop="trialLawyer"
        :min-width="90"
        v-if="columns[7].visible"
      />
      <el-table-column
        label="承办律师"
        align="center"
        key="undertakingLawyer"
        prop="undertakingLawyer"
        :min-width="90"
        v-if="columns[8].visible"
      />
      <el-table-column
        label="开庭次数"
        align="center"
        key="trialSum"
        prop="trialSum"
        v-if="columns[9].visible"
        :width="100"
      />
      <el-table-column
        label="开庭时间"
        align="center"
        key="trialTime"
        prop="trialTime"
        :width="160"
        v-if="columns[10].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="缴费金额"
        align="center"
        key="costAmt"
        prop="costAmt"
        v-if="columns[11].visible"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.costAmt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="开庭法院"
        align="center"
        key="trialCourt"
        prop="trialCourt"
        v-if="columns[12].visible"
        :width="180"
      />
      <el-table-column
        label="开庭方式"
        align="center"
        key="trialMethod"
        prop="trialMethod"
        v-if="columns[13].visible"
        :width="100"
      />
      <el-table-column
        label="服务内容"
        align="center"
        key="content"
        prop="content"
        show-overflow-tooltip
        :min-width="120"
        v-if="columns[14].visible"
      />
      <el-table-column
        label="跟进人员"
        align="center"
        key="updateBy"
        prop="updateBy"
        :width="90"
        v-if="columns[15].visible"
      />
      <el-table-column
        label="最近一次跟进时间"
        align="center"
        key="updateTime"
        prop="updateTime"
        :width="160"
        v-if="columns[16].visible"
        show-overflow-tooltip
      />
      <el-table-column label="操作" fixed="right" width="120px">
        <template #default="{ row }">
          <div>
            <el-button
              v-if="checkPermi([setPermiss('keep')])"
              type="text"
              @click="handleOpenDialog('applyKeepRef', row)"
              >申请保全</el-button
            >
            <el-button
              type="text"
              v-if="checkPermi([setPermiss('check')])"
              @click="toDetails(row, $index)"
              >查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 转移案件 -->
    <transferCase :getList="getList" ref="transferCaseRef" />
    <!-- 预约开庭 -->
    <appointCourtVue :getList="getList" ref="appointCourtRef" />
    <!-- 缴费 -->
    <payFeesVue :getList="getList" ref="payFeesRef" />
    <!-- 申请保全 -->
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <!-- 发送短信 -->
    <sendMessage :getList="getList" ref="sendMessageRef" />
    <importRegister :getList="getList" ref="importRegisterRef" />
    <batchImportWrit :getList="getList" ref="batchImportWritRef" />
  </div>
</template>

<script setup name="FilingCourt">
import { checkPermi } from "@/utils/permission";
import importRegister from "@/views/mediation/dialog/importRegister";
import batchImportWrit from "@/views/mediation/dialog/batchImportWrit";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import appointCourtVue from "../dialogIndex/appointCourt";
import payFeesVue from "../dialogIndex/payFees";
import applyKeep from "@/views/mediation/dialog/applyKeep";
import transferCase from "@/views/mediation/dialog/transferCase";
import { ElMessage } from "element-plus";
import { formatParams } from "@/utils/common";
import { getFilingCourtList } from "@/api/mediation/filingCourt";
import { pageTypeEnum } from "@/utils/enum";
import { selectSessionWithMoney } from "@/api/team/filingCaseOpenCourt";
import { getCourtOptions } from "@/api/common/common";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const store = useStore();
const pageType = "filingCourt";
const stageObj = {
  1: "开庭缴费",
  2: "预约开庭",
  3: "正式开庭",
};
//表格配置数据
const loading = ref(false);
const allQuery = ref(false);
const total = ref(0);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const rangFields = ["trialTime"];
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const courtOptions = ref([]);
const { queryParams } = toRefs(data);
//表单配置信息
const showSearch = ref(false);
// 列显隐信息
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "状态", visible: true },
  { key: 2, label: "被告", visible: true },
  { key: 3, label: "手机号码", visible: true },
  { key: 4, label: "标的额", visible: true },
  { key: 5, label: "身份证号码", visible: true },
  { key: 6, label: "户籍地", visible: true },
  { key: 7, label: "开庭律师", visible: true },
  { key: 8, label: "承办律师", visible: true },
  { key: 9, label: "开庭次数", visible: true },
  { key: 10, label: "开庭时间", visible: true },
  { key: 11, label: "缴费金额", visible: true },
  { key: 12, label: "开庭法院", visible: true },
  { key: 13, label: "开庭方式", visible: true },
  { key: 14, label: "服务内容", visible: true },
  { key: 15, label: "跟进人员", visible: true },
  { key: 16, label: "最近一次跟进时间", visible: true },
]);

//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = [];
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.disposeStage = stageObj[route.meta.query];
  getFilingCourtList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
provide("getList", Function, true);

function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const newAllQuery = row ? false : allQuery.value;
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds };
  const data = {
    query,
    ...row,
    caseIds,
    allQuery: newAllQuery,
    pageType,
    isBatchSend: 1,
    caseId: row?.caseId,
  };
  data.condition = row ? false : allQuery.value;
  data.oneStatus = "立案开庭";
  proxy.$refs[refName].openDialog(data);
}

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { amount1, amount2 } = queryParams.value;
  // 检测输入是否是数字
  if (amount1 && !Number.isFinite(Number(amount1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount1 = undefined;
  }
  if (amount2 && !Number.isFinite(Number(amount2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
  if (!amount1 || !amount2) return;

  const principal1 = parseFloat(amount1);
  const principal2 = parseFloat(amount2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
};

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      disposeStage: stageObj[route.meta.query],
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: stageObj[route.meta.query] };
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  getList();
}

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && caseIds.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      selectSessionWithMoney(getReqParams())
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.principal,
          };
        })
        .finally(() => reslove());
    });
  });
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
  queryParams.value.ids = caseIds.value;
}

//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery);
  reqForm.condition = allQuery.value;
  reqForm.dis = allQuery.value;
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.sign = pageTypeEnum[pageType];
  return reqForm;
}
//导出数据
function downloadCase() {
  const reqForm = getReqParams();
  proxy.downloadforjson(
    "/team/exportWithLawInfoCase",
    reqForm,
    `立案开庭_${+new Date()}.xlsx`
  );
}
getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}

const htrxCall = computed(() => store.getters.htrxCall);
// 设置权限符
function setPermiss(val) {
  const permissBtn = {
    1: "filingCourt:holdCourt:",
    2: "filingCourt:appointmentCourt:",
    3: "filingCourt:rejectCase:",
  };
  return `${permissBtn[route.meta.query]}${val}`;
}
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

watch(
  () => route,
  () => {
    resetQuery();
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.selected-all-content {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 10px;
  justify-content: space-between;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: #9fccdb;
  display: block;
}

.case-tips {
  font-size: 14px;
  background-color: #cce7fa;
  padding: 20px;
  color: #409eff;

  P {
    margin: 0;
    display: block;
  }

  p:nth-child(2) {
    display: inline;
  }
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
