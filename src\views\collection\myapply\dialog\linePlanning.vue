<template>
    <el-dialog title="线路规划" v-model="open" append-to-body width="650px" @before-close="cancel">
        <div id="container"></div>
        <template #footer>
            <div class="text-center">
                <el-button :loading="loading" @click="cancel">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getOutsideAddress } from '@/api/collection/myapply';

const open = ref(false)
const loading = ref(false)

function showMap(json_data) {
    var map = new BMap.Map("container");
    var point = new BMap.Point(116.404, 39.915);
    map.centerAndZoom(point, 15);
    map.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
    var pointArray = new Array();

    var opts = {
        width: 250,     // 信息窗口宽度
        height: 80,     // 信息窗口高度
        title: "", // 信息窗口标题
        enableMessage: true//设置允许信息窗发送短息
    };

    for (var i = 0; i < json_data.length; i++) {
        var marker = new BMap.Marker(new BMap.Point(json_data[i][0], json_data[i][1])); // 创建点
        map.addOverlay(marker);    //增加点
        pointArray[i] = new BMap.Point(json_data[i][0], json_data[i][1]);
        var content = json_data[i][2];
        addClickHandler(content, marker);

    }
    //让所有点在视野范围内
    map.setViewport(pointArray);

    function addClickHandler(content, marker) {
        marker.addEventListener("click", function (e) {
            openInfo(content, e)
        }
        );
    }
    function openInfo(content, e) {
        var p = e.target;
        var point = new BMap.Point(p.getPosition().lng, p.getPosition().lat);
        var infoWindow = new BMap.InfoWindow(content, opts);  // 创建信息窗口对象 
        map.openInfoWindow(infoWindow, point); //开启信息窗口
    }
}

function getOutsideAddressFun(data) {
    return new Promise((reslove, reject) => {
        getOutsideAddress(data).then(res => {
            const locationInfoArr = res.data.map(item => {
                item.locationInfoArr = item.longitudeAtitudeStart.split(',')
                item.locationInfoArr[2] = item.outsideAddress
                return item.locationInfoArr
            })
            reslove(locationInfoArr)
        })
    })

}
function openDialog(data) {
    open.value = true
    getOutsideAddressFun(data).then(res => {
        showMap(res)
    })
}
function cancel() {
    open.value = false
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
#container {
    width: 610px;
    height: 550px;
}
</style>