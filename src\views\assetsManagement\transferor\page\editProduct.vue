<template>
  <div class="app-container">
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="92px"
      style="width: 90%; margin: 0 auto"
    >
      <el-form-item label="转让方名称" prop="ownername">
        <el-input v-model="form.ownername" :disabled="true" style="width: 240px" />
      </el-form-item>
      <el-form-item label="产品类型" prop="name">
        <el-input v-model="form.name" :disabled="true" placeholder="请输入产品类型" style="width: 240px" />
      </el-form-item>
    </el-form>
    <TplField />
    <div class="text-center">
      <el-button type="primary" :loading="loading" @click="save()"
        >保存，并返回列表</el-button
      >
      <el-button type="primary" plain @click="toBack()">返回</el-button>
    </div>
  </div>
</template>

<script setup name="EditProduct">
import TplField from "@/components/TplField";
import { productDetail, editProduct } from "@/api/assets/assetside";

const { proxy } = getCurrentInstance();
const route = useRoute();

const loading = ref(false);
const data = reactive({
  form: {
    ownername: undefined,
    name: undefined,
  },
  rules: {
    ownername: [{ required: true, message: "请输入转让方名称", trigger: "blur" }],
    name: [{ required: true, message: "请输入转让方名称", trigger: "blur" }],
  },
});

const { form, rules } = toRefs(data);

const tplFields = ref(); //字段信息
const checkedres = ref({}); //字段选择数据
provide("tplFields", tplFields);
provide("checkedres", checkedres);

//获取模板信息
function getInfo() {
  productDetail(route.params.proId).then((res) => {
    form.value = res.data;
    form.value.ownername = route.query.ownername;
    form.value.ownerId = route.query.ownerId;
    tplFields.value = res.data.template;
  });
}
getInfo();

//保存
function save() {
  loading.value = true;
  Object.keys(tplFields.value).map((key) => {
    if (key != "additionalInfo") {
      tplFields.value[key].columns.map((item) => {
        if (checkedres.value[key].def || checkedres.value[key].indexOf(item.label) > -1) {
          item.choose = true;
        } else {
          item.choose = false;
        }
      });
    }
  });
  form.value.template = tplFields.value;
  console.log(form.value);
  editProduct(form.value)
    .then((res) => {
      loading.value = false;
      proxy.$modal.msgSuccess("保存成功！");
      toBack();
    })
    .catch(() => {
      loading.value = false;
    });
}

//返回
const toBack = () => {
  const obj = { path: "/assetsManagement/transferor" };
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style scoped></style>
