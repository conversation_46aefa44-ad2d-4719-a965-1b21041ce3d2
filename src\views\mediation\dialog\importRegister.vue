<template>
    <el-dialog title="批量登记" v-model="open" width="550px" :before-close="cancel" append-to-body>
        <el-form :model="form" ref="formRef" :rules="rules" label-width="140px" inline>
            <el-form-item label="下载导入模板">
                <el-button :loading="loading" type="primary" @click="handleDownloadTpl">下载模板</el-button>
            </el-form-item>
            <el-form-item label="上传填好的信息表" prop="fileUrl">
                <FileUpload drag v-model:fileList="fileList" :fileType="['xls', 'xlsx']" gateway="sign"
                    uploadFileUrl="/letter/message/upload" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-right">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { importFilingCourt } from '@/api/mediation/filingCourt';
import { importDataJudge } from '@/api/mediation/judgmentResult';
import { registerCaseOnlineFiling } from '@/api/mediation/onlineFiling';
import FileUpload from '@/components/FileUpload';
const { proxy } = getCurrentInstance()
const props = defineProps({
    getList: { type: Function }
})
const open = ref(false)
const loading = ref(false)
const pageType = ref('')
const data = reactive({
    form: {
        fileUrl: undefined
    },
    rules: {
        fileUrl: [{ required: true, message: '请上传文件', trigger: 'bluer' }]
    },
})
const fileList = ref([])
const { form, rules } = toRefs(data)
function submit() {
    form.value.fileUrl = fileList.value[0].response.data.fileUrl[0]
    form.value.fileName = fileList.value[0].response.data.modifyName[0]
    nextTick(() => {
        proxy.$refs['formRef'].validate((valid) => {
            if (valid) {
                const reqApiType = {
                    onlineFiling: registerCaseOnlineFiling,
                    filingCourt: importFilingCourt,
                    judgmentResult: importDataJudge
                }
                if (reqApiType[pageType.value]) {
                    loading.value = true
                    const reqForm = JSON.parse(JSON.stringify(form.value))
                    reqApiType[pageType.value](reqForm).then(res => {
                        if (res.code == 200) {
                            props.getList && props.getList()
                            proxy.$modal.msgSuccess('操作成功')
                            cancel()
                        }
                    }).finally(() => loading.value = false)
                }
            }
        })
    })
}
function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data }
    pageType.value = data.pageType
}
function cancel() {
    open.value = false
    pageType.value = ''
    fileList.value = []
}

// 下载模板
function handleDownloadTpl() {
    const reqApiType = {
        filingCourt: {
            reqName: '/session/getTemplate',
            fileName: 'tpl_立案开庭批量登记模板.xlsx'
        },
        onlineFiling: {
            reqName: '/filing-case/exportTemplate',
            fileName: 'tpl_网上立案批量登记模板.xlsx'
        },
        judgmentResult: {
            reqName: '/judge/getTemplate',
            fileName: 'tpl_判决与结果批量登记模板.xlsx'
        },
    }
    const reqAPiInfo = reqApiType[pageType.value]
    proxy.downloadforjson(reqAPiInfo.reqName, {}, reqAPiInfo.fileName);
}

defineExpose({ openDialog })
</script>