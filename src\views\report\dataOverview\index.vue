<template>
  <div class="app-container">
    <div class="over-data-overview" v-if="checkPermi(['report:dataOverview:overview'])">
      <div class="over-title">
        <div class="dot"></div>
        <span class="title-icon">数据概览</span>
      </div>
      <div class="explain ml20">注：数据每日凌晨更新</div>
      <div class="over-content">
        <div class="over-box">
          <div class="over-box-content" style="height: 500px">
            <assestChartsVue ref="assestChartsVueRef" />
            <div class="over-box-data" style="width: 49%">
              <div class="over-box-sub">
                <progressBar
                  v-if="dataList?.initialDebtTotal && dataList?.recoveryTotal"
                  :schedule="
                    numFilter(
                      (dataList?.recoveryTotal / dataList?.initialDebtTotal) * 100
                    )
                  "
                />
                <progressBar v-else :schedule="0" />
              </div>
              <div class="over-box-data-table">
                <table>
                  <thead>
                    <tr>
                      <th style="width: 30%">初始债权总金额</th>
                      <th style="width: 30%">剩余债权总金额</th>
                      <th style="width: 20%">总笔数</th>
                      <th style="width: 20%">总户数</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ numFilter(dataList?.initialDebtTotal) || 0 }}元</td>
                      <td>{{ numFilter(dataList?.residualDebtTotal) || 0 }}元</td>
                      <td>{{ dataList?.penNumberTotal || 0 }}笔</td>
                      <td>{{ dataList?.householdsTotal || 0 }}户</td>
                    </tr>
                  </tbody>
                </table>
                <table style="width: 100%; border-top: none">
                  <thead>
                    <tr>
                      <th style="width: 30%">已入账回收总额</th>
                      <th style="width: 30%">未入账回收总额</th>
                      <th colspan="2" style="width: 40%">回收总额</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ numFilter(dataList?.recoveryBelonged) || 0 }}元</td>
                      <td>{{ numFilter(dataList?.recoveryNotBelonged) || 0 }}元</td>
                      <td>{{ numFilter(dataList?.recoveryTotal) || 0 }}元</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="over-box-data-tooltip">
                <div class="tooltip-item">剩余债权总额占比</div>
                <div class="tooltip-item">回收总额占比</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 委案批次维度 -->
    <!-- <assetDimensionVue
      ref="assetDimensionVueRef"
      v-if="checkPermi(['report:dataOverview:assetDimensionVue'])"
    /> -->
    <!-- 员工维度 -->
    <institutionalVue
      ref="institutionalDimensionVueRef"
      v-if="checkPermi(['report:dataOverview:institutionalVue'])"
    />
  </div>
</template>

<script setup name="DataOverview">
import { selectStatistics } from "@/api/report/dataOverview";
import { numFilter } from "@/utils/common.js";
import progressBar from "@/components/ProgressBar/index.vue";
import assetDimensionVue from "./page/assetDimension.vue";
import institutionalVue from "./page/institutional.vue";
import assestChartsVue from "./components/assestCharts.vue";
import { checkPermi } from "@/utils/permission";
//全局参数
const router = useRouter();
const { proxy } = getCurrentInstance();
//概览数据
const dataList = ref({});
//资产总金额
const assestMoney = ref(0);
//已委案总数额
const finAssestMoney = ref(0);
//未委案数额
const notAssestMoney = ref(0);

//获取数据
function getList() {
  selectStatistics().then((res) => {
    dataList.value = res?.data;
    assestMoney.value = res?.data?.residualDebtTotal + res?.data?.recoveryBelonged;
    finAssestMoney.value =
      res?.data?.residualDebtCommittedCase + res?.data?.recoveryTotalCommittedCase;
    notAssestMoney.value =
      res?.data?.residualDebtUncommittedCase + res?.data?.recoveryTotalUncommittedCase;
    getMap();
  });
}

// //生成图表
function getMap() {
  let mapData = [
    {
      value: dataList.value?.recoveryTotal !== 0
      ? numFilter(
        (dataList.value?.recoveryTotal / dataList.value?.recoveryTotal) * 100)
          : 0,
      name: "回收总额占比",
    },
    {
      value: dataList.value?.initialDebtTotal !== 0
          ? numFilter((dataList.value?.residualDebtTotal / dataList.value?.initialDebtTotal) * 100)
          : 0,
      name: "剩余债权总额占比",
    },
  ];
  proxy.$refs["assestChartsVueRef"].getMap(dataList.value?.initialDebtTotal, mapData);
}

onMounted(() => {
  getList();
});

</script>

<style lang="scss" scoped>
.over-data-overview {
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.2), 0 0px 3px 0 rgba(0, 0, 0, 0.19);
}
.over-title {
  background-color: #d5d7db;
  color: #3f3f3f;
  margin: 10px 0px 20px;
  height: 60px;
  font-size: 24px;
  line-height: 60px;
  font-weight: 700;
  .dot {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #409eff;
    margin: 0px 10px 0px 20px;
  }
}
.explain {
  color: #999;
}
.over-content {
  padding: 0px 20px;
  .over-box {
    display: inline-block;
    width: 100%;
    vertical-align: top;
    &-title {
      height: 60px;
      font-size: 22px;
      color: #3f3f3f;
      line-height: 60px;
      border-bottom: 2px solid #dcdfe6;
      span {
        padding-left: 10px;
        border-left: 4px solid #409eff;
      }
    }
    &-content {
      width: 100%;
      .over-box-data {
        display: inline-block;
        width: 100%;
        height: 100%;
        vertical-align: top;
        .over-box-sub {
          margin-top: 50px;
        }
        .over-box-data-table {
          table {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-collapse: collapse;
            thead {
              background-color: #eeeff4;
              tr > th {
                border-bottom: 1px solid #dcdfe6;
                border-right: 1px solid #dcdfe6;
                padding: 12px 20px;
                font-size: 16px;
                color: #909399;
                font-weight: 400;
              }
              tr > th:last-child {
                border-right: none;
              }
            }
            tbody {
              tr > td {
                border-right: 1px solid #dcdfe6;
                padding: 12px 20px;
                font-size: 16px;
                color: #909399;
                font-weight: 400;
                text-align: center;
                color: #409eff;
              }
              tr > td:last-child {
                border-right: none;
              }
            }
          }
        }
        .over-box-data-tooltip {
          margin-top: 20px;
          .tooltip-item {
            font-size: 16px;
            display: inline-block;
            width: 300px;
            height: 40px;
            line-height: 40px;
            span {
              font-size: 18px;
              color: #409eff;
            }
          }
          .tooltip-item::before {
            content: "";
            display: inline-block;
            width: 30px;
            height: 10px;
            background-color: #8492a6;
            border-radius: 5px;
            margin-right: 10px;
          }
          .tooltip-item:nth-of-type(1)::before {
            background-color: #409eff;
          }
        }
      }
    }
  }
}
#assest-charts {
  height: 100%;
  width: 49%;
  margin-right: 2%;
  top: -28px;
  display: inline-block;
}
.sub-content {
  width: 100%;
  .sub-back {
    position: absolute;
    height: 30px;
  }
}
</style>
