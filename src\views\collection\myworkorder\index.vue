<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" :label-width="100"
      class="form-content h-50" :rules="rules" :class="{ 'h-auto': showSearch }">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="UID" prop="uid">
        <el-input v-model="queryParams.uid" placeholder="请输入UID" clearable style="width: 240px"
          onkeyup="value=value.replace(/[^A-z0-9/g, '')" oninput="value=value.replace(/[^A-z0-9]/g, '')"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable type="number" style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="问题类型">
        <el-select v-model="queryParams.questionType" placeholder="请输入或选择问题类型" clearable filterable
          :reserve-keyword="false" @focus="getOrderTypeList" style="width: 240px">
          <el-option v-for="item in orderTypeList" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          style="width: 240px" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <el-row class="mb10 mt10 h32">
      <!-- <el-button
        v-if="activeTab === '待处理'"
        type="primary"
        plain
        :disabled="pass"
        @click="signWorkOrder(0)"
        >标记为已处理</el-button
      > -->
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane v-for="(item, index) in tab" :key="index" :label="`${item.label}(${item.count})`" :name="item.name">
      </el-tab-pane>
    </el-tabs>
    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" :width="100" v-if="columns[0].visible">
        <template #default="scope">
          <div class="df-center">
            <el-tooltip v-if="scope.row.labelContent" placement="top">
              <template #content>{{ scope.row.labelContent }}</template>
              <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
            </el-tooltip>
            <span style="color:#409eff;cursor: pointer;" type="text" v-if="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)">{{
                scope.row.caseId
              }}</span>
            <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="UID" align="center" key="uid" prop="uid" v-if="columns[1].visible" width="100" />
      <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[2].visible"
        width="150" />
      <el-table-column label="问题类型" align="center" key="questionType" prop="questionType" v-if="columns[3].visible" />
      <el-table-column label="渠道来源" align="center" key="channelSource" prop="channelSource" v-if="columns[4].visible" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[5].visible" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" v-if="columns[6].visible" />
      <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[7].visible"
        width="180" />
      <el-table-column label="电话号码" align="center" key="clientPhone" prop="clientPhone" v-if="columns[8].visible"
        width="150" />
      <el-table-column label="来电号码" align="center" key="callNumber" prop="callNumber" v-if="columns[9].visible"
        width="150">
        <template #default="{ row }">
          <span>{{ row?.callNumber }}</span>
          <callBarVue class="ml5" v-if="row?.callNumber" :caseId="row.caseId" :workOrderId="row.id"
            :phoneState="row.phoneState || 0" :key="htrxCall" />
        </template>
      </el-table-column>
      <el-table-column label="工单状态" align="center" key="orderStatus" prop="orderStatus" v-if="columns[10].visible" />
      <el-table-column label="发起人/时间" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[11].visible"
        width="200">
        <template #default="scope">
          <div>{{ `${scope.row.createBy}` }}</div>
          <div>【{{ `${scope.row.createTime ?? "--"}` }}】</div>
        </template>
      </el-table-column>
      <el-table-column label="最后处理人/时间" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[12].visible"
        width="200">
        <template #default="scope">
          <div v-if="scope.row.orderStatus !== `待处理`">{{ `${scope.row.handler} ` }}</div>
          <div v-if="scope.row.orderStatus !== `待处理`">【{{ `${scope.row.processingTime} ` }}】</div>
          <span v-if="scope.row.orderStatus == `待处理`">--</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button type="text" v-hasPermi="['collection:myworkorder:check']"
            @click="workRegister(scope.row)">查看</el-button>
          <!-- <el-button
            type="text"
            v-if="scope.row.orderStatus == '待处理'"
            @click="signWorkOrder(1, scope.row)"
            >标记为已处理</el-button
          > -->
          <!-- <el-button
            type="text"
            v-if="scope.row.orderStatus == '待处理'&& scope.row.button == 1  || scope.row.orderStatus == '处理中' && scope.row.button == 1"
            @click="followUpWorkOrder(scope.row)"
            >继续跟进</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 继续跟进 -->
    <followUp ref="followUpRef" @getdetails="getdetails" />
  </div>
</template>

<script setup name="Myworkorder">
import { getOrderType, getOrderStatus } from "@/api/common/common";
import {
  getWorkOrder,
  updateWorkOrder,
  getWorkCount,
} from "@/api/collection/myworkorder";
import workOrder from "./dialog/workorder.vue";
import followUp from "./dialog/followUp.vue";
import callBarVue from "@/components/callBar/index.vue";


//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
const htrxCall = computed(() => store.getters.htrxCall);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const workIds = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    createTime: [],
    clientPhone: undefined,
    questionType: undefined,
    orderStatus: undefined,
  },
  rules: {
    clientPhone: [
      { required: false, message: "请输入手机号码", trigger: "blur" },

    ],
    clientIdcard: [
      { required: false, message: "请输入证件号码", trigger: "blur" },
    ],
  },
});
const { queryParams, rules } = toRefs(data);
//表格配置
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 50 },
  { key: 1, label: `UID`, visible: true, width: 50 },
  { key: 2, label: `产品类型`, visible: true, width: 200 },
  { key: 3, label: `问题类型`, visible: true, width: 100 },
  { key: 4, label: `姓名`, visible: true, width: 150 },
  { key: 5, label: `证件号码`, visible: true, width: 200 },
  { key: 6, label: `手机号码`, visible: true, width: 100 },
  { key: 7, label: `来电号码`, visible: true, width: 150 },
  { key: 8, label: `工单状态`, visible: true, width: 150 },
  { key: 9, label: `发起人/时间`, visible: true, width: 50 },
  { key: 10, label: `最后处理人/时间`, visible: true, width: 80 },
  { key: 11, label: `渠道来源`, visible: true, width: 120 },
  { key: 12, label: `证件类型`, visible: true, width: 120 },
]);
//表单配置信息
const showSearch = ref(false);
const tab = ref([
  { label: "待处理", count: 0, name: '待处理' },
  { label: "处理中", count: 0, name: '处理中' },
  { label: "已处理", count: 0, name: '已处理' },
  { label: "全部", count: 0, name: 'all' },
]);
const rangfiles = ["createTime"];
const activeTab = ref("待处理");
const caseList = ref([]);
const orderTypeList = ref([]);
const orderStatusList = ref([]);

//获取列表数据
function getList() {
  queryParams.value.orderStatus = activeTab.value == "all" ? undefined : activeTab.value;
  loading.value = true;
  getWorkOrder(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
      getCount();
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//获取工单数量
function getCount() {
  let req = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)));
  req.orderStatus = undefined;
  getWorkCount(req).then((res) => {
    if (res.data.length > 0) {
      tab.value.forEach(item1 => {
        const tabObj = res.data.find(item2 => item2.orderStatus == item1.label)
        item1.count = tabObj?.number || 0
      })
    } else {
      tab.value.forEach(item => item.count = 0);
    }
    tab.value[3].count = tab.value.reduce((sum, item) => sum += (item.count || 0), 0);
  })
}
provide("getList", Function, true);

//继续跟进
function followUpWorkOrder(row) {
  let id = row.id;
  proxy.$refs["followUpRef"].opendialog(id);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//问题类型下拉
function getOrderTypeList() {
  getOrderType().then((res) => {
    orderTypeList.value = res.data;
  });
}

//工单状态下拉
function getOrderStatusList() {
  getOrderStatus().then((res) => {
    orderStatusList.value = res.data;
  });
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    createTime: [],
    clientPhone: undefined,
    questionType: undefined,
    orderStatus: undefined,
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//工单查看
function workRegister(row) {
  store.dispatch("tagsView/delCachedView", { name: "CollectionWorkorderDetails" });
  router.push(`/collection/workorder/details/${row.id}`);
}

//选择列表
function handleSelectionChange(selection) {
  workIds.value = selection.map((item) => item.id);
}

//标记工单
function signWorkOrder(type, row) {
  if (type == 0 && workIds.value.length === 0) return proxy.$modal.msgWarning("请选择左侧工单！");
  let ids = type == 0 ? workIds.value : [row.id];
  proxy.$modal
    .confirm(`此操作将选中的工单标记为已处理，是否继续？`)
    .then(() => {
      loading.value = true;
      return updateWorkOrder(ids);
    })
    .then(() => {
      proxy.$modal.msgSuccess("标记成功");
      getList();
      getCount();
    }).catch(() => { loading.value = false; })
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = queryParams.value;
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

</script>

<style scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h32 {
  height: 32px !important;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}
</style>
