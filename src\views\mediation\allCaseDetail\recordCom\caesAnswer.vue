<template>
  <div class="caes-answer">
    <div class="mb10">
      评分：<span style="color: red; font-size: 18px; font-weight: bold">{{
        score
      }}</span>
      分
    </div>
    <div class="question-wrap">
      <div class="question-item" v-for="item in questions" :key="item.question">
        <div class="text">{{ item.text }}</div>
        <div class="answer-wrap">
          <div class="question">{{ item.question }}</div>
          <div class="answer">
            <el-radio-group v-model="item.answer" class="custom-radio">
              <el-radio :label="true" border>是</el-radio>
              <el-radio :label="false" border>否</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { selectNegotiateRecordList } from "@/api/mediation/allCaseDetail";

const store = useStore();
const score = computed(() => {
  return questions.value.reduce((acc, cur) => acc + (cur.answer ? 1 : 0), 0);
});
const questions = ref([
  {
    text: "核实本人，本人找得越多，越有利于达成调解律",
    question: "是否本人？",
    field: "self",
    answer: undefined,
  },
  {
    text: "我们作为调解中心，每一个达成协商调解方案的都应出具合理的调解协议书，有截止时间、有签名、有印章，以证合法合规！",
    question: "是否发调解告知函？",
    field: "sendMediationNotice",
    answer: undefined,
  },
  {
    text: "对账单信息要准确，不要自以为债务人不清楚，数据准确对方才会相信你的身份。",
    question: "是否核对欠款信息？",
    field: "checkArrears",
    answer: undefined,
  },
  {
    text: "要特殊减免的情况，对方是否愿意配合你，对方做的事情越多达成调解协议概率越高。",
    question: "对方是否发送负债截图？",
    field: "sendArrearsScreenshot",
    answer: undefined,
  },
  {
    text: "为了证实我们确实是真心实意在帮助对方，尽最大的努力要表现给对方知晓！",
    question: "特殊减免申请是否提供记录告知对方？",
    field: "specialReduction",
    answer: undefined,
  },
  {
    text: "不同的项目对应的还款方式不一，要清楚对方的情况，确认对方最能接受的处理方式。",
    question: "是否明确还款方式？",
    field: "repaymentMethodConfirmed",
    answer: undefined,
  },
  {
    text: "每达成一个处置方案都要确认下一次沟通时间，包括对方还款处置账单的具体时间，对时间进度的把控在自己手里。",
    question: "是否确定还款时间？",
    field: "repaymentTimeConfirmed",
    answer: undefined,
  },
  {
    text: "处理债务资金不是凭空而来，要摸清实打实是资金来源，期间会不会有其他动用该笔资金的可能性。",
    question: "是否摸清还款资金来源？",
    field: "fundingSourceConfirmed",
    answer: undefined,
  },
  {
    text: "所有未结清还款的都需要采用不同的线路去施压，不同身份能获取更多的不同信息。",
    question: "是否使用调解/律所线路通知？",
    field: "legalNoticeSent",
    answer: undefined,
  },
  {
    text: "愿意协商的人，就应采取最方便沟通的联系方式，方便提供资料材料。",
    question: "是否加上微信？",
    field: "wechatAdded",
    answer: undefined,
  },
]);
const caseId = inject("caseId");
selectNegotiateRecordList({
  pageNum: 1,
  pageSize: 1,
  caseId: caseId,
}).then((res) => {
  if (res.rows.length > 0) {
    const caseAnswersRecord = res.rows[0].caseAnswersRecord;
    questions.value.forEach((item) => {
      item.answer = caseAnswersRecord[item.field];
    });
    score.value = caseAnswersRecord.score;
  }
});
watch(
  () => questions.value,
  (newval, oldval) => {
    let answerObj = {};
    newval.forEach((item) => {
      answerObj[item.field] = item.answer === undefined ? false : item.answer;
    });
    Object.assign(answerObj, {
      score: score.value,
    });
    store.commit("allCaseDetail/SET_CASE_ANSWERS_RECORD", answerObj);
    store.commit("allCaseDetail/SET_SCORE", oldval ? score.value : undefined);
  },
  { deep: true, immediate: true }
);
</script>

<style scoped>
.question-wrap {
  padding: 10px;
  background-color: #f5f5f5;
}
.answer-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}
.question {
  padding: 15px 10px;
  background-color: #ffffff;
  border-radius: 5px;
  flex: 1;
  margin-right: 20px;
}
.question-item .text {
  font-size: 14px;
}
.answer {
  padding: 10px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 5px;
}
::v-deep(.custom-radio .el-radio__input) {
  display: none;
}
::v-deep(.custom-radio .el-radio):not(:last-child) {
  margin-right: 10px;
}
::v-deep(.custom-radio .el-radio__label) {
  padding-left: 0;
}
::v-deep(.custom-radio .el-radio.is-bordered.el-radio--small) {
  padding: 0 10px;
}
::v-deep(.custom-radio .el-radio.is-bordered) {
  padding-left: 15px;
}
</style>
