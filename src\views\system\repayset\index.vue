<template>
    <div class="app-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="add"
            v-hasPermi="['system:repayset:add']">添加还款渠道</el-button>
        </el-col>
      </el-row>
  
      <el-table :data="dataList" v-loading="loading">
        <el-table-column label="还款渠道" prop="repaymentMethod" align="center" />
        <el-table-column label="登记回款必填字段" prop="registerPayment" align="center" show-overflow-tooltip />
        <el-table-column label="自动对账字段" prop="reconciliation" align="center" show-overflow-tooltip />
        <el-table-column label="创建人" prop="createBy" align="center" />
        <el-table-column label="创建时间" prop="createTime" align="center" />
        <el-table-column label="备注" prop="remark" align="center">
          <template #default="{row}">
            <Tooltip :content="row.remark" :length="10" width="500" />
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="state" align="center">
          <template #default="{ row }">
            <el-switch v-if="checkPermi(['system:repayset:status'])" class="myswitch" v-model="row.state"
              active-color="#2ECC71" :active-value="0" :inactive-value="1" active-text="开" inactive-text="关"
              @change="change(row)"></el-switch>
            <span v-else>{{ row.state == 0 ? '开启' : '关闭' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="text" v-hasPermi="['system:repayset:edit']" @click="edit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="pageNum" v-model:limit="pageSize" @pagination="getList" />
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="134px">
          <el-form-item label="还款渠道名称" prop="repaymentMethod">
            <el-input v-model="form.repaymentMethod" placeholder="请输入还款渠道名称"></el-input>
          </el-form-item>
          <el-form-item label="登记回款必填字段" prop="registerPaymentArr">
            <el-select v-model="form.registerPaymentArr" multiple filterable :reserve-keyword="false" :teleported="false"
              placeholder="请输入或选择" style="width: 426px">
              <el-option v-for="item in registerPaymentArr" :key="item" :disabled="item == '贷方发生额/元(收入)'" :label="item"
                :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" show-word-limit maxlength="300" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup name="PayType">
  import {
    repaysetList,
    editState,
    getRequired,
    addRepayment,
    editRepayment,
  } from "@/api/system/repaySet";
  import { checkPermi } from "@/utils/permission";
  const { proxy } = getCurrentInstance();
  
  const loading = ref(false);
  const total = ref(0);
  const pageNum = ref(1);
  const pageSize = ref(10);
  const dataList = ref([]);
  
  const title = ref("");
  const open = ref(false);
  const subloading = ref(false);
  const registerPaymentArr = ref(['']);
  const reconciliationArr = ref([]);
  const data = reactive({
    form: {
      repaymentMethod: undefined,
      registerPaymentArr: ['贷方发生额/元(收入)'],
      reconciliationArr: [],
      remark: undefined,
    },
    rules: {
      repaymentMethod: [
        { required: true, message: "请填写还款渠道名称！", trigger: "blur" },
      ],
      registerPaymentArr: [
        { required: true, message: "请选择登记回款必填字段！", trigger: "change" },
      ],
      reconciliationArr: [
        { required: true, message: "请选择自动对账匹配字段！", trigger: "change" },
      ],
    },
  });
  const { form, rules } = toRefs(data);
  //获取列表
  function getList() {
    loading.value = true;
    let req = {
      pageNum:pageNum.value,
      pageSize:pageSize.value
    }
    repaysetList(req)
      .then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getList();
  
  //新建操作
  function add() {
    reset();
    getRequired().then((res) => {
      let data = res.data;
      registerPaymentArr.value = data.registerPaymentArr;
    //   reconciliationArr.value = data.reconciliationArr;
      title.value = "添加还款渠道";
      open.value = true;
    });
  }
  //编辑操作
  function edit(row) {
    reset();
    getRequired().then((res) => {
      let data = res.data;
      registerPaymentArr.value = data.registerPaymentArr;
    //   reconciliationArr.value = data.reconciliationArr;
      form.value.id = row.id;
      form.value.repaymentMethod = row.repaymentMethod;
      form.value.registerPaymentArr = row.registerPayment.split(";");
    //   form.value.reconciliationArr = row.reconciliation.split(";");
      form.value.remark = row.remark;
      title.value = "编辑还款渠道";
      open.value = true;
    });
  }
  //状态
  function change(row) {
    let req = {
      id: row.id,
      state: row.state,
    };
    editState(req).catch(() => {
      getList();
    });
  }
  
  //提交
  function submit() {
    subloading.value = true;
    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        let req = JSON.parse(JSON.stringify(form.value));
        req.registerPaymentArr =
          req.registerPaymentArr?.length === 0 ? undefined : req.registerPaymentArr;
        // req.reconciliationArr =
        //   req.reconciliationArr?.length === 0 ? undefined : req.reconciliationArr;
        if (form.value.id) {
          editRepayment(req)
            .then(() => {
              proxy.$modal.msgSuccess("修改成功！");
              getList();
              cancel();
            })
            .finally(() => {
              subloading.value = false;
            });
        } else {
          addRepayment(req)
            .then(() => {
              proxy.$modal.msgSuccess("新建成功！");
              getList();
              cancel();
            })
            .finally(() => {
              subloading.value = false;
            });
        }
      } else {
        subloading.value = false;
      }
    });
  }
  
  //取消
  function cancel() {
    reset();
    open.value = false;
  }
  
  //重置
  function reset() {
    proxy.resetForm("formRef");
    form.value = {
      repaymentMethod: undefined,
      registerPaymentArr: ['贷方发生额/元(收入)'],
    //   reconciliationArr: ['贷方发生额/元(收入)'],
      remark: undefined,
    };
  }
  </script>
  
  <style scoped></style>
  