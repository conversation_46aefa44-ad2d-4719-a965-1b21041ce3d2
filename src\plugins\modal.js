import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'

let loadingInstance;

let messageDom = null
const resetMessage = (options) => {
  if (messageDom) messageDom.close() // 判断弹窗是否已存在,若存在则关闭
  messageDom = ElMessage(options)
}
const typeArr = ['success', 'error', 'warning', 'info']
typeArr.forEach(type => {
  resetMessage[type] = options => {
    if (typeof options === 'string') options = { message: options }
    options.type = type
    return resetMessage(options)
  }
})
const mymessage = resetMessage;

export default {
  // 消息提示
  msg(content) {
    mymessage.info(content)
  },
  // 错误消息
  msgError(content) {
    mymessage.error(content)
  },
  // 成功消息
  msgSuccess(content) {
    mymessage.success(content)
  },
  // 警告消息
  msgWarning(content) {
    mymessage.warning(content)
  },
  // 弹出提示
  alert(content) {
    ElMessageBox.alert(content, "系统提示")
  },
  // 错误提示
  alertError(content) {
    ElMessageBox.alert(content, "系统提示", { type: 'error' })
  },
  // 成功提示
  alertSuccess(content) {
    ElMessageBox.alert(content, "系统提示", { type: 'success' })
  },
  // 警告提示
  alertWarning(content, title = "系统提示") {
    ElMessageBox.alert(content, title, { type: 'warning', dangerouslyUseHTMLString: true })
  },
  // 通知提示
  notify(content) {
    ElNotification.info(content)
  },
  // 错误通知
  notifyError(content) {
    ElNotification.error(content);
  },
  // 成功通知
  notifySuccess(content) {
    ElNotification.success(content)
  },
  // 警告通知
  notifyWarning(content) {
    ElNotification.warning(content)
  },
  // 确认窗体
  confirm(content, title = "系统提示", option = {}) {
    let options = {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: "warning",
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '提交中...'
          setTimeout(() => {
            done();
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 1000)
          }, 500)
        } else {
          done()
        }
      },
    }
    Object.assign(options, option)
    return ElMessageBox.confirm(content, title, options)
  },
  // 提交内容
  prompt(content) {
    return ElMessageBox.prompt(content, "系统提示", {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: "warning",
    })
  },
  // 打开遮罩层
  loading(content) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: "rgba(0, 0, 0, 0.7)",
    })
  },
  // 关闭遮罩层
  closeLoading() {
    loadingInstance.close();
  },
  exportTip(msg) {
    const tipMsg = `此导出操作已成功提交，请前往导出日志页面查看导出结果！<br/>任务文件名称：${msg}`
    this.alertWarning(tipMsg, '提示')
  }
}
