<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container mb20" style="color: #888888">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          组织架构
        </div>
        <el-input class="mb8" v-model="filterText" placeholder="请输入关键字" />
        <el-tree
          ref="treeRef"
          class="filter-tree"
          :data="tree"
          :props="defaultProps"
          :expand-on-click-node="false"
          check-on-click-node
          :render-after-expand="false"
          node-key="id"
          @node-click="getNode"
          :filter-node-method="filterNode"
        >
          <template #default="{ data }">
            <el-tooltip
              effect="light"
              :content="data.name"
              placement="right-start"
            >
              <span class="el-tree-node__label">{{ data.name }}</span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="20" :xs="24" v-if="radioType != undefined">
        <el-radio-group v-model="radioType" @tab-click="tabChange">
          <el-radio-button label="0" v-hasPermi="['team:apply:repay']">
            团队回款
          </el-radio-button>
          <el-radio-button label="1" v-hasPermi="['team:apply:reduce']">
            团队减免
          </el-radio-button>
          <el-radio-button label="2" v-hasPermi="['team:apply:stage']">
            团队分期
          </el-radio-button>
          <el-radio-button label="3" v-hasPermi="['team:apply:stay']">
            团队留案
          </el-radio-button>
          <el-radio-button label="4" v-hasPermi="['team:apply:stop']">
            团队停案
          </el-radio-button>
          <el-radio-button label="5" v-hasPermi="['team:apply:back']">
            团队退案
          </el-radio-button>
          <el-radio-button label="6" v-hasPermi="['team:apply:outsize']">
            团队外访
          </el-radio-button>
          <el-radio-button label="7" v-hasPermi="['team:apply:assest']">
            团队协案
          </el-radio-button>
          <el-radio-button label="9" v-hasPermi="['team:apply:lawsuit']">
            团队诉讼
          </el-radio-button>
          <!-- <el-radio-button label="8" v-hasPermi="['team:apply:data']">
            资料调取
          </el-radio-button> -->
        </el-radio-group>

        <!-- 回款列表 -->
        <returned v-if="radioType === '0'" :deptId="deptId" ref="returnRef" />

        <!-- 减免列表 -->
        <mitigate v-if="radioType === '1'" :deptId="deptId" ref="mitigateRef" />

        <!-- 减免列表 -->
        <stages v-if="radioType === '2'" :deptId="deptId" ref="stagesRef" />

        <!-- 留案 -->
        <optPublic
          v-if="radioType === '3'"
          :radioType="radioType"
          :deptId="deptId"
          ref="optPublicRef"
        />

        <!-- 停案 -->
        <optPublic
          v-if="radioType === '4'"
          :radioType="radioType"
          :deptId="deptId"
          ref="optPublicRef"
        />

        <!-- 退案 -->
        <optPublic
          v-if="radioType === '5'"
          :radioType="radioType"
          :deptId="deptId"
          ref="optPublicRef"
        />

        <!-- 外访 -->
        <outsize v-if="radioType === '6'" :deptId="deptId" ref="outsizeRef" />

        <!-- 协案 -->
        <assistrecord
          v-if="radioType === '7'"
          :deptId="deptId"
          ref="assistrecordRef"
        />

        <!-- 资料调取 -->
        <dataRetrievalVue v-if="radioType === '8'" ref="dataRetrievalVueRef" />

        <!-- 诉讼 -->
        <lawsuit v-if="radioType === '9'" ref="lawsuitRef" />
      </el-col>
    </el-row>
  </div>
</template>
<script setup name="TeamApply">
import { getTeamTree } from "@/api/team/team";
import returned from "./table/returned.vue";
import mitigate from "./table/mitigate.vue";
import stages from "./table/stages.vue";
import optPublic from "./table/optpublic.vue";
import outsize from "./table/outsize.vue";
import assistrecord from "./table/assistrecord.vue";
import dataRetrievalVue from "./table/dataRetrieval.vue";
import lawsuit from "./table/lawsuit.vue";
import { checkPermi } from "@/utils/permission";

//全局数据
const { proxy } = getCurrentInstance();
//树结构
const tree = ref([]);
const filterText = ref("");
const defaultProps = {
  children: "children",
  label: "name",
};
const deptId = ref(undefined);
const allDept = ref([{ id: "all", name: "全部" }]);
//tab列表
const radioType = ref("0");
//模块信息
const tableRef = ref([
  "returnRef",
  "mitigateRef",
  "stagesRef",
  "optPublicRef",
  "optPublicRef",
  "optPublicRef",
  "outsizeRef",
  "assistrecordRef",
  "dataRetrievalVueRef",
  "lawsuitRef",
]);

//获取部门列表optPublicRef
function getTeamTreeData() {
  getTeamTree().then((res) => {
    let formatterDept = [...allDept.value, ...res.data];
    tree.value = formatterDept;
  });
}
getTeamTreeData();

// 检测权限页面
function checkPermiRadio() {
  if (checkPermi(["team:apply:repay"])) {
    radioType.value = "0";
  } else if (checkPermi(["team:apply:reduce"])) {
    radioType.value = "1";
  } else if (checkPermi(["team:apply:stage"])) {
    radioType.value = "2";
  } else if (checkPermi(["team:apply:stay"])) {
    radioType.value = "3";
  } else if (checkPermi(["team:apply:stop"])) {
    radioType.value = "4";
  } else if (checkPermi(["team:apply:back"])) {
    radioType.value = "5";
  } else if (checkPermi(["team:apply:outsize"])) {
    radioType.value = "6";
  } else if (checkPermi(["team:apply:assest"])) {
    radioType.value = "7";
  } else if (checkPermi(["team:apply:data"])) {
    radioType.value = "8";
  } else if (checkPermi(["team:apply:lawsuit"])) {
    radioType.value = "9";
  } else {
    radioType.value = undefined;
  }
}
checkPermiRadio();

//节点点击事件
function getNode(node, details) {
  deptId.value = node?.id == "all" ? undefined : node.id;
  proxy.$refs[tableRef.value[radioType.value]].getList(deptId.value);
}

//表单切换
function tabChange() {
  deptId.value = undefined;
}

//机构过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};
//搜索部门
watch(filterText, (val) => {
  proxy.$refs["treeRef"].filter(val);
});
</script>
<style lang="scss" scoped>
:deep(.el-tree-node__label) {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
