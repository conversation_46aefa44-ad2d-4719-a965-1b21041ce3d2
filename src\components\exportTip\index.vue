<template>
  <el-dialog title="提示" v-model="open" width="600px" :before-close="cancel" append-to-body>
    <div class="export-tips">
        <span class="tip-icon"><el-icon :size="24" color="#f7ba2a"><WarningFilled /></el-icon></span>
        <span class="tip-text" v-if="exportType == `短信记录`">导出短信记录操作已成功提交，请前往导出日志页面查看导出结果！</span>
        <span class="tip-text" v-if="exportType == `案件导出`">此案件导出操作已成功提交，请前往导出日志页面查看导出结果！</span>
        <span class="tip-text" v-if="exportType == `团队案件导出`">此团队案件导出操作已成功提交，请前往导出日志页面查看导出结果！</span>
        <span class="tip-text" v-if="exportType == `沟通记录导出`">此沟通记录导出操作已成功提交，请前往导出日志页面查看导出结果！</span>
    </div>
    <div class="file-name">任务文件名称：{{fileName}}</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
const props = defineProps({
  exportType: { //导出类型
    type: [String],
    default: "",
  }
})
//全局配置
const { proxy } = getCurrentInstance();
//表单属性
const open = ref(false);
const fileName = ref(undefined);

function opendialog(name) {
  open.value = true;
  fileName.value = name;
}


//取消
function cancel() {
  fileName.value = undefined;
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
.tip-text{
  line-height: 30px;
  vertical-align: top;
  font-size: 16px;
  margin-left: 5px;
}
.file-name{
  margin-top:10px;
  font-size: 16px;
  line-height: 30px;
  margin-left: 30px;
}
</style>
