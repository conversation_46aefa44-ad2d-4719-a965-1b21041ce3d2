<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-position="right"
      class="form-content h-50 mt20"
      label-width="120px"
    >
      <el-form-item label="签章状态" prop="status">
        <el-select
          v-model="queryParams.signStatusList"
          placeholder="请选择签章状态"
          clearable
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
          :reserve-keyword="false"
          style="width: 240px"
        >
          <el-option
            v-for="item in statusList"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="函件单号" prop="serialNo">
        <el-input
          v-model="queryParams.serialNo"
          placeholder="请输入函件单号"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="签章时间" prop="examineTime">
        <el-date-picker
          v-model="queryParams.examineTime"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div class="text-center mb10">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >查询</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="mt10">
      <el-table v-loading="loading" :data="dataList" ref="multipleTableRef">
        <el-table-column label="函件单号" align="center" prop="serialNo" />
        <el-table-column
          label="签章状态"
          align="center"
          prop="signStatus"
          :formatter="statusFor"
        />
        <el-table-column
          label="签章时间"
          align="center"
          key="examineTime"
          prop="examineTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.examineTime ? scope.row.examineTime : "--" }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="250" label="操作">
          <template #default="scope">
            <el-button type="text" link @click="handlePreview(scope.row)">预览</el-button>
            <el-button
              type="text"
              v-if="scope.row.signStatus == 1"
              link
              @click="Download(scope.row)"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页器 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加预览 -->
    <!-- <previewFile ref="previewFileRef" /> -->
    <previewBox ref="previewBoxRef" />
  </div>
</template>

<script setup name="SigDocsList">
import previewFile from "@/components/previewFile";
import previewBox from "@/components/previewBox";
import { getItemList } from "@/api/signature/signatureDocuments";
import { getPreviewUrl } from "@/api/signature/aduit";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    signStatusList:[]
  },
});
const { queryParams } = toRefs(data);
const statusList = ref([
  { code: "0", info: "签章中" },
  { code: "1", info: "已签章" },
  { code: "2", info: "签章失败" },
  { code: "3", info: "待签章" },
]);
const dataList = ref([]);
const total = ref(0);
const rangFields = ["examineTime"]; //范围字段
//获取列表
function getList() {
  loading.value = true;
  let req = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields))
  );
  if (req.examineTime1 && req.examineTime2) {
    req.examineTime1 = `${req.examineTime1} 00:00:00`;
    req.examineTime2 = `${req.examineTime2} 23:59:59`;
  }
  req.signStatusList = req.signStatusList?.toString()
  getItemList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
  };

  getList();
}

// 预览
function handlePreview(row) {
  let req = {
    id: row.id,
  };
  getPreviewUrl(req)
    .then((res) => {
      if (res.data) {
        const resObj = res.data;
        nextTick(() => {
          if(resObj.total && resObj.total > 20){
            proxy.$modal.msgWarning('文件过大，当前浏览器不支持直接预览，已下载请在本地进行预览！')
            window.open(resObj.url)
          }else{
            proxy.$refs["previewBoxRef"].opendialog(resObj);
          }
        });
      } else {
        proxy.$modal.msgWarning("该批次没有预览的Pdf！");
      }
    })
    .catch((error) => {
      proxy.$modal.msgWarning("该批次没有预览的Pdf！");
    });
}

// 签章状态
function statusFor(row) {
  if (!row.signStatus) return "待签章";
  return ["签章中", "已签章", "签章失败", "待签章"][row.signStatus];
}

//下载资料
function Download(row) {
  if (row.signPreviewUrl) {
    window.open(row.signPreviewUrl);
  }
}
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.popover-file {
  max-height: 340px;
  overflow: auto;
}

.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.text-blue {
  color: var(--theme);
}

.text-blue:hover {
  color: #79bbff;
}

.file-path {
  margin: 10px;
}

.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
