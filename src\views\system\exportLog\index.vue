<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" class="demo-tabs">
      <el-tab-pane v-for="(item, index) in tabs" :key="index" :label="item.info" :name="item.info" />
    </el-tabs>
    <!-- 案件记录 -->
    <vCaseLog ref="vCaseLogRef" v-if="activeTab == `案件导出`" :activeTab="activeTab" />
    <!-- 短信记录 -->
    <vNoteLog ref="vNoteLogRef" v-if="activeTab == `短信导出`" :activeTab="activeTab" />
    <!-- 催记记录 -->
    <vReminder ref="vReminderRef" v-if="activeTab == `沟通记录导出`" :activeTab="activeTab" />
    <vTemplateFileLogs ref="vTemplateFileLogsRef" v-if="activeTab == `模板分案`" :activeTab="activeTab" />
  </div>
</template>

<script setup name="ExportLogSys">
import vCaseLog from "./page/caseLog";
import vNoteLog from "./page/noteLog";
import vReminder from "./page/reminderLog";
import vTemplateFileLogs from "./page/templateFileLogs";
import { checkPermi } from "@/utils/permission";
const activeTab = ref(undefined);
//全局配置
const { proxy } = getCurrentInstance();
// tab权限
const tabPanes = ref([
  { info: '短信导出', code: 'exportManage:exportLog:noteLog' },
  { info: '案件导出', code: 'exportManage:exportLog:caseLog' },
  { info: '沟通记录导出', code: 'exportManage:exportLog:reminder' },
  { info: '模板分案', code: 'exportManage:exportLog:templateFileLogs' },
])
//检测权限
function checkPower() {
  const tabs = tabPanes.value.filter(item => checkPermi([item.code]))
  activeTab.value = tabs.length == 0 ? undefined : tabs[0].info;
}
checkPower();

const tabs = computed(() => tabPanes.value.filter(item => checkPermi([item.code])))

</script>

<style></style>
