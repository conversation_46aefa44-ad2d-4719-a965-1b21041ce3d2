<template>
  <!-- 审核不通过 -->
  <el-dialog title="不通过" v-model="open" width="600px" append-to-body :before-close="cancel" :close-on-click-modal="false">
    <el-form :model="form" ref="formRef">
      <el-form-item label="不通过原因" prop="failureReason">
        <el-input
          v-model="form.failureReason"
          type="textarea"
          maxlength="300"
          show-word-limit
          rows="4"
          placeholder="请输入不通过原因"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          >确定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>

const emits = defineEmits(["notPass"]);
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
//提交数据
const data = reactive({
  form: {
    ids: undefined,
    failureReason: undefined,
    approveStart:1
  },
  rules: {
    failureReason: [{ required: true, message: "请输入不通过原因", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
function opendialog(req) {
  form.value.ids = req.ids;
  open.value = true;
}

//提交
function submit() {
  loading.value = true;
  emits('notPass',form.value)
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    ids: undefined,
    failureReason: undefined,
    approveStart:1
  };
}

//取消
function cancel() {
  if(loading.value) return false;
  reset();
  open.value = false;
}

function loadingChage(){
  loading.value = false;
}

defineExpose({
  loadingChage,
  opendialog,
  cancel,
});
</script>
<style scoped></style>
