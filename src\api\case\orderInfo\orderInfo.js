import request from '@/utils/request'

// 订单列表
export function orderList(query) {
  return request({
    url: '/projectOrderInfo/list',
    method: 'get',
    params: query
  })
}

// 订单详情
export function orderDetail(query) {
  return request({
    url: '/projectOrderInfo/selectOne',
    method: 'get',
    params: query
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/projectOrderInfo/insert',
    method: 'post',
    data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/projectOrderInfo/update',
    method: 'post',
    data
  })
}

// 删除订单
export function deleteOrder(ids) {
  return request({
    url: '/projectOrderInfo/delete',
    method: 'post',
    data: ids
  })
}

// 出单登记
export function orderSign(data) {
  return request({
    url: '/projectOrderInfo/outOrder',
    method: 'post',
    data
  })
}

// 获取调解员下拉
export function getSolverSelect() {
  return request({
    url: '/data/outline/getEmployee',
    method: 'get'
  })
}

// 获取项目下拉
export function getProjectSelect() {
  return request({
    url: '/teamManage/option',
    method: 'get',
    gateway: 'cis'
  })
}