<template>
  <div class="app-container">
    <div class="add-template">
      <div class="wcc-el-steps">
        <el-steps :active="stepActive" align-center>
          <el-step :title="`${isCompress ? '导入' : '创建'}模板`" :description="`编辑模板${isCompress ? '相关' : '正文'}内容`" />
          <el-step title="签章" description="选择签章机构及律师"></el-step>
          <el-step title="预览完成" description="完成模板创建"></el-step>
        </el-steps>
      </div>
      <!-- 创建模板 -->
      <div v-if="stepActive === 0" class="step-item pt20">
        <el-form :model="form" :rules="rules" ref="formActiveRef" label-width="180px">
          <el-form-item label="模板类型" prop="classifyIds">
            <el-cascader class="cascader-select" v-model="form.classifyIds" :options="templateTypeList" />
          </el-form-item>
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="form.templateName"
              @input="form.templateName=form.templateName.replace(/\s/g, '')"
              placeholder="输入模板名称" style="width: 400px"
              @blur="checkTemplateName" maxlength="100" show-word-limit />
          </el-form-item>
          <div v-if="!isCompress">
            <el-form-item label="正文内容" prop="bodyContent">
              <div class="template-item" v-if="stepActive === 0">
                <!-- <myEditor v-model:modelValue="form.bodyContent" ref="myEditorRef" :imgUploadUrl="upload.url" /> -->
                <RichEditor v-model:modelValue="form.bodyContent" :key="form" ref="richEditorRef" imgUploadUrl="/document/template/upload" />
              </div>
              <div class="template-botton-wrap">
                <div class="template-botton">
                  <el-button v-for="(item, index) in paramsList" :key="index" type="primary" plain
                    @click="setContent(item.info)">
                    {{ item.info && item.info.length > 8 ? `${item.info.substring(0, 8)}...]` : item.info }}
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="增加页眉" prop="pageHeader">
              <el-upload ref="uploadHeadRef" accept=".png, .jpg, .jpeg" :headers="upload.headers" :action="upload.url"
                :before-upload="handleCoverUploadBefore" :on-change="handleBackChange" :limit="1"
                :on-success="handleBackFileSuccess" :file-list="fileHeaderList" :auto-upload="false"
                :on-remove="handleBackRemove" list-type="picture-card"
                :class="{ concealcrad: (form.pageHeader && form.pageHeader != '' ? [form.pageHeader] : []).length == 1, }">
                <el-icon>
                  <Plus />
                </el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    选择上传，上传内容为png、jpg、jpeg、内容不能超过20M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="增加页脚" prop="pageFooter">
              <el-upload ref="uploadFootRef" accept=".png, .jpg, .jpeg" :headers="upload.headers" :action="upload.url"
                :before-upload="handleCoverUploadBefore" :on-change="handleFootChange" :limit="1"
                :on-success="handleFootFileSuccess" :file-list="fileFooterList" :auto-upload="false"
                :on-remove="handleFootRemove" list-type="picture-card"
                :class="{ concealcrad: (form.pageFooter && form.pageFooter != '' ? [form.pageFooter] : []).length == 1, }">
                <el-icon>
                  <Plus />
                </el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    选择上传，上传内容为png、jpg、jpeg、内容不能超过20M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
          <div v-else>
            <el-form-item label="上传模板" prop="previewUrl">
              <ImportTemplatePdf v-model:sourceFileUrl="form.previewUrl" v-model:fileName="form.fileName"
                fileType=".pdf" @getPage="handleGetPage" />
            </el-form-item>
          </div>
        </el-form>

        <div class="text-center mt20">
          <el-button @click="toBack">返回</el-button>
          <el-button class="ml10" type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
        </div>
      </div>

      <!-- 签章 -->
      <div v-show="stepActive === 1" class="step-item pt20">
        <el-form :model="form" :rules="rules" ref="signFormRef" class="sign-form" label-position="right"
          :label-width="120">
          <div v-for="(item, index) in form.lawyer" :key="index" class="template-sign">
            <el-form-item label="签章名称" :prop="`lawyer.${index}.lawId`" :rules="rules.lawId">
              <el-select v-model="form.lawyer[index].lawId" placeholder="请选择签章名称" clearable filterable
                :reserve-keyword="false" style="width: 400px" @focus="getLawList"
                @change="getAttorneyList(index, $event)">
                <el-option v-for="item in lawList" :key="item.id" :label="item.signName" :value="item.id" />
              </el-select>
              <el-space class="ml20" v-if="index === 0">
                <el-button @click="addSign()">添加</el-button>
                <el-button :disabled="form.lawyer.length === 1" @click="delSign()">删除</el-button>
              </el-space>
            </el-form-item>
          </div>
        </el-form>
        <div class="template-preview-chapter">
          <div class="template-preview">
            <div ref="previewContentRef" class="template-preview-content">
              <previewPdf v-if="pdfSrcNotSign && pdfSrcNotSign.length > 0" :key="pdfSrcNotSign" :pdfSrc="pdfSrcNotSign"
                v-loading="pdfLoading" :total="totalNotSign" />
            </div>
            <div ref="previewRef" class="template-preview-sign-area" @dragover="handleDragover"
              @dragleave="handleDragleave" @drop="handleDrop" />
            <template v-if="route.params.id">
              <div class="template-chapter-item" draggable="true" v-for="(item, index) in positionListLeft()"
                ref="dragElRef" data-old="true" @dragstart="handleDragstart(item.index, $event)" @mousedown="getMouse"
                :key="index" :style="`position: absolute;left:${item.left}px; top:${item.top}px; z-index:${index + 3};`"
                :data-chapter-info="JSON.stringify(item)">
                <img class="chapter-img" :src="item.signPic || item.info" alt="" />
              </div>
            </template>
          </div>
          <div ref="chapterListRef" class="template-chapter-list ml10">
            <template v-for="(item, index) in positionListRight()" :key="index">
              <div class="template-chapter-item" draggable="true" v-if="item.signPic || item.info" ref="dragElRef"
                @dragstart="handleDragstart(item.index, $event)" @mousedown="getMouse" :data-chapter-info="item.index">
                <img class="chapter-img" :src="item.signPic || item.info" alt="" />
              </div>
            </template>
          </div>
        </div>

        <div class="text-center mt20">
          <el-button @click="backstep">上一步</el-button>
          <el-button class="ml10" @click="toBack">取消</el-button>
          <el-button class="ml10" type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
        </div>
      </div>

      <!-- 预览完成 -->
      <div v-if="stepActive === 2" class="step-item pt20">
        <div class="case-pdf">
          <previewPdf v-if="pdfSrc && pdfSrc.length > 0" :pdfSrc="pdfSrc" :key="pdfSrc" ref="previewPdfRef-2"
            :total="total">
          </previewPdf>
        </div>
        <div class="text-center mt20">
          <el-button @click="backFootStrp">返回上一步</el-button>
          <el-button class="ml10" type="primary" :loading="loading" plain @click="submitInfo(form.value)"> 保存
          </el-button>
        </div>
      </div>
    </div>

    <!-- 设置参数 -->
    <addParamsVue ref="addParamsRef" @getList="getParamsList" />
  </div>
</template>

<script setup name="AddTemplate">
import RichEditor from '@/components/RichEditor';
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
import { getToken } from "@/utils/auth";
import ImportTemplatePdf from "./importTemplatePdf.vue";
import addParamsVue from "./dialog/addParams.vue";
import {
  getClassifyList,
  getLawOption,
  getAttorneyOption,
  getOpenOptions,
  checkUniqueName,
  getDetails,
  addTemplate,
  editTemplate,
  createTemplatePreview,
  createTemplatePdf,
  creatTemplatePDFZip,
  creatPreviewPDF,
  getPages,
} from "@/api/writ/template";
//全局变量
const { proxy } = getCurrentInstance();
const route = useRoute();
//上传
const fileHeaderList = ref([]); //页眉数组
const fileFooterList = ref([]); //页眉数组
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/document/template/upload",
});
const loading = ref(false);
const pdfLoading = ref(false);
const subLoading = ref(false);
//新增编辑
const typeInfo = ref([addTemplate, editTemplate, creatTemplatePDFZip]);
const type = ref(0);
//表单参数
const data = reactive({
  form: {
    classifyIds: undefined,
    templateName: undefined,
    bodyContent: undefined,
    lawyer: [{ lawId: undefined }],
    status: 0,
    pageHeader: undefined,
    pageFooter: undefined,
    scaleHeader: undefined,
    scaleFooter: undefined,
    previewUrl: undefined,
    positionList: [],
  },
  rules: {
    classifyIds: [
      { required: true, message: "请选择模板类型！", trigger: "change" },
    ],
    templateName: [
      { required: true, message: "请选择模板名称！", trigger: "blur" },
    ],
    bodyContent: [
      { required: true, message: "请输入正文内容", trigger: "blur" },
    ],
    previewUrl: [
      { required: true, message: "请选择模板！", trigger: "change" },
    ],
  },
});
const { form, rules } = toRefs(data);
//当前操作进度
const stepActive = ref(0);
//模板类型
const templateTypeList = ref([]);
const lawList = ref([]);
//模板参数
const paramsList = ref([]);
//签章机构
const attorneyList = ref([]);
//签章链接
const total = ref(undefined);
const pdfSrc = ref(undefined);
const pdfSrcNotSign = ref(undefined);
const detailSrc = ref(undefined);
const totalNotSign = ref(undefined);
const timerId = ref(null) // 延时器
const isCompress = ref(false);
onBeforeUnmount(() => {
  document.querySelector(".add-template").style.display = "none";
});
//获取分类数据
function getClassify() {
  getClassifyList().then((res) => {
    templateTypeList.value = res.data;
  });
}
getClassify();

//获取签章机构
function getLawList() {
  getLawOption().then((res) => {
    lawList.value = res.data;
  });
}

//选项-获取代理律师（签章名称）

function getAttorneyList(index, lawId) {
  form.value.lawyer[index] = {};
  form.value.lawyer[index].lawId = lawId;
  if (lawId) {
    const lawObj = lawList.value.find((law) => law.id == lawId);
    form.value.positionList[index] = {
      ...lawObj,
      index: index,
      signType: 1,
      signId: lawObj.code,
      lawId: lawId,
      signCode: lawObj.signCode,
      signerId: lawObj.signCode,
      location: "right",
    };
  } else {
    form.value.positionList[index] = { index, location: "right", };
    form.value.lawyer[index].signId = "";
  }
}

//选项-获取模板变量
function getParamsList() {
  getOpenOptions().then((res) => {
    paramsList.value = res.data;
  });
}
getParamsList();

//检测模板名称
function checkTemplateName() {
  if (form.value.templateName && form.value.templateName.length > 0) {
    let req = {
      id: type.value == 1 ? route.params.id : undefined,
      templateName: form.value.templateName,
    };
    checkUniqueName(req).then((res) => {
      if (res.data == 1) {
        form.value.templateName = "";
        proxy.$modal.msgError(res.data.msg);
      }
    });
  }
}

//设置模板
function setContent(text) {
  // proxy.$refs["myEditorRef"].getBookMark(text);
  proxy.$refs["richEditorRef"].insertText(text);
}

// ------------------------------  签章 start ----------------------------------------

// 添加签章
function addSign() {
  data.form.lawyer.push({ lawId: undefined, signId: undefined });
}
// 删除签章
function delSign() {
  const index = data.form.lawyer && data.form.lawyer.length - 1;
  data.form.positionList[index] && data.form.positionList.splice(index, 1);
  data.form.lawyer.pop();
}

let mouseX = 0;
let mouseY = 0;
// 获取鼠标在图片的位置
function getMouse(e) {
  mouseX = e.offsetX;
  mouseY = e.offsetY;
}
// 获取拖动元素
let signImg = null;

// 拖动第几个元素
let index = 0;

// 设置 z-index 层级变量
let zIndex = 3;

// 存放编辑时，拖动的图片src径路
let signImgSrc = undefined;

// 开始拖动签章
function handleDragstart(i, e) {
  signImg = e.target.parentNode;
  if (route.params.id) {
    const positionList = data.form.positionList[i];
    signImgSrc = positionList.signPic || positionList.info;
    e.target.src = positionList.signPic || positionList.info;
  }

  index = i;
  if (signImg.style["z-index"] !== "") {
    signImg.style["zIndex"] = 1000;
    ++zIndex;
    proxy.$refs["previewRef"].style["zIndex"] = 999;
  }
}

// 目标元素事件
// 拖动元素在目标元素区域中拖动
function handleDragover(e) {
  e.preventDefault();
}

let pageLength = 1123;

// 鼠标放下的事件
function handleDrop(e) {
  let top = e.offsetY - mouseY;
  let left = e.offsetX - mouseX;
  const newZIndex = zIndex < 3 ? 3 : zIndex
  signImg.style = `position: absolute;top: ${top}px;left: ${left}px;z-index:${newZIndex};`;
  data.form.positionList[index].top = top;
  data.form.positionList[index].left = left;
  const location = data.form.positionList[index].signPic ? "right" : "left";
  data.form.positionList[index].location = location;
  let pageNum = Math.ceil(top / pageLength < 1 ? 1 : top / pageLength);

  let bottom = 0;
  if (pageNum > 1) {
    bottom = top % 1123;
  } else {
    bottom = top;
  }
  data.form.positionList[index].pageNum = pageNum;
  data.form.positionList[index].bottom = bottom;

  const { code, info, signName, signPic } = data.form.positionList[index];
  data.form.positionList[index].signName = code || signName;
  data.form.positionList[index].signPic = info || signPic;
  data.form.positionList[index].index = index;
  proxy.$refs["previewRef"].style["zIndex"] = 2;
  const isOld = signImg.dataset["old"];
  if (
    route.params.id &&
    signImg.firstChild.tagName == "IMG" &&
    isOld &&
    signImgSrc
  ) {
    signImg.innerHTML = `<img class="chapter-img" src=${signImgSrc}>`;
  }
}

// ------------------------------  签章 end -------------------------------------------

//返回
function toBack() {
  const obj = { path: "/signature/writ-template" };
  proxy.$tab.closeOpenPage(obj);
}

//上一步
function backstep() {
  switch (stepActive.value) {
    case 1:
      fileFooterList.value = form.value.pageFooter ? [{ url: form.value.pageFooter }] : []
      fileHeaderList.value = form.value.pageHeader ? [{ url: form.value.pageHeader }] : []
      stepActive.value--;
      pdfLoading.value = false;
      break;
    case 2:
      stepActive.value--;
      pdfLoading.value = false;
      break;
  }
}
let flag = false;
//第三步上一步
function backFootStrp() {
  flag = false;
  stepActive.value--;
}

function nextstep() {
  switch (stepActive.value) {
    case 0:
      if (!flag) {
        proxy.$refs["formActiveRef"].validate((valid) => {
          if (valid) {
            flag = true;
            pdfSrcNotSign.value = undefined;
            pdfLoading.value = true;
            if (!isCompress.value) {
              const req = JSON.parse(JSON.stringify(form.value));
              pdfSrcNotSign.value = undefined;
              req.pageHeader = req.pageHeader || "";
              req.pageFooter = req.pageFooter || "";
              req.scaleHeader = req.scaleHeader || "";
              req.scaleFooter = req.scaleFooter || "";
              createTemplatePdf(req)
                .then((res) => {
                  if (res.code == 200) {
                    pdfSrcNotSign.value = res.data.url;
                    totalNotSign.value = res.data.total;
                    if (stepActive.value == 0) {
                      stepActive.value++;
                    }
                    const height = pageLength * res.data.total + res.data.total
                    proxy.$refs["previewContentRef"].style.height = `${height}px`;
                    pdfLoading.value = false;
                    flag = false;
                  }
                })
                .catch((err) => {
                  pdfLoading.value = false;
                  flag = false;
                });
            } else {
              if (stepActive.value == 0) {
                stepActive.value++;
                pdfLoading.value = false;
                flag = false;
                pdfSrcNotSign.value = detailSrc.value;
                if (route.params.id) {
                  nextTick(() => {
                    const height = pageLength * totalNotSign.value + totalNotSign.value
                    proxy.$refs["previewContentRef"].style.height = `${height}px`;
                  });
                }
              }
            }

            if (type.value == 1) {
              getAttorneyList();
            }
          }
        });
      }

      break;
    case 1:
      if (!flag) {
        proxy.$refs["signFormRef"].validate((valid) => {
          if (valid) {
            flag = true;
            let req = JSON.parse(JSON.stringify(form.value));
            req.positionList = req.positionList.filter((sign, index) => {
              if (sign) {
                sign.signPic = sign.info || sign.signPic;
                sign.lawyerInfo = req.lawyer[index];
              }
              return sign;
            });
            pdfSrc.value = undefined;
            total.value = undefined;
            pdfLoading.value = true;
            req.pageHeader = req.pageHeader || "";
            req.pageFooter = req.pageFooter || "";
            req.scaleHeader = req.scaleHeader || "";
            req.scaleFooter = req.scaleFooter || "";
            req.originalUrl = detailSrc.value;
            req.previewUrl = detailSrc.value;
            req.sourceFileType = isCompress.value ? 1 : 0;
            req.positionList = req.positionList.filter(item => item.bottom)
            const request = isCompress.value
              ? creatPreviewPDF
              : createTemplatePreview;
            request(req)
              .then((res) => {
                if (stepActive.value == 1) {
                  stepActive.value++;
                  if (res.data) {
                    nextTick(() => {
                      pdfSrc.value = res.data.url;
                      total.value = res.data.total;
                    });
                  }
                }
                pdfLoading.value = false;
                flag = false;
              })
              .catch((err) => {
                flag = false;
                pdfLoading.value = false;
              });
          }
        });
      }
      break;
  }
}

/** 文件上传前的处理*/
const handleCoverUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    return false;
  }
};

//文件列表变化
function handleBackChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    fileList.pop();
    return false;
  }
  if (!checkFileType(file)) {
    proxy.$modal.msgWarning("请上传正确的文件格式!");
    fileList.pop();
    fileHeaderList.value.pop();
    return false;
  }
  if (fileList.length > 0) {
    fileHeaderList.value = fileList;
  }
  if (fileList.length > 0) {
    proxy.$refs[`uploadHeadRef`].submit();
  }
}
//检查文件格式
function checkFileType(file) {
  //文件名
  let fileName = file.name;
  //获取最后一个.的位置
  var index = fileName.lastIndexOf(".");
  //获取后缀
  let fileType = fileName.substring(index + 1);
  //判断是否合法后缀
  return isAssetTypeAnImage(fileType);
}
//判断是否合法的格式
function isAssetTypeAnImage(ext) {
  return ["png", "jpg", "jpeg"].indexOf(ext.toLowerCase()) !== -1;
}
/* 上传文件上传成功处理 */
const handleBackFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.pageHeader = data.fileUrl;
    form.value.scaleHeader = data.scaleFileUrl;
    fileHeaderList.value = [{ url: data.fileUrl }]
  }
};

//删除
function handleBackRemove(file) {
  form.value.pageHeader = undefined;
  form.value.scaleHeader = undefined;
  fileHeaderList.value = [];
}

//文件列表变化
function handleFootChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    fileList.pop();
    return false;
  }
  if (!checkFileType(file)) {
    proxy.$modal.msgWarning("请上传正确的文件格式!");
    fileFooterList.value.pop();
    fileList.pop();
    return false;
  }
  if (fileList.length > 0) {
    fileFooterList.value = fileList;
  }
  if (fileList.length > 0) {
    proxy.$refs[`uploadFootRef`].submit();
  }
}

/* 上传文件上传成功处理 */
const handleFootFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.pageFooter = data.fileUrl;
    fileFooterList.value = [{ url: data.fileUrl }];
    form.value.scaleFooter = data.scaleFileUrl;
  }
};

//删除
function handleFootRemove(file) {
  form.value.pageFooter = undefined;
  form.value.scaleFooter = undefined;
  fileFooterList.value = [];
}

//提交接口
function submitInfo() {
  if (!flag) {
    flag = true;
    let req = JSON.parse(JSON.stringify(form.value));
    req.signId = req.signId == "" ? null : Number(req.signId);
    req.lawId = Number(req.lawId);
    req.classifyLabel = req.classifyIds.toString().replace(",", "/");
    req.previewUrl = pdfSrc.value;
    req.originalUrl = detailSrc.value;
    req.sourceFileType = isCompress.value ? 1 : 0;
    req.positionList = req.positionList.filter((sign, index) => {
      if (sign) {
        sign.lawyerInfo = req.lawyer[index];
        sign.signPic = sign.info || sign.signPic;
      }
      return sign;
    });
    req.pageHeader = req.pageHeader || "";
    req.pageFooter = req.pageFooter || "";
    req.scaleHeader = req.scaleHeader || "";
    req.scaleFooter = req.scaleFooter || "";
    let typeIndex = isCompress.value ? 2 : type.value;
    if (route.params.id) {
      typeIndex = 1;
    }
    req.positionList = req.positionList.filter(item => item.bottom)
    typeInfo.value[typeIndex](req)
      .then((res) => {
        proxy.$modal.msgSuccess("操作成功");
        flag = false;
        toBack();
      })
      .catch(() => {
        flag = false;
        loading.value = false;
        subLoading.value = false;
      });
  }
}

// 获取-页数
function handleGetPage(pdfUrl) {
  pdfSrcNotSign.value = undefined;
  detailSrc.value = undefined;
  totalNotSign.value = undefined;
  getPages({ pdfUrl }).then((res) => {
    const height = pageLength * res.data + res.data
    proxy.$refs["previewContentRef"].style.height = `${height}px`;
    detailSrc.value = pdfUrl;
    totalNotSign.value = res.data;
  });
}

// 计算-盖章的个数
function positionListLeft() {
  return (
    form.value.positionList &&
    form.value.positionList?.filter((sign) => sign.location == "left")
  );
}
// 计算-没盖章的个数
function positionListRight() {
  console.log('positionListRight', form.value.positionList?.filter((sign) => sign.location == "right"));
  return (
    form.value.positionList &&
    form.value.positionList?.filter((sign) => sign.location == "right")
  );
}

// 监听 签章个数
watch(
  () => form.value.positionList && form.value.positionList?.length,
  () => {
    form.value.positionList &&
      form.value.positionList?.forEach((sign, index) => {
        sign.index = index;
      });
  },
  { immediate: true }
);

onMounted(() => {
  if (Boolean(route.query.isCompress)) {
    isCompress.value = Boolean(route.query.isCompress);
  }
});
onMounted(() => {
  if (route.params.id) {
    getLawList();
    let req = { id: route.params.id };
    getAttorneyOption({ lawId: route.params.id }).then((res) => {
      attorneyList.value = res.data;
    });
    getDetails(req).then((res) => {
      const { originalUrl, previewPages, sourceFileType, classifyLabel, bodyContent, pageHeader, pageFooter, lawId } = res.data
      form.value = res.data;
      type.value = 1;
      const positionList = res.data?.positionDat ? JSON.parse(res.data?.positionData || []) : []
      detailSrc.value = originalUrl;
      totalNotSign.value = previewPages;
      isCompress.value = sourceFileType != 0;
      const newPositionList = positionList
      newPositionList?.forEach((sign) => sign.location = "left");
      form.value.classifyIds = classifyLabel?.split("/");
      form.value.lawId = lawId?.toString();
      form.value.bodyContent = bodyContent
      form.value.positionList = newPositionList
      form.value.lawyer = newPositionList.map((sign) => ({ lawId: sign.lawId ? +sign.lawId : undefined }));
      if (newPositionList.length == 0) {
        form.value.lawyer = [{ lawId: undefined },];
      }
      fileHeaderList.value = pageHeader ? [{ url: pageHeader }] : [];
      fileFooterList.value = pageFooter ? [{ url: pageFooter }] : [];
    });
  } else {
    type.value = 0;
  }
});

// watch(() => form.value.bodyContent, () => {
//   clearTimeout(timerId.value)
//   if (form.value.bodyContent) {
//     let bodyContent = form.value.bodyContent.replace(/<\/?[^>]*>/g, '')
//     bodyContent = bodyContent.replace(/[|]*\n/, '')
//     bodyContent = bodyContent.replace(/&nbsp;/ig, '')
//     timerId.value = setTimeout(() => {
//       const numText = bodyContent ? bodyContent.length : 0
//       document.querySelector('.tox-statusbar__wordcount').innerText = `${numText}字`
//     }, 200)
//   }
// }, { deep: true })
</script>
<style>
.el-cascader-menu__wrap.el-scrollbar__wrap {
  min-height: 44px !important;
  height: auto;
  max-height: 204px !important;
}

.concealcrad .el-upload--picture-card {
  display: none;
}

img.chapter-img {
  display: block !important;
  /* width: 157px !important;
  height: 158px !important; */
}
</style>
<style lang="scss" scoped>
.case-pdf {
  width: 794px;
  height: 500px;
  margin: 30px auto 60px auto;
  overflow: auto;
  overflow-x: hidden;
}

.sign-name {
  position: absolute;
  font-family: 宋体;
  display: flex;
  width: 138px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-left: 18px;
  z-index: -1;
  margin-top: 22px;
}

.step-item {
  width: 95%;
  min-height: 400px;
  margin: 40px auto;

  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;

    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;

      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.params-text {
  display: inline-block;
  line-height: 32px;
  vertical-align: text-bottom;
}

.hint-div {
  align-self: start;
  display: flex;
  margin-top: 8px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.template-item {
  width: 730px;
  height: 500px;
  display: inline-block;
  margin-right: 10px;
}

.template-botton {
  width: 200px;
  height: 470px;
  overflow: auto;
  overflow-x: hidden;
  display: inline-block;
  text-align: left;

  button {
    display: block;
    margin-bottom: 10px;
    width: 150px;
  }
}

.sign-box {
  .law-label {
    position: absolute;
    min-width: 150px;
    text-align: center;
    top: 75px;
    left: 75px;
    font-size: 15px;
  }

  .law-person {
    position: absolute;
    top: 100px;
    min-width: 150px;
    text-align: center;
    left: 80px;
  }

  .law-time {
    position: absolute;
    top: 180px;
    left: 100px;
  }

  .sign-img {
    width: 300px;
    height: 300px;
  }
}

.template-preview-chapter {
  position: relative;
  display: flex;

  .template-preview {
    position: relative;
    width: 794px;
    min-height: 1123px;

    .template-preview-sign-area {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 1px solid #999;
    }

    .template-preview-content {
      width: 794px;
      min-height: 1123px;
    }

    .template-preview-sign-area {
      z-index: 2;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }

  .template-chapter-list {
    &>div {
      margin-bottom: 10px;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0px;
}

:deep(.el-upload-list__item-name) {
  width: 180px;
}

:deep(.el-upload-list__item-preview) {
  .el-icon {
    display: none;
  }
}
</style>
