<template>
    <el-dialog title="预览" v-model="open" width="940px">
        <div class="content">
            <previewPdf ref="previewPdfRef" :pdfSrc="writInfo?.previewUrl" :total="writInfo?.previewPages" />
        </div>
    </el-dialog>
</template>
<script setup>
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
const open = ref(false)
const loading = ref(false)
const writInfo = ref({})
function openDialog(row) {
    open.value = true
    writInfo.value = row
}
function cancel() {
    open.value = false
}
// 获取样式
function getPageStyle(page) {
    const height = page ? page * 1123 + page - 1 : 1123
    const TBpadding = page ? page * 35 : 35
    return `height:${height}px;padding:${TBpadding}px 52px;`
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped>
.content {
    width: 794px;
    height: 70vh;
    margin: 0 auto;
    overflow-y: auto;
    border: 1px solid #ccc;

    .writ-content {
        min-height: 1124px;
        border: 1px solid transparent;
    }
}
</style>