<template>
    <el-dialog title="关联文书" :before-close="cancel" v-model="open" width="600px">
        <div class="box">
            <el-row :gutter="24" v-for="item in writList" :key="item.id">
                <el-col :span="9">{{ item.classifyLabel }}：</el-col>
                <el-col :span="11"><img class="pdf_png" src="@/assets/images/PDF.png" alt="">
                    {{ item.templateName }}.pdf
                </el-col>
                <el-col :span="4">
                    <el-button @click="handlePreview(item)" type="text">预览</el-button>
                </el-col>
            </el-row>
        </div>

    </el-dialog>
</template>
<script setup>
import { getWritTemplateByCourtId } from '@/api/institutionManage/institutionManage';
const { proxy } = getCurrentInstance()
const props = defineProps({ openPreivewPdf: { type: Function } })
const open = ref(false)
const loading = ref(false)
const writList = ref([])

// 预览
function handlePreview(row) {
    props.openPreivewPdf(row)
}

// 打开
function openDialog(data) {
    open.value = true
    getWritTemplate(data.courtId)
}

// 根据机构查询文书模板
function getWritTemplate(courtId) {
    getWritTemplateByCourtId({ courtId }).then(res => {
        writList.value = res.data
    })
}

// 关闭
function cancel() {
    open.value = false
    writList.value = []
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped>
.pdf_png {
    width: 20px;
    margin-right: 10px;
}

.box {
    height: 50vh;
    padding: 0 20px;
    overflow-y: auto;
}
</style>