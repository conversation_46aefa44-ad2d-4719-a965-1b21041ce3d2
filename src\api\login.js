import request from '@/utils/request'
// 登录方法
export function login(data) {
    return request({
        url: '/sign/login',
        method: 'post',
        data: data
    })
}

//合规先导
export function getLoginPersuade(data) {
    return request({
        url: '/sign/selectComplianceAdvocacy',
        method: 'get',
    })
}

//判断是否在合规宣导时点击确定
export function lastLoginTime() {
    return request({
        url: '/sign/lastLoginTime',
        method: 'get',
    })
}

//根据登录用户查询修改密码时间（判断是否已90天未修改密码)
export function selectPasswordTime() {
    return request({
        url: '/sign/selectPasswordTime',
        method: 'get',
    })
}

//判断是否在合规宣导时点击确定
export function signGetInfo() {
    return request({
        url: '/sign/getInfo',
        method: 'get',
    })
}

export function getInfo() {
    return new Promise(reslove => {
        reslove(true)
    })
}

export function logout() {
    return new Promise(reslove => {
        reslove(true)
    })
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: '/sign/code',
        headers: {
            isToken: false
        },
        method: 'get',
        timeout: 20000
    })
}

//发送短信验证码登录
export function sendSmsCode(data) {
    return request({
      url: '/sign/sendSmsCode',
      method: 'post',
      data:data
    })
}


//发送短信验证码
export function smsLogin(data) {
    return request({
        url: '/sign/smsLogin',
        method: 'post',
        data:data
    })
}
  
// 404报错
export function getPath(params) {
    return request({ url: '/sign/getPath', method: 'get', params })
}