<template>
  <div class="chudan-dengji">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item prop="repaymentDate" label="还款日期">
        <el-date-picker
          v-model="form.repaymentDate"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item prop="repayment" label="还款金额">
        <NumberInput
          v-model="form.repayment"
          :decimals="2"
          style="width: 220px"
          @input="jisuanChuangYong"
        >
          <template #append>元</template>
        </NumberInput>
      </el-form-item>
      <el-form-item prop="repaymentMethod" label="还款方式">
        <el-select v-model="form.repaymentMethod" placeholder="请选择还款方式">
          <el-option label="app" value="app"></el-option>
          <el-option label="对公账户" value="对公账户"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="isDianFu" label="是否垫付" @change="isDianFuChange">
        <el-radio-group v-model="isDianFu">
          <el-radio label="是">是</el-radio>
          <el-radio label="否">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="isDianFu === '是'" prop="advance" label="垫资">
        <NumberInput
          v-model="form.advance"
          :decimals="2"
          maxlength="15"
          show-word-limit
          style="width: 220px"
          @input="jisuanChuangYong"
        >
          <template #append>元</template>
        </NumberInput>
      </el-form-item>
      <el-form-item prop="settlement" label="是否结清">
        <el-radio-group v-model="form.settlement">
          <el-radio label="结清">结清</el-radio>
          <el-radio label="分期">分期</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="ratio" label="系数">
        <NumberInput
          :disabled="true"
          v-model="form.ratio"
          :decimals="2"
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item prop="createCommission" label="创佣">
        <NumberInput
          :disabled="true"
          v-model="form.createCommission"
          :decimals="2"
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item prop="certificates" label="上传证明">
        <FileUpload
          ref="fileUploadRef"
          v-model:fileList="form.certificates"
          :data="form"
          :limit="5"
          btnText="选择图片"
          :fileType="['png', 'jpg', 'jpeg', 'jpge']"
          uploadFileUrl="/upload"
          gateway="file"
        />
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button @click="cancel">重置</el-button>
      <el-button @click="onSubmit" :loading="loading">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { orderSign } from "@/api/case/orderInfo/orderInfo";
import { getFmlResult } from "@/api/assetsManagement/formula";
import { nextTick } from "vue";

const { proxy } = getCurrentInstance();
const store = useStore();
const caseInfo = computed(() => store.state.allCaseDetail.caseInfoData);
const isDianFu = ref("是");
const caseId = inject("caseId");
const formulaId = ref(caseInfo.value?.infoBase?.formulaId);
const ratio = ref(caseInfo.value?.infoBase?.ratio);
const form = ref({
  caseId: caseId,
  repayment: undefined,
  repaymentDate: undefined,
  repaymentMethod: undefined,
  advance: undefined,
  settlement: "结清",
  ratio: ratio.value,
  createCommission: undefined,
  certificates: [],
});
const rules = ref({
  repaymentDate: [
    { required: true, message: "请选择还款日期", trigger: "blur" },
  ],
  repayment: [{ required: true, message: "请填写还款金额", trigger: "blur" }],
  advance: [{ type: "number", message: "请输入数字", trigger: "blur" }],
});
const loading = ref(false);
const Timer = ref(null);

function jisuanChuangYong() {
  if (!formulaId.value || !ratio.value) {
    proxy.$modal.msg("未获取到公式或系数，无法计算创佣，请联系管理员！");
    return;
  }
  nextTick(() => {
    if (!form.value.repayment) {
      proxy.$modal.msg("请填写还款金额！");
      return;
    }
    if (Timer.value) {
      clearTimeout(Timer.value);
      Timer.value = null;
    }
    Timer.value = setTimeout(() => {
      getFmlResult({
        formulaId: formulaId.value,
        repaymentAmount: form.value.repayment,
        advanceFunding: form.value.advance || 0,
        coefficient: ratio.value || 0,
        specialCommission: 0,
      })
        .then((res) => {
          form.value.createCommission = res.data;
        })
        .finally(() => {
          clearTimeout(Timer.value);
          Timer.value = null;
        });
    }, 500);
  });
}

function isDianFuChange() {
  nextTick(() => {
    if (isDianFu.value === "否") {
      form.value.advance = undefined;
    }
  });
}

function cancel() {
  proxy.$refs.formRef.resetFields();
  form.value = {
    caseId: caseId,
    repayment: undefined,
    repaymentDate: undefined,
    advance: undefined,
    settlement: "结清",
    ratio: ratio.value,
    createCommission: undefined,
    certificates: [],
  };
  isDianFu.value = "是";
}

function onSubmit() {
  const reqForm = JSON.parse(JSON.stringify(form.value));
  if (reqForm.certificates.length === 0) {
    reqForm.certificates = undefined;
  } else {
    reqForm.certificates = reqForm.certificates.map(
      (item) => item.response.data.url
    );
    reqForm.certificates = reqForm.certificates.join(",");
  }
  proxy.$refs.formRef.validate((valid) => {
    loading.value = true;
    if (!valid) {
      loading.value = false;
      return;
    }
    orderSign(reqForm)
      .then(() => {
        proxy.$message.success("保存成功！");
        cancel();
      })
      .finally(() => {
        loading.value = false;
      });
  });
}
</script>

<style scoped>
.chudan-dengji {
  padding: 10px;
  margin-top: 10px;
  border: 1px solid #e2e2e2;
}
</style>
