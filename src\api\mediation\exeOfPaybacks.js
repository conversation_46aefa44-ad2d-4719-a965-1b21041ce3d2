import request from '@/utils/request'

// 回款登记 - 列表查询 
export function getReturnList(query) {
    return request({
        url: '/execute-case/selectWithRefund',
        method: 'get',
        params: query
    })
}

// 回款登记 - 列表统计 
export function totalMoneyReturn(data) {
    return request({
        url: '/execute-case/selectWithRefundMoney',
        method: 'post',
        data: data
    })
}

// 回款登记 - 列表统计 
export function importDataReturn(data) {
    return request({
        url: '/execute-case/importWithExcel',
        method: 'post',
        data: data
    })
}
