<template>
  <el-dialog
    title="批量上传资料"
    v-loading="loading"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" ref="formRef">
      <el-form-item label="资料上传">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :limit="1"
          accept=".zip"
          :headers="upload.headers"
          :action="uploadUrl"
          :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange"
          :before-remove="handleRemove"
          :on-success="handleFileSuccess"
          :file-list="fileData"
          :auto-upload="false"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <div>将文件拖到此处，或<em>点击上传</em></div>
            <span>文件格式：.zip</span>
          </div>
          <template #tip>
            <div class="mt10">
              <el-button type="success" :loading="loading" @click="uploadSerive"
                >上传到服务器</el-button
              >
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="footer mt20">
      <div class="rules-explain">
        <div class="title">规则说明</div>
        <p>
          1.一个压缩包内给已选案件批量上传资料，请规范命名文件夹名称，文件夹名称请以案件的案件ID进行命名，否则无法进行上传，例如压缩包下文件夹为16697、16696、16695。排序不受影响。
        </p>
        <!-- <p>
          2.如勾选案件为共债案件，则互为共债案件的资料保持一致(包括未勾选的共债案件)
        </p> -->
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          >提 交</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { getToken } from "@/utils/auth";
import { nextTick } from "vue";
import { uploadFileList } from "@/api/assets/dataManage.js";
const { proxy } = getCurrentInstance();
const open = ref(false);
const uploadding = ref(false);
const loading = ref(false);
const uploadData = ref({
  idList: [],
  whetherSearch: false,
  manage: undefined,
  name: undefined,
  originalFilename: undefined,
  url: undefined,
  path: undefined,
});
const subimtInfo = ref({ isSuccess: false, msg: undefined });
const upload = ref({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/file/upload",
});
const uploadUrl = computed(() => {
  return upload.value.url.replace("/appeal", "");
})
const emit = defineEmits(["getList"]);
const fileData = ref([]);

// 提交
function submit() {
  if (!uploadData.value.url) {
    proxy.$modal.msgWarning("请上传文件！");
    return false;
  }
  loading.value = true;
  const params = new FormData();
  for (const key in uploadData.value) {
    if (Object.hasOwnProperty.call(uploadData.value, key)) {
      params.append(key, uploadData.value[key]);
    }
  }
  uploadFileList(params)
    .then((result) => {
      proxy.$modal.msgSuccess("提交成功");
      reset();
      emit("getList");
    })
    .catch((err) => {
      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 1024 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过1GB!");
    return false;
  }
  uploadding.value = true;
  loading.value = true;
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 1024 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过1GB!");
    fileList.pop();
    return false;
  }
  if (file.name.indexOf(".zip") == -1) {
    proxy.$modal.msgWarning("请上传 .zip格式的文件!");
    fileList.pop();
    return false;
  }
  loading.value = false;
  if (fileList.length > 0) {
    fileData.value = fileList;
  }
}
/* 案件上传文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    uploadData.value.name = data.name;
    uploadData.value.originalFilename = data.originalFilename;
    uploadData.value.url = data.url;
    uploadData.value.path = data.path;
    uploadding.value = false;
    loading.value = false;
  }
};


// 移除文件
function handleRemove(file, fileList) {
  fileData.value = [];
  proxy.resetForm("formRef");
  uploadData.value.name = undefined;
  uploadData.value.originalFilename = undefined;
  uploadData.value.url = undefined;
  uploadData.value.path = undefined;
  loading.value = false;
  uploadding.value = false
}

function uploadSerive() {
  proxy.$refs[`uploadRef`].submit();
}

// 打开批量上传弹窗方法
function opendialog(data) {
  delete data.queryParams.pageNum;
  delete data.queryParams.pageSize;
  uploadData.value.idList = data?.idList.toString();
  uploadData.value.whetherSearch = data.queryParams.whetherSearch;
  const queryParams = JSON.parse(JSON.stringify(data.queryParams));
  delete queryParams.whetherSearch;
  uploadData.value.manage =
    Object.values(queryParams).length == 0 ? null : JSON.stringify(queryParams);
  open.value = true;
}

// 关闭弹窗
function cancel() {
  reset();
}
//重置
function reset() {
  open.value = false;
  proxy.resetForm("formRef");
  uploadData.value = {
    idList: [],
    whetherSearch: false,
    manage: undefined,
    name: undefined,
    originalFilename: undefined,
    url: undefined,
    path: undefined,
  };
  uploadding.value = false;
  fileData.value = [];
}

defineExpose({ opendialog });
</script>
<style lang="scss" scoped>
.el-upload__text {
  span {
    font-size: 14px;
    color: #666;
  }
}
.upload-demo {
  width: 490px;
}
.rules-explain {
  padding: 10px;
  color: #409eff;
  background-color: #f0f7ff;
  border-radius: 10px;
  border: 2px dashed #63b0ff;
}
</style>