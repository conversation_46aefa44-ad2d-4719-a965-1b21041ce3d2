<template>
    <el-dialog title="审核状态登记" v-model="open" width="600px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" label-width="100px" ref="formRef">
            <el-form-item prop="checkStatus" label="审核状态">
                <el-radio-group v-model="form.checkStatus">
                    <el-radio v-for="item in checkStatusOption" :label="item.code" :key="item.code" :value="item.code">
                        {{ item.info }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item prop="remark" label="备注">
                <el-input v-model="form.remark" type="textarea" :rows="3" :maxlength="200" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-center">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="submit" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { checkCase } from '@/api/mediation/onlineFiling'
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const data = reactive({
    form: {},
    rules: {
        checkStatus: [{ required: true, messgae: '请选择审核状态', trigger: 'blur' }]
    },
})
const checkStatusOption = ref([
    { code: '0', prop: '已网上立案', info: '审核通过' },
    { code: '1', prop: '立案不通过', info: '审核不通过' },
    { code: '2', prop: '接受调解', info: '审核不通过但接收调解' },
])
const { form, rules } = toRefs(data)
function submit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            reqForm.disposeStage = checkStatusOption.value.find(item => item.code == form.value.checkStatus).prop
            checkCase(reqForm).then(res => {
                if (res.code == 200) {
                    props.getList && props.getList()
                    proxy.$modal.msgSuccess('操作成功')
                    cancel()
                }
            })
        }
    })
}
function openDialog(data) {
    form.value = { ...data.query, ...data }
    open.value = true
}
function cancel() {
    proxy.resetForm('formRef')
    form.value = {}
    open.value = false
}
defineExpose({ openDialog })
</script>