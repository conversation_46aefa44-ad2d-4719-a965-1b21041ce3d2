<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-position="right"
      :label-width="130"
      class="form-content"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-select
          v-model="queryParams.taskName"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @visible-change="getTaskNameListHandle"
          style="width: 280px"
        >
          <el-option
            v-for="(item, index) in taskNameList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="话术模板名称" prop="dialogueTemplateName">
        <el-select
          v-model="tempNameList"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          style="width: 280px"
          @focus="changeAiDataT"
        >
          <el-option
            v-for="(item, index) in templateList"
            :key="index"
            :label="item.tempName"
            :value="item.tempName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 280px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="接通率">
        <el-row style="width: 280px">
          <el-col :span="10">
            <NumberInput
              placeholder="请输入内容"
              v-model="queryParams.reachability1"
              :decimals="0"
              clearable
            />
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="10">
            <NumberInput
              placeholder="请输入内容"
              v-model="queryParams.reachability2"
              :decimals="0"
              clearable
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="任务状态" prop="statusStr">
        <el-select
          v-model="queryParams.statusStr"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @focus="getTaskStatusListHandle"
          style="width: 280px"
        >
          <el-option
            v-for="item in taskStatusList"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb10 mt10" style="min-height: 26px">
      <!-- <el-button
        plain
        @click="openCreateTaskDialog"
        v-if="checkPermi(['appreciation:voiceTask:createTask'])"
        >新建任务</el-button
      > -->
      <el-button
        plain
        @click="operationHandle(3)"
        :disabled="!isClicked[3]"
        v-if="checkPermi(['appreciation:voiceTask:executeTask'])"
        >执行</el-button
      >
      <el-button
        plain
        @click="operationHandle(1)"
        :disabled="!isClicked[1]"
        v-if="checkPermi(['appreciation:voiceTask:suspendTask'])"
        >暂停</el-button
      >
      <el-button
        plain
        @click="operationHandle(2)"
        :disabled="!isClicked[2]"
        v-if="checkPermi(['appreciation:voiceTask:revokeTask'])"
        >撤销</el-button
      >
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      ref="multipleTableRef"
      @selection-change="SelectedIdChangeHandle"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" key="id" prop="id" />
      <el-table-column label="任务名称" align="center" prop="">
        <template #default="{ row }">
          <Tooltip :content="row.taskName" :length="8" />
        </template>
      </el-table-column>
      <el-table-column label="话术模板名称" align="center" prop="">
        <template #default="{ row }">
          <Tooltip :content="row.dialogueTemplateName" :length="8" />
        </template>
      </el-table-column>
      <el-table-column label="呼叫数量" align="center" prop="callCount" />
      <el-table-column label="机器人数量" align="center" prop="robotCount">
        <template #default="{ row }">
          {{ row.robotCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :min-width="150"
      />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column
        label="任务状态"
        align="center"
        :formatter="taskStatusFor"
      />
      <el-table-column
        label="任务完成进度"
        align="center"
        prop="taskCompleteSchedule"
        :min-width="100"
      >
        <template #header>
          <div class="header-tooltip">
            任务完成进度
            <el-tooltip
              effect="customized"
              content="任务完成进度=已呼叫数÷外呼任务总数"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <template #default="{ row }">
          <span> <el-progress :percentage="row.taskCompleteSchedule" /></span>
        </template>
      </el-table-column>
      <el-table-column
        label="接通率"
        align="center"
        prop="reachability"
        :min-width="100"
      >
        <template #header>
          <div class="header-tooltip">
            接通率
            <el-tooltip
              effect="customized"
              content="接通率=已接通÷已呼叫数量（已接通=接听+漏接+未接听）"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <template #default="{ row }">
          <span> {{ row.reachability }} %</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark">
        <template #default="{ row }">
          <Tooltip :content="row.remark" :length="8" />
        </template>
      </el-table-column>
      <el-table-column label="任务结果" align="center" prop="" :min-width="120">
        <template #default="{ row }">
          <el-button
            type="text"
            @click="viewResultsReport(row)"
            v-if="checkPermi(['appreciation:voiceTask:viewResultsReport'])"
            >查看结果报告</el-button
          >
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="{ row }">
          <el-button
            type="text"
            @click="operationHandle(1, row)"
            :disabled="row.taskStatus != 2"
            v-if="checkPermi(['appreciation:voiceTask:suspendTask'])"
            >暂停</el-button
          >
          <el-button
            type="text"
            @click="operationHandle(2, row)"
            :disabled="row.taskStatus != 2 && row.taskStatus != 4"
            v-if="checkPermi(['appreciation:voiceTask:revokeTask'])"
            >撤销</el-button
          >
          <el-button
            type="text"
            @click="operationHandle(3, row)"
            :disabled="row.taskStatus != 1 && row.taskStatus != 4"
            v-if="checkPermi(['appreciation:voiceTask:executeTask'])"
            >执行</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <createTaskDialog ref="createTaskRef" @close="getList" />
  </div>
</template>

<script setup>
import createTaskDialog from "@/views/appreciation/voiceTask/dialog/createTask.vue";
import { nextTick, onMounted } from "vue";
import {
  listIntelligenceTask,
  getTaskNameList,
  notConnectRecallTask,
} from "@/api/appreciation/callOut";
import {
  findAiVoiceTaskList,
  getTaskStatus,
  aiSuspendTask,
  aiRevokeTask,
  aiExecuteTask,
  getAiTaskNameList,
  getAiVoiceTplList,
} from "@/api/appreciation/voiceTask";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const router = useRouter();

const dataList = ref([]);
const checkedType = ref([]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

const ids = ref([]);
const selectedArr = ref([]);

const templateList = ref([]);
const tempNameList = ref([]);

//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    statusStr: undefined,
    createTime: undefined,
  },
});
//需要拆分的字段
const { queryParams } = toRefs(data);

const rangFields = ["createTime"];

const total = ref(undefined);

const loading = ref(false);

const timeFor = (str) => {
  const weekDays = [
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
    "星期日",
  ];
  if (str) {
    // 将字符串转换为数组，映射到对应的星期名称
    const convertedDays = str.split(",").map((day) => weekDays[day - 1]);
    // 对数组进行排序
    convertedDays.sort((a, b) => {
      return weekDays.indexOf(a) - weekDays.indexOf(b);
    });
    const result = convertedDays.join("，");
    return result;
  }
  return "";
};

//获取列表数据
const getList = () => {
  loading.value = true;
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  if (reqForm.statusStr) {
    if (reqForm.statusStr.includes(2)) {
      reqForm.statusStr.push(6);
    }
    reqForm.statusStr = reqForm.statusStr.join(",");
    // reqForm.statusStr.includes("2") ? reqForm.statusStr += ',6' : ''
  }
  reqForm.taskName && (reqForm.taskName = reqForm.taskName.join(","));
  tempNameList.value.length > 0 &&
    (reqForm.dialogueTemplateName = tempNameList.value.join(","));
  nextTick(() => {
    findAiVoiceTaskList(reqForm)
      .then((res) => {
        dataList.value = res.rows;
        Array.isArray(dataList.value) &&
          dataList.value.forEach((item) => {
            item.time =
              timeFor(item.executionTime) + "；" + item.executionCallTime;
          });
        total.value = res.total;
      })
      .finally(() => (loading.value = false));
  });
};
getList();

const taskNameList = ref([]);
// 获取任务名称列表
const getTaskNameListHandle = () => {
  getAiTaskNameList().then((res) => {
    console.log(res);
    taskNameList.value = res.data;
  });
};

const taskStatusList = ref([]);
// 获取任务状态
getTaskStatus().then((res) => {
  taskStatusList.value = res.data;
});

const changeAiDataT = () => {
  // 获取话术模板
  getAiVoiceTplList().then((res) => {
    templateList.value = res.data;
  });
};
// const operateType = ref(["未接通重呼", "暂停", "撤销", "执行"]);

// onMounted(() => {
//     console.log("11111111111")
// })

const taskStatusFor = (row) => {
  const stutasEnum = {
    1: "未启动",
    2: "进行中",
    3: "已完成",
    4: "已暂停",
    5: "已撤销",
    6: "进行中",
  };
  return stutasEnum[row.taskStatus];
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const operationHandle = (type, row) => {
  const queryApi = [
    notConnectRecallTask,
    aiSuspendTask,
    aiRevokeTask,
    aiExecuteTask,
  ];
  const api = queryApi[type];
  const queryText = [
    "确认后任务会重新启动，将未接通的号码重新呼叫1遍",
    "将对所勾选的任务进行暂停操作，是否继续？",
    "将对所勾选的任务进行撤销操作，是否继续？",
    "将对所勾选的任务进行执行操作，是否继续？",
  ];
  const text = queryText[type];
  if (row && row.id) {
    let req;
    if (type == 0) {
      req = {
        id: row.id,
      };
    } else {
      req = {
        ids: [row.id],
      };
    }
    proxy.$modal
      .confirm(text)
      .then(function () {
        api(req).then((res) => {
          proxy.$modal.msgSuccess(res.msg);
          getList();
        });
      })
      .catch((err) => {});
  } else {
    // console.log(ids.value);
    ids.value = [];
    switch (type) {
      case 1:
        selectedArr.value.forEach((item) => {
          if (item.taskStatus == 2) {
            ids.value.push(item.id);
          }
        });
        break;
      case 2:
        selectedArr.value.forEach((item) => {
          if (item.taskStatus == 2 || item.taskStatus == 4) {
            ids.value.push(item.id);
          }
        });
        break;
      case 3:
        selectedArr.value.forEach((item) => {
          if (item.taskStatus == 4 || item.taskStatus == 1) {
            ids.value.push(item.id);
          }
        });
        break;
      default:
        break;
    }
    if (ids.value.length == 0) {
      proxy.$modal.msgWarning("没有可执行的任务");
      return;
    }
    // console.log(ids.value);
    let req = {
      ids: ids.value,
    };
    proxy.$modal
      .confirm(text)
      .then(function () {
        api(req).then((res) => {
          proxy.$modal.msgSuccess(res.msg);
          getList();
        });
      })
      .catch((err) => {});
  }
};

const SelectedIdChangeHandle = (selection) => {
  selectedArr.value = selection;
};

const isClicked = ref({
  1: false,
  2: false,
  3: false,
});
watch(
  () => selectedArr.value,
  () => {
    Object.keys(isClicked.value).forEach((key) => {
      isClicked.value[key] = false;
      switch (Number(key)) {
        case 1:
          isClicked.value[key] = selectedArr.value.some(
            (item) => item.taskStatus == 2
          );
          break;
        case 2:
          isClicked.value[key] = selectedArr.value.some(
            (item) => item.taskStatus == 2 || item.taskStatus == 4
          );
          break;
        case 3:
          isClicked.value[key] = selectedArr.value.some(
            (item) => item.taskStatus == 4 || item.taskStatus == 1
          );
          break;
        default:
          break;
      }
    });
  }
);

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    statusStr: undefined,
    createTime: undefined,
  };
  selectedArr.value = [];
  tempNameList.value = [];
  ids.value = [];
  getList();
}

const openCreateTaskDialog = () => {
  proxy.$refs["createTaskRef"].openDialog();
};

const viewResultsReport = (row) => {
  router.push({
    path: "/voiceTask/resultsReport",
    query: { id: row.id },
  });
};
</script>
<style lang="scss" scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.header-tooltip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  :deep(.el-icon) {
    font-size: 16px;
  }
}
</style>