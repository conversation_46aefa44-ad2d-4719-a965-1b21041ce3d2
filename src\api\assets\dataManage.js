import request from '@/utils/request'

//查询获取案件信息以及案件资料路径信息
export function dataManageList(query) {
    return request({
        url: '/management/retrieval/filePath',
        method: 'get',
        params: query
    })
}

//根据案件id查询案件资料信息
export function getInformation(query) {
    return request({
        url: '/management/retrieval/information',
        method: 'get',
        params: query
    })
}

//上传文件传回案件id
export function getRetrievalCaseId(query) {
    return request({
        url: '/management/retrieval/caseId',
        method: 'get',
        params: query
    })
}

//修复资料/上传资料提交数据
export function repairUploadedData(data) {
    return request({
        url: '/management/retrieval/repairUploadedData',
        method: 'post',
        data: data
    })
}

//检测文件
export function checkDownloadFile(query) {
    return request({
        url: '/management/retrieval/checkDownloadFile',
        method: 'get',
        params: query
    })
}

//检测文件
export function uploadFileList(data) {
    return request({
        url: '/management/retrieval/uploadFileList',
        method: 'post',
        data: data,
        timeout: 300000
    })
}

