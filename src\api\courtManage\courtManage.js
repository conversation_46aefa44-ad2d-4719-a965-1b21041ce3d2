import request from '@/utils/request'
// 根据条件查询机构信息
export function getCourtList(query) {
    return request({
        url: '/agency/getCourtNameOptions',
        method: 'get',
        params: query
    })
}
// 根据主键id查询机构信息
export function getCourtById(query) {
    return request({
        url: '/agency/listById',
        method: 'get',
        params: query
    })
}

// 添加机构
export function addCourt(data) {
    return request({
        url: '/agency/add',
        method: 'post',
        data: data
    })
}

// 修改机构信息
export function updateCourt(data) {
    return request({
        url: '/agency/edit',
        method: 'post',
        data: data
    })
}

// 删除机构信息
export function delCourt(data) {
    return request({
        url: '/agency/remove',
        method: 'post',
        data: data
    })
}

// 根据机构id查询文书模板信息
export function getWritTemplateByCourtId(query) {
    return request({
        url: '/agency/selectByCourtId',
        method: 'get',
        params: query
    })
}

// 根据机构id查询文书信息
export function getWritTemplateBylawAgencyId(query) {
    return request({
        url: '/court/manage/getAgencyLetters',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}

// 根据机构id查询文书模板信息
export function listNotPaging(query) {
    return request({
        url: '/agency/listNotPaging',
        method: 'get',
        params: query
    })
}
