<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="106px">
      <el-form-item label="导入案件信息">
        【{{ product.ownerName }}】-- {{ product.productName }}
      </el-form-item>
      <el-form-item v-if="batchNum" label="导入批次">
        {{ batchNum }}
      </el-form-item>
      <el-form-item label="模板下载">
        <el-button type="primary" link @click="downTpl('contactTplVertical')"
          >点击下载联系人竖向模板</el-button
        >
        <el-button type="primary" link @click="downTpl('contactTplHorizontal')"
          >点击下载联系人横向模板</el-button
        >
      </el-form-item>
      <el-form-item label="联系人模板">
        <el-upload
          ref="uploadRef"
          drag
          :limit="1"
          accept=".xlsx"
          :headers="upload.headers"
          :action="uploadUrl"
          :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange"
          :before-remove="handleRemove"
          :on-success="handleFileSuccess"
          :auto-upload="false"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div>
              <el-button type="success" @click="submitFile">上传到服务器</el-button>
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <div class="text-danger">注：导入联系人以案件ID、姓名、证件号码进行匹配</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { importContactBN, importContact } from "@/api/assets/asset/asset";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["contactTplVertical", "contactTplHorizontal"]);
const title = ref("");
const open = ref(false);
const loading = ref(false);
const batchNum = ref("");
const product = ref({});
const data = reactive({
  form: {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  },
  rules: {},
});
const { form, rules } = toRefs(data);

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/file/upload",
});
const uploadUrl = computed(() => {
  return upload.url.replace("/appeal", "");
}) 
const files = ref([]); //上传成功文件列表

//打开弹窗
function opendialog(row, prod) {
  product.value = JSON.parse(JSON.stringify(row));
  form.value.id = row.id;
  title.value =
    `导入联系人信息` + (row.ownerName ? `【${row.ownerName}】--${row.productName}` : "");
  batchNum.value = row.batchNum;
  open.value = true;
}

//下载文件
function downTpl(type) {
  emit(type);
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
}

/* 文件移除 */
function handleRemove(file, fileList) {
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.response.data.name;
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == name) {
        files.value.splice(i, 1);
        break;
      }
    }
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: file.name,
      modifyName: data.name,
      fileUrl: data.url,
    };
    files.value.push(obj);
  }
};

//提交
function submit() {
  loading.value = true;
  try {
    if (files.value.length === 0) {
      throw new Error("请上传联系人模板!");
    }
    form.value.fileUrl = files.value[0].fileUrl;
    form.value.originalFilename = files.value[0].firstName;
    if (batchNum.value) {
      //按批次
      importContactBN(form.value)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功");
          cancel();
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      //按唯一案件ID
      form.value.productId = product.value.productId;
      importContact(form.value)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功");
          cancel();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  } catch (error) {
    proxy.$modal.msgWarning(error.message);
    loading.value = false;
  }
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  };
  files.value = [];
  title.value = "";
  batchNum.value = "";
  proxy.$refs["uploadRef"].clearFiles();
  product.value = {};
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped>
.ml-12 {
  margin-left: 12px;
}
</style>
