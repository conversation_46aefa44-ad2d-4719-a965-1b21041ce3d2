<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane v-hasPermi="['writ:typeSet:set']" label="类型设置" name="0"></el-tab-pane>
      <el-tab-pane v-hasPermi="['writ:paramSet:set']" label="参数设置" name="1"></el-tab-pane>
    </el-tabs>
    <!-- 类型设置 -->
    <typeSet ref="typeSetRef" :activeTab="activeTab" v-if="activeTab == '0'" />
    <!-- 变量设置 -->
    <paramSet ref="paramSetRef" :activeTab="activeTab" v-else />
  </div>
</template>

<script setup name="Parametric">
import typeSet from "./page/typeSet.vue";
import paramSet from "./page/paramSet.vue";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
//tab切换
const activeTab = ref("0");
</script>

<style scoped>
:deep(.el-tabs--border-card > .el-tabs__content) {
  min-height: 760px;
}
</style>
<style lang="scss" scoped>
.tab-list {
  padding-left: 20px;
}
</style>
