<template>
  <el-table v-loading="loading" :data="dataList">
    <el-table-column
      label="ID"
      prop="id"
      key="id"
      align="center"
      v-if="columns[0].visible"
    />
    <el-table-column
      label="导入压缩包名称"
      prop="fileName"
      key="fileName"
      align="center"
      v-if="columns[1].visible"
    />
    <el-table-column
      label="导入类型"
      prop="importType"
      key="importType"
      align="center"
      v-if="columns[2].visible"
      :formatter="(row) => formatterImportType(row.importType)"
    />
    <el-table-column
      label="成功数量"
      prop="successNumber"
      key="successNumber"
      align="center"
      v-if="columns[3].visible"
    >
      <template #default="{ row }">
        {{ formatAmountWithComma(row.successNumber || 0) }}
        <!-- {{ row.successNumber || 0 }} -->

      </template>
    </el-table-column>
    <el-table-column
      label="操作人"
      prop="operatorName"
      key="operatorName"
      align="center"
      v-if="columns[4].visible"
    />
    <el-table-column
      label="导入时间"
      prop="importTime"
      key="importTime"
      align="center"
      v-if="columns[5].visible"
    />
    <el-table-column
      label="导入状态"
      prop="importStart"
      key="importStart"
      align="center"
      v-if="columns[6].visible"
      :formatter="(row) => formatterImportStart(row.importStart)"
    />
    <el-table-column label="描述及原因" width="180px">
      <template #default="{row}">
        <el-button
          type="text"
          v-hasPermi="['assets:importLog:imData:source']"
          link
          @click="downSourceFile(row.id,row.fileName)"
          >源文件下载</el-button
        >
        <el-button
          v-if="(row.importStart == 2 || row.importStart == 3)||(row.importStart == 4 && row.failFileUrl)"
          type="text"
          link
          v-hasPermi="['assets:importLog:imData:fail']"
          @click="downFailFile(row.failFileUrl)"
          >失败下载</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>
  
<script setup>
import { getToken } from "@/utils/auth";
import { formatAmountWithComma } from "@/utils/common";
const { proxy } = getCurrentInstance();
const baseURL = import.meta.env.VITE_APP_BASE_API;
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  dataList: {
    type: Array,
    default: [],
  },
  columns: {
    type: Array,
    default: [],
  },
});
//下载源文件
function downSourceFile(id,fileName) {
  // window.open(url);
  let req = {
    fileId:id
  }
  proxy.download("/caseManage/management/retrieval/originalFile",req , fileName);
}

//下载失败文件
function downFailFile(url) {
  if (!url || url == "") {
    proxy.$modal.msgWarning("失败文件不存在，请联系管理员～");
    return false;
  }
  window.open(url);
}

// 导入类型
function formatterImportType(type) {
  return { 1: "导入资料" }[type];
}
// 导入状态
function formatterImportStart(start) {
  return {
    0: "导入中",
    1: "导入成功",
    2: "导入失败",
    3: "部分成功",
    4: "导入错误",
  }[start];
}
</script>
  
  <style scoped></style>
  