<template>
  <el-dialog :title="'外访登记'" v-model="visible" width="500px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="90px">
      <el-form-item label="外访时间" prop="outsideEnd" required>
        <el-date-picker
          v-model="form.outsideEnd"
          type="datetime"
          placeholder="请选择日期和时间"
          style="width: 100%"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="外访详情" prop="outsideContent" required>
        <el-input
          type="textarea"
          v-model="form.outsideContent"
          maxlength="150"
          show-word-limit
          placeholder="请输入内容"
          :rows="4"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { outsideRegistrationAPI } from '@/api/collection/myapply';

const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: Boolean,
  row: Object, 
});
const emits = defineEmits(['update:visible', 'success']);

const formRef = ref();
const loading = ref(false);
const form = ref({
  outsideEnd: '',
  outsideContent: '',
});

const rules = {
  outsideEnd: [
    { required: true, message: '请选择外访时间', trigger: 'blur' },
  ],
  outsideContent: [
    { required: true, message: '请输入外访详情', trigger: 'blur' },
    { max: 150, message: '最多150字', trigger: 'blur' },
  ],
};

function handleClose() {
  emits('update:visible', false);
}

async function handleSubmit() {
  await formRef.value.validate(async (valid) => {
    if (!valid) return;
    loading.value = true;
    try {
      await outsideRegistrationAPI({
        id: props.row.id,
        outsideEnd: form.value.outsideEnd,
        outsideContent: form.value.outsideContent,
      });
      proxy.$modal.msgSuccess("外访登记成功");
      emits('success');
      handleClose();
    } catch (e) {
      proxy.$modal.msgError("外访登记失败");
    } finally {
      loading.value = false;
    }
  });
}
</script>
