<template>
  <el-dialog title="提示" v-model="open" append-to-body @close="cancel">
    <table class="my-table" v-loading="loading">
      <thead>
        <tr class="text-left">
          <td colspan="4">案件信息</td>
        </tr>
      </thead>
      <tbody class="text-center">
        <tr>
          <td>案件总量</td>
          <td>总金额</td>
          <td>已分配</td>
          <td>未分配</td>
        </tr>
        <tr>
          <td>{{ data.caseNum }}</td>
          <td>{{ numFilter(data.totalMoney) }}</td>
          <td>{{ data.assignedNum }}</td>
          <td>{{ data.unAssignedNum }}</td>
        </tr>
      </tbody>
    </table>
    <el-form v-if="handletype == 1 || handletype == 2" :model="form" class="mt10" label-position="top" :rules="rules"
      :reason="reason" ref="formRef">
      <el-form-item class="mar0" label="申请原因">
        <el-input style="height: 70px" v-model="form.caseReason" maxlength="300" show-word-limit type="textarea" />
      </el-form-item>
    </el-form>
    <!-- <div class="hint text-danger mt10">{{ hint }}</div> -->

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  reclaimCase,
  insertRetreat,
  insertKeep,
  insertStop,
  strategyAllocationQuery
} from "@/api/case/index/index";
const props = defineProps({
  checkedType: {
    //本页选中，搜索结果选中
    type: String,
  },
  query: {
    //查询条件
    type: Object,
  },
});
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const reqarr = ref([reclaimCase, insertRetreat, insertKeep, insertStop]);
const handletype = ref(undefined);
const open = ref(false);
const hint = ref("");
const loading = ref(false);
const subloading = ref(false);
const data = ref({
  caseNum: 0,
  totalMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
  caseId: [],
});
const caseIds = ref([])
const reason = reactive({
  form: {
    caseReason: undefined,
  },
  rules: {
    caseReason: [{ required: true, message: `请输入申请原因！`, trigger: "blur" }],
  },
});
const { form, rules } = toRefs(reason);

//打开弹窗
async function openDialog(data) {
  caseIds.value = data.caseIds
  form.value = data.sign
  handletype.value = data.type;
  const hint_arr = [
    "提交案件回收，当前选择案件将会从业务员身上回收，变成未分配状态。你还要继续吗？",
    "此操作将会对案件进行退案, 是否继续?",
    "此操作将会对案件进行留案, 是否继续?",
    "此操作会将案件状态申请为停催，资产端审核通过后，案件会退回资产端, 是否继续?",
    "此操作将会把停催的案件恢复成资产未分配状态, 是否继续?",
  ];
  hint.value = hint_arr[data.type];
  open.value = true;
  loading.value = true;
  await handledata().then((res) => {
    loading.value = false;
  });
}

//数据处理
function handledata() {
  return new Promise((reslove, reject) => {
    const reqForm = props.query.condition ? props.query : { ids: caseIds.value, condition: false }
    strategyAllocationQuery(reqForm).then((res) => {
      data.value.caseIds = res.data.arrayList;
      data.value.caseNum = res.data.zongshu;
      data.value.totalMoney = res.data.zongjine;
      data.value.assignedNum = res.data.yifenpei;
      data.value.unAssignedNum = res.data.weifenpei;
      reslove(true);
    });
  });
}

//取消
function cancel() {
  data.value = {
    caseNum: 0,
    totalMoney: 0,
    assignedNum: 0,
    unAssignedNum: 0,
    caseId: [],
  };
  open.value = false;
  form.value = {};
}

//提交操作数据
function submit() {
  let i = handletype.value;
  subloading.value = true;
  const reqForm = props.query.condition ? props.query : { ids: caseIds.value, condition: false }
  if (handletype.value == 1 || handletype.value == 2) {
    Object.assign(reqForm, { reason: reason.form?.caseReason })
  }
  reqarr.value[i](reqForm).then((res) => {
    proxy.$modal.msgSuccess(res.msg);
    subloading.value = false;
    emits("getList");
    cancel();
  }).catch(() => {
    subloading.value = false;
  });
}

defineExpose({ openDialog });

</script>

<style lang="scss" scoped>
.mar0 {
  margin: 0;
  margin-bottom: 20px !important;
}

.hint {
  font-size: 14px;
}

.my-table {
  width: 100%;
  color: #666;
  text-align: center;
  border: 1px solid #ededed;
  outline: none;
  border-spacing: 0px !important;

  thead {
    background-color: #f2f2f2;
    font-size: 14px;
    border: 1px solid #ededed;

    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }

  tbody {
    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }
}

:deep(.el-textarea textarea) {
  height: 70px !important;
}
</style>
