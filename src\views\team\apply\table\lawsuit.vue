<template>
  <div class="assistrecord">
    <el-form
      class="form-content h-50 mt20"
      :class="{ 'h-auto': showSearch }"
      :model="queryParams"
      label-position="right"
      :label-width="100"
      ref="queryRef"
      :inline="true"
    >
      <el-form-item label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="客户姓名" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入姓名"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="clientIdcard">
        <el-input
          v-model="queryParams.clientIdcard"
          placeholder="请输入身份证号"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <!-- <el-form-item label="委托方" prop="entrustingPartyId">
        <el-select
          v-model="queryParams.entrustingPartyId"
          placeholder="请输入或选择转让方"
          clearable
          filterable
          :reserve-keyword="false"
          @focus="OwnerList"
          style="width: 328px"
        >
          <el-option
            v-for="item in entrusts"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="发起时间" style="width: 336px">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb40 mt10">
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <div class="fxn-tabs">
      <el-tabs
        class="mb8"
        v-model="lawsuitTabs"
        @tab-click="tabChange"
        style="flex: 1"
      >
        <el-tab-pane
          v-for="item in tab"
          :key="item.id"
          :label="item.label"
          :name="item.id"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="fxn-status">
        <span>诉讼进度&nbsp;&nbsp;</span>
        <el-select v-model="queryParams.lawsuitProcess">
          <el-option
            v-for="item in lawStatusWatch"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
    </div>

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="columns[0].width"
        v-show="columns[0].visible"
      >
        <template #default="scope">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <el-tooltip v-show="scope.row.labelContent" placement="top">
              <template #content>{{ scope.row.labelContent }}</template>
              <case-label
                class="ml5"
                v-show="scope.row.label && scope.row.label != 7"
                :code="scope.row.label"
              />
            </el-tooltip>
            <span
              style="color: #409eff; cursor: pointer"
              type="text"
              v-show="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)"
              >{{ scope.row.caseId }}</span
            >
            <span v-show="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="调解员"
        align="center"
        prop="odvName"
        :width="columns[1].width"
        v-show="columns[1].visible"
      />
      <el-table-column
        label="诉讼进度"
        align="center"
        prop="lawsuitProcess"
        :width="columns[2].width"
        v-show="columns[2].visible"
      >
      <template #default="scope">
        {{getLawStatusTitle(scope.row.lawsuitProcess) }}
      </template>
    </el-table-column>
      <el-table-column
        label="批次号"
        align="center"
        prop="batchNum"
        :width="columns[3].width"
        v-show="columns[3].visible"
      >
      </el-table-column>
      <el-table-column
        label="委托方"
        align="center"
        prop="entrustingPartyName"
        :width="columns[4].width"
        v-show="columns[4].visible"
      >
      </el-table-column>
      <el-table-column
        label="客户姓名"
        align="center"
        prop="clientName"
        :width="columns[5].width"
        v-show="columns[5].visible"
      >
      </el-table-column>
      <el-table-column
        label="身份证号"
        align="center"
        prop="clientIdcard"
        :width="columns[6].width"
        v-show="columns[6].visible"
      />
      <el-table-column
        label="委托金额"
        align="center"
        prop="clientMoney"
        :width="columns[7].width"
        v-show="columns[7].visible"
      />

      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button type="text" @click="urgedRecord(scope.row)"
            >诉讼查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList()"
    />

    <!-- 协催记录 -->
    <selectAssistDetails ref="selectAssistDetailsRef" />
  </div>
</template>
<script setup>
import selectAssistDetails from "../dialog/selectAssistDetails.vue";
import { getTeamApplyList } from "@/api/approval";
import {replaceNull} from "@/utils/common"
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表格配置数据
const loading = ref(false);
const total = ref(0);
// 列显隐信息
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 80 },
  { key: 1, label: `姓名`, visible: true, width: 100 },
  { key: 2, label: `证件号码`, visible: true, width: 200 },
  { key: 3, label: `债权总金额`, visible: true, width: 150 },
  { key: 4, label: `申请原因`, visible: true, width: 150 },
  { key: 5, label: `协催人`, visible: true, width: 100 },
  { key: 6, label: `协催状态`, visible: true, width: 100 },
  { key: 7, label: `申请时间`, visible: true, width: 190 },
  { key: 8, label: `证件类型`, visible: true, width: 120 },
]);
//表单配置信息
const tab = ref([
  { label: "进行中", id: "0", count: 0 },
  { label: "已完成", id: "1", count: 0 },
  { label: "全部", id: "all", count: 0 },
]);
//列表切换字段
const showSearch = ref(false);
const lawsuitTabs = ref("all");

const lawStatusWatch = ref([
  { id: "all", title: "全部" },
  { id: 0, title: "发起诉讼" },
  { id: 1, title: "网上立案" },
  { id: 2, title: "立案庭立案" },
  { id: 3, title: "执行局立案" },
  { id: 4, title: "诉讼立案" },
  { id: 5, title: "调解" },
  { id: 6, title: "判决生效" }, // lawsuitTabs == 1
  { id: 7, title: "按撤诉处理" },
  { id: 8, title: "撤诉" }, // lawsuitTabs == 1
  { id: 9, title: "公告判决" },
  { id: 10, title: "强行执行" },
  { id: 12, title: "结案" }, // lawsuitTabs == 1
]);

function getLawStatusTitle(id) {
  const match = lawStatusWatch.value.find(item => String(item.id) === String(id));
  return match ? match.title : '未知状态';
}

//表格数据
const caseList = ref([]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    lawsuitProcess: undefined,
    createTime: [],
  },
});
//需要拆分的字段
const rangfiles = ["createTime"];
const { queryParams } = toRefs(data);

//获取列表
function getList(deptId) {
  queryParams.value.approveProcessState = 2;
  queryParams.value.approveState = 2;
  loading.value = true;
  let req = {
    approveCode: "lawsuit",
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    approveProcessState: queryParams.value.approveProcessState,
    approveState: queryParams.value.approveProcessState,
    approveData: {
      stringDeptId: deptId,
      ...proxy.addFieldsRange(queryParams.value, rangfiles),
    },
  };
  if (lawsuitTabs.value == "all") {
    delete req.approveData.lawsuitProcess;
  } else {
    req.approveData.lawsuitProcessList =
      lawsuitTabs.value == 1 ? [6, 8, 12] : [0, 1, 2, 3, 4, 5, 7, 9, 10, 11];
  }
  getTeamApplyList(req)
    .then((res) => {
      caseList.value = replaceNull(res.rows);
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//协催记录
function urgedRecord(row) {
  proxy.$refs["selectAssistDetailsRef"].opendialog(row.id);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    createTime: [],
  };
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNumber =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({
    path: `/case/teamIndex-detail/teamCaseDetails/${caseId}`,
    query: { type: "caseManage" },
  });
}

//协催状态 0-待协催，1-持续协催，2-完成协催，3-终止协催
function stateFor(row) {
  return ["待协案", "持续协案", "完成协案", "终止协案"][row.state];
}

//tab切换
function tabChange() {
  getList();
}

defineExpose({
  getList,
});
</script>
<style lang="scss" scoped>
.form-content {
  .el-form-item {
    width: 30% !important;

    .el-select .el-select__tags .el-tag--info {
      max-width: 100px;
      overflow: hidden;
    }
  }
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.filter-tree {
  height: 70%;
  overflow: auto;
}

.top-right-btn {
  z-index: 1;
}
.fxn-tabs {
  position: relative;
  .fxn-status {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
