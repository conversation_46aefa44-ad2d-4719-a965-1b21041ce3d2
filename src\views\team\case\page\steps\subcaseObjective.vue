<template>
    <div>
        <div class="table">
            <div class="search-result">
                <div class="table-thead">分案目标（搜索结果）</div>
                <div class="table-body">
                    <div class="table-column">
                        <div class="table-column-content table-column-label">可分配案件量</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.caseNum }}</div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">总金额</div>
                        <div class="table-column-content table-column-value">
                            {{ numFilter(queryRes?.caseMoney) }}
                        </div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">已分配</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.unAssignedNum }}</div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">未分配</div>
                        <div class="table-column-content table-column-value">{{ queryRes?.assignedNum }}</div>
                    </div>
                </div>
            </div>
            <div class="division-rule-set">
                <div class="table-thead">分案规则设置</div>
                <div class="table-body">
                    <div class="table-column">
                        <div class="table-column-content table-column-label">分案规则：{{ divisionRule() }}</div>
                        <div class="table-column-content table-column-label">分案范围：{{ scopeStatus[form.divisionScope -
                            1].label }}</div>
                    </div>
                    <div class="table-column">
                        <div class="table-column-content table-column-label">分案模式：{{ modeList[form.divisionalMode - 1].label
                        }}</div>
                        <div class="table-column-content table-column-label">分案数值：{{ mannerList[form.divisionMethod -
                            1].label }}</div>
                    </div>
                </div>
            </div>
            <div class="select-expediter">
                <div class="table-thead">选择处置人员</div>
                <div class="table-body">
                    <slot name="optTeam" />
                </div>
            </div>
        </div>
        <div class="text-center mt20">
            <el-button :loading="loading" @click="prevStep">上一步，重新设置分案规则</el-button>
            <el-button type="primary" :loading="loading" plain @click="nextstep">下一步，预览分案结果</el-button>
        </div>
    </div>
</template>
<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
    prevStep: { type: Function },
    previewStep: { type: Function },
    loading: { type: Boolean },
    form: { type: Object },
    queryRes: { type: Object },
    rules: { type: Object },
    checkStatus: { type: Array },
    scopeStatus: { type: Array },
    modeList: { type: Array },
    mannerList: { type: Array },
    stepActive: { type: [String, Number] },
})

// 上一步
function prevStep() {
    if (props.stepActive == 1) {
        props.prevStep()
    }
}

// 下一步
function nextstep() {
    if (props.stepActive == 1) {
        props.previewStep()
    }
}

// 分案规则
function divisionRule() {
    const labels = props.checkStatus.filter(item => props.form.divisionalPrinciple?.includes(item.is_settle))
    if (labels.length > 0) {
        return labels.map(item => item.label).toString()
    } else {
        return '无'
    }
}

</script>
<style lang="scss" scoped>
.table {
    border: 1px solid #ededed;

    .table-thead {
        height: 50px;
        color: #666;
        line-height: 50px;
        text-align: center;
        background-color: #f2f2f2;
    }

    .table-body {
        display: flex;
        color: #666;

        .table-column {
            flex: 1;
            text-align: center;

            .table-column-label {
                border-bottom: 1px solid #ededed;
            }

            .table-column-label,
            .table-column-value {
                border-right: 1px solid #ededed;

                &:last-of-type(1) {
                    border-right: none;
                }
            }

            .table-column-content {
                height: 44px;
                line-height: 44px;
            }
        }
    }

    .division-rule-set {
        .table-body {
            .table-column-label {
                text-align: left;
                margin-left: 20px;
            }
        }
    }

    .select-expediter {
        .table-body {
            width: 100%;

        }
    }
}
</style>
<style>
.select-expediter .table-body>div {
    width: 100%;
}
</style>