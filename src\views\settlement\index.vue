<template>
    <div class="app-container  content-box">
        <div class="left">
            <el-tabs v-model="activeTab" tab-position="left">
                <el-tab-pane label="坐席服务" name="0" v-if="checkPermi(['settlement:index:agentService'])"></el-tab-pane>
            </el-tabs>
        </div>
        <div class="right">
            <agentService v-if="activeTab == '0'" />
        </div>
    </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import agentService from "../settlement/agentService/index.vue"

const activeTab = ref("0");
</script>

<style lang="scss" scoped>
.content-box {
    display: flex;

    .right {
        flex: 1;
        margin-left: 10px;
        overflow: hidden;
    }
}
</style>