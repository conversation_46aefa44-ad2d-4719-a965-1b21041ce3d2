<template>
  <div class="loading-box ">
    <img class="trams-route" src="@/assets/images/loading.svg" />
  </div>
  <div class="sub-progress">
    <div class="back-progress">
      <div class="color-progress" :style="`width:${schedule}%`"></div>
    </div>
    <div class="progress-number"> {{ schedule }}%</div>
  </div>
  <div class="progress-tips">分案数据预览加载中，请勿关闭该页面！</div>
  <div class="progress-second-tips">如关闭当前页面则无法预览分案信息，需重新分案！</div>
</template>

<script setup>
import { scheduleAllocation } from "@/api/case/index/index";
const schedule = ref(0) // 进度值
const timeId = ref(null) // 定时器
const remarks = ref(undefined) // 描述
const props = defineProps({
  getData: {
    type: Function
  },
  scheduleName: {
    type: String
  }
})
const normal = ref(true)
const previewData = ref(undefined)
async function pollTime(scheduleNo, num) {
  normal.value = true
  if (num) {
    schedule.value = 0
  } else {
    schedule.value = sessionStorage.getItem(props.scheduleName)
  }
  getSchedule(scheduleNo)
  // while (true) {
  //   if (schedule.value == 100 || !normal.value) {
  //     return false
  //   }
  //   getSchedule(scheduleNo)
  //   await new Promise(resolve => timeId.value = setTimeout(resolve, 1000));
  // }
}

function getSchedule(scheduleNo) {
  if (!scheduleNo) {
    return false
  }
  const reqForm = { scheduleNo }
  scheduleAllocation(reqForm).then(res => {
    if (res.code == 200) {
      if (res.data.normal) {
        sessionStorage.setItem(props.scheduleName, res.data.schedule)
        schedule.value = res.data.schedule
        remarks.value = res.data.remarks
        previewData.value = res.data.originalData
      }
      normal.value = res.data.normal
      props.getData(res.data)
      if (res.data.schedule == 100 || !res.data.normal) {
        return false
      } else {
        setTimeout(() => {
          getSchedule(scheduleNo)
        }, 1000)
      }
    }
  })
}

defineExpose({ pollTime })
</script>

<style lang="scss" scoped>
.loading-box {
  display: flex;
  justify-content: center;
  margin-top: 100px;
}

.sub-progress {
  display: flex;
  align-items: center;
  width: 310px;
  margin: 40px auto 20px;
  height: 8px;

  .back-progress {
    height: 8px;
    background-color: #cccccc;
    flex: 1;

    .color-progress {
      height: 8px;
      background-color: #409eff;
    }
  }
  .progress-number {
    width: 50px;
    margin-left: 10px;
  }

  span {
    margin-left: 10px;
  }
}

.progress-tips,
.progress-second-tips {
  text-align: center;
  font-size: 18px;
  margin-top: 20px;
  color: #409eff;
}

.progress-second-tips {
  font-size: 12px;
  margin-top: 10px;
}

.trams-route {
  transform: rotate;
  animation: rotate 2.5s linear infinite;
}

.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
    /* 初始角度为0 */
  }

  to {
    transform: rotate(360deg);
    /* 最终角度为360度，即一周 */
  }
}
</style>
