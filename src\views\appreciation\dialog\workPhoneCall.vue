<template>
    <el-dialog title="工作手机来电弹屏" v-model="open" width="700px" append-to-body :before-close="cancel"
        :modal-append-to-body="false" :close-on-click-modal="false">
        <div class="">客户信息</div>

        <div class="mt20 mb20">
            <el-table height="180px" :header-cell-style="{ background: '#EEEFF4', color: '#888888', width: '100%' }"
                v-loading="loading" :data="dataList" ref="multipleTableRef">
                <el-table-column label="案件ID" prop="id" align="center">
                    <template #default="scope">
                        <span style="color: #409eff; cursor: pointer"
                         @click="toCaseDetails(scope.row.caseId, scope.$index)">{{ scope.row.caseId }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="客户姓名" prop="caseName" align="center"></el-table-column>
                <el-table-column label="联系方式" prop="casePhone" align="center"></el-table-column>
                <el-table-column label="产品名称" prop="productName" align="center"></el-table-column>
                <el-table-column label="债权总金额" prop="entrustMoney" align="center"></el-table-column>
                <el-table-column label="剩余应还债权金额" prop="remainMoney" align="center"></el-table-column>
                <el-table-column label="剩余应还本金" prop="residualPrincipal" align="center"></el-table-column>
                <el-table-column label="逾期时间" prop="overTime" align="center"></el-table-column>
            </el-table>

            <pagination v-show="total > 10" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button class="submitBtn" type="primary" :loading="loading" @click="submit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script setup>
import {
    workPhonePopOnScreen,
} from "@/api/workPhone/index";
const emits = defineEmits(["submitFinish"]);
//全局配置
const {
    proxy
} = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const open = ref(false);
//提交数据
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
const {
    queryParams
} = toRefs(data);
const rangFields = []; 

const total = ref(1);

const dataList = ref([]);
const callInfo = ref(undefined);

// const dataLength = computed(() => {
//     if (dataList) {
//         return dataList.value.length;
//     } else {
//         return 0;
//     }
// })

const getList = () => {
    const query = {
        ...queryParams.value,
        ...callInfo.value
    };
    workPhonePopOnScreen(query).then((res) => {
        // console.log(res);
        dataList.value = res.rows;
        total.value = res.total;
    })
}


//打开窗口
function openDialog(message) {
    console.log(message);
    open.value = true;
    callInfo.value = {
        phone: message.phone,
    };
    nextTick(() => {
        // const formData = new FormData();
        getList();
    })
}

//跳转案件详情
function toCaseDetails(caseId, index) {
    // let queryChange = {
    //     pageNum: 1,
    //     pageSize: 1
    // };
    // let searchInfo = {
    //     query: queryChange,
    //     type: "myCase",
    // };
    // localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
    // nextTick(() => {
    //     open.value = false;
    //     router.push({ path: `/case/caseIndex-detail/manageDetails/${caseId}` });
    // })
    let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
    let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
    delete queryChange.pageNum;
    delete queryChange.pageSize;
    let searchInfo = {
        query: {
        manageQueryParam: queryChange, //查询参数
        pageNumber: pageNumber, //当前第几页(变动)
        pageSize: 1, //一页一条
        pageIndex: 0, //当前第一条
        caseIdCurrent: caseId, //当前案件id
        },
        type: "mycase",
        total: total.value, //查询到的案件总数
    };
    localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
    nextTick(() => {
        open.value = false;
        router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
    })
}

//提交
function submit() {
    cancel();
    // emits('submitFinish')
}

//重置
function reset() {
}

//取消
function cancel() {
    if (loading.value) return false;
    reset();
    open.value = false;
}

defineExpose({
    openDialog,
    cancel,
});
</script>

<style lang="scss" scoped>
:deep(.el-textarea),
.dialog-footer {
    margin-right: 20px;
}
</style>