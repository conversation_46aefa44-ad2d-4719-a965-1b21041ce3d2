import request from '@/utils/request'

//获取催收记录
export function selectUrgeRecord(query) {
  return request({
    url: '/collection/selectUrgeRecord' ,
    method: 'get',
    params: query
  })
}
//获取调诉记录
export function selectUrgeAppealRecord(query) {
  return request({
    url: '/stage/register/getAppealRecord',
    method: 'get',
    params: query
  })
}
//获取调执记录
export function getPhoneUrgRecord(query) {
  return request({
    url: '/stage/register/getPhoneUrgRecord',
    method: 'get',
    params: query
  })
}
//获取调诉执记录
export function getAppMed(query) {
  return request({
    url: '/stage/register/getAppMed',
    method: 'get',
    params: query
  })
}

//获取回款记录
export function selectRepaymentRecord(query) {
  return request({
    url: '/collection/selectRepaymentRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}

//获取协催记录
export function selectAssistRecord(query) {
  return request({
    url: '/collection/selectAssistRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}

//获取减免记录
export function selectReductionRecord(query) {
  return request({
    url: '/collection/selectReductionRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}
//获取诉讼记录
export function selectLawsuitRecord(query) {
  return request({
    url: '/stage/register/getAppealRecord',
    method: 'get',
    params: query
  })
}
//获取便签记录
export function selectNoteRecord(query) {
  return request({
    url: '/collection/selectNoteRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}
//获取投诉记录
export function selectComplaintRecord(query) {
  return request({
    url: '/collection/selectComplaintRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}
//获取外访记录
export function selectOutsideRecord(query) {
  return request({
    url: '/collection/selectOutsideRecord/' + query.caseId,
    method: 'get',
    params: query
  })
}
//获取收费记录
export function selectCostRecord(query) {
  return request({
    url: '/cost/record/list',
    method: 'get',
    params: query
  })
}
//获取保全记录
export function selectUrgeKeepRecord(query) {
  return request({
    url: '/stage/register/getSaveRecord',
    method: 'get',
    params: query
  })
}
// 物流记录/调诉记录列表
export function selectExpressRecord(query) {
  return request({
    url: '/letter/item/selectExpressInfo',
    method: 'get',
    params: query,
    gateway: 'sign'
  })
}
// 执行登记
export function getPursueUrgRecord(query) {
  return request({
    url: '/stage/register/getPursueUrgRecord',
    method: 'get',
    params: query,
  })
}
// 案件详情-获取Tab
export function selectWithTab(query) {
  return request({
    url: '/collection/selectWithTab',
    method: 'get',
    params: query,
  })
}