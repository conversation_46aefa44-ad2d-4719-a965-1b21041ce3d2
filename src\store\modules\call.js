/**
 * 外呼拨号状态
 * **/ 
import { checkUserSip, getEmployeeInfo } from '@/api/system/user'

const state = {
  showCallBtn: false, //是否显示呼叫按钮
  callState: 0, //呼叫状态 0-初始状态（电话挂机）1-呼叫中 2-通话中
  callTime: 0, //通话时间
  callTimer: null, //通话计时器
  callingPhone: null, //电话号码
  currentCallShowId: undefined, //当前显示的id
  taskMessage: undefined, //预测式外呼来电信息
  caseTaskMessage: undefined, //案件-预测式外呼来电信息
  workPhoneMessage: undefined, //工作手机弹窗信息,
  showWorkPhoneIcon: false, //是否显示工作手机图标
}

const mutations = {
  SET_SHOW_CALL_BTN: (state, showCallBtn) => {
    state.showCallBtn = showCallBtn
  },
  SET_CALL_STATE: (state, callState) => {
    if (callState != state.callState) {
      state.callState = callState;
    }
  },
  SET_CALL_TIME: (state, callTime) => {
    state.callTime = callTime
  },
  SET_CALL_TIMER: (state, callTimer) => {
    state.callTimer = callTimer
  },
  SET_CALLING_PHONE: (state, callingPhone) => {
    state.callingPhone = callingPhone
  },
  SET_CURRENT_CALL_SHOWID: (state, currentCallShowId) => {
    state.currentCallShowId = currentCallShowId
  },
  SET_INCOMING_CALL: (state, Message) => {
    state.taskMessage = Message
    // console.log(state.taskMessage);
  },
  SET_CASE_INCOMING_CALL: (state, Message) => {
    state.caseTaskMessage = Message
  },
  SET_WORK_PHONE_INCOMING_CALL: (state, Message) => {
    state.workPhoneMessage = Message
  },
  SET_SHOW_WORK_PHONE_ICON: (state, showWorkPhoneIcon) => {
    state.showWorkPhoneIcon = showWorkPhoneIcon
  }
}

const actions = {
  //开始计时
  startTime({ commit, state }) {
    if (state.callTimer === null) {
      commit('SET_CALL_TIME', 0)
      var timer = setInterval(() => {
        commit('SET_CALL_TIME', ++state.callTime)
      }, 1000)
      commit('SET_CALL_TIMER', timer)
    }
  },
  //结束计时
  endTime({ commit, state }) { 
    clearInterval(state.callTimer)
    commit('SET_CALL_TIMER', null)
  },

  //判断是否有坐席
  checksip({ commit }) {
    checkUserSip().then(res => {
      commit('SET_SHOW_CALL_BTN',res.data.isSip)
    }).catch(() => {
      commit('SET_SHOW_CALL_BTN',false)
    })
  },

  //判断是否有工作手机
  checkWorkPhone({ commit }) {
    getEmployeeInfo().then(res => {
      console.log('SET_SHOW_WORK_PHONE_ICON',res.data?.bindWorkPhone);
      commit('SET_SHOW_WORK_PHONE_ICON',res.data?.bindWorkPhone);
    }).catch(() => {
      commit('SET_SHOW_WORK_PHONE_ICON',false)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}