import request from '@/utils/request'

//委案批次下拉
export function batchOptions() {
  return request({
    url: '/dropDown/selectCaseManage',
    method: 'get',
  })
}

//转让方下拉
export function assetOwnerOptions() {
  return request({
    url: '/dropDown/selectAssetOwner',
    method: 'get',
  })
}

//户籍地下拉
export function provincesOptions() {
  return request({
    url: '/dropDown/getProvinces',
    method: 'get',
  })
}

//标签下拉
export function labelOptions() {
  return request({
    url: '/dropDown/selectLabel',
    method: 'get',
  })
}

//获取列表
export function mycaseList(query) {
  return request({
    url: '/collection/selectCaseManage',
    method: 'get',
    params: query
  })
}

//统计
export function statisticsCase(data) {
  return request({
    url: '/collection/myCollectionCase',
    method: 'post',
    data: data
  })
}

//统计
export function selectMoneySizeById(data) {
  return request({
    url: '/collection/selectMoneySizeById',
    method: 'post',
    data: data
  })
}

//统计
export function CaseManageSizeMoney(query) {
  return request({
    url: '/collection/CaseManageSizeMoney',
    method: 'get',
    params: query
  })
}

//留案
export function stayCase(data) {
  return request({
    url: '/collection/insertKeep',
    method: 'post',
    data: data
  })
}

//退案
export function backCase(data) {
  return request({
    url: '/collection/insertRetreat',
    method: 'post',
    data: data
  })
}

//协催
export function helpUrge(data) {
  return request({
    url: '/collection/insertAssistRecord',
    method: 'post',
    data: data
  })
}

//停催 
export function stopUrge(data) {
  return request({
    url: '/collection/insertStop',
    method: 'post',
    data: data
  })
}

//标记案件
export function markCase(data) {
  return request({
    url: '/collection/MarkCase',
    method: 'post',
    data: data
  })
}

//获取短信签名
export function selectAllSmsSignature(query) {
  return request({
    url: '/collection/selectAllSmsSignature',
    method: 'get',
    params: query
  })
}

//获取短信模板标签
export function selectAllSmsTemplate(query) {
  return request({
    url: '/collection/selectAllSmsTemplate',
    method: 'get',
    params: query
  })
}

//催收端生成预览模板之后筛选可发送短信用户
export function previewTemplate(data) {
  return request({
    url: '/collection/previewTemplate',
    method: 'post',
    data: data
  })
}
// 发送飞信短信接口
export function sendCuiShouMessage(data) {
  return request({
    url: 'collection/sendCuiShouMessage',
    method: 'post',
    data: data,
    timeout: 30 * 60 * 1000
  })
}

// 预览文书
export function documentPreview(data) {
  return request({
    url: '/team/letter/doc/previewDoc',
    method: 'post',
    data: data,
    gateway: 'sign'
  });
}

// 批扣生成诉讼文书
export function batchAddLitigationDoc(data) {
  return request({
    url: '/team/letter/doc/batchAddLitigationDoc',
    method: 'post',
    data: data,
    gateway: 'sign'
  });
}

// 批扣生成调诉函
export function batchAddMediationLetter(data) {
  return request({
    url: '/team/letter/doc/batchAddMediationLetter',
    method: 'post',
    data: data,
  });
}


