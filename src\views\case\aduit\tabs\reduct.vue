<template>
  <div>
    <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams"
      label-position="right" :label-width="100" ref="queryRef" :inline="true">
      <el-form-item label="案件ID" prop="caseId">
        <el-input style="width: 336px" v-model="queryParams.caseId" placeholder="请输入案件ID" clearable
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input style="width: 336px" v-model="queryParams.clientName" placeholder="请输入姓名" clearable
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input style="width: 336px" v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input style="width: 336px" v-model="queryParams.applicant" placeholder="请输入申请人" clearable
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="处理时间" style="width: 336px">
        <el-date-picker style="width: 336px" v-model="queryParams.updateTime" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="申请时间" style="width: 336px">
        <el-date-picker style="width: 336px" v-model="queryParams.applyDate" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="退案日期" style="width: 336px">
        <el-date-picker style="width: 336px" v-model="queryParams.returnCaseDate" value-format="YYYY-MM-DD"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态" prop="batchNum">
        <el-select style="width: 336px" v-model="examineStateList" placeholder="请选择审核状态" clearable filterable
          :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip>
          <el-option v-for="item in examineList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <el-row class="mb40 mt10">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="handle-div mt20 mb10">
      <el-button v-if="activeTab === '2' && checkPermi([`case:aduit:pass:reduct`])" type="primary" plain
        :disabled="loading || ids.length == 0" @click="handle(0)">通过</el-button>
      <el-button v-if="activeTab === '2' && checkPermi([`case:aduit:unpass:reduct`])" type="primary" plain
        :disabled="loading || ids.length == 0" @click="handle(1)">不通过</el-button>
    </div>
    <selectedAll ref="selectedAllRef" :selectedArr="selectedArr" :dataList="caseList">
      <template #content>
        <span class="case-data-list">案件数量：<i class="danger">{{ caseNum }}</i></span>
      </template>
    </selectedAll>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
    </el-tabs>
    <el-table class="multiple-table" v-loading="loading" ref="multipleTableRef" :data="caseList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" :selectable="checkSelectable" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible"
        :width="columns[0].width">
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
            </el-tooltip>
            <el-button type="text" v-if="row.button == 1" @click="toDetails(row, $index)">
              {{ row.caseId }}</el-button>
            <span v-if="row.button == 0">{{ row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
        :width="columns[1].width" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible"
        :width="columns[2].width" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" :width="columns[3].width"
        v-if="columns[3].visible" />
      <el-table-column label="证件号码【籍贯】" width="180" align="center" v-if="columns[4].visible">
        <template #default="scope">
          <span>
            {{ `${scope.row.clientIdcard == null ? "--" : scope.row.clientIdcard}` }} <br />
            {{ `【${scope.row.clientCensusRegister == null ? "未知" : scope.row.clientCensusRegister || "--"}】` }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" v-if="columns[5].visible"
        :width="columns[5].width" />
      <el-table-column label="申请时间" align="center" prop="applyDate" v-if="columns[6].visible"
        :width="columns[6].width" />
      <el-table-column label="退案日期" align="center" v-if="columns[7].visible" key="returnCaseDate"
        prop="returnCaseDate" />
      <el-table-column label="申请原因" align="center" v-if="columns[8].visible">
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.reason }}</p>
            </template>
            <div>
              <span>
                {{ scope.row.reason?.length > 15 ? `${scope.row.reason?.substring(0, 15)}...` : scope.row.reason }}
              </span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="剩余应还债权金额" v-if="columns[9].visible" align="center">
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="减免后应还金额" v-if="columns[10].visible" align="center">
        <template #default="{ row }">
          <span>{{ numFilter(row.amountAfterDeduction) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="减免后还款日" align="center" key="afterReductionDate" prop="afterReductionDate"
        v-if="columns[11].visible" :width="columns[11].width" />
      <el-table-column label="处理状态" align="center" key="approveStart" prop="approveStart" :formatter="approveStartFor"
        v-if="columns[12].visible" :width="columns[12].width" />
      <el-table-column label="处理时间" align="center" key="approveTime" prop="approveTime" v-if="columns[13].visible"
        :width="columns[13].width" />
      <el-table-column label="审核状态" v-if="columns[14].visible" align="center">
        <template #default="scope">
          <el-popover placement="bottom" :width="500" :ref="`popover-${scope.$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(scope.row)" type="text">
                {{ scope.row.state }}
              </el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column width="200" property="approveTime" label="处理时间" />
              <el-table-column width="100" property="reviewer" label="处理人" :show-overflow-tooltip="true" />
              <el-table-column width="100" property="approveStart" :formatter="approveStartFor" label="处理状态" />
              <el-table-column width="100" property="refuseReason" :formatter="reasonFor" label="原因" />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.approveStart == 2" v-hasPermi="['case:aduit:pass:reduct']" type="text"
            :disabled="loading" @click="handle(0, row)">
            通过
          </el-button>
          <el-button v-if="row.approveStart == 2" v-hasPermi="['case:aduit:unpass:reduct']" type="text"
            :disabled="loading" @click="handle(1, row)">
            不通过
          </el-button>
          <el-button type="text" v-hasPermi="['case:aduit:lookOver']" @click="openCheckalter(row)">
            查看凭证
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <el-dialog title="审核不通过" v-model="open" width="600px" :before-close="cancel" append-to-body
      :close-on-click-modal="false">
      <el-form :model="form" ref="formRef">
        <el-form-item label="审核原因" prop="failureReason">
          <el-input v-model="form.failureReason" type="textarea" maxlength="300" show-word-limit placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="notPass">
            确定审核
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看凭证 -->
    <el-dialog title="预览" v-loading="loadingPreview" v-model="iframeOpen" width="70vw" :before-close="cancelPreview">
      <div class="left-span" v-if="srcUrl?.length > 1" @click="beforePreview()">
        <el-icon :size="40" :color="iframeIndex !== 0">
          <ArrowLeftBold />
        </el-icon>
      </div>
      <iframe class="iframe" :src="iframeSrc"></iframe>
      <div class="right-span" v-if="srcUrl?.length > 1" @click="iframeIndex !== srcUrl.length ? nextPreview() : ''">
        <el-icon :size="40">
          <ArrowRightBold />
        </el-icon>
      </div>
    </el-dialog>
  </div>
</template>
<script setup name="Aduit">
import { selectReductionRecord, ExemptionApproval, selectStagingRecordById, selectApproveProceTwo } from "@/api/case/aduit/aduit";
import { getBatchNums } from "@/api/common/common";

import { checkPermi } from "@/utils/permission";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表单配置信息
const showSearch = ref(false);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const examineList = ref([
  { label: "已通过", value: "已通过" },
  { label: "未通过", value: "未通过" },
  { label: "待审核", value: "待审核" },
  // { label: "审核中", value: "审核中" },
]);
const tabList = ref([
  { code: '2', info: '待处理' },
  { code: '0', info: '已同意' },
  { code: '1', info: '未同意' },
  { code: 'all', info: '全部' },
])
//表格数据
const caseList = ref([]);
const gridData = ref([]);
const batchs = ref([]);
const ids = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合

const settleState = ref("all");
const activeTab = ref("2");
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 50,
    approveStarts: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    applicant: undefined,
    examineState: undefined,
    state: undefined,
    examineTime: [],
    applyDate: [],
    createTime: [],
    updateTime: [],
    approveTime: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
  },
});
const { queryParams } = toRefs(data);
//多选列表
const examineStateList = ref([]);
//列显示/隐藏
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 80 },
  { key: 1, label: `产品类型`, visible: true, width: 150 },
  { key: 2, label: `姓名`, visible: true, width: 150 },
  { key: 3, label: `证件类型`, visible: true, width: 100 },
  { key: 4, label: `证件号码【籍贯】`, visible: true, width: 280 },
  { key: 5, label: `申请人`, visible: true, width: 100 },
  { key: 6, label: `申请时间`, visible: true, width: 100 },
  { key: 7, label: `退案日期`, visible: true, width: 100 },
  { key: 8, label: `申请原因`, visible: true, width: 150 },
  { key: 9, label: `剩余应还债权金额`, visible: true, width: 150 },
  { key: 10, label: `减免后应还金额`, visible: true, width: 150 },
  { key: 11, label: `减免后还款日`, visible: true, width: 150 },
  { key: 12, label: `处理状态`, visible: true, width: 150 },
  { key: 13, label: `处理时间`, visible: true, width: 150 },
  { key: 14, label: `审核状态`, visible: true, width: 150 },
]);

const rangfiles = [
  "examineTime",
  "applyDate",
  "entrustingCaseDate",
  "returnCaseDate",
  "updateTime",
  "approveTime",
  "createTime",
];
//弹窗属性
const open = ref(false);
const form = ref({});
const subloading = ref(false);

//查看凭证
const loadingPreview = ref(false);
const iframeOpen = ref(false);
const iframeSrc = ref(undefined);
const srcUrl = ref([]);
const iframeIndex = ref(0);

//获取列表
function getList() {
  loading.value = true;
  queryParams.value.approveStarts =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.repaymentType =
    settleState.value == "all" ? undefined : settleState.value;
  if (examineStateList.value.length > 0) {
    queryParams.value.state = examineStateList.value.toString();
  }
  const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
  selectReductionRecord(reqForm).then((res) => {
    caseList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  })
    .catch((err) => {
      loading.value = false;
    });
}
getList();

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 50,
    approveStarts: undefined,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    applicant: undefined,
    examineTime: [],
    applyDate: [],
    entrustingCaseDate: [],
    returnCaseDate: [],
  };
  examineStateList.value = [];
  getList();
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange,
    type: "caseAudit",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/aduit-detail/aduitDetails/${caseId}`, query: { type: "caseAudit" } });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.applyIds);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//tab切换
function tabChange() {
  getList();
}

//气泡框展示
function showPopover(row) {
  let req = { applyId: row.applyIds };
  selectApproveProceTwo(req).then((res) => {
    gridData.value = res.data;
  }).catch(() => {
    loading.value = false;
  });
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
  const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 5: '已退案关闭' }
  return stutasEnum[row.approveStart];
}

//通过原因
function reasonFor(row) {
  return row.refuseReason ? row.refuseReason : `--`;
}

//审核操作
function handle(type, row) {
  let query = proxy.addFieldsRange(queryParams.value, rangfiles);
  delete query.pageNum;
  delete query.pageSize;
  let req = { 
    ids: row ? [row.applyIds] : ids.value,
    allQuery: allQuery.value,
    queryParam : query
  };
  req.approveStart = type;
  if (req.ids.length === 0)
    return proxy.$modal.msgWarning("请选择操作的案件！");
  if (0 === type) {
    //通过
    proxy.$modal
      .confirm("此操作将通过选中的申请, 是否继续?")
      .then(function () {
        loading.value = true;
        return ExemptionApproval(req);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功！");
      })
      .catch((err) => {
        loading.value = false;
      })
  } else {
    reset();
    Object.assign(form.value, req);
    open.value = true;
  }
}

//重置取消审核表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
  };
}

//取消审核不通过
function cancel() {
  if (subloading.value) return false
  reset();
  open.value = false;
}

//提交审核不通过
function notPass() {
  form.value.failureReason =
    form.value.failureReason == "" ? undefined : form.value.failureReason;
  subloading.value = true;
  ExemptionApproval(form.value).then(() => {
    subloading.value = false;
    proxy.$modal.msgSuccess("操作成功！");
    getList();
    cancel();
  })
    .finally(() => {
      subloading.value = false;
    });
}

//查看凭证
function openCheckalter(row) {
  proxy.$modal.loading("正在查询用户凭证，请稍候...");
  selectStagingRecordById(row.applyIds).then((res) => {
    proxy.$modal.closeLoading();
    if (res?.data?.length == 0) {
      return proxy.$modal.msgWarning("当前用户没有上传凭证！");
    }
    let allFileUrl = res.data?.map((item) => item.fileUrl);
    //获取png，pdf格式的
    srcUrl.value = allFileUrl.filter(
      (item) =>
        item?.indexOf(".png") !== -1 ||
        item?.indexOf(".jpg") !== -1 ||
        item?.indexOf(".pdf") !== -1
    );
    //获取不是png，pdf格式的
    let downloadFileList = allFileUrl.filter(
      (item) =>
        item?.indexOf(".png") === -1 &&
        item?.indexOf(".jpg") === -1 &&
        item?.indexOf(".pdf") === -1
    );
    if (srcUrl.value?.length > 0) {
      loadingPreview.value = true;
      iframeOpen.value = true;
      iframeSrc.value = srcUrl.value?.[0];
      iframeIndex.value = 0;
      setTimeout(() => {
        loadingPreview.value = false;
      }, 1000);
    }
    if (downloadFileList.length > 0) {
      downloadFileList.forEach(item => window.open(item))
    }
  })
    .catch((error) => {
      proxy.$modal.closeLoading();
      loadingPreview.value = false;
    });
}

//上一张
function beforePreview() {
  if (iframeIndex.value == 0) return proxy.$modal.msgWarning("已经是第一张了");
  loadingPreview.value = true;
  iframeIndex.value--;
  iframeSrc.value = srcUrl.value?.[iframeIndex.value];
  setTimeout(() => {
    loadingPreview.value = false;
  }, 1000);
}

//下一张
function nextPreview() {
  if (iframeIndex.value >= srcUrl.value.length - 1) {
    return proxy.$modal.msgWarning("已经是最后一张了")
  } else {
    iframeIndex.value++;
    iframeSrc.value = srcUrl.value?.[iframeIndex.value];
  }
}

//预览凭证取消
function cancelPreview() {
  iframeSrc.value = undefined;
  iframeOpen.value = false;
  srcUrl.value = [];
  iframeIndex.value = 0;
  loadingPreview.value = false;
}

// 表格是否可以选择
function checkSelectable() {
  return !proxy.$refs['selectedAllRef']?.isAll
}

// 格式化金额
function moneyFor(num) {
  return num ? proxy.setNumberToFixed(num) : '--'
}

const caseNum = computed(() => {
  return proxy.$refs['selectedAllRef']?.isAll ? total.value : selectedArr.value.length
})

const allQuery = computed(() => {
  return proxy.$refs['selectedAllRef']?.isAll;
})

watch(caseList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (allQuery.value) {
      // allQuery.value = false;
      nextTick(() => {
        caseList.value.forEach((item, index) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
      });
    } else {
      nextTick(() => {
        caseList.value?.forEach((item, index) => {
          if (ids.value?.includes(item.id)) {
            proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
          }
        });
      });
    }
  }
});
</script>
<style lang="scss" scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

.top-right-btn {
  z-index: 1;
}

.iframe {
  min-height: 65vh;
  width: 100%;
}

.left-span {
  position: absolute;
  top: 50%;
  left: 10px;
  text-align: center;
  width: 50px;
  height: 50px;
  padding: 5px;
  border-radius: 50%;
  background-color: #cccccc;
}

.right-span {
  position: absolute;
  top: 50%;
  right: 10px;
  text-align: center;
  width: 50px;
  height: 50px;
  padding: 5px;
  border-radius: 50%;
  background-color: #cccccc;
}

.left-span:hover,
.right-span:hover {
  background-color: #dedede;
}
</style>
