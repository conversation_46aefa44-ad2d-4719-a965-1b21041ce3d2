<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :class="{ 'form-h50': !showSearch }" label-width="100px" inline>
      <el-form-item label="订单号">
        <el-input v-model="queryParams.serialNo" placeholder="请输入订单号" style="width: 240px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="operation-revealing-area mb10 mt10">
      <el-radio-group v-model="activeTab" @change="tabChange">
        <!-- 0-待审核，1-审核中，2-已通过，3-未通过 -->
        <el-radio-button label="0">待处理</el-radio-button>
        <el-radio-button label="2">通过</el-radio-button>
        <el-radio-button label="3">不通过</el-radio-button>
        <el-radio-button label="all">全部</el-radio-button>
      </el-radio-group>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column v-if="columns[0].visible" label="订单号" align="center" prop="serialNo" />
      <el-table-column v-if="columns[1].visible" label="处理状态" align="center" prop="status">
        <template #default="{ row, $index }">
          <el-popover placement="bottom" :width="500" :ref="`popover-${$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(row)" type="text">{{ statusFor(row) }}</el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column width="120" property="approveStart" label="处理状态" :formatter="approveStartFor" />
              <el-table-column width="220" property="approveTime" label="处理时间" />
              <el-table-column width="150" property="reviewer" label="处理人" :show-overflow-tooltip="true" />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[2].visible" label="模板名称" align="center" prop="templateName" />
      <el-table-column v-if="columns[3].visible" label="最后处理人" align="center" prop="examineBy" />
      <el-table-column v-if="columns[4].visible" label="最后处理时间" align="center" prop="examineTime" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button v-hasPermi="['mailServer:aduit:num:detail']" type="text"
            @click="previewSigntrue(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-area">
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 新增类型 -->
    <unpassBox ref="unpassBoxRef" @getList="getList" />
    <!-- 详情 -->
    <templateDetailsBox ref="templateDetailsBoxRef" />
  </div>
</template>

<script setup name="AduitBatchNum">
import unpassBox from "../dialog/unpass.vue";
import templateDetailsBox from "../../lawyer/page/templateDetails.vue";
import { getProceList, getPreview } from "@/api/lawyer/lawyer";
import { messageItemList, messageItemPass } from "@/api/lawyer/aduit";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const ids = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    proceStatus: undefined,
  },
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
const statusList = ref([]);
const single = ref(true);
const activeTab = ref("0");
//审核信息
const gridData = ref([]);

const showSearch = ref(false)
const columns = ref([
  { key: 0, label: `订单号`, visible: true },
  { key: 1, label: `处理状态`, visible: true },
  { key: 2, label: `模板名称`, visible: true },
  { key: 3, label: `最后处理人`, visible: true },
  { key: 4, label: `最后处理时间`, visible: true },
])
//获取列表
function getList() {
  loading.value = true;
  queryParams.value.proceStatus = activeTab.value == "all" ? undefined : activeTab.value;
  messageItemList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//审核状态
function approveStartFor(row) {
  return ["待处理", "通过", "不通过"][row.approveStart];
}
//状态枚举
function statusFor(row) {
  // 0-待审核，1-审核中，2-已通过，3-未通过
  return ["待处理", "处理中", "通过", "不通过"][row.status];
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//通过
function pass() {
  let req = {
    ids: ids.value,
  };
  proxy.$modal
    .confirm("是否确认审核？此操作将处理通过，是否确认？")
    .then(function () {
      return messageItemPass(req);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功！");
    })
}

//不通过
function unpass() {
  proxy.$refs["unpassBoxRef"].opendialog(ids.value, 1);
}

//表单切换
function tabChange() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  let signStatusList = selection.map((item) => item.approveStart);
  //判断通过不通过按钮状态
  let statusFlag = false;
  if (signStatusList.length > 0) {
    signStatusList.forEach((item, index) => {
      if ([1, 2, 3].includes(item)) {
        single.value = true;
        statusFlag = true;
      }
    });
    if (!statusFlag) {
      single.value = false;
    }
  } else {
    single.value = true;
  }
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    status: undefined,
  };
  getList();
}

//气泡框展示
function showPopover(row) {
  let req = {
    id: row.id,
  };
  getProceList(req)
    .then((res) => {
      gridData.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

//模板详情
function previewSigntrue(row) {
  proxy.$refs["templateDetailsBoxRef"].opendialog(row.id);
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
