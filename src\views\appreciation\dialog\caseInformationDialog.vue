<template>
  <el-dialog
    v-model="open"
    width="850px"
    append-to-body
    :before-close="cancel"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    custom-class="caseInfo-dialog"
  >
    <template #title>
      <!-- 自定义头部内容 -->
      <div class="case-title">
        <span>案件信息</span>
        <div class="call-status">
          <el-icon><Headset /></el-icon> 繁忙/通话中
        </div>
      </div>
    </template>

    <div class="case-info-content">
      <div class="case-info">
        <div class="man-info flex-wrap">
          <div class="flex-wrap-item">
            <div class="title">{{ infoBase?.clientName || "--" }}</div>
            <div class="text">{{ infoBase?.clientPhone || "--" }}</div>
          </div>
          <div class="flex-wrap-item">
            <div class="title">
              证件号（{{ infoBase?.clientIdType || "--" }}）
            </div>
            <div class="text">{{ infoBase?.clientIdNum || "--" }}</div>
          </div>
          <div class="flex-wrap-item">
            <div class="title">减免状态</div>
            <div class="text">{{ infoBase?.state || "--" }}</div>
          </div>
          <div class="flex-wrap-item">
            <div class="title">当前跟进状态</div>
            <div class="text">{{ infoBase?.followUpState || "--" }}</div>
          </div>
        </div>
        <div class="form-title mt10 mb10">
          贷款信息（案件ID：<span class="form-links" @click="toCaseDetails">{{
            caseId
          }}</span
          >）<span class="form-tip">查看更多信息点击案件ID</span>
        </div>
        <formCom :form="infoLoan" :form2="infoBase" :formList="infoLoanList" />
      </div>

      <div>
        <div class="mt10 mb10">跟进结果（催收记录）</div>
        <handleUrgeVue
          :contactOptions="contactData"
          :selected="selectedId"
          :caseId="caseId"
          :isOutgoingCall="true"
          @getUrgeList="updataTable"
          ref="handleUrgeRef"
        />
      </div>

      <div>
        <el-tabs v-model="activeTab">
          <el-tab-pane
            v-for="(item, index) in tabs"
            :key="index"
            :label="item.info"
            :name="item.info"
          />
        </el-tabs>
        <!-- 案件记录 -->
      </div>
      <collectionRecordTable
        v-if="activeTab == '催记记录'"
        :caseId="caseId"
        ref="collectionRecordTableRef"
      />
      <repayTable v-if="activeTab == '回款记录'" :caseId="caseId" />
      <reductionTable v-if="activeTab == '减免记录'" :caseId="caseId" />
    </div>
  </el-dialog>
</template>
<script setup>
import formCom from "@/views/caseDetails/components/formCom";
import handleUrgeVue from "@/views/caseDetails/handleUrge.vue";
import collectionRecordTable from "@/views/appreciation/dialog/table/collectionRecord.vue";
import repayTable from "@/views/appreciation/dialog/table/repay.vue";
import reductionTable from "@/views/appreciation/dialog/table/reduction.vue";
import { getCaseInfo, selectContact } from "@/api/caseDetail/detail";
import { nextTick } from "vue";
const emits = defineEmits(["submitFinish"]);
//全局配置
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const open = ref(false);
//提交数据

const caseId = ref(undefined);
const contactsId = ref(undefined);

const caseInfoData = ref({}); //案件所有信息
const infoBase = ref({});
const infoLoan = ref({});
const contactData = ref([]);
const selectedId = ref({});

const infoLoanList = ref([
  { code: "ycContractNo", info: "合同号" },
  { code: "productType", info: "产品类型" },
  { code: "loanMoney", isNum: true, info: "贷款金额" },
  { code: "ycOverdueDays", info: "实际逾期天数", class: "text-red" },
  { code: "accountPeriod", info: "账期（M1~Mn）" },
  { code: "loanInstitution", info: "债权机构" },
  { code: "loanPeriods", info: "贷款期数" },
  { code: "alreadyPeriods", info: "已还期数" },
  { code: "notPeriods", info: "未还期数", class: "text-red" },
  { code: "entrustMoney", isNum: true, info: "债权总金额", class: "text-blue" },
  { code: "loanPrincipal", isNum: true, info: "贷款本金" },
  {
    code: "residualPrincipal",
    isNum: true,
    info: "债权本金",
    class: "text-blue",
  },
  {
    code: "syYhPrincipal",
    isNum: true,
    info: "剩余应还本金",
    class: "text-red",
  },
  {
    code: "syYhInterest",
    isNum: true,
    info: "剩余应还利息",
    class: "text-red",
  },
  { code: "ycAbdRepayment", isNum: true, info: "基准日后还款金额" },
  {
    code: "amountCalledBack",
    isNum: true,
    info: "累计已还",
    class: "text-red",
  },
  {
    code: "amountAfterDeduction",
    isNum: true,
    info: "减免后应还金额",
    class: "text-red",
  },
  { code: "syYhFees", isNum: true, info: "剩余应还费用", class: "text-red" },
  { code: "latestFollowUpTime", info: "最近跟进时间", class: "text-blue" },
]);

const tabs = ref([
  { info: "催记记录" },
  { info: "回款记录" },
  { info: "减免记录" },
]);

const activeTab = ref("催记记录");

//更新表格
function updataTable() {
  // getCaseInfo(caseId.value).then((res) => {
  //     caseInfoData.value = res.data;
  //     infoBase.value = caseInfoData.value.infoBase;
  //     infoLoan.value = caseInfoData.value.infoLoan;
  // });
  proxy.$refs.collectionRecordTableRef.getList();
}

//打开窗口
function openDialog(message) {
  console.log(message);
  caseId.value = message.caseId;
  contactsId.value = message.contactsId;
  open.value = true;
  nextTick(() => {
    getCaseInfo(caseId.value).then((res) => {
      caseInfoData.value = res.data;
      infoBase.value = caseInfoData.value.infoBase;
      infoLoan.value = caseInfoData.value.infoLoan;
    });
    getContactList();
  });
}

//获取案件联系人列表
function getContactList() {
  loading.value = true;
  let form = {
    caseId: caseId.value,
  };
  selectContact(form)
    .then((res) => {
      contactData.value = res.data.infoContacts;
      selectedId.value = {
        // id: res.data.infoContacts[0].id
        id: contactsId.value,
      };
    })
    .finally(() => {
      loading.value = false;
    });
}

//跳转案件详情
function toCaseDetails() {
  let queryChange = {
    pageNum: 1,
    pageSize: 1,
  };
  let searchInfo = {
    query: queryChange,
    type: "caseManage",
  };
  localStorage.setItem(
    `searchInfo/${caseId.value}`,
    JSON.stringify(searchInfo)
  );
  nextTick(() => {
    open.value = false;
    // router.push({ path: `/case/caseIndex-detail/manageDetails/${caseId.value}`, query: { caseState: infoLoan?.value.caseState } });
    router.push({
      path: `/case/caseIndex-detail/manageDetails/${caseId.value}`,
    });
  });
}

//提交
function submit() {
  loading.value = true;
}

//重置
function reset() {}

//取消
function cancel() {
  if (loading.value) return false;
  reset();
  open.value = false;
}

defineExpose({
  openDialog,
  cancel,
});
</script>
<style lang="scss" scoped>
.case-title {
  display: flex;
  justify-content: flex-start;
  gap: 0.625rem;

  .call-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #888888;
  }
}
.case-info-content {
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  .case-info {
    border-radius: 2px;
    border: 1px solid rgba(171, 186, 223, 0.1);

    .flex-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      padding: 20px;
      border-bottom: 1px solid rgba(171, 186, 223, 0.1);

      .flex-wrap-item {
        width: 25%;
        padding: 0 20px;
        border-right: 1px solid rgba(171, 186, 223, 0.1);
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .title {
          margin-bottom: 5px;
          font-size: 14px;
        }

        .text {
          color: #888888;
          font-size: 15px;
        }
      }

      .flex-wrap-item:first-child {
        padding-left: 0;
      }

      .flex-wrap-item:last-child {
        padding-right: 0;
      }
    }

    .form-title {
      .form-links {
        color: #027db4;
      }

      .form-tip {
        font-size: 13px;
      }
    }
  }
}
</style>