<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="选择处置人员"></el-step>
        <el-step title="预览分案结果"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <!-- 指定分案 -->
    <div v-show="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef">
        <div class="step-item-title mt20"> 搜索结果 </div>
        <div class="step-item-content">
          <div class="content-item">
            <div class="content-item-label">可分配案件量</div>
            <div class="content-item-value">{{ queryRes.caseNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">总金额</div>
            <div class="content-item-value">{{ numFilter(queryRes.caseMoney) }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">已分配</div>
            <div class="content-item-value">{{ queryRes.unAssignedNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">未分配</div>
            <div class="content-item-value">{{ queryRes.assignedNum }}</div>
          </div>
        </div>
        <div class="step-item-title"> 选择处置人员 </div>
        <optTeam class="opt-team" ref="optteamRef" :type="form.allocationMode" :queryRes="queryRes" :unAssigned="0" :returnCaseDateRequired="true"/>
      </el-form>
      <div class="text-center mt20">
        <el-button @click="toBack">返回</el-button>
        <el-button type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
      </div>
    </div>
    <!-- 下一步 -->
    <div v-show="stepActive === 1" class="step-item pt20">
      <div :style="`display:${isShowDivisionalLoad}`" class="preview-loading">
        <divisionalLoad ref="divisionalLoadRef" :scheduleName="`case-caseManage-point-schedule-${route.query.time}`"
          :getData="getList" />
      </div>
      <div v-if="schedule == 100" class="perview-data">
        <div class="step-item-title mt20"> 搜索结果 </div>
        <div class="step-item-content">
          <div class="step-item-content-item">
            <div class="content-item-label">可分配案件量</div>
            <div class="content-item-value">{{ queryRes.caseNum }}</div>
          </div>
          <div class="step-item-content-item">
            <div class="content-item-label">总金额</div>
            <div class="content-item-value">{{ numFilter(queryRes.caseMoney) }}</div>
          </div>
          <div class="step-item-content-item">
            <div class="content-item-label">已分配</div>
            <div class="content-item-value">{{ queryRes.unAssignedNum }}</div>
          </div>
          <div class="step-item-content-item">
            <div class="content-item-label">未分配</div>
            <div class="content-item-value">{{ queryRes.assignedNum }}</div>
          </div>
        </div>
        <div class="step-item-title"> 分案规则设置</div>
        <div class="step-item-both-sides">
          <div class="step-item-content-value">分案方式：指定分案分案</div>
          <div class="step-item-content-value">分案方式：所选择案件</div>
        </div>
        <div class="step-item-title"> 预览分配结果</div>
        <div class="table-list">
          <div class="table-thead">
            <div class="table-thead-item">工号</div>
            <div class="table-thead-item">处置人员姓名</div>
            <div class="table-thead-item">案件量</div>
            <div class="table-thead-item">退案日期</div>
          </div>
          <div class="table-body">
            <div class="table-body-item" v-for="item in previewData.data" :key="item.odvId">
              <div>{{ item.jobNumber }}</div>
              <div>{{ item.odvName }}</div>
              <div>{{ item.number }}</div>
              <div>{{ item.returnCaseDate }}</div>
            </div>
          </div>
        </div>
        <div class="text-center mt20">
          <el-button @click="prevStep">上一步，选择处置人员</el-button>
          <el-button :loading="loading" type="primary" plain @click="submit">提交分案</el-button>
        </div>
      </div>
    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
      </div>
      <div class="text-center mt30">
        <el-button type="primary" @click="toBack">案件分配完成，返回</el-button>
      </div>
    </div>
  </div>
</template>
<script setup name="mediationTeamPoint">
import optTeam from "@/components/CaseOptTeam";
import { strategyAllocationQuery } from "@/api/mediation/appealCase"
import { previewAllocationResults, updateCase, scheduleAllocation } from "@/api/case/index/index";

const { proxy } = getCurrentInstance();
const route = useRoute();
const stepActive = ref(0);
const isShowDivisionalLoad = ref('none')
const loading = ref(false);
const schedule = ref(0);

const queryRes = ref({
  caseIds: [],
  caseNum: 0,
  caseMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
  distributeType: route.query.distributeType
});

const data = reactive({
  form: {
    allocationMode: 1,
  },
  rules: {
    allocationMode: [{ required: true, message: "请选择", trigger: "change" }],
  },
});

//预览数据
const previewData = ref({
  data: [],
});
//提交数据
const submitData = ref({});

const { form, rules } = toRefs(data);

onMounted(() => {
  try {
    const { checkedType, query } = JSON.parse(localStorage.getItem(`point/${route.query.time}`));
    query.pageSize = undefined;
    query.pageNum = undefined;
    let obj = JSON.parse(JSON.stringify(query));
    if (obj.ids) {
      const caseIds = obj.ids;
      delete obj.ids;
      obj.caseIds = caseIds;
    }
    strategyAllocationQuery(obj).then((res) => {
      queryRes.value.caseIds = res.data.arrayList;
      queryRes.value.caseNum = res.data.zongshu;
      queryRes.value.caseMoney = res.data.zongjine;
      queryRes.value.unAssignedNum = res.data.yifenpei;
      queryRes.value.assignedNum = res.data.weifenpei;
    });
  } catch (error) {
    toBack()
  }

});

// 获取数据
function getList(data) {
  if (data.normal) {
    schedule.value = data.schedule;
    if (data.schedule == 100) {
      previewData.value.data = data.originalData;
      isShowDivisionalLoad.value = 'none'
      sessionStorage.setItem(`case-caseManage-point-loading-${route.query.time}`, 'none')
    }
  } else {

    proxy.$modal.msgWarning(data.remarks)
    stepActive.value = 0
    sessionStorage.setItem(`case-caseManage-point-loading-${route.query.time}`, 'none')
  }
}

//下一步
function nextstep() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let reqData = {
        list: queryRes.value.caseIds,
      };
      loading.value = true;
      proxy.$refs["optteamRef"].exposeData().then((res) => {
        if (res && res.length > 0) {
          let distributions = res?.map((item) => {
            item.odvId = item.id;
            item.odvName = item.jobName;
            item.number = item.caseNum;
            return item;
          });
          reqData.distributions = distributions;
          reqData.distributeType = route.query.distributeType;
          isShowDivisionalLoad.value = 'block'
          sessionStorage.setItem(`case-caseManage-point-loading-${route.query.time}`, 'block')
          previewAllocationResults(reqData)
            .then((res) => {
              loading.value = false;
              stepActive.value++;
              submitData.value = reqData;
              sessionStorage.setItem(`case-caseManage-point-no-${route.query.time}`, res.data.scheduleNo)
              proxy.$refs['divisionalLoadRef'].pollTime(res.data.scheduleNo, '0')
            })
            .catch(() => {
              loading.value = false;
            });
        } else {
          loading.value = false;
        }
      });
    }
  });
}

watch(isShowDivisionalLoad.value, () => {
  const isBlock = sessionStorage.getItem(`case-caseManage-point-loading-${route.query.time}`)
  isShowDivisionalLoad.value = isBlock
  if (isBlock == 'block') {
    stepActive.value = 1
    nextTick(() => {
      proxy.$refs['divisionalLoadRef']?.pollTime(sessionStorage.getItem(`case-caseManage-point-no-${route.query.time}`))
    })
  }
}, { deep: true, immediate: true })

//上一步
function prevStep() {
  stepActive.value--;
  if (stepActive.value == 0) {
    remove()
  }
}

// 移除数据
function remove() {
  sessionStorage.removeItem(`case-caseManage-point-loading-${route.query.time}`)
  sessionStorage.removeItem(`case-caseManage-point-no-${route.query.time}`)
  sessionStorage.removeItem(`case-caseManage-point-schedule-${route.query.time}`)
}

//提交分案
function submit() {
  loading.value = true;
  const reqForm = JSON.parse(JSON.stringify(submitData.value))
  reqForm.distributeType = route.query.distributeType
  updateCase(JSON.stringify(reqForm)).then((res) => {
    stepActive.value++;
  }).finally(() => {
    loading.value = false;
  });
}

//返回
const toBack = () => {
  remove()
  const obj = { path: route.query.path };
  proxy.$tab.closeOpenPage(obj);
};

</script>
<style lang="scss" scoped>
.blue {
  color: #409eff;
}

.step-icon {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 0 auto;
  background-color: #3cc556;
  border-radius: 50%;

  .check-icon {
    font-size: 34px;
  }
}

.step-item-title {
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  color: #666666;
  background-color: #f2f2f2;
}

.step-item-content {
  &>div {
    color: #666666;

    .content-item-label {
      border-bottom: 1px solid #ededed;
    }
  }
}

.step-item-content,
.table-thead,
.table-body-item {
  display: flex;
  border-right: 1px solid #ededed;

  &>div {
    flex: 1;
    line-height: 44px;
    text-align: center;
    border-left: 1px solid #ededed;
  }
}

.step-item-both-sides {
  display: flex;
  flex-wrap: wrap;
  line-height: 44px;
  font-size: 14px;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;

  &>div {
    width: 50%;
    padding-left: 20px;
  }

  .step-item-content-value:nth-of-type(odd) {
    border-right: 1px solid #ededed;
  }
}

.table-list {
  .table-thead {
    font-size: 14px;
    border-bottom: 1px solid #ededed;
  }

  .table-body {
    max-height: 400px;
    overflow: auto;

    .table-body-item {
      border-bottom: 1px solid #ededed;
    }

  }
}

.opt-team {
  padding: 0 20px;
  border: 1px solid #ededed;
}
</style>
