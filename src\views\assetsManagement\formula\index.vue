<!-- 资产管理-项目计算公式配置 -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="公式名称" prop="formulaName">
        <el-input
          v-model="queryParams.formulaName"
          placeholder="公式名称"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8 mt10">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['assetsManagement:formula:add']"
          type="primary"
          icon="plus"
          @click="handleAddFormula(null)"
          >添加公式</el-button
        >
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="formulaName" label="公式名称" align="center" />
      <el-table-column
        prop="calculationFormula"
        label="计算公式"
        align="center"
      />
      <el-table-column prop="remarks" label="备注" align="center" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            v-hasPermi="['assetsManagement:formula:edit']"
            type="text"
            @click="handleAddFormula(row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加编辑公式 -->
    <el-dialog
      :title="title"
      v-model="open"
      :before-close="cancel"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="公式名称" prop="formulaName">
          <el-input
            v-model="form.formulaName"
            placeholder="公式名称"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="计算公式" prop="calculationFormula">
          <el-input
            v-show="false"
            v-model="form.calculationFormula"
            placeholder="计算公式"
          />
          <div class="formula-editor">
            <div class="formula-editor-value mb10 pb10">
              <div
                v-if="
                  !form.calculationFormula || form.calculationFormula === ''
                "
                style="text-align: center; font-weight: bold"
              >
                None
              </div>
              <div v-else class="value-wrap">
                <div>{{ form.calculationFormula }}</div>
                <div style="min-width: 120px">
                  <el-button
                    type="danger"
                    size="small"
                    @click="form.calculationFormula = ''"
                    >重置</el-button
                  >
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleBackFormula"
                    ><el-icon><Back /></el-icon
                  ></el-button>
                </div>
              </div>
            </div>
            <div class="mb10">
              <span class="mr10">符号:</span>
              <div style="display: inline-block" @click="handleOperatorClick">
                <el-button data-op="+">+</el-button>
                <el-button data-op="-">-</el-button>
                <el-button data-op="*">*</el-button>
                <el-button data-op="÷">÷</el-button>
                <el-button data-op="(">(</el-button>
                <el-button data-op=")">)</el-button>
              </div>
            </div>
            <div>
              <span class="mr10">字段:</span>
              <div style="display: inline-block" @click="handleFieldClick">
                <el-button type="primary" data-op="还款金额"
                  >还款金额</el-button
                >
                <el-button type="primary" data-op="系数">系数</el-button>
                <el-button type="primary" data-op="垫资">垫资</el-button>
                <el-button type="primary" data-op="特殊创佣"
                  >特殊创佣</el-button
                >
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            rows="4"
            placeholder="请填写备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submitFormula"
            >保存公式</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { fmlList, addFml, updateFml } from "@/api/assetsManagement/formula";
const { proxy } = getCurrentInstance();

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    formulaName: undefined,
  },
  form: {
    formulaName: undefined,
    calculationFormula: "",
    remarks: undefined,
  },
  rules: {
    formulaName: [
      { required: true, message: "请输入公式名称", trigger: "blur" },
    ],
    calculationFormula: [
      { required: true, message: "请输入计算公式", trigger: "blur" },
    ],
  },
});
const { queryParams, form, rules } = toRefs(data);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const title = ref("添加公式");
const open = ref(false);
const subloading = ref(false);

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    formulaName: undefined,
  };
  getList();
}

function getList() {
  loading.value = true;
  fmlList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

// 添加/编辑公式
function handleAddFormula(row) {
  if (row !== null) {
    title.value = "编辑公式";
    for (const key in form.value) {
      form.value[key] = row[key];
    }
    Object.assign(form.value, {formulaId: row.formulaId})
  } else {
    title.value = "添加公式";
  }
  open.value = true;
  
}

// 判断最后一个字段是否在数组内
function lastFieldIsInArray(arr) {
  const form_arr = form.value.calculationFormula.split(" ");
  const last = form_arr[form_arr.length - 1];
  return arr.includes(last);
}

// 辅助函数：判断是否是运算符
function isOperator(char) {
  return ["+", "-", "*", "÷"].includes(char);
}

// 符号点击
function handleOperatorClick(event) {
  const buttonEl = event.target.closest(".el-button");
  if (!buttonEl) return;

  const operator = buttonEl.dataset.op;
  const formula = form.value.calculationFormula;
  const lastChar = formula.split(" ").pop();

  // 1. 不能运算符和)开头
  if (formula === "" && (isOperator(operator) || operator === ")")) {
    return;
  }

  // 2. (之后不能接运算符
  if (isOperator(operator) && lastChar === "(") {
    return;
  }

  // 3. (之前不能是),必须是运算符
  if (
    operator === "(" &&
    (lastChar === ")" ||
      lastFieldIsInArray(["还款金额", "系数", "垫资", "特殊创佣"]))
  ) {
    return;
  }

  // 4. 不能连续两个运算符
  if (isOperator(operator) && isOperator(lastChar)) {
    return;
  }

  //  )前必须是字段或)
  if (
    operator === ")" &&
    !lastFieldIsInArray(["还款金额", "系数", "垫资", "特殊创佣", ")"])
  ) {
    return;
  }

  form.value.calculationFormula += formula === "" ? operator : ` ${operator}`;
}

function handleFieldClick(event) {
  const buttonEl = event.target.closest(".el-button");
  if (!buttonEl) return;

  const field = buttonEl.dataset.op;
  const formula = form.value.calculationFormula;
  const lastChar = formula.split(" ").pop();

  // 1. 字段前不能是其他字段或")"
  if (lastFieldIsInArray(["还款金额", "系数", "垫资", "特殊创佣", ")"])) {
    return;
  }

  // 2. 字段前必须是运算符或"("或空
  if (
    !(
      formula === "" ||
      lastChar === "(" ||
      lastFieldIsInArray(["+", "-", "*", "÷"])
    )
  ) {
    return;
  }
  form.value.calculationFormula += formula === "" ? field : ` ${field}`;
}

// 回退到上一步
function handleBackFormula() {
  let arr = form.value.calculationFormula.split(" ");
  arr.pop();
  form.value.calculationFormula = arr.join(" ");
}

// 提交公式
function submitFormula() {
  proxy.$refs.formRef.validate((valid) => {
    if (!valid) return;
    const formula = form.value.calculationFormula.trim();
    const parts = formula.split(" ");

    // 1. 基本验证
    if (!formula) {
      proxy.$message.warning("请输入计算公式");
      return;
    }

    // 2. 括号匹配验证（包括嵌套检查）
    let bracketStack = 0;
    let lastBracketPos = -1;
    for (let i = 0; i < formula.length; i++) {
      const char = formula[i];
      if (char === "(") {
        bracketStack++;
        lastBracketPos = i;
        // 检查空括号
        if (i < formula.length - 1 && formula[i + 1] === ")") {
          proxy.$message.warning("存在空括号");
          return;
        }
      } else if (char === ")") {
        bracketStack--;
        // 检查括号是否匹配
        if (bracketStack < 0) {
          proxy.$message.warning("括号不匹配");
          return;
        }
      }
    }
    if (bracketStack !== 0) {
      proxy.$message.warning("括号不匹配");
      return;
    }

    // 3. 开头和结尾验证
    const firstChar = parts[0];
    const lastChar = parts[parts.length - 1];

    // 开头不能是运算符或")"
    if (["+", "-", "*", "÷", ")"].includes(firstChar)) {
      proxy.$message.warning("公式不能以运算符或右括号开头");
      return;
    }

    // 结尾不能是运算符或"("
    if (["+", "-", "*", "÷", "("].includes(lastChar)) {
      proxy.$message.warning("公式不能以运算符或左括号结尾");
      return;
    }

    // 4. 运算符和字段的交替验证
    for (let i = 0; i < parts.length - 1; i++) {
      const current = parts[i];
      const next = parts[i + 1];

      // 字段后必须是运算符或")"
      if (
        ["还款金额", "系数", "垫资", "特殊创佣"].includes(current) &&
        !["+", "-", "*", "÷", ")"].includes(next)
      ) {
        proxy.$message.warning(`字段 ${current} 后必须接运算符或右括号`);
        return;
      }

      // 运算符后必须是字段或"("
      if (
        ["+", "-", "*", "÷"].includes(current) &&
        !["还款金额", "系数", "垫资", "特殊创佣", "("].includes(next)
      ) {
        proxy.$message.warning(`运算符 ${current} 后必须接字段或左括号`);
        return;
      }

      // "("后必须是字段或"("
      if (
        current === "(" &&
        !["还款金额", "系数", "垫资", "特殊创佣", "("].includes(next)
      ) {
        proxy.$message.warning("左括号后必须接字段或另一个左括号");
        return;
      }

      // ")"前必须是字段或")"
      if (
        next === ")" &&
        !["还款金额", "系数", "垫资", "特殊创佣", ")"].includes(current)
      ) {
        proxy.$message.warning("右括号前必须是字段或另一个右括号");
        return;
      }
    }

    // 5. 特殊验证：不能有连续的运算符
    for (let i = 0; i < parts.length - 1; i++) {
      if (
        ["+", "-", "*", "÷"].includes(parts[i]) &&
        ["+", "-", "*", "÷"].includes(parts[i + 1])
      ) {
        proxy.$message.warning("不能有连续的运算符");
        return;
      }
    }
    subloading.value = true;
    if (form.value.formulaId) {
      // 编辑
      updateFml(form.value)
        .then(() => {
          proxy.$message.success("保存成功");
          cancel();
          getList();
        })
        .finally(() => {
          subloading.value = false;
        });
    } else {
      // 新增
      addFml(form.value)
        .then(() => {
          proxy.$message.success("保存成功");
          cancel();
          getList();
        })
        .finally(() => {
          subloading.value = false;
        });
    }
  });
}

// 取消
function cancel() {
  proxy.resetForm("formRef");
  form.value = {
    formulaName: undefined,
    calculationFormula: "",
    remarks: undefined,
  }
  open.value = false;
}
</script>

<style scoped>
.formula-editor {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.formula-editor-value {
  border-bottom: 1px solid #ccc;
  font-size: 16px;
}
.value-wrap {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
</style>
