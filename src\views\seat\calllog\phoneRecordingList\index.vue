<template>
  <div>
    <el-form class="form-content h-50" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
      :inline="true" label-width="68px">
      <el-form-item label="主叫号码" prop="userPhone">
        <el-input v-model="queryParams.userPhone" placeholder="请输入" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="被叫号码" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="处置人员" prop="odvName">
        <el-input v-model="queryParams.odvName" placeholder="请输入" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="呼叫类型" prop="direction">
        <el-select v-model="queryParams.direction" placeholder="请选择" clearable style="width: 240px">
          <el-option label="全部" value="全部" />
          <el-option label="呼入" value="1" />
          <el-option label="呼出" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="呼叫时间" prop="callTime" style="width: 308px">
        <el-date-picker v-model="queryParams.callTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="通话时长" style="width: 308px">
        <el-row>
          <el-col :span="11">
            <el-input v-model="queryParams.agentDuration1" @input="value => queryParams.agentDuration1 = value.replace(/[^0-9]/g, '')" clearable />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input v-model="queryParams.agentDuration2" @input="value => queryParams.agentDuration2 = value.replace(/[^0-9]/g, '')" clearable />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb8">
      <el-button v-if="activeTab == 'y' && checkPermi(['calllog:phoneRecordingList:create'])" plain :disabled="single" type="primary"
        @click="addDownTask">创建下载录音任务</el-button>
      <el-button plain :disabled="single" type="info" v-if="checkPermi(['calllog:phoneRecordingList:import'])"
        @click="downloadRecord">导出记录</el-button>
    </el-row>

    <el-row class="hint">
      <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
        <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
          :indeterminate="item.indeterminate" :disabled="dataList.length === 0" />
      </el-checkbox-group>

      <el-tooltip effect="light" placement="bottom-start">
        <div>
          <svg-icon class="hint-item text-warning" icon-class="question" />
        </div>
        <template #content>
          <div class="info-tip">
            <el-icon class="info-tip-icon" :size="16">
              <warning-filled />
            </el-icon>
            <div>
              <p>注：</p>
              <p>
                1、默认只展示当月的通话记录,请于晚22:00前创建任务，否则无法下载；录音下载任务创建后，将于次日（24小时后）进行批量下载；
              </p>
              <p>
                2、录音按照双方合同约束默认保留储存期，服务器将自动彻底删除录音，请系统使用方即时保留录音到本地，避免造成录音丢失。
              </p>
            </div>
          </div>
        </template>
      </el-tooltip>

      <!-- <div class="top-right-btn" style="right: 146px; top: -10px">
        <audioVue :src="videoUrl" />
      </div> -->

      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" :types="[1, 2, 3]" />
    </el-row>

    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane v-for="item in answerOptions" :key="item.code" :label="item.info" :name="item.code" />
    </el-tabs>

    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
        <template #default="scope">
          <el-tooltip placement="top" v-if="scope.row.caseId != null && scope.row.caseId.length > 1" >
            <template #content>
              <div class="caseid-tooltip">
                <span v-for="(item, index) in scope.row.caseId" :key="index" @click="toDetails(item, scope.$index)">{{item}}</span>
              </div>
            </template>
            <span style="color: #409eff; cursor: pointer"
              @click="toDetails(scope.row.caseId[0], scope.$index)">{{ `${scope.row.caseId[0]}...` }}</span>
          </el-tooltip>
          <span style="color: #409eff; cursor: pointer" v-else-if="scope.row.caseId != null && scope.row.caseId.length == 1"
            @click="toDetails(scope.row.caseId[0], scope.$index)">{{ scope.row.caseId[0] }}</span>
          <span v-else>{{ '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="呼叫时间" align="center" key="createTime" prop="createTime" v-if="columns[1].visible" 
       width="180"/>
      <el-table-column label="通话时长（s）" align="center" key="duration" prop="duration"
        v-if="columns[2].visible" />
      <el-table-column label="主叫号码" align="center" key="userPhone" prop="userPhone" v-if="columns[3].visible" />
      <el-table-column label="被叫号码" align="center" key="phone" prop="phone" v-if="columns[4].visible" />
      <el-table-column label="借款人" align="center" key="borrower" prop="borrower" v-if="columns[5].visible">
        <template #default="{ row }">
          <Tooltip :content="row.borrower" :length="8" />
        </template>
      </el-table-column>
      <el-table-column label="呼叫类型" align="center" key="direction" prop="direction"
        :formatter="row => workPhoneCallOutEnum[row.direction] || '--'" v-if="columns[6].visible" />
      <el-table-column label="处置人员" align="center" key="odvName" prop="odvName" v-if="columns[7].visible" />
      <el-table-column label="操作" width="350">
        <template #default="scope">
          <!-- <el-button
            v-show="scope.row.recordUrl"
            type="text"
            v-hasPermi="['seat:calllog:play']"
            @click="playAudio(scope.row.recordUrl)"
            >播放</el-button
          > -->
          <div class="play-button">
            <musicPlayer v-if="checkPermi(['calllog:phoneRecordingList:play']) && scope.row.recordUrl"
              :ref="`audioRef${scope.$index}`" @stopCheck="stopCheck(scope.$index)"
              :audioSrc="`${scope.row.recordUrl}`" :durationOrigin="scope.row.agentDuration" />
          </div>
          <div class="download-button">
            <el-button v-show="scope.row.recordUrl" type="text" v-hasPermi="['calllog:phoneRecordingList:down']"
              @click="downVideo(scope.row.recordUrl)">下载</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="Calllog">

import musicPlayer from "@/components/MusicPlay/musicPlayer";
import audioVue from "@/components/audioVue/index.vue";
import { checkPermi } from "@/utils/permission";
import { workPhoneCallOutEnum } from '@/utils/enum';
import { getAnswerStatus } from "@/api/seat/calllog";
import { callRecordList, createDownloadTask } from "@/api/workPhone/index.js"
const { proxy } = getCurrentInstance();
const router = useRouter();

const directionOptions = ref([]); //呼叫类型下拉
const selectLoading = ref(false);
const single = ref(true); //能否操作
const selectedArr = ref([]); //表格选中数据
const ids = ref([]);
const answerOptions = ref([]); //tab数据
const activeTab = ref(undefined);
const showSearch = ref(false);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const multipleTableRef = ref();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userPhone: undefined,
    phone: undefined,
    teamName: undefined,
    odvName: undefined,
    direction: undefined,
    callTime: [],
    agentDuration1: undefined,
    agentDuration2: undefined,
    allQuery: false, //是否搜索结果全选
  },
});
const videoUrl = ref(""); //录音路径

getAnswerStatus().then((res) => {
  answerOptions.value = res.data;
  activeTab.value = res.data[0].code;
  answerOptions.value.push({ code: "全部", info: "全部" });
  getList();
});

const { queryParams } = toRefs(data);
const handledForm = ref({}); //处理后的搜索条件
const rangFields = ["callTime"];
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "呼叫时间", visible: true },
  { key: 2, label: "通话时长（s）", visible: true },
  { key: 3, label: "主叫号码", visible: true },
  { key: 4, label: "被叫号码", visible: true },
  { key: 5, label: "借款人", visible: true },
  { key: 6, label: "呼叫类型", visible: true },
  { key: 7, label: "处置人员", visible: true },
]);

const checkedType = ref(["本页选中"]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

//获取列表数据
function getList() {
  loading.value = true;
  handledForm.value = proxy.addFieldsRange(queryParams.value, rangFields);
  handledForm.value.answer = activeTab.value == "全部" ? undefined : activeTab.value;
  handledForm.value.direction =
    handledForm.value.direction == "全部" ? undefined : handledForm.value.direction;
  callRecordList(handledForm.value)
    .then((res) => {
      dataList.value = res.rows;
      dataList.value?.length > 0 && dataList.value.forEach((item) => {
        item.caseId = item.caseId.split(",").map(Number);
      })
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 跳转详情
function toDetails(caseId, index) {
  let queryChange = JSON.parse(JSON.stringify(queryParams.value));
  queryChange.pageNum = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange,
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/case/caseIndex-detail/manageDetails/${caseId}`, query: { type: "caseManage" } });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    userPhone: undefined,
    phone: undefined,
    teamName: undefined,
    odvName: undefined,
    direction: undefined,
    callTime: [],
    agentDuration1: undefined,
    agentDuration2: undefined,
    allQuery: false, //是否搜索结果全选
  };
  getList();
}

//当有录音播放的时候停止其他录音
function stopCheck(count) {
  dataList.value.forEach((item, index) => {
    if (index !== count) {
      proxy.$refs[`audioRef${index}`].stopAudio();
    }
  });
}

//创建下载录音任务
function addDownTask() {
  let form = {};
  if (queryParams.value.allQuery) {
    form = handledForm.value;
  } else {
    form.ids = ids.value;
    form.allQuery = false;
  }
  createDownloadTask(form).then((res) => {
    proxy.$modal
      .confirm(res.msg, "操作成功")
      .then(() => { })
      .catch(() => { });
  });
}

//导出记录
function downloadRecord() {
  let form = {};
  if (queryParams.value.allQuery) {
    form = handledForm.value;
  } else {
    form.ids = ids.value;
    form.allQuery = false;
  }
  proxy.downloadforjson(
    "/call/work-phone/exportCallRecord",
    { ...form },
    `通话记录_${new Date().getTime()}.xlsx`
  );
}

//tab选择
function tabChange() {
  nextTick(() => {
    getList();
  });
}

//表格选中
function selectTable() {
  return new Promise((reslove, reject) => {
    try {
      dataList.value.map((item, index) => {
        multipleTableRef.value.toggleRowSelection(item, true);
      });
      reslove(true);
    } catch (error) {
      reject(error);
    }
  });
}

//全选类型
function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    multipleTableRef.value.clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else {
    dataList.value.length > 0 &&
      dataList.value.map((item) => {
        multipleTableRef.value.toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.allQuery = true;
    handledForm.value.allQuery = true;
  } else {
    queryParams.value.allQuery = false;
    handledForm.value.allQuery = false;
  }
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.callId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
  if (checkedType.value[0] != "搜索结果全选") {
    if (selectedArr.value.length) {
      checkedType.value[0] = "本页选中";
    } else {
      checkedType.value = [];
    }
    checkStatus.value[0].indeterminate =
      selectedArr.value.length > 0 && selectedArr.value.length < dataList.value.length;
  }
}

//表格行能否选择
function checkSelectable(row, index) {
  if (queryParams.value.allQuery) {
    return false;
  } else {
    return true;
  }
}

watch(dataList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (queryParams.value.allQuery) {
      queryParams.value.allQuery = false;
      handledForm.value.allQuery = false;
      nextTick(() => {
        selectTable()
          .then((res) => { })
          .finally(() => {
            queryParams.value.allQuery = true;
            handledForm.value.allQuery = true;
          });
      });
    } else {
      multipleTableRef.value.toggleAllSelection();
    }
  }
});

//播放录音
function playAudio(url) {
  videoUrl.value = url;
}

//下载录音
function downVideo(url) {
  window.open(url);
}

</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.play-button {
  display: inline-block;
  width: 80%;
  vertical-align: middle;
}

.download-button {
  display: inline-block;
  width: 20%;
  vertical-align: middle;
  margin-top: 5px;
}

.caseid-tooltip {
  max-width: 180px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  span {
    cursor: pointer;
  }
}
</style>
