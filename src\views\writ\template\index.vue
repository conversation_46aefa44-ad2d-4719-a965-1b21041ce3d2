<template>
  <div class="app-container">
    <el-form :model="queryParams" :class="{ 'form-h50': !showSearch }" ref="queryRef" inline>
      <el-form-item label="模板类型：">
        <el-cascader style="width: 280px" v-model="classifyIdsList" :props="{ multiple: true }" :options="classifyList"
          collapse-tags filterable collapse-tags-tooltip />
      </el-form-item>
      <el-form-item label="模板名称：">
        <el-select v-model="templateIdsList" clearable filterable style="width: 280px" @focus="getTemplate" multiple
          collapse-tags collapse-tags-tooltip placeholder="请选择模板名称">
          <el-option v-for="(item, index) in templateList" :key="index" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="operation-revealing-area mb10 mt10">
      <el-button type="primary" plain @click="addTemplate()" v-if="checkPermi(['writ:template:add'])">创建模板</el-button>
      <!-- <el-button type="primary" v-hasPermi="['writ:template:import']" plain @click="ImportTemplate()">导入PDF模板</el-button> -->
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column v-if="columns[0].visible" label="编号" align="center" prop="templateCode" show-overflow-tooltip />
      <el-table-column v-if="columns[1].visible" label="模板类型" align="center" prop="classifyLabel"
        show-overflow-tooltip />
      <el-table-column v-if="columns[2].visible" label="模板名称" align="center" prop="templateName"
        show-overflow-tooltip />
      <el-table-column v-if="columns[3].visible" label="创建人" align="center" prop="createBy" show-overflow-tooltip />
      <el-table-column v-if="columns[4].visible" label="创建时间" align="center" prop="createTime" />
      <el-table-column v-if="columns[5].visible" label="状态" align="center" prop="status">
        <template #default="{ row }">
          <span v-if="row.status == 0" class="blue">启用中</span>
          <span v-else class="red">禁止中</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="text" link v-hasPermi="['writ:template:details']"
            @click="templateDetails(row)">详情</el-button>
          <el-button type="text" link v-hasPermi="['writ:template:edit']" @click="addTemplate(row)">编辑</el-button>
          <el-button v-if="row.status == 0" type="text" link @click="setStatus(row)"
            v-hasPermi="['writ:template:close']">禁用</el-button>
          <el-button v-else type="text" link @click="setStatus(row)" v-hasPermi="['writ:template:open']">启用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 详情 -->
    <templateDetailsBox ref="templateDetailsBoxRef" />
  </div>
</template>

<script setup name="TemplateIndex">
import { checkPermi } from "@/utils/permission";
import {
  getClassifyList,
  getTemplateList,
  editStatus,
  getUserOption,
} from "@/api/writ/template";
import { getTemplateOptions } from "@/api/writ/lawyer";
import templateDetailsBox from "./page/templateDetails";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
//多选数据
const classifyList = ref([]);
const classifyIdsList = ref([]);
const templateList = ref([]);
const templateIdsList = ref([]);
const columns = ref([
  { key: 0, label: `编号`, visible: true },
  { key: 1, label: `模板类型`, visible: true },
  { key: 2, label: `模板名称`, visible: true },
  { key: 3, label: `创建人`, visible: true },
  { key: 4, label: `创建时间`, visible: true },
  { key: 5, label: `状态`, visible: true },
]);
const createByIdsList = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateIds: undefined,
    createByIds: undefined,
    classifyIds: undefined,
  },
});
const { queryParams } = toRefs(data);
//多选字段
const checkMoreList = ref(["templateIds", "createByIds"]);
const checkMoreName = ref([templateIdsList, createByIdsList]);
//数据参数
const dataList = ref([]);
const total = ref(0);
//获取列表
function getList() {
  queryParams.value.classifyIds;
  let formatterIdsList = classifyIdsList.value.map((item) => {
    let text = item.toString().replace(",", "/");
    return text;
  });
  queryParams.value.classifyIds = formatterIdsList.toString();
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });
  loading.value = true;
  getTemplateList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取分类数据
function getClassify() {
  classifyList.value = [];
  getClassifyList().then((res) => {
    classifyList.value = res.data;
  });
}
getClassify();

//获取模板数据
function getTemplate() {
  getTemplateOptions().then((res) => {
    templateList.value = res.data;
  });
}
getTemplate();

//获取创建人数据
// function getCreateBys() {
//   getUserOption().then((res) => {
//     createByList.value = res.data;
//   });
// }
// getCreateBys();

/** 多选框选中数据 */
function addTemplate(row) {
  if (row) {
    router.push(`/writ-template/editTemplate/${row.id}`);
  } else {
    router.push("/writ-template/addTemplate");
  }
}

// 导入模板
function ImportTemplate() {
  router.push({ path: "/writ-template/addTemplate", query: { isCompress: true } });
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    templateIds: undefined,
    createByIds: undefined,
    classifyIds: undefined,
  };
  templateIdsList.value = [];
  createByIdsList.value = [];
  classifyIdsList.value = [];
  getList();
}

//停用
function setStatus(row) {
  let req = { status: row.status == 0 ? 1 : 0, id: row.id };
  const content = `是否确认${row.status == 0 ? "启用" : "停用"}？此操作将${row.status == 0 ? "启用" : "停用"}该模板，是否确认？`
  proxy.$modal.confirm(content).then(() => {
    editStatus(req).then((res) => {
      getList();
    })
  }).finally(() => getList())
}

//模板详情
function templateDetails(row) {
  proxy.$refs["templateDetailsBoxRef"].opendialog(row.id);
}
</script>
<style lang="scss" scoped>
:deep(.el-cascader .el-cascader__tags) {
  left: -3px !important;
}
</style>
<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
