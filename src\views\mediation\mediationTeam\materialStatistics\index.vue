<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="side-edge">
        <div class="head-container mb10 pl20">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          团队送达排名
        </div>
        <div class="dept-list">
          <div class="dept-item" v-for="dept in allDept" :key="dept.id">
            <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
              {{ `${dept.name}(${dept.caseNum || 0})` }}
            </div>
            <!-- <div :class="`employ-item ${activeDept == employ.id ? 'active' : ''}`" v-for="employ in dept.children"
              :key="employ.id">
              <div @click="handleChangeRanke(employ.id, 1)">{{ `${employ.name}(${employ.caseNum || 0})` }}</div>
            </div> -->
          </div>
        </div>
      </el-col>
      <el-col :span="20" :xs="24" class="pl20">
        <el-form :model="queryParams" :loading="loading" ref="queryRef" inline
          :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
          <el-form-item prop="clientName" label="被告">
            <el-input v-model="queryParams.clientName" style="width: 280px" placeholder="请输入被告" />
          </el-form-item>
          <el-form-item prop="caseId" label="案件ID">
            <el-input v-model="queryParams.caseId" style="width: 280px" placeholder="请输入案件ID" />
          </el-form-item>
          <el-form-item prop="expressNumber" label="寄送单号">
            <el-input v-model="queryParams.expressNumber" style="width: 280px" placeholder="请输入寄送单号" />
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
          <el-button type="primary" :disabled="selectedArr.length == 0" :loading="loading" v-if="checkPermi(['mediationTeam:materialStatistics:download'])"
            @click="handleDownload()">批量导出</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <el-tabs class="mb8" v-model="activeTab" @tab-click="handleQuery">
          <el-tab-pane v-for="(item, index) in sendEnum" :key="index" :label="item" :name="index" />
          <el-tab-pane label="全部" name="全部" />
        </el-tabs>
        <selectedAll v-model:allQuery="allQuery" :cusTableRef="proxy.$refs.multipleTableRef" :selectedArr="selectedArr"
          :dataList="dataList">
          <template #content>
            <div class="text-flex ml20">
              <span>案件数量：</span>
              <span class="text-danger mr10">{{ statistics.size || 0 }} </span>
              <span>标的额：</span>
              <span class="text-danger mr10">{{ numFilter(statistics.money) }}</span>
            </div>
          </template>
        </selectedAll>
        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column v-if="columns[0].visible" label="案件ID" align="center" key="caseId" prop="caseId" width="80">
            <template #default="{ row, $index }">
              <el-button type="text" @click="toDetails(row.caseId, $index)"> {{ row.caseId }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="被告" align="center" key="clientName" prop="clientName"
            width="120px" />
          <el-table-column v-if="columns[2].visible" label="标的额" align="center" key="remainingDue" prop="remainingDue"
            width="120px">
            <template #default="{ row }">
              <span>{{ numFilter(row.remainingDue) }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[3].visible" label="身份证号码" align="center" key="clientIdCard" prop="clientIdCard"
            width="180px" />
          <el-table-column v-if="columns[4].visible" label="户籍地" align="center" key="clientCensusRegister"
            prop="clientCensusRegister" show-overflow-tooltip width="160px" />
          <el-table-column v-if="columns[5].visible" label="寄送状态" align="center" key="sentStatus" prop="sentStatus"
            width="100px" :formatter="row => sendEnum[row.sentStatus]" />
          <el-table-column v-if="columns[6].visible" label="寄送材料数量" align="center" key="sentCount" prop="sentCount"
            width="120px" />
          <el-table-column v-if="columns[7].visible" label="寄送法院" align="center" key="sentCourt" prop="sentCourt"
            width="160px" show-overflow-tooltip />
          <el-table-column v-if="columns[8].visible" label="寄送单号" align="center" key="expressNumber"
            prop="expressNumber" width="160px">
            <template #default="{ row }">
              <el-button type="text" @click="handleOpenDialog('logisticsInfoRef', row)">
                {{ row.expressNumber }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[9].visible" label="操作人" align="center" key="creatBy" prop="creatBy"
            :width="100" />
          <el-table-column v-if="columns[10].visible" label="操作时间" align="center" key="creatTime" prop="creatTime"
            :width="160" show-overflow-tooltip />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <logisticsInfo ref="logisticsInfoRef" />
  </div>
</template>
<script setup name="TeamsCases">
import { checkPermi } from "@/utils/permission";
import logisticsInfo from './dialog/logisticsInfo';
import { formatParams } from "@/utils/common";
import { selectMaterialDeliveryList, selectMaterialDeliveryMoney, selectmaterialDeliveryRanking } from "@/api/team/materialStatistics";
import { sendEnum } from "@/utils/enum";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
//树结构
const activeDept = ref(undefined);
const allDept = ref([]);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const allQuery = ref(false);
const showSearch = ref(false)
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const statistics = ref({
  caseNum: 0,
  money: 0,
  principal: 0,
});
//列表切换字段
const activeTab = ref("全部");
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '标的额', visible: true },
  { key: 3, label: '身份证号码', visible: true },
  { key: 4, label: '户籍地', visible: true },
  { key: 5, label: '寄送状态', visible: true },
  { key: 6, label: '寄送材料数量', visible: true },
  { key: 7, label: '寄送法院', visible: true },
  { key: 8, label: '寄送单号', visible: true },
  { key: 9, label: '操作人', visible: true },
  { key: 10, label: '操作时间', visible: true },
])
//需要拆分的字段
const rangFiles = [];
//获取部门案件
function getList() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFiles)
  reqForm.sentStatus = activeTab.value == '全部' ? undefined : activeTab.value
  loading.value = true
  selectMaterialDeliveryList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}
provide("getList", Function, true);
getList();

//获取部门列表
function getTeamTreeData() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFiles)
  reqForm.sentStatus = activeTab.value == '全部' ? undefined : activeTab.value
  delete reqForm.pageNum
  delete reqForm.pageSize
  selectmaterialDeliveryRanking(reqForm).then((res) => {
    allDept.value = res.data
  });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
  queryParams.value.stringDeptId = val
  activeDept.value = val
  queryParams.value.pageNum = 1
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  queryChange.pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "mycase",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
}

// 打开弹窗
function handleOpenDialog(refName, row) {
  const data = {
    lettersNumber: row.serialNo,
    expressNumber: row.expressNumber,
    address: row.address,
  }
  proxy.$refs[refName].openDialog(data)
}

//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

//重置
function resetQuery() {
  activeDept.value = undefined
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFiles)
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.ids = undefined;
  reqForm.sentStatus = activeTab.value == '全部' ? undefined : activeTab.value
  reqForm.condition = allQuery.value
  return reqForm
}

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0, };
      reslove()
      return false
    }
    selectMaterialDeliveryMoney(getReqParams()).then((res) => {
      statistics.value = { ...res.data, principal: res.data.concludeCaseAmount };
    }).finally(() => reslove());
  })
}
// 格式化金额
function moneyFor(num, str = '') {
  return num ? proxy.setNumberToFixed(num) : str
}

// 导出
function handleDownload() {
  const reqForm = getReqParams()
  proxy.downloadforjson('/team/exportWithMaterialDelivery', reqForm, `材料寄送统计_${+new Date()}.xlsx`)
}

//选择列表
function handleSelectionChange(selection) {
  selectedArr.value = selection;
}
watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })
</script>
<style scoped>
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: red;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

:deep(.el-tree-node__label) {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.df-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
<style lang="scss" scoped>
.side-edge {
  height: calc(100vh - 125px);
  padding: 0 !important;
  border-right: 2px solid #eee;
}

.head-container {
  color: #333;
  font-weight: bold;
}

.dept-list {
  color: #5a5a5a;
  cursor: pointer;
  height: 80vh;
  overflow: auto;

  .active {
    border-right: 2px solid #60b2ff;
    background-color: #e6f7ff;
  }

  .dept-item {
    width: 100%;
    line-height: 44px;
    padding-left: 20px;
  }

  .employ-item {
    width: 100%;
    line-height: 44px;
    height: 44px;
    padding-left: 20px;
  }
}
</style>