<template>
    <div>
        <div class="title mb20">
            <span>开启后，系统中相关字段信息将被强制脱敏</span> 
            <el-switch class="myswitch ml20" v-model="state.informationStatus" active-color="#2ECC71" :active-value="1" :inactive-value="0"
            active-text="开" inactive-text="关" @change="hadnleChangeDesensitization"  v-if="checkPermi(['system:safetySet:desensitization:switch'])" />
        </div>
        <el-table :data="list" v-if="state.informationStatus">
            <el-table-column align="center" prop="fieldname" label=" 字段" />
            <el-table-column align="center" prop="" label=" 状态">
                <template #default="{ row }">
                    <el-switch v-model="row.status" active-color="#2ECC71" :active-value="1" :inactive-value="0"
                        active-text="开" inactive-text="关" :disabled="state.informationStatus == 0"
                        @change="handleChange" v-if="checkPermi(['system:safetySet:desensitization:status'])" />
                </template>
            </el-table-column>
            <el-table-column align="center" prop="rules" label=" 脱敏规则" />
        </el-table>
    </div>
</template>
<script setup>
const dataList = ref([])
import { changeSafeStatus, changSecrecys } from "@/api/safetySet/index";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);
const list = inject("desensitization");
const _desensitization = inject("_desensitization"); //脱敏

// 设置脱敏
function hadnleChangeDesensitization() {
    changeSafeStatus(state.value)
    .then((res) => {})
    .catch(() => {
      getTeamSafe();
    });
}
function handleChange() {
    let obj = _desensitization.value;
    list.value.map((item) => {
      let key = item.filed;
      if (key in obj) {
        obj[key] = item.status;
      }
    });
    changSecrecys(obj)
      .then((res) => {})
      .catch(() => {
        getTeamSafe();
      });
}
</script>
<style scoped>
.el-switch .el-switch__label {
    width: auto !important;
}
</style>