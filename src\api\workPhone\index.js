import request from '@/utils/request'

// 手机外呼
export function partner<PERSON>ustomer(data) {
    return request({
        url: '/call/work-phone/partnerCustomer',
        method: 'post',
        data: data
    })
}

// 工作手机数据同步
export function workPhoneDataSync(data) {
    return request({
        url: '/call/work-phone/dataSync',
        method: 'post',
        data: data,
        gateway: 'caseManage'
    })
}

//获取工作手机列表
export function workPhoneList(query) {
    return request({
        url: '/call/work-phone/list',
        method: 'get',
        params: query
    })
}

//获取工作手机话单列表
export function callRecordList(query) {
    return request({
        url: '/call/work-phone/callRecordList',
        method: 'get',
        params: query
    })
}

//创建下载录音任务
export function createDownloadTask(data) {
    return request({
        url: '/call/work-phone/createDownloadTask',
        method: 'post',
        data: data
    })
}

//绑定工作手机
export function bind(data) {
    return request({
        url: '/call/work-phone/bind',
        method: 'post',
        data: data
    })
}

//解绑工作手机
export function unbind(data) {
    return request({
        url: '/call/work-phone/unbind',
        method: 'post',
        data: data
    })
}

// 判断机构是否开通工作手机
export function getOpenWorkPhone() {
    return request({
        url: '/call/work-phone/getOpenWorkPhone',
        method: 'get',
    })
}

// 来电弹屏
export function workPhonePopOnScreen(query) {
    return request({
        url: '/callCustom/workPhonePopOnScreen',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}
