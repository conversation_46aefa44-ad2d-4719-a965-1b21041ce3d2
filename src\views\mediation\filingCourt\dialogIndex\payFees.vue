<template>
    <el-dialog title="缴费" v-model="open" width="600px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item prop="costAmt" label="收费金额">
                <el-input style="width:80%" v-model="form.costAmt" placeholder="请输入收费金额" />
            </el-form-item>
            <el-form-item prop="costDate" label="收费日期">
                <el-date-picker style="width:80%" v-model="form.costDate" value-format="YYYY-MM-DD" type="date"
                    placeholder="请选择收费日期" />
            </el-form-item>
            <el-form-item prop="costType" label="款项类别">
                <el-select style="width:80%" v-model="form.costType" placeholder="请选择款项类别">
                    <el-option v-for="item in costTypeOption" :key="item.code" :label="item.info" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item prop="payMethod" label="付款方式">
                <el-select style="width:80%" v-model="form.payMethod" placeholder="请选择付款方式">
                    <el-option v-for="item in methodOption" :key="item.code" :label="item.info" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item prop="feeRecipient" label="收费对象">
                <el-input style="width:80%" v-model="form.feeRecipient" placeholder="请输入收费对象" />
            </el-form-item>
            <el-form-item prop="accountUnit" label="入账单位">
                <el-input style="width:80%" v-model="form.accountUnit" placeholder="请输入入账单位" />
            </el-form-item>
            <el-form-item prop="receiptUrl" label="收费回执">
                <FileUpload autoUpload btnText="上传" :fileType="['jpg', 'png']" v-model:fileList="fileList"
                    uploadFileUrl='/collection/uploadReduction' />
            </el-form-item>
            <el-form-item prop="remark" label="备注">
                <el-input style="width:80%" v-model="form.remark" type="textarea" placeholder="请输入备注" :maxlength="800"
                    show-word-limit :rows="5" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="subimt" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getPayMethodOption } from '@/api/caseDetail/detail'
import { addCostFilingCourt } from '@/api/mediation/filingCourt'
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const costTypeOption = ref([
    { code: "开庭费", info: "开庭费" }
])
const methodOption = ref([])
const fileList = ref([])
const data = reactive({
    form: {
        status: 0,
        costType: '开庭费',
    },
    rules: {
        costAmt: [
            { required: true, message: '请输入收费金额', trigger: 'blur' },
            { pattern: /^\d{1,20}(\.[0-9]{1,4})?$/, message: '请输入1~20位数字，可以保留四位小数', trigger: "blur" }
        ],
        costDate: [{ required: true, message: '请选择收费日期', trigger: 'blur' }],
        costType: [{ required: true, message: '请选择款项类别', trigger: 'blur' }],
        payMethod: [{ required: true, message: '请选择付款方式', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)
function subimt() {
    form.value.receiptUrl = fileList.value[0]?.response.data.fileUrl[0]
    form.value.receiptFileName = fileList.value[0]?.response.data.firstName[0]
    nextTick(() => {
        proxy.$refs['formRef'].validate((vaild) => {
            if (vaild) {
                const reqForm = JSON.parse(JSON.stringify(form.value))
                loading.value = true
                addCostFilingCourt(reqForm).then(res => {
                    if (res.code == 200) {
                        props.getList && props.getList()
                        proxy.$modal.msgSuccess('操作成功！')
                        cancel()
                    }
                }).finally(() => loading.value = false)
            }
        })
    })
}

//获取付款方式
getPaymethod()
function getPaymethod() {
    getPayMethodOption().then((res) => {
        methodOption.value = res.data;
    })
}

function openDialog(data) {
    open.value = true
    fileList.value = []
    form.value = { ...data, status: 0 }
}
function cancel() {
    open.value = false
    form.value = { status: 0, costType: '开庭费', }
}
defineExpose({ openDialog })
</script>