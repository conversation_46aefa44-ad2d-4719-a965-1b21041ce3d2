<template>
  <div class="app-container">
    <el-tabs class="mb8">
      <el-tab-pane label="修改密码" name="all"></el-tab-pane>
    </el-tabs>
    <el-form
      :model="form"
      ref="formRef"
      label-position="right"
      :rules="rules"
      :label-width="100"
    >
      <el-form-item label="旧密码" prop="primaryPassWord">
        <el-input
          v-model="form.primaryPassWord"
          type="password"
          style="width: 550px"
          auto-complete="off"
          placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
          show-password
          oncut="return false"
          onpaste="return false"
          oncopy="return false"
          onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
        />
      </el-form-item>
      <el-form-item label="新密码" :width="350"  prop="newPassword">
        <el-input
          v-model="form.newPassword"
          type="password"
          style="width: 550px"
          auto-complete="off"
          placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
          show-password
          oncut="return false"
          onpaste="return false"
          oncopy="return false"
          onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
          oninput="value=value.replace(/[^\x00-\xff]/g, '')"
        />
      </el-form-item>
       <el-form-item label="确认新密码" :width="350"  prop="checkNewPassword">
        <el-input
          v-model="form.checkNewPassword"
          type="password"
          style="width: 550px"
          auto-complete="off"
          placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
          show-password
          oncut="return false"
          onpaste="return false"
          oncopy="return false"
          onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
          oninput="value=value.replace(/[^\x00-\xff]/g, '')"
        />
      </el-form-item>
    </el-form>
      <div style="margin-left:180px">
          <el-button type="primary" :loading="loading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="toBack">取 消</el-button>
        </div>
  </div>
</template>
<script setup name="EditPassword">
import { updatePassWord } from "@/api/system/user";

//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const data = reactive({
  form: {
    primaryPassWord:undefined,
    newPassword:undefined,
    checkNewPassword:undefined
  },
  rules: {
    primaryPassWord: [{
        required: true,
        message: "旧密码不能为空",
        trigger: "blur",
      }],
    newPassword: [{
        required: true,
        pattern:  /^\S*$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },{
        required: true,
        pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      }],
    checkNewPassword: [{
        required: true,
        pattern:  /^\S*$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },{
        required: true,
        pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
        message: "长度为8-20位，只能包含大小写字母、数字以及标点符号（除空格）",
        trigger: "blur",
      }]
  }
});
const { form, rules } = toRefs(data);

//提交密码
function submitForm(){
    proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req = JSON.stringify(form.value)
      if(form.value.checkNewPassword !== form.value.newPassword){
        return proxy.$modal.msgWarning('两次密码输入不一致！')
      }
      loading.value = true;
      updatePassWord(req)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          toBack();
          location.reload();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

//返回
const toBack = () => {
  const obj = { path: "/index" };
  proxy.$tab.closeOpenPage(obj);
};

</script>
<style lang="scss" scoped></style>
