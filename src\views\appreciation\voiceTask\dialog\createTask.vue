<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="创建任务"></el-step>
        <el-step title="任务配置"></el-step>
        <el-step title="挂机短信"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>

    <div v-if="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="150px">
        <el-form-item label="任务名称：" prop="taskName">
          <el-input
            v-model="form.taskName"
            placeholder="输入任务名称"
            style="width: 400px"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="话术模板：" prop="voiceTplUuid">
          <el-select
            v-model="form.voiceTplUuid"
            placeholder="请选择模板"
            @change="changeS"
            @focus="changeAiDataT"
          >
            <el-option
              v-for="(item, index) in templateList"
              :key="index"
              :label="item.tempName"
              :value="item.voiceTplUuid"
            />
          </el-select>
          <div v-if="templateList.length > 0">
            {{
              templateList.find(
                (item) => item.voiceTplUuid == form.voiceTplUuid
              ).tempContent
            }}
          </div>
        </el-form-item>
        <el-form-item label="客户名单：" prop="fileUrl">
          <el-upload
            v-model="form.fileUrl"
            :file-list="uploadFileList"
            :headers="upload.headers"
            :action="uploadUrl"
            ref="uploadHeadRef"
            accept=".xlsx, .xls"
            :limit="1"
            :before-upload="handleFileUploadBefore"
            :on-change="handleFileEditChange"
            :on-success="handleFileSuccess"
            :before-remove="handleRemove"
          >
            <div class="upload-attachment">
              <el-button type="primary"
                ><el-icon> <Upload /> </el-icon>上传</el-button
              >
            </div>
            <template #tip>
              <span @click="downModal">
                点击<span style="cursor: pointer; color: #409eff">下载</span
                >模板
              </span>
              <div class="el-upload__tip">
                <div>
                  1、文件数量不超过1个，大小不超过20MB，仅支持格式： .xlsx .xls
                </div>
                <div>
                  2、文件上传成功后，为保证数据准确性，请先点击校验客户名单
                </div>
                <div>3、文件上传成功后将存在客户名单页面</div>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <div class="text-center mt20">
        <el-button @click="cancel">取消</el-button>
        <el-button
          class="ml10"
          type="primary"
          plain
          @click="nextstep"
          :loading="loading"
          >校验名单，下一步</el-button
        >
      </div>
    </div>

    <div v-show="stepActive === 1" class="step-item pt20">
      <el-form
        :model="configForm"
        :rules="configRules"
        ref="configFormRef"
        label-width="150px"
      >
        <el-form-item label="执行时间：">
          <el-radio-group>
            <el-radio
              v-for="(item, index) in timeList"
              :key="index"
              :label="item.code"
              >{{ item.info }}</el-radio
            >
          </el-radio-group>
          <el-checkbox-group>
            <el-row>
              <el-col :span="6" v-for="(item, index) in weekList" :key="item">
                <el-checkbox :label="index + 1" :value="index + 1">{{
                  item
                }}</el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="外呼时段：">
          <div v-for="(item, index) in outboundList" :key="index">
            <el-time-picker
              v-model="item.timeValue"
              is-range
              format="HH:mm"
              value-format="HH:mm"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
            <el-icon
              v-if="index == 0"
              class="ml10"
              style="cursor: pointer"
              :size="20"
              @click="addOutboundList"
              ><Plus
            /></el-icon>
            <el-icon
              v-if="index !== 0"
              class="ml10"
              style="cursor: pointer"
              :size="20"
              @click="removeOutboundList(index)"
              ><DeleteFilled
            /></el-icon>
          </div>
        </el-form-item>
        <el-form-item label="重呼次数：" prop="recallCount">
          <el-input-number
            :min="0"
            :max="3"
            v-model="configForm.recallCount"
            @change="initCount"
          />
          <span class="text-margin">次</span>
        </el-form-item>
        <el-form-item
          label="重呼间隔："
          prop="recallMinute"
          v-if="configForm.recallCount != 0"
        >
          <el-input-number
            :min="5"
            :max="120"
            v-model="configForm.recallMinute"
          />
          <span class="text-margin">分钟</span>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="configForm.remark"
            type="textarea"
            :rows="5"
            show-word-limit
            maxlength="300"
          ></el-input>
        </el-form-item>
      </el-form>

      <div class="text-center mt20">
        <el-button class="ml10" type="primary" plain @click="backstep"
          >上一步</el-button
        >
        <el-button @click="cancel">取消</el-button>
        <el-button class="ml10" type="primary" plain @click="nextstep"
          >下一步</el-button
        >
      </div>
    </div>

    <div v-if="stepActive === 2" class="step-item pt20">
      <el-form
        :model="allocationForm"
        :rules="allocationRules"
        ref="allocationFormRef"
        label-width="150px"
      >
        <el-form-item label="是否发送挂机短信：">
          <el-radio-group>
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="短信模板：">
          <el-select placeholder="请选择短信模板："></el-select>
        </el-form-item>
        <el-form-item label="选择发送对象：">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="changeCheckAll"
            >全部</el-checkbox
          >
          <el-checkbox-group v-model="sendArr">
            <el-checkbox
              v-for="(item, index) in sendObjectList"
              :key="index"
              :label="item.info"
              :value="item.info"
            />
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div class="text-center mt20">
        <el-button class="ml10" type="primary" plain @click="backstep"
          >上一步</el-button
        >
        <el-button @click="cancel">取消</el-button>
        <el-button
          class="ml10"
          type="primary"
          plain
          @click="nextstep"
          :loading="submitLoading"
          >提交并执行</el-button
        >
      </div>
    </div>

    <div v-if="stepActive === 3" class="step-item pt20">
      <div class="text-center mt20">
        <div v-if="createTaskState == 1" class="mb20">
          任务已创建成功并执行中
        </div>
        <div v-else class="mb20">
          任务已创建成功，但该任务中无坐席置闲，无法外呼，请先将坐席置闲后，任务即将开启；
        </div>
        <el-button class="ml10" type="primary" plain @click="cancel"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import SelectTwentyFour from "@/components/SelectTwentyFour";
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance();
import {
  customImportTemplate,
  importDataCustom,
  DeptTreeType,
} from "@/api/appreciation/customerList";
import { submitTask } from "@/api/appreciation/callOut";
import {
  getAiVoiceTplList,
  AiVoiceCaseSubmitTaskData,
} from "@/api/appreciation/voiceTask";
import { nextTick } from "vue";

const title = ref("新建任务");
const open = ref(false);
//当前操作进度
const stepActive = ref(0);
const weekString = ref([]);
const weekList = [
  "星期一",
  "星期二",
  "星期三",
  "星期四",
  "星期五",
  "星期六",
  "星期日",
];

const timeList = ref([
  { code: 0, info: "每天执行" },
  { code: 1, info: "周一至周五执行" },
  { code: 2, info: "自定义时间" },
]);

const sendObjectList = ref([
  { code: "", info: "接通" },
  { code: "", info: "未接通" },
  { code: "", info: "空号" },
  { code: "", info: "停机" },
  { code: "", info: "关机" },
  { code: "", info: "忙线" },
  { code: "", info: "呼叫转移" },
  { code: "", info: "未知" },
]);

const outboundList = ref([{ timeValue: ["09:00", "23:00"] }]);
const sendArr = ref([]);

const taskAllocationTreeList = ref([]);
const emit = defineEmits(["close"]);

const loading = ref(false);
const submitLoading = ref(false);

const uploadFileList = ref([]);

const templateList = ref([]);

const data = reactive({
  form: {
    taskName: undefined,
    fileUrl: undefined,
  },
  configForm: {
    executionTime: undefined,
    executionCallTime: "",
    recallCount: 0,
    recallMinute: 5,
    isScreen: 1,
    singleCallNumber: 1,
    remark: undefined,
  },
  allocationForm: {
    taskAllocationPersonnelId: [],
    answerSettings: 1,
  },
  rules: {
    taskName: [
      { required: true, message: "任务名称不能为空", trigger: "blur" },
    ],
    fileUrl: [{ required: true, message: "请上传名单", trigger: "blur" }],
  },
  configRules: {
    executionTime: [
      { required: true, message: "请选择执行时间", trigger: "blur" },
    ],
    recallCount: [
      { required: true, message: "请选择重呼次数", trigger: "blur" },
    ],
    recallMinute: [
      { required: true, message: "请选择重呼间隔", trigger: "blur" },
    ],
  },
  allocationRules: {
    taskAllocationPersonnelId: [
      { required: true, message: "请选择任务分配", trigger: "change" },
    ],
    answerSettings: [
      { required: true, message: "请选择接听设置", trigger: "change" },
    ],
  },
});

const {
  form,
  rules,
  configForm,
  configRules,
  allocationForm,
  allocationRules,
} = toRefs(data);

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/file/upload",
});
const uploadUrl = computed(() => {
  return upload.url.replace("/dispose", "");
});

//默认时间段
const defaultExecutionCallTime = ref([]);
const checkAll = ref(false);
const isIndeterminate = computed(() => {
  if (sendArr.value.length > 0) {
    if (sendArr.value.length == sendObjectList.value.length) {
      checkAll.value = true;
      return false;
    } else {
      return true;
    }
  } else {
    return false;
  }
});

const changeAiDataT = () => {
  // 获取话术模板
  getAiVoiceTplList().then((res) => {
    templateList.value = res.data;
  });
};

function changeS(value) {
  console.log(value, "value");
}

const changeCheckAll = (value) => {
  if (value) {
    sendArr.value = sendObjectList.value.map((item) => item.info);
  } else {
    sendArr.value = [];
  }
};

const initCount = (currentValue, oldValue) => {
  if (oldValue == 0) {
    configForm.value.recallMinute = 5;
  }
};

const updateExecutionCallTime = (newTime) => {
  // console.log(newTime);
  configForm.value.executionCallTime = newTime;
};

const handleCheckAllChange = (val) => {
  if (val) {
    proxy.$refs.SelectTwentyFourRef.selectAll();
  } else {
    proxy.$refs.SelectTwentyFourRef.clear();
  }
};

const getDeptTreeType = () => {
  function filterDepartments(departments) {
    const result = [];

    for (const dept of departments) {
      // 递归处理子项
      if (dept.children) {
        dept.children = filterDepartments(dept.children);

        // 只保留有有效子项的部门
        if (dept.children.length > 0) {
          result.push(dept);
        }
      }

      // 处理没有子项的情况
      if (!dept.id.includes("Dept:")) {
        result.push(dept);
      }
    }

    return result;
  }
  DeptTreeType().then(async (res) => {
    if (res.code == 200) {
      // console.log(res);
      nextTick(() => {
        const result = filterDepartments(res.data);
        taskAllocationTreeList.value = result;
      });
    } else {
      proxy.$modal.msgWarning(res.msg);
    }
  });
};
getDeptTreeType();

const changeOption = (val) => {
  let checkedMap = proxy.$refs["taskAllocationRef"].getCheckedNodes();
  // console.log(val);
  // console.log(checkedMap);
  // console.log(allocationForm.value.taskAllocationPersonnelId);
};

//打开弹窗
const openDialog = () => {
  open.value = true;
};

const createTaskState = ref(undefined);

const checkForm = () => {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      importDataCustom(form.value)
        .then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess(res.msg);
            form.value.key = res.data.key;
            // 回显
            allocationForm.value.taskAllocationPersonnelId =
              res.data.employeesIdList.map(String);
            stepActive.value += 1;
          } else {
            proxy.$modal.msgWarning(res.msg);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const checkConfigForm = () => {
  proxy.$refs["configFormRef"].validate((valid) => {
    if (valid) {
      nextTick(() => {
        configForm.value.executionTime = String(configForm.value.executionTime);
        if (configForm.value.recallCount == 0) {
          configForm.value.recallMinute = undefined;
        }
        if (!configForm.value.executionCallTime) {
          configForm.value.executionCallTime = "00:00-23:59";
        }
        stepActive.value += 1;
      });
    }
  });
};

const checkAllocationForm = () => {
  proxy.$refs["allocationFormRef"].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      let idList = new Set();

      if (typeof allocationForm.value.taskAllocationPersonnelId == "string") {
        allocationForm.value.taskAllocationPersonnelId =
          allocationForm.value.taskAllocationPersonnelId.split(",");
      }
      allocationForm.value.taskAllocationPersonnelId &&
        allocationForm.value.taskAllocationPersonnelId?.forEach((item) => {
          const sign = "Dept:";
          // console.log(item.indexOf(sign));
          if (Array.isArray(item)) {
            item.forEach((child) => {
              if (child.indexOf(sign) == -1) {
                idList.add(child);
              }
            });
          } else {
            if (item?.indexOf(sign) == -1) {
              idList.add(item);
            }
          }
        });

      let sipList = [];
      // 递归获取
      const recursion = (list, idsArray) => {
        list.forEach((item) => {
          if (idsArray.includes(item.id)) {
            sipList.push(item.sipAccountNumber);
          }
          if (item.children) {
            recursion(item.children, idsArray);
          }
        });
      };
      recursion(taskAllocationTreeList.value, Array.from(idList));

      // allocationForm.value.taskAllocationPersonnelId = Array.from(idList).join(",");
      // allocationForm.value.taskAllocationPersonnelSeating = sipList.join(",");

      const intelligenceTask = { ...configForm.value, ...allocationForm.value };
      intelligenceTask.taskAllocationPersonnelId = Array.from(idList).join(",");
      intelligenceTask.taskAllocationPersonnelSeating = sipList.join(",");
      const req = { ...form.value, intelligenceTask };
      submitTask(req)
        .then((res) => {
          if (res.code == 200) {
            stepActive.value += 1;
            createTaskState.value = res.data;
          }
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
};

const addOutboundList = () => {
  outboundList.value.push({ timeValue: [] });
};

const removeOutboundList = (index) => {
  outboundList.value.splice(index, 1);
};

//校验名单，下一步
const nextstep = () => {
  // console.log(stepActive.value);
  const checkedMap = {
    0: checkForm,
    1: checkConfigForm,
    2: checkAllocationForm,
  };
  checkedMap[stepActive.value]();
};

//取消
function cancel() {
  form.value.taskName = "";
  form.value.fileUrl = "";
  uploadFileList.value = [];
  configForm.value.executionTime = undefined;
  // proxy.$refs.SelectTwentyFourRef.clear();
  configForm.value.executionCallTime = "";
  configForm.value.recallCount = 0;
  configForm.value.recallMinute = 5;
  configForm.value.isScreen = 1;
  configForm.value.singleCallNumber = 1;
  configForm.value.remark = undefined;
  allocationForm.value.taskAllocationPersonnelId = [];
  allocationForm.value.answerSettings = 1;
  outboundList.value = [{ timeValue: ["09:00", "23:00"] }];
  // proxy.$refs["configFormRef"].resetFields();
  // proxy.$refs["allocationFormRef"].resetFields();
  open.value = false;
  stepActive.value = 0;
  loading.value = false;
  submitLoading.value = false;
  emit("close");
}

const downModal = () => {
  proxy.download("callCustom/customImportTemplate", {}, `tpl.xlsx`, {
    gateway: "cis",
  });
};

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    return false;
  }
};

function handleFileEditChange(file, fileList) {
  if (fileList?.length > 1) {
    proxy.$modal.msgWarning("只能上传一个文件");
    fileList.pop();
    return false;
  }
}

/* 案件上传文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  // console.log(fileList);
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.fileUrl = data.url;
    form.value.originalFileName = file.name;
  }
};

/* 案件上传文件移除 */
function handleRemove(file, fileList) {
  form.value.fileUrl = "";
}

const backstep = () => {
  stepActive.value -= 1;
  switch (stepActive.value) {
    case 0:
      uploadFileList.value = form.value.fileUrl
        ? [{ url: form.value.fileUrl, name: form.value.originalFileName }]
        : [];
      break;
    case 1:
      configForm.value.executionTime = configForm.value.executionTime
        .split(",")
        .map(Number);
      // console.log(configForm.value.executionTime);
      break;
  }
};

defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.wcc-el-steps {
  padding-top: 0px;
}

.upload-attachment {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
}

.text-margin {
  margin-left: 20px;
}

.fxn-text-red {
  color: #fb4848;
}

:deep(.el-step__line) {
  left: 65% !important;
  right: -35% !important;
}

:deep(.el-cascader__tags) {
  input {
    margin-left: 10px;
  }
}
</style>