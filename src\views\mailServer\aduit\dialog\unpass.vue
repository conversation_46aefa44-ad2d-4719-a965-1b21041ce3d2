<template>
  <el-dialog :title="title" v-model="open" width="600px" :before-close="cancel" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item label="不通过原因" prop="refuseReason">
        <el-input type="textarea" v-model="form.refuseReason" placeholder="请输入不通过原因" maxlength="300" rows="4"
          show-word-limit></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="Unpass">
import { messageNotPass, messageItemNotPass } from "@/api/lawyer/aduit";
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
const title = ref('审核不通过');
const emit = defineEmits(["getList"]);
const typeInfo = ref([messageNotPass, messageItemNotPass]);
const type = ref(0);
//提交数据
const data = reactive({
  form: {
    ids: [],
    refuseReason: undefined,
  },
  rules: {
    refuseReason: [{ required: true, message: "请输入申请原因", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
async function opendialog(row, item) {
  open.value = true;
  form.value.ids = row;
  if (item) {
    type.value = 1;
  } else {
    type.value = 0;
  }
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = JSON.parse(JSON.stringify(form.value));
      typeInfo.value[type.value](req)
        .then((res) => {
          proxy.$modal.msgSuccess(res.msg);
          cancel();
          emit("getList");
          loading.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    ids: [],
    refuseReason: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog
})
</script>
<style scoped></style>
