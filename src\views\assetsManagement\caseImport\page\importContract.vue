<template>
  <div class="app-container">
    <el-row class="mb8">
      <el-button 
        type="primary"
        plain
        :disabled="single"
        @click="downFile(false)"
      >下载</el-button>
      <el-button 
        type="primary" 
        :disabled="single || loading"
        plain
        @click="remove(false)"
      >删除</el-button>
        
      <div class="top-right-btn">
        <el-button @click="toBack">返回</el-button>
      </div>
    </el-row>
    <el-table 
      v-loading="loading"
      :data="dataList"
      ref="multipleTableRef"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="50"
        align="center"
      />
      <el-table-column
        label="附件名称"
        align="center"
        prop="fileName"
        show-overflow-tooltip
      />
      <el-table-column
        label="附件格式"
        align="center"
        prop="fileFormat"
        show-overflow-tooltip
      />
      <el-table-column
        label="文件大小"
        align="center"
        prop="fileSizeInfo"
        show-overflow-tooltip
      />
      <el-table-column
        label="上传时间"
        align="center"
        prop="createTime"
        show-overflow-tooltip
      />
      <el-table-column
        label="上传人员"
        align="center"
        prop="createBy"
        show-overflow-tooltip
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        show-overflow-tooltip
      />
      <el-table-column
        label="操作"
        :min-width="126"
      >
        <template #default="{ row }">
          <el-button type="primary" v-if="checkFile(row.fileFormat)" link @click="review(row.fileUrl)">预览</el-button>
          <el-button type="primary" link @click="downFile(true, row)">下载</el-button>
          <el-button type="primary" link @click="remove(true, row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Contract">
import { contractList, contractDel } from '@/api/assets/asset/contract'

const { proxy } = getCurrentInstance();
const route = useRoute();
const loading = ref(false)
const total = ref(0);
const dataList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  assetManageId: route.params.assetId
})
const single = ref(true);
const selectedArr = ref([]);
const ids = ref([])

function getList() {
  loading.value = true;
  contractList(queryParams.value).then(res => {
    total.value = res.total;
    dataList.value = res.rows;
  }).finally(() => {
    loading.value = false;
  })
}
getList()

//删除
function remove(type,id) {
  const form = type?[id]:ids.value
  proxy.$modal.confirm("本次操作将会删除该合同文件，是否继续？").then(()=> {
    loading.value = true;
    return contractDel(form);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功！");
    getList();
  }).catch((err) =>{
    loading.value = false;
  });
}


//选择列表
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  ids.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
}

//预览
function review(url) {
  window.open(url)
}

//下载文件
function downFile(type,row) {
  let fileArr = [];
  if (type) {
    fileArr = [row]
  } else {
    fileArr = selectedArr.value
  }
  fileArr.map(item => {
    let name = '';
    name = `${item.fileName}.${item.fileFormat}`
    proxy.download(
      "caseManage/asset/contract/download",
      { id: item.id },
      name
    );
  })
    
  
}

//文件类型检查
function checkFile(fileFormat) {
  if (/(xls|XLS|xlsx|XLSX|doc|DOC|docx|DOCX)$/.test(fileFormat)) {
    return false
  } else {
     return true
  }
}

//返回
const toBack = () => {
  const obj = { path: "/assets/asset" };
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style scoped>
   
</style>