<template>
    <div>
        <el-form-item prop="courtId" label="立案法院">
            <el-select v-model="form.courtId" style="width:320px" filterable clearable placeholder="请选择法院">
                <el-option v-for="(item, index) in courtOption" :key="index" :label="item.info" :value="item.code" />
            </el-select>
        </el-form-item>
        <el-form-item prop="filingNumber" label="立案号">
            <el-input v-model="form.filingNumber" style="width:320px" :maxlength="100" placeholder="请输入立案号" />
        </el-form-item>
        <el-form-item prop="filingTime" label="立案时间">
            <el-date-picker v-model="form.filingTime" style="width:320px" value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime" placeholder="请选择立案时间" />
        </el-form-item>
        <el-form-item prop="contractor" label="承办员">
            <el-input v-model="form.contractor" style="width:320px" :maxlength="20" placeholder="请输入承办员" />
        </el-form-item>
        <el-form-item prop="clerk" label="书记员">
            <el-input v-model="form.clerk" style="width:320px" :maxlength="20" placeholder="请输入书记员" />
        </el-form-item>
    </div>
</template>
<script setup>
const props = defineProps({
    form: { type: Object, default: {} },
    rules: { type: Object, default: {} },
    courtOption: { type: Array, default: [] }
})
</script>