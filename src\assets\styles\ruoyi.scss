 /**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */

 /** 基础通用 **/
 .pt5 {
 	padding-top: 5px;
 }

 .pt8 {
 	padding-top: 8px;
 }

 .pt10 {
 	padding-top: 10px;
 }

 .pt20 {
 	padding-top: 20px;
 }

 .pr5 {
 	padding-right: 5px;
 }

 .pr10 {
 	padding-right: 10px;
 }

 .pr20 {
 	padding-right: 20px;
 }

 .pb5 {
 	padding-bottom: 5px;
 }

 .pb10 {
 	padding-bottom: 10px;
 }

 .pb20 {
 	padding-bottom: 20px;
 }

 .pb15 {
 	padding-bottom: 15px;
 }

 .pd20 {
 	padding: 20px
 }

 .pl20 {
 	padding-left: 20px;
 }

 .mb5 {
 	margin-bottom: 5px;
 }

 .mb8 {
 	margin-bottom: 8px;
 }

 .mb10 {
 	margin-bottom: 10px;
 }

 .mb20 {
 	margin-bottom: 20px !important;
 }

 .mb30 {
 	margin-bottom: 30px;
 }

 .mb40 {
 	margin-bottom: 40px;
 }

 .mr5 {
 	margin-right: 5px;
 }

 .mr10 {
 	margin-right: 10px !important;
 }

 .mr15 {
 	margin-right: 15px !important;
 }

 .mr20 {
 	margin-right: 20px !important;
 }

 .mt3 {
 	margin-top: 3px;
 }

 .mt5 {
 	margin-top: 5px;
 }

 .mt10 {
 	margin-top: 10px;
 }

 .mt12 {
 	margin-top: 12px;
 }

 .mt20 {
 	margin-top: 20px;
 }

 .mt30 {
 	margin-top: 30px;
 }

 .ml5 {
 	margin-left: 5px !important;
 }

 .ml10 {
 	margin-left: 10px !important;
 }

 .ml20 {
 	margin-left: 20px;
 }

 .h1,
 .h2,
 .h3,
 .h4,
 .h5,
 .h6,
 h1,
 h2,
 h3,
 h4,
 h5,
 h6 {
 	font-family: inherit;
 	font-weight: 500;
 	line-height: 1.1;
 	color: inherit;
 }

 .el-dialog:not(.is-fullscreen) {
 	margin-top: 6vh !important;
 }

 .el-dialog.scrollbar .el-dialog__body {
 	overflow: auto;
 	overflow-x: hidden;
 	max-height: 70vh;
 	padding: 10px 20px 0;
 }

 .el-table {

 	.el-table__header-wrapper,
 	.el-table__fixed-header-wrapper {
 		th {
 			word-break: break-word;
 			background-color: #f8f8f9 !important;
 			color: #515a6e;
 			height: 40px !important;
 			font-size: 13px;
 		}
 	}

 	.el-table__body-wrapper {
 		.el-button [class*="el-icon-"]+span {
 			margin-left: 1px;
 		}
 	}
 }

 /** 表单布局 **/
 .form-header {
 	font-size: 15px;
 	color: #6379bb;
 	border-bottom: 1px solid #ddd;
 	margin: 8px 10px 25px 10px;
 	padding-bottom: 5px
 }

 /** 表格布局 **/
 .pagination-container {
 	// position: relative;
 	height: 25px;
 	margin-bottom: 10px;
 	margin-top: 15px;
 	// padding: 10px 20px !important;
 }

 /* tree border */
 .tree-border {
 	margin-top: 5px;
 	border: 1px solid #e5e6e7;
 	background: #FFFFFF none;
 	border-radius: 4px;
 	width: 100%;
 }

 .message-badge {
 	.el-badge__content {
 		top: 10px !important;
 		right: 20px !important;
 	}
 }


 .pagination-container .el-pagination {
 	right: 0;
 	position: absolute;
 }

 @media (max-width : 768px) {
 	.pagination-container .el-pagination>.el-pagination__jump {
 		display: none !important;
 	}

 	.pagination-container .el-pagination>.el-pagination__sizes {
 		display: none !important;
 	}
 }

 .el-table .fixed-width .el-button--small {
 	padding-left: 0;
 	padding-right: 0;
 	width: inherit;
 }

 /** 表格更多操作下拉样式 */
 .el-table .el-dropdown-link {
 	cursor: pointer;
 	color: #409EFF;
 	margin-left: 10px;
 }

 .el-table .el-dropdown,
 .el-icon-arrow-down {
 	font-size: 12px;
 }

 .el-tree-node__content>.el-checkbox {
 	margin-right: 8px;
 }

 .list-group-striped>.list-group-item {
 	border-left: 0;
 	border-right: 0;
 	border-radius: 0;
 	padding-left: 0;
 	padding-right: 0;
 }

 .list-group {
 	padding-left: 0px;
 	list-style: none;
 }

 .list-group-item {
 	border-bottom: 1px solid #e7eaec;
 	border-top: 1px solid #e7eaec;
 	margin-bottom: -1px;
 	padding: 11px 0px;
 	font-size: 13px;
 }

 .pull-right {
 	float: right !important;
 }

 .el-card__header {
 	padding: 14px 15px 7px !important;
 	min-height: 40px;
 }

 .el-card__body {
 	padding: 15px 20px 20px 20px !important;
 }

 .card-box {
 	padding-right: 15px;
 	padding-left: 15px;
 	margin-bottom: 10px;
 }

 /* button color */
 .el-button--cyan.is-active,
 .el-button--cyan:active {
 	background: #20B2AA;
 	border-color: #20B2AA;
 	color: #FFFFFF;
 }

 .el-button--cyan:focus,
 .el-button--cyan:hover {
 	background: #48D1CC;
 	border-color: #48D1CC;
 	color: #FFFFFF;
 }

 .el-button--cyan {
 	background-color: #20B2AA;
 	border-color: #20B2AA;
 	color: #FFFFFF;
 }

 /* text color */
 .text-navy {
 	color: #1ab394;
 }

 .text-primary {
 	color: #409eff;
 }

 .text-success {
 	color: #1c84c6;
 }

 .text-info {
 	color: #23c6c8;
 }

 .text-warning {
 	color: #f8ac59;
 }

 .text-danger {
 	color: #ed5565;
 }

 .text-muted {
 	color: #888888;
 }

 /* image */
 .img-circle {
 	border-radius: 50%;
 }

 .img-lg {
 	width: 120px;
 	height: 120px;
 }

 .avatar-upload-preview {
 	position: absolute;
 	top: 50%;
 	transform: translate(50%, -50%);
 	width: 200px;
 	height: 200px;
 	border-radius: 50%;
 	box-shadow: 0 0 4px #ccc;
 	overflow: hidden;
 }

 /* 拖拽列样式 */
 .sortable-ghost {
 	opacity: .8;
 	color: #fff !important;
 	background: #42b983 !important;
 }

 .top-right-btn {
 	position: absolute;
 	right: 0;
 }

 /* 信息提示框 */
 .info-tip {
 	display: flex;
 	padding: 10px 16px;
 	color: #409eff;
 	font-size: 14px;
 	line-height: 24px;
 	background-color: #e5f0ff;
 	border-radius: var(--el-border-radius-base);
 	border: solid 1px #97c7ff;
 	font-weight: 450;
 	overflow: hidden;
 }

 .info-tip-icon {
 	top: 4px;
 	margin-right: 5px;
 }

 .info-tip p {
 	margin: 0;
 }

 /* 自定义表格 */
 .my-table {
 	width: 100%;
 	line-height: 24px;
 	border-top: 1px solid #E8e8e8;
 	border-left: 1px solid #E8e8e8;
 	border-collapse: collapse;
 	border-spacing: 0;

 	td>p {
 		margin: 0;
 	}

 	tbody td {
 		font-size: 14px;
 	}
 }

 .my-table>thead {
 	background: #eceef4;
 	text-align: center;
 	height: 50px;
 }

 .my-table td {
 	padding: 8px 20px;
 	border-right: 1px solid #E8e8e8;
 	border-bottom: 1px solid #E8e8e8;
 }

 .caseinfo-wrap {
 	box-shadow: 0px 2px 4px rgba(171, 186, 223, 0.2);
 	border-radius: 2px;
 	margin-bottom: 20px;
 	border: 1px solid rgba(171, 186, 223, 0.1);
 }

 .flex-wrap {
 	display: flex;

 	.flex-wrap-item {
 		flex: 1;
 	}
 }

 .el-row.h32 {
 	height: 32px !important;
 }


 //合规宣导弹窗样式
 .el-overlay-dialog {
 	display: flex;
 	align-items: center;
 	justify-content: center;

 	.el-dialog.pro-dialog {
 		margin: 0 !important;
 		height: 90%;
 		overflow: hidden;

 		.el-dialog__body {
 			height: calc(100% - 118px);
 			overflow: hidden;
 			overflow-y: auto;
 		}
 	}
 }


 input::-webkit-outer-spin-button,
 input::-webkit-inner-spin-button {
 	-webkit-appearance: none;
 }

 input[type="number"] {
 	-moz-appearance: textfield;
 }

 .operation-revealing-area {
 	min-height: 32px;
 	position: relative;
 	margin-top: 20px;

 	.top-right-btn {
 		top: 0;
 	}
 }

 .form-h50 {
 	height: 50px;
 	overflow: hidden;
 }

 .form-auto {
 	height: auto !important;
 }

 .bind-workPhone-dialog {
	.el-dialog__body {
	  padding-top: 10px;
	}
 }