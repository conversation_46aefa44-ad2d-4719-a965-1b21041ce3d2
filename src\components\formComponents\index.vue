<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formNewRef" label-width="160px">
      <el-form-item v-for="(item, index) in formOption" :key="index" :label="item.fieldName"
        :prop="item.sysDictType?.receiveParam || `filed${item.id}`">
        <!-- 文本框 -->
        <el-input v-if="item.sysDictType.fieldType == '填写项'" style="width:360px"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]" :placeholder="`请输入${item.fieldName}`" />
        <!-- 单选项 -->
        <el-select v-if="item.sysDictType.fieldType == '下拉框(单选)'"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]" clearable style="width:360px"
          :placeholder="`请选择${item.fieldName}`">
          <el-option v-for="(v, h) in item.sysDictType?.sysDictDataList" :key="h" :label="v.dictLabel"
            :value="v.dictLabel" />
        </el-select>
        <!-- 文件项 -->
        <el-upload v-if="item.sysDictType.fieldType == '上传文件'"
          :ref="`uploadRef-${item.sysDictType?.receiveParam || `filed${item.id}`}-${item.id}`" :limit="1"
          accept=".jpg,.png,.pdf" :headers="upload.headers" :action="upload.url"
          :file-list="fileList[item.sysDictType?.receiveParam || `filed${item.id}`]" :key="item.id"
          :before-upload="handleFileUploadBefore" :on-change="handleEditChange" :before-remove="handleRemove"
          :on-success="(response, file, fileList) => handleFileSuccess(response, file, fileList, item.sysDictType?.receiveParam || `filed${item.id}`)">
          <template #trigger>
            <el-button class="mr10" type="text">本地上传文件</el-button>
          </template>
          <el-button class="ml20" type="text"
            :disabled="!form[`${item.sysDictType?.receiveParam || `filed${item.id}`}Url`]"
            @click="handlePreview(form[`${item.sysDictType?.receiveParam || `filed${item.id}`}Url`])">预览</el-button>
          <div><el-button type="text">(仅支持上传jpg/png/pdf格式)</el-button></div>
        </el-upload>
        <!-- 多选 -->
        <el-select v-if="item.sysDictType.fieldType == '下拉框(多选)'"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]" clearable filterable
          :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip style="width:360px"
          :placeholder="`请选择${item.fieldName}`">
          <el-option v-for="(v, h) in item.sysDictType?.sysDictDataList" :key="h" :label="v.dictLabel"
            :value="v.dictLabel" />
        </el-select>
        <el-radio-group v-if="item.sysDictType.fieldType == '单选按钮'"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]">
          <el-radio v-for="(v, h) in item.sysDictType?.sysDictDataList" :key="h" :label="v.dictLabel"
            :value="v.dictValue">
            {{ v.dictLabel }}
          </el-radio>
        </el-radio-group>
        <el-checkbox-group v-if="item.sysDictType.fieldType == '多选按钮'"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]">
          <el-checkbox v-for="(v, h) in item.sysDictType.sysDictDataList" :key="h" :label="v.dictLabel"
            :value="v.dictValue">
            {{ v.dictLabel }}
          </el-checkbox>
        </el-checkbox-group>
        <el-date-picker v-if="item.sysDictType.fieldType == '选择日期'" style="width:360px"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]" type="date"
          :placeholder="`请选择${item.fieldName}`" value-format="YYYY-MM-DD" />
        <el-date-picker v-if="item.sysDictType.fieldType == '选择时间'" style="width:360px" value-format="YYYY-MM-DD hh:mm:ss"
          v-model="form[item.sysDictType?.receiveParam || `filed${item.id}`]" :placeholder="`请选择${item.fieldName}`" />
      </el-form-item>
    </el-form>
    <previewFile ref="previewFileRef" />
  </div>
</template>

<script setup name="FormComponents">
import previewFile from '../previewFile';
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance();
const emit = defineEmits();
//下载
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/message/upload",
});
const fileList = ref({});
const props = defineProps({
  formOption: {
    type: Array,
    default: [],
  },
  worksheetForm: {
    type: Object,
    default: {}
  },
  contactMedium: { type: [String, Number] }
});
const data = reactive({
  form: {},
  rules: {}
})
const { form, rules } = toRefs(data);
watch(() => props.contactMedium, () => {
  nextTick(() => {
    form.value = {}
  })
}, { deep: true })
//返回标签宽度
function returnLabelWidth() {
  //设定最小的宽度
  let labelCount = 8;
  props.formOption?.forEach((item) => {
    if (item.dictName?.length > labelCount) {
      labelCount = item.dictName?.length;
    }
  })
  return `${labelCount * 20}px`;
}

//设置表单
function resetNewForm() {
  props.formOption?.forEach((item) => {
    if (["多选"].includes(item.sysDictType?.fieldType)) {
      form.value[item.sysDictType?.receiveParam || `filed${item.id}`] = []
    } else {
      form.value[item.sysDictType?.receiveParam || `filed${item.id}`] = undefined
    }
  })
  proxy.resetForm("formNewRef");
}

// 设置规则
function setRules() {
  props.formOption?.forEach((item) => {
    let rulesArr = [];
    if (item.sysDictType.fieldType == '填写项') {
      let rulesArrOption = {
        required: item.sysDictType.isDefault == "Y",
        pattern: /^\S*$/,
        message: "请不要输入空格",
        trigger: "blur",
      }
      rulesArr.push(rulesArrOption)
    }
    //设置默认规则
    rulesArr.push([{ required: item.sysDictType.isDefault == "Y", trigger: "blur", message: `请输入${item.fieldName}` }])
    rules.value[item.sysDictType?.receiveParam || `filed${item.id}`] = rulesArr;
  })
}

//设置fileList
function setFileList() {
  props.formOption?.forEach((item) => {
    if (item.sysDictType.fieldType == '上传文件') {
      fileList.value[item.sysDictType?.receiveParam || `filed${item.id}`] = []
    }
  })
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 100 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过100M!");
    return false;
  }
  const index = file.name.lastIndexOf('.')
  const fileType = file.name.slice(index, file.name.length)
  if (!['.pdf', '.jpg', '.png'].includes(fileType)) {
    proxy.$modal.msgWarning("请上传jpg/png/pdf格式!");
    fileList.pop();
    return false
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 100 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过100M!");
    fileList.pop();
    return false;
  }
  const index = file.name.lastIndexOf('.')
  const fileType = file.name.slice(index, file.name.length)
  if (!['.pdf', '.jpg', '.png'].includes(fileType)) {
    proxy.$modal.msgWarning("请上传jpg/png/pdf格式!");
    fileList.pop();
    return false
  }
}

//上传文件到服务器
function submitFile(dom) {
  proxy.$refs[dom][0].submit();
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList, receiveValue) => {
  const { code, data, msg } = response;
  if (code === 9000) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    return false;
  } else if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.originalFilename}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value[`${receiveValue}Url`] = data.url;
    form.value[receiveValue] = data.originalFilename;
  }
};

onUpdated(() => {
  setFileList()
  // setRules()
  resetNewForm()
})

//提交参数
function submitForm() {
  proxy.$refs["formNewRef"].validate((valid) => {
    if (valid) {
      let saveContent = [];
      let formatForm = JSON.parse(JSON.stringify(form.value))
      props.formOption?.forEach((item) => {
        if (formatForm[item.sysDictType?.receiveParam || `filed${item.id}`]) {
          let saveObj = {
            fieldName: item.fieldName,
            fieldType: item.sysDictType.fieldType,
            receiveParam: item.sysDictType?.receiveParam || `filed${item.id}`,
            labelName: formatForm[item.sysDictType?.receiveParam || `filed${item.id}`]
          }
          if (item.sysDictType?.receiveParam || `filed${item.id}` && formatForm[`${item.sysDictType?.receiveParam || `filed${item.id}`}Url`]) {
            saveObj.fileUrl = formatForm[`${item.sysDictType?.receiveParam || `filed${item.id}`}Url`]
          }
          if (["多选"].includes(item.sysDictType?.fieldType)) {
            formatForm[item.sysDictType?.receiveParam || `filed${item.id}`] = formatForm?.[item.sysDictType?.receiveParam || `filed${item.id}`]?.toString()
          }
          saveObj[item.sysDictType?.receiveParam || `filed${item.id}`] = formatForm[item.sysDictType?.receiveParam || `filed${item.id}`];
          saveContent.push(JSON.parse(JSON.stringify(saveObj)))
        }
      })
      emit('update:worksheetForm', { objContent: saveContent })
    }
  });
}

// 预览
function handlePreview(url) {
  window.open(url)
}

defineExpose({
  submitForm,
  resetNewForm
});
</script>

<style lang="scss" scoped>
.ml20 {
  margin-left: 20px;
}
</style>