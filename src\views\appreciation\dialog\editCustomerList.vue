<template>
    <!-- 审核不通过 -->
    <el-dialog title="编辑" v-model="open" width="600px" append-to-body :before-close="cancel"
      :close-on-click-modal="false">
      <el-form :model="form" ref="formRef">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" maxlength="500" show-word-limit rows="4"
            placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button class="submitBtn" type="primary" :loading="loading" @click="submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  <script setup>
import { updateCustomDetails } from "@/api/appreciation/customerList";
  const emits = defineEmits(["editFinish"]);
  //全局配置
  const { proxy } = getCurrentInstance();
  const loading = ref(false);
  const open = ref(false);
  //提交数据
  const data = reactive({
    form: {
      id: undefined,
      remark: undefined,
    },
  });
  const { form } = toRefs(data);
  
  //打开窗口
  function openDialog(row) {
    console.log(row.id);
    form.value.id = row.id;
    form.value.remark = row.remark;
    open.value = true;
  }
  
  //提交
  function submit() {
    loading.value = true;
    nextTick(() => {
      updateCustomDetails(form.value).then((res) => {
        console.log(res);
        if (res.code == 200) {
          proxy.$modal.msgSuccess(res.msg);
        }
        loading.value = false;
        reset();
        open.value = false;
      }).finally(() => {
        loading.value = false;
        emits('editFinish')
      });
    })
  }
  
  //重置
  function reset() {
    proxy.resetForm("formRef");
    form.value = {
        remark: undefined,
    };
  }
  
  //取消
  function cancel() {
    if (loading.value) return false;
    reset();
    open.value = false;
  }
  
  defineExpose({
    openDialog,
    cancel,
  });
  </script>
  <style lang="scss" scoped>
  :deep(.el-textarea),
  .dialog-footer {
    margin-right: 20px;
  }
  </style>