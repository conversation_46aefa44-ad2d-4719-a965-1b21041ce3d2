<template>
    <div class="preview-area">
        <div class="left">
            <el-button type="text" :loading="loading" :disabled="writShowSetp == 0" size="large" @click="handleLeft"
                link icon="ArrowLeftBold" />
        </div>
        <div class="content">
            <template v-for="(item, index) in preivewArr" :key="item.fileUrl">
                <previewPdf ref="previewPdfRef" class="preview-pdf" v-show="index == writShowSetp"
                    :pdfSrc="item.fileUrl" :total="item.count" />
            </template>
        </div>
        <div class="right">
            <el-button type="text" :loading="loading" :disabled="writShowSetp == preivewArr.length - 1" size="large"
                @click="handleRight" link icon="ArrowRightBold" />
        </div>
    </div>
</template>
<script setup>
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
const props = defineProps({
    form: { type: Object, default: {} },
    loading: { type: Boolean, default: false },
    preivewArr: { type: Array, default: [] }
})
const writShowSetp = ref(0)
function handleLeft() {
    if (writShowSetp.value > 0) {
        nextTick(() => {
            writShowSetp.value--
        })
    }
}

function handleRight() {
    if (writShowSetp.value < props.preivewArr?.length - 1) {
        nextTick(() => {
            writShowSetp.value++
        })
    }
}
</script>
<style lang="scss" scoped>
.preview-area {
    width: 850px;
    display: flex;
    align-items: center;

    .content {
        width: 794px;
        height: 50vh;
        overflow-y: auto;
        border: 1px solid #ccc;

        .writ-content {
            min-height: 1124px;
            border: 1px solid transparent;
        }
    }

    .left,
    .right {
        flex: 1;

        .el-button {
            :deep(.el-icon) {
                font-size: 35px;
            }
        }
    }
}
</style>

<style lang="scss" scoped>
.preview-area {
    .preview-pdf {
        canvas {
            height: auto !important;
            width: 792px !important;
        }
    }
}
</style>