<template>
  <div class="pdf">
    <div v-for="v in props.total || 1" :key="v">
      <VuePdfEmbed :source="props.pdfSrc || ''" style="font-weight: 700" :page="props.total || 1" />
    </div>
  </div>
</template>
<script setup>
import VuePdfEmbed from "vue-pdf-embed";

const { proxy } = getCurrentInstance();
const props = defineProps({
  pdfSrc: {
    type: String,
    default: "",
  },
  total: {
    type: Number,
    default: 1,
  },
});

const state = reactive({
  source: props.pdfSrc,
  pageNum: 1,
  scale: 1.5, // 缩放比例
  numPages: props.total || 1, // 总页数
});

function scaleFun(index) {
  // 缩放
  let scale = state.value.scale;
  return `transform:scale(${scale})`;
}

function getTotalList() {
  let totalList = [];
  let num = state.numPages;
  for (var i = 1; i <= num; i++) {
    totalList.push(i);
  }
  return totalList;
}

function zoomA() {
  state.value.scale += 0.1;
}

function zoomB() {
  state.value.scale -= 0.1;
}
</script>
<style lang="scss" scoped>
.textLayer {
  span {
    font-weight: 700;
  }
}
</style>