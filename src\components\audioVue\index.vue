<template>
  <audio id="myVideo" controls :src="src" autoplay></audio>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
  src: {
    type: String,
    default: ''
  }
})
onMounted(() => {
  var vid = document.getElementById("myVideo")
  vid.onerror = function() {
    if (props.src != '') {
      proxy.$modal.msgError("播放失败，请检查网络或者联系管理人员！")
    }
  }
})
</script>

<style scoped>
   
</style>