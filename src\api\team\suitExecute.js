import request from '@/utils/request'
//获取机构树结构
export function getDeptTreeWithDisposeStage(params) {
  return request({
    url: '/team/DeptTreeWithExecute',
    method: 'get',
    params
  })
}

// 根据条件查询列表信息
export function selectExecuteList(query) {
  return request({
    url: '/team/selectExecuteList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectExecuteMoney(data) {
  return request({
    url: '/team/selectExecuteMoney',
    method: 'post',
    data: data,
  })
}
