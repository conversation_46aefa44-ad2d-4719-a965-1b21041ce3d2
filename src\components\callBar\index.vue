<template>
  <div class="call-warp" v-if="showCallBtn">
    <div
      class="phone-call-warp"
      v-if="btnType === 0 && isRegistered && phoneState == 0"
      :key="isRegistered"
      :class="{
        'no-click':
          currentCallShowId &&
          currentCallShowId != callshowId &&
          callState != 0,
      }"
      @click="iscurrentShowFoot"
    >
      <div
        style="display: inline-block"
        v-if="currentCallShowId !== callshowId"
      >
        <svg-icon icon-class="phone-call" :style="'font-size:' + size + 'px'" />
        <span
          v-if="typeIndex"
          class="number"
          :style="'font-size:' + size / 2 + 'px'"
          >{{ typeIndex }}</span
        >
      </div>
      <div style="display: inline-block" v-else>
        <svg-icon
          icon-class="phone-end"
          :style="'font-size:' + size + 'px;' + 'color:#F56C6C'"
        />
      </div>
      <!-- 呼叫图标 -->
    </div>
    <div
      class="phone-call-warp no-click"
      v-if="btnType === 0 && isRegistered && phoneState == 1"
      :key="isRegistered"
    >
      <div style="display: inline-block">
        <svg-icon
          icon-class="phone-call"
          :style="'font-size:' + size + 'px' + 'color:#dedede'"
        />
      </div>
    </div>
    <!-- <div v-show="btnType === 1"  class="fixed-warp" ref="phonebtnRef">
      <el-button
        type="success"
        :disabled="currentCallShowId && currentCallShowId !== callshowId"
        @click="iscurrentShowFoot('mdf')"
        >拨号</el-button
      >
    </div> -->
    <!-- 右侧悬浮 -->

    <div class="phone-bottom-warp" v-if="false" :class="{ hideMenu: !opened }">
      <div>
        <el-input
          v-model="inputPhone"
          :disabled="
            contactId !== false ||
            caseId !== false ||
            workOrderId !== false ||
            callState != 0
          "
          style="width: 240px"
          placeholder="请输入拨打号码"
        />
        <el-button
          class="mybtn ml10"
          type="success"
          :disabled="
            contactId !== false ||
            caseId !== false ||
            workOrderId !== false ||
            callState != 0
          "
          @click="phoneCall"
        >
          <svg-icon
            icon-class="phonecallbtn"
            style="font-size: 14px; color: #ffffff"
          />
        </el-button>
      </div>
      <div class="state-warp">
        <div class="head-icon">
          <div class="icon-backg">
            <svg-icon
              icon-class="mine"
              style="font-size: 16px; color: #b9c0d3"
            />
          </div>
        </div>
        <div
          v-if="callingPhone"
          class="ml10"
          style="color: #3f3f3f; font-size: 14px"
        >
          {{ callingPhone }}
        </div>
        <div class="state ml20 text-primary" v-if="callState != 0">
          <span v-if="callState == 1">正在呼叫...</span>
          <span v-else-if="callState == 2">{{ callTimeFormat(callTime) }}</span>
          繁忙中/通话中
        </div>
      </div>
      <div class="btn-warp" v-if="callState != 0">
        <el-button type="danger" plain :loading="hanguploading" @click="hangUp">
          <div class="hang-up mr5">
            <svg-icon
              icon-class="phone-end"
              style="font-size: 12px; color: #ffffff"
            />
          </div>
          {{ hanguploading ? "挂机中..." : "挂机" }}
        </el-button>
      </div>
    </div>
    <!-- 底部悬浮 -->
  </div>
</template>

<script setup>
import { getUuid, loadJs } from "@/utils/ruoyi";
import {
  callOutByContact,
  callOutByCase,
  callOutByPhoneNumber,
  callOutByWorkOrderPhoneNumber,
  callOutHangup,
} from "@/api/call";
const { proxy } = getCurrentInstance();

const props = defineProps({
  workOrderId: {
    //联系人id
    type: [String, Number, Boolean],
    default: false,
  },
  contactId: {
    //联系人id
    type: [String, Number, Boolean],
    default: false,
  },
  caseId: {
    //案件id
    type: [String, Number, Boolean],
    default: false,
  },
  phoneNumber: {
    //电话号码
    type: [String, Number, Boolean],
    default: false,
  },
  size: {
    //图标字体大小
    type: Number,
    default: 16,
  },
  btnType: {
    //按钮类型：0:电话图标，1: 右侧悬浮按钮
    type: Number,
    default: 0,
  },
  odvId: {
    type: [String, Number, Boolean],
    default: false,
  },
  phoneState: {
    type: [String, Number],
    default: 0,
  },
  clickCallOut: {
    type: Boolean,
    default: false,
  },
  customRelay: { // 自定义透传
    type: Object,
    default: {}
  }
});

const store = useStore();
//菜单收缩
const opened = computed(() => store.state.app.sidebar.opened);
//当前组件标识
const callshowId = ref(null);
//通话状态
const callState = computed(() => store.getters.callState);
//Mrtc通话状态
const iscalling = computed(() => store.getters.iscalling);
//回拨状态
const isincall = computed(() => store.getters.isincall);
//登录状态
const isRegistered = computed(() => store.getters.isRegistered);
//通话号码
const callingPhone = computed(() => store.getters.callingPhone);
//通话时间
const callTime = computed(() => store.getters.callTime);
//是否显示呼叫按钮
const showCallBtn = inject("showCallBtn");
//当前显示的底部呼叫组件id
const currentCallShowId = computed(() => store.getters.currentCallShowId);
//上一个显示的呼叫组件标识
const olduuid = ref(null);
//是否开始计时
const istime = ref(false);

const dragDiv = ref(null);

watch(iscalling, (newval, oldval) => {
  // if (newval == 2 && !istime.value) {
  //   istime.value = true
  //   store.dispatch('call/startTime')
  // }
  if (newval == false && oldval != false) {
    inputPhone.value = undefined;
    store.commit("call/SET_CALL_STATE", 0);
    store.commit("call/SET_CALLING_PHONE", undefined);
    if (currentCallShowId.value === callshowId.value && props.btnType != 1) {
      store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
    }
  }
});

watch(isincall, (newval, oldval) => {
  // if (newval == 2 && !istime.value) {
  //   istime.value = true
  //   store.dispatch('call/startTime')
  // }
  if (newval == false && oldval != false) {
    inputPhone.value = undefined;
    store.commit("call/SET_CALL_STATE", 0);
    store.commit("call/SET_CALLING_PHONE", undefined);
    if (currentCallShowId.value === callshowId.value && props.btnType != 1) {
      store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
    }
  }
});

// onMounted(() => {
//   if (props.btnType === 1) {
//     if (!dragDiv.value) {
//       // 获取DOM元素
//       let dragDiv = proxy.$refs["phonebtnRef"];
//       dragDiv.value = dragDiv;
//       // 缓存 clientX clientY 的对象: 用于判断是点击事件还是移动事件
//       let clientOffset = {};
//       // 绑定鼠标按下事件
//       dragDiv.addEventListener(
//         "mousedown",
//         (event) => {
//           let offsetX = dragDiv.getBoundingClientRect().left; // 获取当前的x轴距离
//           let offsetY = dragDiv.getBoundingClientRect().top; // 获取当前的y轴距离
//           let innerX = event.clientX - offsetX; // 获取鼠标在方块内的x轴距
//           let innerY = event.clientY - offsetY; // 获取鼠标在方块内的y轴距
//           // 缓存 clientX clientY
//           clientOffset.clientX = event.clientX;
//           clientOffset.clientY = event.clientY;
//           // 鼠标移动的时候不停的修改div的left和top值
//           document.onmousemove = function (event) {
//             dragDiv.style.left = event.clientX - innerX + "px";
//             dragDiv.style.top = event.clientY - innerY + "px";
//             // dragDiv 距离顶部的距离
//             let dragDivTop = window.innerHeight - dragDiv.getBoundingClientRect().height;
//             // dragDiv 距离左部的距离
//             let dragDivLeft = window.innerWidth - dragDiv.getBoundingClientRect().width;
//             // 边界判断处理
//             // 1、设置左右不能动
//             dragDiv.style.left = dragDivLeft - 6 + "px";
//             // 2、超出顶部处理
//             if (dragDiv.getBoundingClientRect().top <= 0) {
//               dragDiv.style.top = "0px";
//             }
//             // 3、超出底部处理
//             if (dragDiv.getBoundingClientRect().top >= dragDivTop) {
//               dragDiv.style.top = dragDivTop + "px";
//             }
//           };
//           // 鼠标抬起时，清除绑定在文档上的mousemove和mouseup事件；否则鼠标抬起后还可以继续拖拽方块
//           document.onmouseup = function () {
//             document.onmousemove = null;
//             document.onmouseup = null;
//           };
//         },
//         false
//       );

//       // 绑定鼠标松开事件
//       dragDiv.addEventListener("mouseup", (event) => {
//         let clientX = event.clientX;
//         let clientY = event.clientY;
//         if (clientX === clientOffset.clientX && clientY === clientOffset.clientY) {
//           console.log("click 事件");
//         } else {
//           console.log("drag 事件");
//         }
//       });
//     }
//   }
// })

//输入框电话号码
const inputPhone = ref(undefined);

//挂机
const hanguploading = ref(false);

//底部显示
function iscurrentShowFoot(mark) {
  if (currentCallShowId.value === callshowId.value) {
    //关闭
    if (callState.value != 0) {
      //通话中
      proxy.$modal
        .confirm("关闭通话弹窗将导致通话中断，是否确认继续！？")
        .then(function () {
          webrtcSDK.endCall();
          istime.value = false;
          proxy.$modal.msgSuccess("通话已结束！");
          store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
          hanguploading.value = false;
        });
    } else {
      store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
    }
  } else {
    //打开
    //当前组件第一次打开生成唯一标识
    if (!callshowId.value) {
      callshowId.value = getUuid(mark);
    }

    let arr = [];
    if (currentCallShowId.value) {
      //其他组件通话中
      if (callState.value != 0) {
        return false;
      } else {
        arr = currentCallShowId.value.split("-");
        if (arr[arr.length - 1] == "mdf") {
          olduuid.value = JSON.parse(JSON.stringify(currentCallShowId.value));
        }
      }
    }

    //显示当前组件底部
    store.commit("call/SET_CURRENT_CALL_SHOWID", callshowId.value);
    // 联系人，案件自动呼叫
    if (props.contactId !== false) {
      //联系人呼叫
      store.commit("call/SET_CALL_STATE", 1);
      callOutByContact({ contactId: props.contactId })
        .then((res) => {
          try {
            let { data } = res;
            inputPhone.value = data.phone || undefined;
            store.commit("call/SET_CALLING_PHONE", data.phone || null);
            const obj = {
              ext: data?.ext || "",
              apiCallid: data?.apiCallid || "",
              phone: data?.phone || "",
              customerId: data?.customerId || "",
              UID: data?.uid || "",
              relationID: data?.relationID || "",
              hideNumber: data?.hideNumber || "",
              callbackDomain: data?.callbackDomain || "",
            };
            const params = JSON.stringify(obj);
            let paramsText = new URLSearchParams(obj).toString();
            webrtcSDK.callPhone({ phone: data.phone,customRelay: props.customRelay, params: paramsText });
          } catch (error) {
            inputPhone.value = undefined;
            store.commit("call/SET_CALLING_PHONE", null);
            store.commit("call/SET_CALL_STATE", 0);
            store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
          }
        })
        .catch((err) => {
          store.commit("call/SET_CALL_STATE", 0);
        });
    } else if (props.workOrderId !== false) {
      store.commit("call/SET_CALL_STATE", 1);
      callOutByWorkOrderPhoneNumber({ workOrderId: props.workOrderId })
        .then((res) => {
          try {
            let { data } = res;
            inputPhone.value = data.phone || undefined;
            store.commit("call/SET_CALLING_PHONE", data.phone || null);
            const obj = {
              ext: data?.ext || "",
              apiCallid: data?.apiCallid || "",
              phone: data?.phone || "",
              customerId: data?.customerId || "",
              UID: data?.uid || data?.UID || "",
              uid: data?.uid || data?.UID || "",
              Uid: data?.uid || data?.UID || "",
              relationID: data?.relationID || "",
              hideNumber: data?.hideNumber || "",
              callbackDomain: data?.callbackDomain || "",
            };
            const params = JSON.stringify(obj);
            let paramsText = new URLSearchParams(obj).toString();
            webrtcSDK.callPhone({ phone: data.phone, customRelay: props.customRelay, params: paramsText });
          } catch (error) {
            inputPhone.value = undefined;
            store.commit("call/SET_CALLING_PHONE", null);
            store.commit("call/SET_CALL_STATE", 0);
            store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
          }
        })
        .catch((error) => {
          store.commit("call/SET_CALL_STATE", 0);
        });
    } else if (props.caseId !== false) {
      //案件呼叫
      store.commit("call/SET_CALL_STATE", 1);
      callOutByCase({ caseId: props.caseId })
        .then((res) => {
          try {
            let { data } = res;
            inputPhone.value = data.phone || undefined;
            store.commit("call/SET_CALLING_PHONE", data.phone || null);
            const obj = {
              ext: data?.ext || "",
              apiCallid: data?.apiCallid || "",
              phone: data?.phone || "",
              customerId: data?.customerId || "",
              UID: data?.uid || data?.UID || "",
              uid: data?.uid || data?.UID || "",
              Uid: data?.uid || data?.UID || "",
              relationID: data?.relationID || "",
              hideNumber: data?.hideNumber || "",
              callbackDomain: data?.callbackDomain || "",
              caseId: props.caseId,
            };
            const params = JSON.stringify(obj);
            let paramsText = new URLSearchParams(obj).toString();
            webrtcSDK.callPhone({ phone: data.phone, customRelay: props.customRelay, params: paramsText });
          } catch (error) {
            inputPhone.value = undefined;
            store.commit("call/SET_CALLING_PHONE", null);
            store.commit("call/SET_CALL_STATE", 0);
            store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
          }
        })
        .catch(() => {
          store.commit("call/SET_CALL_STATE", 0);
          store.commit("call/SET_CALLING_PHONE", null);
          store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
        });
    } else if (props.clickCallOut) {
      try {
        store.commit("call/SET_CALL_STATE", 1);
        webrtcSDK.callPhone({ phone: props.phoneNumber, customRelay: props.customRelay, });
      } catch (error) {
        store.commit("call/SET_CALL_STATE", 0);
      }
    }
  }
}

//电话号码外呼
function phoneCall() {
  if (!inputPhone.value || inputPhone.value == "") {
    proxy.$modal.msgWarning("请输入电话号码！");
    return false;
  }
  var Phone = /^(1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8})?$/;
  var FixedPhone = /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/;
  // if (!Phone.test(inputPhone.value ) && !FixedPhone.test(inputPhone.value )) {
  //   proxy.$modal.msgWarning('请输入正确的电话号码！')
  //   return false
  // }
  store.commit("call/SET_CALL_STATE", 1);
  callOutByPhoneNumber({ phoneNumber: inputPhone.value })
    .then((res) => {
      let { data } = res;
      store.commit("call/SET_CALLING_PHONE", inputPhone.value);
      webrtcSDK.callPhone({
        phone: data.phone,
        customRelay: props.customRelay,
        params: JSON.stringify({
          ext: data?.ext || "",
          apiCallid: data?.apiCallid || "",
          phone: data?.phone || "",
          customerId: data?.customerId || "",
          UID: data?.uid || data?.UID || "",
          uid: data?.uid || data?.UID || "",
          Uid: data?.uid || data?.UID || "",
          relationID: data?.relationID || "",
          hideNumber: data?.hideNumber || "",
          callbackDomain: data?.callbackDomain || "",
        }),
      });
    })
    .catch(() => {
      store.commit("call/SET_CALLING_PHONE", null);
      store.commit("call/SET_CALL_STATE", 0);
      store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
    });
}

//计时格式化
function callTimeFormat(time) {
  let second = parseInt(time); //秒
  let minute = 0; //分
  let hour = 0; //时

  if (second > 59) {
    minute = parseInt(time / 60);
    second = parseInt(second % 60);
    if (minute > 59) {
      hour = parseInt(minute / 60);
      minute = parseInt(minute % 60);
    }
  }
  hour < 10 && (hour = `0${hour}`);
  minute < 10 && (minute = `0${minute}`);
  second < 10 && (second = `0${second}`);
  let str = "";
  if (hour !== "00") {
    return `${hour}:${minute}:${second}`;
  } else {
    return `${minute}:${second}`;
  }
}

//挂机
function hangUp() {
  proxy.$modal.confirm("是否确认结束通话！？").then(function () {
    hanguploading.value = true;
    callOutHangup()
      .then((res) => {
        if (istime.value) {
          istime.value = false;
          store.dispatch("call/endTime");
        }
        proxy.$modal.msgSuccess("通话已结束！");
        if (!props.contactId && !props.caseId) {
          store.commit("call/SET_CALL_STATE", 0);
          // store.commit('call/SET_CALL_TIME', 0)
        } else {
          if (olduuid.value != null) {
            store.commit("call/SET_CURRENT_CALL_SHOWID", olduuid.value);
            olduuid.value = null;
          } else {
            store.commit("call/SET_CURRENT_CALL_SHOWID", undefined);
          }
        }
        inputPhone.value = undefined;
        store.commit("call/SET_CALLING_PHONE", undefined);
      })
      .finally(() => {
        hanguploading.value = false;
      });
  });
}

// //Mrtc
// const option = ref({
//   fixedTop: 85,
//   user: null,
//   password: null,
// });
// // const webrtcSDKRef = ref(undefined)
// loadJs("https://amcmj_os.amcmj.com/webrtcSDK.umd.min.js").then(() => {
//   console.log(webrtcSDK.callPhone)
//   // webrtcSDKRef.value = webrtcSDK
//   webrtcSDK.init(
//       option,
//       (e) => {
//           console.log(e)
//           let { isRegistered, iscalling, isincall } = e;
//           console.log(isRegistered)
//           console.log(iscalling)
//           console.log(isincall)
//           // this.$store.commit('webrtc/SET_IS_REGISTERED',isRegistered)
//           // this.$store.commit('webrtc/SET_IS_CALLING',iscalling)
//           // this.$store.commit('webrtc/SET_IS_IN_CALL',isincall)
//       }
//   )
//   webrtcSDK.callPhone({phone:13266226685})
// })
</script>

<style lang="scss" scoped>
.call-warp {
  display: inline-block;

  .phone-call-warp {
    position: relative;
    color: #22c362;
    cursor: pointer;

    .number {
      position: absolute;
      top: -6%;
      right: 10%;
    }
  }

  .fixed-warp {
    position: fixed;
    display: flex;
    flex-direction: column;
    top: 86px;
    right: 0;
    width: 52px;
    padding: 10px;
    cursor: move;
    height: auto;
    z-index: 9999;
  }

  .fixed-warp:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .phone-bottom-warp {
    position: fixed;
    bottom: 0;
    left: 200px;
    right: 0;
    height: 80px;
    background-color: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
    padding: 0 42px;
    z-index: 9999;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    transition: left 0.28s;
  }

  .phone-bottom-warp.hideMenu {
    left: 54px !important;
  }

  .state-warp {
    margin-left: 30px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .head-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border: 1px solid #c0c4cc;
      border-radius: 50%;

      .icon-backg {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        background-color: #eeeeef;
        border-radius: 50%;
      }
    }
  }

  .btn-warp {
    flex: 1;
    text-align: right;
  }
}

:deep(.mybtn .el-button) {
  padding: 8px 12px;
}

.hang-up {
  width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #f56c6c;
  border-radius: 50%;
}

.fixed-warp :deep(.el-button) {
  width: 32px;
  height: auto;
  padding: 15px 8px;
  margin-left: 0;
}

.fixed-warp :deep(.el-button > span) {
  text-align: center;
  writing-mode: vertical-rl;
}

.phone-call-warp.no-click {
  color: #c0c4cc;
}

// 禁止点击
.no-click {
  cursor: not-allowed !important;
  pointer-events: none;
}
</style>
