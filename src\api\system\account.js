import request from '@/utils/request'

//获取列表
export function getAccountList(query) {
  return request({
    url: '/teamAccount/list',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}

//添加账户
export function addAccount(data) {
    return request({
      url: '/teamAccount/insert',
      method: 'post',
      data: data,
		  gateway: 'cis'
    })
  }

//修改账户
export function updateAccount(data) {
    return request({
      url: '/teamAccount/update',
      method: 'post',
      data: data,
		  gateway: 'cis'
    })
  }

  
//删除账户
export function delAccount(query) {
    return request({
      url: '/teamAccount/delete',
      method: 'post',
      params: query,
		  gateway: 'cis'
    })
  }