<template>
    <el-dialog title="预览" v-model="open" width="650px" :before-close="cancel">
        <div class="case-pdf" v-loading="loading">
            <previewPdf v-if="pdfSrc && pdfSrc.length > 0" :pdfSrc="pdfSrc" :total="total"/>
        </div>
        <template #footer>
            <div class="dialog-footer" style="text-align:center">
                <el-button type="primary"  @click="cancel">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="TemplateDetails">
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
import { getPreview } from "@/api/writ/template";
import { ref } from "@vue/reactivity";
//全局变量
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const pdfSrc = ref(undefined);
const total=ref(undefined)
//打开弹窗
function opendialog(id){
    getPreviewData(id) 
    open.value = true; 
}

//获取预览
async function getPreviewData(id){
  loading.value = true
  let req = { id };
  await getPreview(req).then((res) =>{
    loading.value = false;
    pdfSrc.value = res.data.url;
    total.value = res.data.total;
  }) 
  .catch(() => {
      loading.value = false;
  });
}

//关闭弹窗
function cancel(){
    open.value = false;
    pdfSrc.value = false
}

defineExpose({
  opendialog
});

</script>

<style scoped>
.case-pdf{
    width: 100%;
    height: 550px;
    overflow: auto;
    overflow-x: hidden;
}
</style>