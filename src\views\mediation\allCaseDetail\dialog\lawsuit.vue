<template>
  <el-dialog
    title="申请诉讼"
    v-model="open"
    width="700px"
    top="70px"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="申请原因" prop="remarks">
        <el-input
          type="textarea"
          v-model="form.remarks"
          :rows="4"
          show-word-limit
          maxlength="500"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { applyHandle } from "@/api/mediation/allCaseDetail";
const { proxy } = getCurrentInstance();
const getcaseDetailInfo = inject("getcaseDetailInfo");
const emit = defineEmits(["getDetails"]);

const open = ref(false);
const loading = ref(false);
const caseId = inject("caseId");

const data = reactive({
  form: {},
  rules: {
    remarks: [{ required: true, message: "请填写申请原因！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog() {
  reset();
  form.value.caseId = caseId;
  open.value = true;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let reqForm = {
        approveCode: "lawsuit",
        approveData: form.value
      }
      applyHandle(reqForm)
        .then((res) => {
          proxy.$modal.msgSuccess("申请提交成功！");
          cancel();
          getcaseDetailInfo();
          emit("getDetails");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    remarks: undefined,
    caseId: caseId,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
