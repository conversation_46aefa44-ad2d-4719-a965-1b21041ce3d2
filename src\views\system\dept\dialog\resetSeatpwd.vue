<template>
  <el-dialog title="密码重置" v-model="open" width="650px" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
      <el-form-item label="已选员工" :error="validSelected" required>
        <div class="selected-user" v-for="(item) in selectedUsers" :key="item.id">
          <div>
            <span class="name">{{ item.employeeName }}</span>
            <span class="role">{{ item.departments + " / " + item.theRole }}</span>
          </div>
          <!-- <el-icon class="close" @click="delSelected(index)"><close /></el-icon> -->
        </div>
      </el-form-item>
      <el-form-item label="密码" prop="sipPassword">
        <el-input type="password" v-model="form.sipPassword" auto-complete="off" placeholder="请输入密码"
          style="width: 240px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateSipPwd } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
const selectedUsers = ref([]);
const validSelected = ref(undefined);

const open = ref(false);
const loading = ref(false);

const data = reactive({
  form: {},
  rules: {
    sipPassword: [
      { required: true, message: '请填写密码！', trigger: 'blur' },
      {
        required: true,
        pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },
    ]
  },
});
const { form, rules } = toRefs(data);

function opendialog(selecteds) {
  reset();
  selectedUsers.value = JSON.parse(JSON.stringify(selecteds));
  open.value = true;
}

//删除已选员工
function remove(index) {
  selectedUsers.value.splice(index, 1);
}

//提交
function submitForm() {
  loading.value = true;
  try {
    if (selectedUsers.value.length === 0) {
      validSelected.value = "请选择员工";
      throw new Error("请选择员工");
    } else {
      validSelected.value = undefined;
    }

    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        form.value.employeeIds = [];
        selectedUsers.value.map((item) => {
          form.value.employeeIds.push(item.id)
        });
        updateSipPwd(form.value)
          .then((res) => {
            proxy.$modal.msgSuccess("修改成功！");
            emit("getList")
            cancel();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  } catch (error) {
    loading.value = false;
    // throw error
  }
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    employeeIds: undefined,
    sipPassword: undefined
  }
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.selected-user {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;

  .name {
    font-weight: 450;
    margin-right: 10px;
  }

  .role {
    font-size: 12px;
    color: #888888;
  }

  .close {
    cursor: pointer;
  }
}
</style>
