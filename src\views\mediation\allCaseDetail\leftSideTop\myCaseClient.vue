<template>
  <div class="my-case-client">
    <el-input
      v-model="queryParams.nameOrPhone"
      placeholder="请输入客户姓名或联系电话"
      class="mb10"
    >
      <template #append>
        <el-button icon="Search" @click="getDataList" />
      </template>
    </el-input>
    <!-- 1 待处理，2 处理中，3 已完成 -->
    <el-radio-group
      v-model="queryParams.caseState"
      size="small"
      class="custom-radio"
      @change="getDataList"
    >
      <el-radio label="" border>全部</el-radio>
      <el-radio label="1" border
        >待处理({{ tagCountData?.["待处理"] || 0 }})</el-radio
      >
      <el-radio label="2" border
        >处理中({{ tagCountData?.["处理中"] || 0 }})</el-radio
      >
      <el-radio label="3" border
        >已完成({{ tagCountData?.["已完成"] || 0 }})</el-radio
      >
    </el-radio-group>

    <div class="tag-list" :class="{ 'tag-expand': tagExpand }">
      <el-checkbox-group
        v-model="queryParams.intentionLabel"
        size="small"
        class="custom-checkbox mt20"
        @change="getDataList"
      >
        <el-checkbox
          v-for="item in intentionLabelList"
          :key="item.value"
          :label="item.value"
          border
          >{{
            item.label + `(${tagCountData?.[item.value] || 0})`
          }}</el-checkbox
        >
      </el-checkbox-group>
      <el-checkbox-group
        v-model="queryParams.contactLabel"
        size="small"
        class="custom-checkbox"
        @change="getDataList"
      >
        <el-checkbox
          v-for="item in contactLabelList"
          :key="item.value"
          :label="item.value"
          border
          >{{
            item.label + `(${tagCountData?.[item.value] || 0})`
          }}</el-checkbox
        >
      </el-checkbox-group>
      <el-checkbox-group
        v-model="queryParams.wechatLabel"
        size="small"
        class="custom-checkbox"
        @change="getDataList"
      >
        <el-checkbox
          v-for="item in wechatLabelList"
          :key="item.value"
          :label="item.value"
          border
          >{{
            item.label + `(${tagCountData?.[item.value] || 0})`
          }}</el-checkbox
        >
      </el-checkbox-group>
      <el-checkbox-group
        v-model="queryParams.contactInfoLabel"
        size="small"
        class="custom-checkbox"
        @change="getDataList"
      >
        <el-checkbox
          v-for="item in contactInfoLabelList"
          :key="item.value"
          :label="item.value"
          border
          >{{
            item.label + `(${tagCountData?.[item.value] || 0})`
          }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
    <div style="text-align: center">
      <el-button type="text" @click="tagExpand = !tagExpand"
        >展开/收缩标签</el-button
      >
    </div>

    <div class="client-list" v-loading="loading">
      <template v-for="item in clientListData" :key="item.caseId">
        <div
          class="client-list-item"
          :class="{ active: item.caseId == caseId }"
          @click="toRouteForCaseId(item.caseId)"
        >
          <div class="item-left">
            <div class="case-id">案件号：{{ item.caseId }}</div>
            <div class="case-client">
              {{ item.clientName }}-{{ item.productName }}
            </div>
            <div class="case-follow">最后跟进：{{ item.followUpAst }}</div>
          </div>
          <el-tag
            :type="
              { 未接通: 'danger', 已接通: 'success', 待呼叫: 'info' }[
                item.callLabel
              ]
            "
            >{{ item.callLabel }}</el-tag
          >
        </div>
      </template>
      <div v-show="clientListData.length === 0" style="text-align: center">
        暂无数据
      </div>
    </div>
    <pagination
      v-show="total > 12"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getDataList"
      layout="prev, pager, next"
    />
  </div>
</template>

<script setup>
import {
  getMyCaseCustomerList,
  selectCaseLabelCount,
} from "@/api/mediation/allCaseDetail";
const { proxy } = getCurrentInstance();
const caseId = inject("caseId");
const tagCountData = ref(null);
selectCaseLabelCount().then((res) => {
  tagCountData.value = res.data;
});

const intentionLabelList = ref([
  { label: "X", value: "X", num: 0 },
  { label: "S", value: "S", num: 0 },
  { label: "A", value: "A", num: 0 },
  { label: "B", value: "B", num: 0 },
  { label: "C", value: "C", num: 0 },
  { label: "D", value: "D", num: 0 },
]);
const contactLabelList = ref([
  { label: "可联", value: "可联", num: 0 },
  { label: "不可联", value: "不可联", num: 0 },
]);
const wechatLabelList = ref([
  { label: "已加微信", value: "已加微信", num: 0 },
  { label: "未加微信", value: "未加微信", num: 0 },
]);
const contactInfoLabelList = ref([
  { label: "普通联系线路", value: "普通联系线路", num: 0 },
  { label: "律所联系路线", value: "律所联系路线", num: 0 },
  { label: "调解联系路线", value: "调解联系路线", num: 0 },
  { label: "白名单联系", value: "白名单联系", num: 0 },
  { label: "已发短信", value: "已发短信", num: 0 },
  { label: "失联", value: "失联", num: 0 },
  { label: "正在协商中", value: "正在协商中", num: 0 },
  { label: "敏感客户", value: "敏感客户", num: 0 },
  { label: "新案", value: "新案", num: 0 },
  { label: "留案", value: "留案", num: 0 },
  { label: "预成交", value: "预成交", num: 0 },
]);
const tagExpand = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 12,
  nameOrPhone: undefined,
  caseState: "",
  contactLabel: [],
  wechatLabel: [],
  intentionLabel: [],
  contactInfoLabel: [],
});
const Timer = ref(null);
const loading = ref(false);
const total = ref(0);
const clientListData = ref([]);
function getDataList() {
  if (Timer.value) {
    clearTimeout(Timer.value);
    Timer.value = null;
  }
  Timer.value = setTimeout(() => {
    loading.value = true;
    let reqForm = JSON.parse(JSON.stringify(queryParams.value));
    for (let key in reqForm) {
      const item = reqForm[key];
      // 处理空数组 空字符串
      if (Array.isArray(item) && item.length === 0) {
        delete reqForm[key];
      } else if (item === "") {
        delete reqForm[key];
      }
    }
    getMyCaseCustomerList(reqForm)
      .then((res) => {
        clientListData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }, 500);
}
getDataList();

// 跳转其他案件Id详情
function toRouteForCaseId(id) {
  proxy.$tab.closeOpenPage({ path: `/case/allcase-detail/caseDetails/${id}`});
}
</script>

<style scoped>
.my-case-client {
  padding: 10px 0;
  display: flex;
  flex-direction: column;
}
::v-deep(.custom-radio .el-radio__input) {
  display: none;
}
::v-deep(.custom-radio .el-radio):not(:last-child) {
  margin-right: 10px;
}
::v-deep(.custom-radio .el-radio__label) {
  padding-left: 0;
}
::v-deep(.custom-radio .el-radio.is-bordered.el-radio--small) {
  padding: 0 10px;
}

.tag-list {
  height: 50px;
  overflow: hidden;
}
.tag-list.tag-expand {
  height: auto;
}

::v-deep(.custom-checkbox .el-checkbox__input) {
  display: none;
}
::v-deep(.custom-checkbox .el-checkbox):not(:last-child) {
  margin-right: 10px;
}
::v-deep(.custom-checkbox .el-checkbox__label) {
  padding: 0 10px;
}
::v-deep(.custom-checkbox .el-checkbox) {
  margin-bottom: 10px;
}
::v-deep(.custom-checkbox .el-checkbox.is-bordered.el-checkbox--small) {
  padding: 0;
}

.client-list {
  min-height: 300px;
}
.client-list-item:first-child {
  border-top: 1px solid #eee;
}
.client-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  cursor: pointer;
}
.client-list-item:hover {
  background-color: #f5f5f5;
}
.client-list-item.active {
  background-color: #f5f5f5;
}
.case-id {
  font-weight: bold;
}
.case-client {
  padding: 5px 0;
}
.case-follow {
  font-size: 14px;
  color: #999;
}
::v-deep(.pagination-container) {
  position: relative;
  margin-top: 0;
}
</style>
