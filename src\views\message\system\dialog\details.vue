<template>
    <div class="app-container">
         <el-row class="mt10 mb10"><el-button @click="toBack">返回上一级</el-button></el-row>
        <el-row class="mt10 mb10 message-title">{{messageList.messageTitle}}</el-row>
        <el-row class="mt10 mb10 message-subheading">
            <span class="mr20">发布时间：{{messageList.creationtime}}</span>
            <span class="mr20">阅读量：{{messageList.readingVolume ?? 0}}</span>
        </el-row>
        <el-divider />
        <el-row class="mt10 mb10">
            <div class="text-html" v-html="messageList.messageContent"></div>
        </el-row>
    </div>
</template>
<script setup name="SystemDetailsMessage">
import xss from 'xss'
import { selectMessageCenter } from "@/api/message/system";
//全局配置
const { proxy } = getCurrentInstance();
const route = useRoute();
//详情列表
const messageList = ref({});
//列表参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    id:route.params.id
})
//获取列表
function getList(){
     selectMessageCenter(queryParams.value)
    .then((res) => {
        res?.rows?.forEach((item) => {
            messageList.value = item
            if(item?.deleteLogo == 1){
                setTimeout(function(){
                    toBack()
                },2000)
                return proxy.$modal.msgWarning('该消息详情已被删除，将自动跳回系统消息页面！');
            }
        });
    })
    .catch(() => {
      messageList.value.messageContent =''
    });
}
getList();

//返回
const toBack = () => {
  const obj = { path: "/ApplicationManagement/message/systemMessage" };
  proxy.$tab.closeOpenPage(obj);
};

</script>
<style lang="scss" scoped>
.message-title{
    text-align: center;
    display: block;
    font-size: 18px;
    font-weight: 600;
}
.message-subheading{
    text-align: center;
    display: block;
    font-size: 14px;
}
.text-html{
  width:100%;
    :deep(a){
        color:#409eff !important;
        text-decoration: underline !important;
    }
    :deep(p){
        word-break:break-all;
    }
}
</style>
