<template>
  <el-dialog
    title="添加标签"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="" prop="code">
        <labelRadioGroupVue :labels="labelOptions" v-model:code="form.code" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import labelRadioGroupVue from "@/components/CaseLabel/labelRadioGroup.vue";
import { selectLabel } from "@/api/common/common";
import { selectMarkCase } from "@/api/team/team";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);

const open = ref(false);
const loading = ref(false);
const labelOptions = ref([]); //标签列表

const data = reactive({
  form: {},
  rules: {
    code: [{ required: true, message: "请选择标签！", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog(data) {
  Object.assign(form.value, data);
  selectLabel().then((res) => {
    labelOptions.value = res.data;
    open.value = true;
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    code: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      selectMarkCase(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess("操作成功！");
          cancel();
          emit("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});

</script>

<style scoped></style>
