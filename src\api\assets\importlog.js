import request from '@/utils/request'

//获取树
export function getImportState() {
    return request({
        url: '/teamImportLog/getImportStart',
        method: 'get',
		gateway: 'cis'
    })
}

//导入案件列表
export function imCaseList(query) {
    return request({
        url: '/teamImportLog/caseList',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//导入联系人列表
export function imContactList(query) {
    return request({
        url: '/teamImportLog/contactList',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//导入催记列表
export function imUrgeList(query) {
    return request({
        url: '/teamImportLog/urgeList',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//导入还款计划
export function imPlanList(query) {
    return request({
        url: '/teamImportLog/planList',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//导入批量操作
export function imBatchList(query) {
    return request({
        url: '/teamImportLog/batchList',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

// 导入资料
export function imDataList(query) {
    return request({
        url: '/management/retrieval/selectRetrievalFileLog',
        method: 'get',
        params: query
    })
}
// 导入资料
export function getAllState(query) {
    return request({
        url: '/management/retrieval/getAllState',
        method: 'get',
        params: query
    })
}