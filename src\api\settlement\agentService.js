import request from '@/utils/request'

/*
* 消费记录
*/
//获取消费明细列表
export function settleList(query) {
    return request({
        url: '/settle/list',
        method: 'get',
        params: query
    })
}

//获取消费明细统计数据
export function selectListCost(query) {
    return request({
        url: '/settle/selectListCost',
        method: 'get',
        params: query
    })
}

/*
* 充值记录
*/
//获取充值记录列表
export function billList(query) {
    return request({
        url: '/bill/list',
        method: 'get',
        params: query
    })
}

