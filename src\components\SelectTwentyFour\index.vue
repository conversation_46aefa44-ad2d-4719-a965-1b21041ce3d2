<template>
  <div class="byted-weektime">
    <div class="calendar" style="width: calc(100% - -1px)">
      <table class="calendar-table">
        <tbody id="tableBody">
          <!-- <div
              id="kuang"
              :style="{
                width: kuangObj.width + 'px',
                height: kuangObj.height + 'px',
                top: kuangObj.top + 'px',
                left: kuangObj.left + 'px',
                bottom: kuangObj.bottom + 'px',
                right: kuangObj.right + 'px',
              }"
            ></div> -->
          <tr>
            <td @mousedown.prevent="handleMouseDown(i, 0)" @mouseup.prevent="handleMouseUp(i, 0)"
              @mouseover.prevent="handleMouseOver(i, 0)" class="calendar-atom-time"
              :class="item.hoverClass || item.class" v-for="(item, i) in rowUnit" :key="i" :title="toStr(item.timeData)"
              style="width:26px">
              <!-- timeData为偶数才显示 -->

              <span v-if="item.timeData % 2 === 0" class="calendar-atom-time-text">{{ toStr(item.timeData)
                }}</span>
            </td>
          </tr>
          <!-- <tr>
              <td colspan="145" class="timeContent">
                <div
                  v-for="(item, index) in timeStr"
                  v-show="item.length"
                  :key="index"
                >
                  <strong
                    ><span>{{ item }}</span></strong
                  >
                </div>
              </td>
            </tr> -->
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { watch } from 'vue';

const props = defineProps({
  timeArr: {
    type: Array,
    default: []
  },
  edit: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(["input"]);

const rowUnit = ref([]);
const timeContent = ref([])
const timeSection = ref([])
const timeStr = ref("")
const beginDay = ref(0)
const beginTime = ref(0)
const downEvent = ref(false)
const kuangObj = ref({
  width: 0,
  height: 0,
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  oldLeft: 0,
  oldTop: 0,
  flag: false,
});

const downBoo = computed(() => {
  return props.edit && downEvent.value;
})

watch(() => props.timeArr, (newValue) => {
  infoEcho();
})

watch(timeStr, (newValue) => {
  emit("input", newValue);
})
const init = () => {
  let arr = [];
  for (let j = 0; j < 16; j++) {
    // 0:8 1:9 2:10 3:11 4:12 5:13 6:14 7:15 8:16 9:17 10:18 11:19 12:20 13:21 14:22 15:23
    let timeData = j + 8;
    arr.push({ class: null, timeData: timeData, hoverClass: null });
  }
  rowUnit.value = arr;
  timeContent.value = { arr: [] };
  timeSection.value = [];
  timeStr.value = "";
  infoEcho();
}
const infoEcho = () => {
  if (props.timeArr.value && props.timeArr.value.length) {
    props.timeArr.value.forEach((item) => {
      let startArr = item.start.split(":");
      let endArr = item.end.split(":");
      let start = (startArr[0] * 1).toFixed(0) - 8;
      let end = (endArr[0] * 1 === 0 ? 24 : endArr[0] * 1).toFixed(0) - 9;
      for (let y = start; y < end + 1; y++) {
        if (rowUnit.value[y].class == null) {
          rowUnit.value[y].class = "ui-selected";
          timeContent.value.arr.push(rowUnit.value[y].timeData);
        }
      }
    });
    filterTime();
  } else {
    clear();
  }
}
const handleMouseDown = (i, day) => {
  downEvent.value = true;
  beginDay.value = day;
  beginTime.value = i;
};

const handleMouseOver = (i) => {
  if (downBoo.value) {
    let begin = beginTime.value;
    let start = begin <= i ? begin : i; //x轴 起点
    let length = Math.abs(begin - i);
    let end = start + length; //x轴 终点
    clearHoverClass();
    for (let y = start; y < end + 1; y++) {
      rowUnit.value[y].hoverClass = "ui-hovers";
    }
  }
}

const clearHoverClass = () => {
  rowUnit.value.forEach((item) => {
    item.hoverClass = null;
  });
}
const handleMouseUp = (i, day) => {
  //当点击事件是在table内才触发选取数据操作
  if (downBoo.value) {
    //选时间段
    clearHoverClass();
    let begin = beginTime.value;
    let start = begin <= i ? begin : i; //x轴 起点
    let length = Math.abs(begin - i);
    let end = start + length; //x轴 终点
    //当框选范围内所有块都是选中状态时,执行反选
    const isAdd = () => {
      for (let y = start; y < end + 1; y++) {
        if (rowUnit.value[y].class == null) return true;
      }
      return false;
    }

    if (isAdd()) {
      //没选中的全都选上
      for (let y = start; y < end + 1; y++) {
        if (rowUnit.value[y].class == null) {
          rowUnit.value[y].class = "ui-selected";
          timeContent.value.arr.push(rowUnit.value[y].timeData);
        }
      }
    } else {
      //反选
      for (let y = start; y < end + 1; y++) {
        rowUnit.value[y].class = null;
        timeContent.value.arr = timeContent.value.arr.filter(
          (item) => item != rowUnit.value[y].timeData
        );
      }
    }
  }
  //过滤时间段,将临近的时间段合并
  filterTime();
  downEvent.value = false;
}

const filterTime = (start, end) => {
  //选中的x,y坐标信息 x:0-47  y:0-6
  const sortCut = (arr) => {
    arr = Array.from(new Set(arr));
    //提取连续的数字
    var result = [];
    arr.forEach(function (v, i) {
      var temp = result[result.length - 1];
      if (!i) {
        result.push([v]);
      } else if (v % 1 === 0 && v - temp[temp.length - 1] == 1) {
        temp.push(v);
      } else {
        result.push([v]);
      }
    });
    return result;
  }
  const timeToStr = (arr) => {
    //把数组转成方便人看到字符串
    let str = "";
    arr.forEach((arr, index) => {
      let str1 = "";
      if (index == 0) {
        str1 = toStr(arr[0]) + "-" + toStr(arr[1], 'end');
      } else {
        str1 = "," + toStr(arr[0]) + "-" + toStr(arr[1], 'end');
      }
      str += str1;
    });
    return str;
  }
  //排序,分割成
  let arr1 = sortCut(timeContent.value.arr.sort((a, b) => a - b));
  let arr2 = [];
  arr1.forEach((arr) => {
    //过滤连续的时间段
    let arr3 = [];
    arr3.push(arr[0]);
    arr3.push(arr[arr.length - 1]);
    arr2.push(arr3);
  });
  timeStr.value = timeToStr(arr2);
  timeSection.value = arr2;
}

const toStr = (num, type) => {
  if (type === 'end' && num == 24) {
    --num
  }
  if (Number.isInteger(num)) {
    let str = '';
    str = num < 10 ? "0" + num : num.toString();
    return str + (type === 'end' ? ":59" : ":00");
  } else {
    let str =
      Math.floor(num) < 10
        ? "0" + Math.floor(num)
        : Math.floor(num).toString();
    return str + ":" + (type === 'end' ? "59" : ((num % 1) * 60).toFixed(0));
  }
}

const clear = () => {
  rowUnit.value.forEach((item) => {
    item.class = null;
  });
  timeContent.value.arr = [];
  timeSection.value.length = 0;
  timeStr.value = "";
}

const selectAll = () => {
  rowUnit.value.forEach((item) => {
    item.class = "ui-selected";
    timeContent.value.arr.push(item.timeData);
  })
  filterTime();
}
init()

defineExpose({
  selectAll,
  clear
})
</script>

<style scoped>
.byted-weektime .calendar {
  user-select: none;
  -webkit-user-select: none;
  position: relative;
  display: inline-block;
}

.byted-weektime .calendar .calendar-table {
  table-layout: fixed;
  border-collapse: collapse;
  border-radius: 4px;
}

.byted-weektime .calendar .calendar-table tr .calendar-atom-time:hover {
  background: #E4E7ED;
}

.byted-weektime .calendar .calendar-table tr .calendar-atom-time {
  position: relative;
}

.byted-weektime .calendar .calendar-table tr .calendar-atom-time .calendar-atom-time-text {
  position: absolute;
  width: 38px;
  bottom: -24px;
  left: -4px;
  font-size: 12px;
  color: #3f3f3f;
}

.byted-weektime .calendar .calendar-table tr .ui-selected {
  background: #2f88ff !important;
}

.byted-weektime .calendar .calendar-table tr .ui-hovers {
  background: #6e9dda !important;
}

.byted-weektime .calendar .calendar-table tr .ui-selected:hover {
  background: #326FEA !important;
}

.byted-weektime .calendar .calendar-table tr,
.byted-weektime .calendar .calendar-table td,
.byted-weektime .calendar .calendar-table th {
  border: 1px solid #E4E7ED;
  font-size: 12px;
  text-align: center;
  min-width: 3px;
  line-height: 1.8em;
  -webkit-transition: background 200ms ease;
  -moz-transition: background 200ms ease;
  -ms-transition: background 200ms ease;
  transition: background 200ms ease;
}

.byted-weektime .calendar .calendar-table tbody tr {
  height: 32px;
}

.byted-weektime .calendar .calendar-table tbody tr td:first-child {
  background: #f8f9fa;
}

.byted-weektime .calendar .calendar-table thead th,
.byted-weektime .calendar .calendar-table thead td {
  background: #f8f9fa;
}

.byted-weektime .calendar .calendar-table .td-table-tip {
  line-height: 2.4em;
  padding: 0 12px 0 19px;
  background: #fff !important;
}

.byted-weektime .calendar .calendar-table .td-table-tip .clearfix {
  height: 46px;
  line-height: 46px;
}

.byted-weektime .calendar .calendar-table .td-table-tip .pull-left {
  font-size: 14px;
  color: #333333;
}

.byted-weektime .week-td {
  width: 75px;
  padding: 20px 0;
}

.byted-weektime a {
  cursor: pointer;
  color: #2f88ff;
  font-size: 14px;
}

#kuang {
  position: absolute;
  background-color: blue;
  opacity: 0.3;
}
</style>