<template>
  <div class="my-tinymce">
    <Editor @focus="handleFocus" v-model="contentValue" :init="myInit" />
  </div>
</template>

<script>
import { onMounted, reactive, toRefs, watch } from "vue";
import axios from "axios";
// 引入tinymce编辑器
import Editor from "@tinymce/tinymce-vue";
import tinymce from "tinymce/tinymce"; // tinymce默认hidden，不引入则不显示编辑器
// 导入配置文件
import "./js/importTinymce";
import { init } from "./js/config";

import { getToken } from "@/utils/auth";

export default {
  name: "myEditor",
  components: {
    Editor,
  },
  props: {
    // 绑定文本值
    modelValue: {
      type: String,
      default: "",
    },
    // placeholder
    placeholder: {
      type: String,
      default: "请输入内容",
    },
    // 默认样式
    style: {
      type: Object,
      default: () => {
        return { width: "100%", heigth: "400" };
      },
    },
    // 图片上传服务器地址
    imgUploadUrl: {
      type: String,
      default: "",
    },
    myEditorFocus: {
      type: Function,
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      myInit: customer(init), // 初始化
      contentValue: props.modelValue, // 绑定文本
      timeout: null,
    });

    onMounted(() => {
      tinymce.init({});
    });

    // 侦听文本变化并传给外界
    watch(
      () => state.contentValue,
      (n) => {
        debounce(() => {
          emit("update:modelValue", state.contentValue);
        });
      }
    );
    // 侦听默认值 外界第一次传进来一个 v-model 就赋值给 contentValue
    watch(
      () => props.modelValue,
      (n) => {
        if (n && n !== state.contentValue) {
          state.contentValue = n;
        }
      }
    );

    function debounce(fn, wait = 400) {
      // console.log('进到了防抖', wait)
      if (state.timeout !== null) {
        clearTimeout(state.timeout);
      }
      state.timeout = setTimeout(fn, wait);
    }

    // 参数自定义初始化
    function customer(init) {
      // 允许外界传进来高度和placeholder
      init.height = props.style.heigth;
      init.placeholder = props.placeholder;
      // 粘贴图片 自动处理 base64
      init.urlconverter_callback = (url, node, onSave, name) => {
        if (node === "img" && url.startsWith("blob:")) {
          tinymce.activeEditor && tinymce.activeEditor.uploadImages();
        }
        return url;
      };
      // 图片上传
      init.images_upload_handler = (blobInfo, success, failure) => {
        imgUploadFn(blobInfo, success, failure);
      };
      return init;
    }

    function imgUploadFn(blobInfo, success, failure) {
      // 可以限制图片大小
      const params = new FormData();
      params.append("file", blobInfo.blob());
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: "Bearer " + getToken(),
        },
      };
      // 图片上传
      axios
        .post(props.imgUploadUrl, params, config)
        .then((res) => {
          if (res.data.code == 200) {
            success(res.data.data.url); // 上传成功，在成功函数里填入图片路径
          } else {
            failure("上传失败");
          }
        })
        .catch(() => {
          failure("上传出错，服务器开小差了呢");
        });
    }

    function getBookMark(text) {
      tinymce.activeEditor.selection.setContent(text);
      let content = tinymce.activeEditor.getContent();
      state.contentValue = content;
    }
    function handleFocus() {
      if (typeof props.myEditorFocus == "function") {
        props.myEditorFocus();
      }
    }

    return {
      ...toRefs(state),
      customer,
      debounce,
      getBookMark,
      handleFocus,
    };
  },
};
</script>

<style></style>
