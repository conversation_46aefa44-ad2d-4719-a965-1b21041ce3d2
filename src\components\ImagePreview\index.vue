<template>
  <el-image
    :src="`${Array.isArray(src) ? src[0] : src }`"
    fit="cover"
    ref="imgRef"
    :style="`width:${realWidth};height:${realHeight};`"
    append-to-body="true"
    :zoom-rate="1.2"
    :max-scale="7"
    :min-scale="0.2"
    :preview-src-list="realSrcList"
  >
    <template #error>
      <div class="image-slot">
        <el-icon><picture-filled /></el-icon>
      </div>
    </template>
  </el-image>
</template>

<script setup>
import { isExternal } from "@/utils/validate";
//全局数据
const { proxy } = getCurrentInstance();
const open = ref(false);

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  width: {
    type: [Number, String],
    default: ""
  },
  height: {
    type: [Number, String],
    default: ""
  }
});

const realSrc = computed(() => {
  let real_src = props.src.split(",")[0];
  if (isExternal(real_src)) {
    return real_src;
  }
  return import.meta.env.VITE_APP_BASE_API + real_src;
});

const realSrcList = computed(() => {
  if (Array.isArray(props.src)) {
    return props.src;
  }
  return [props.src];
});

const realWidth = computed(() =>
  typeof props.width == "string" ? props.width : `${props.width}px`
);

const realHeight = computed(() =>
  typeof props.height == "string" ? props.height : `${props.height}px`
);

//打开窗口
function opendialog() {
  open.value = true;
  proxy.$refs['imgRef'].clickHandler()
}

//关闭
function cancel(){
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;
  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;
    &:hover {
      transform: scale(1.2);
    }
  }
  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
