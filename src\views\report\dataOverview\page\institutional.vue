<template>
  <div class="asset-dimension">
    <div class="dimension-title">
      <div class="dot"></div>
      <span class="title-icon">员工维度</span>
    </div>
    <el-form :model="queryParams" class="form-content" ref="queryRef" :inline="true" label-width="120px">
      <el-form-item class="mar0" label="日期" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM" type="monthrange" clearable
          unlink-panels range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="请选择还款时间"
          style="width: 260px" />
      </el-form-item>
      <el-form-item class="mar0" label="员工名称" prop="cnameArr">
        <el-select v-model="queryParams.employeeId" placeholder="请输入或选择员工" @focus="getEmployeeOptions" clearable
          filterable style="width: 260px" :reserve-keyword="false">
          <el-option v-for="item in employeeOptions" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Refresh" @click="antiShake(resetQuery)"> 重置 </el-button>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">
          搜索
        </el-button>
      </el-form-item>
      <div class="export-btn">
        <el-button type="primary" :loading="loading" :disabled="selectedArr.length == 0" plain
          @click="downloadEmployee">导出</el-button>
      </div>
    </el-form>
    <div class="ml20 mb10">
      <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
        <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
          :indeterminate="item.indeterminate" />
      </el-checkbox-group>
    </div>
    <el-table v-loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" :data="dataList">
      <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
      <el-table-column label="员工名称" align="center" key="employeeName" prop="employeeName">
        <template #default="{ row }">
          {{ row.employeeName ? row.employeeName : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="所属资产包" align="center" :width="380" key="assetPackName" prop="assetPackName">
        <template #default="{ row }">
          <Tooltip :content="row.assetPackName" :length="61" />
        </template>
      </el-table-column>
      <el-table-column label="总回款" align="center" :width="180" key="collectionAmountTotal" prop="collectionAmountTotal">
        <template #default="{ row }">
          {{ numFilter(row.collectionAmountTotal) }}
        </template>
      </el-table-column>
      <el-table-column label="总通话时长" align="center" :width="180" key="agentDuration" prop="agentDuration">
        <template #default="{ row }">
          {{ row.agentDuration ? row.agentDuration : "--" }}
        </template>
      </el-table-column>
      <el-table-column label="拨打次数" align="center" key="callNum" prop="callNum">
      </el-table-column>
      <el-table-column label="接通数量" prop="availableNum" key="availableNum" align="center">
      </el-table-column>
      <el-table-column label="委案金额" prop="entrustMoney" key="entrustMoney" align="center" :width="120">
        <template #default="{ row }">
          {{ numFilter(row.entrustMoney) }}
        </template>
      </el-table-column>
      <el-table-column label="委案户数" prop="commissionHouseholds" key="commissionHouseholds" align="center">
      </el-table-column>
      <el-table-column label="目前在案金额" prop="recordedAmount" key="recordedAmount" align="center" :width="120">
        <template #default="{ row }">
          {{ numFilter(row.recordedAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="目前在案户数" prop="recordedNumber" key="recordedNumber" align="center" :width="120">
      </el-table-column>
      <el-table-column label="累计回收率" prop="recoveryRateCumulative" key="recoveryRateCumulative" align="center"
        :width="100">
        <template #default="{ row }">
          {{
            row.recoveryRateCumulative
          }}%
        </template>
      </el-table-column>
      <el-table-column label="累计结佣金额" prop="settlementAmount" key="settlementAmount" align="center" :width="120">
        <template #default="{ row }">
          {{ numFilter(row.settlementAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="调解记录" prop="tsSum" key="tsSum" align="center" :width="100">
      </el-table-column>
      <el-table-column label="电调记录" prop="urgeSum" key="urgeSum" align="center" :width="100">
      </el-table-column>
      <el-table-column label="人均案件量" prop="caseVolume" key="caseVolume" align="center" :width="100">
      </el-table-column>
      <el-table-column label="催记总量" prop="reminderTotal" key="reminderTotal" align="center" :width="100">
      </el-table-column>
    </el-table>
    <div class="pagination-box">
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" :autoScroll="false" @pagination="getList" />
    </div>
  </div>
</template>

<script setup>
import { getCensusEmployeeList, getEmployee } from "@/api/report/dataOverview.js";
import Tooltip from "@/components/ToolTipLength/index";
const rangFields = ["createTime"]; //范围字段
const { proxy } = getCurrentInstance();
//表格参数
const total = ref(0);
const loading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  createTime: [],
  employeeId: undefined,
  allQuery: false,
});
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
const employeeIds = ref([]);
const checkedType = ref(["本页选中"]);
//表单参数
const dataList = ref([]);
const selectedArr = ref([]);

// 员工列表
const employeeOptions = ref([]);

//查询参数
function getList() {
  loading.value = true;
  const reqForm = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields))
  );
  if (reqForm.createTime1 && reqForm.createTime2) {
    reqForm.createTime1 = `${reqForm.createTime1}-01`;
    const year = reqForm.createTime2.split("-")[0];
    const month = reqForm.createTime2.split("-")[1];
    reqForm.createTime2 = `${reqForm.createTime2}-${new Date(year, month, 0).getDate()}`;
  }

  getCensusEmployeeList(reqForm)
    .then((res) => {
      loading.value = false;
      total.value = res.total;
      dataList.value = res.rows || [];
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

// 下载员工列表
function downloadEmployee() {
  let reqForm = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields))
  );
  if (checkedType.value[0] == "本页选中" && checkedType.value.length == 1) {
    reqForm.employeeIds = selectedArr.value.map((item) => item.employeeId);
  }
  proxy.downloadforjson(
    "/data/outline/exportEmployeeDimension",
    { ...reqForm },
    `员工列表维度_${+new Date()}.xlsx`
  );
}

//表格行能否选择
function checkSelectable() {
  if (queryParams.value.allQuery) {
    return false;
  } else {
    return true;
  }
}

// 选中下载员工列表

//选择列表
function handleSelectionChange(selection) {
  employeeIds.value = selection.map((item) => item.employeeId);
  selectedArr.value = selection;
  if (checkedType.value[0] != "搜索结果全选") {
    if (selectedArr.value.length) {
      checkedType.value[0] = "本页选中";
    } else {
      checkedType.value = [];
    }
    checkStatus.value[0].indeterminate =
      selectedArr.value.length > 0 && selectedArr.value.length < dataList.value.length;
  }
}

//全选类型
function checkedTypeChange() {
  checkedType.value?.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value?.length === 0) {
    //全不选
    proxy.$refs["multipleTableRef"].clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else {
    dataList.value.length > 0 &&
      dataList.value.map((item) => {
        proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.allQuery = true;
  } else {
    queryParams.value.allQuery = false;
  }
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    createTime: [],
    employeeId: undefined,
  };
  checkedType.value = ref(["本页选中"]);
  getList();
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function getEmployeeOptions() {
  getEmployee().then((res) => {
    employeeOptions.value = res.data || [];
  });
}

watch(dataList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (queryParams.value.allQuery) {
      queryParams.value.allQuery = false;
      nextTick(() => {
        selectTable()
          .then((res) => { })
          .finally(() => {
            queryParams.value.allQuery = true;
          });
      });
    } else {
      proxy.$refs["multipleTableRef"].toggleAllSelection();
    }
  }
});
watch(() => checkedType.value, () => {
  if (checkedType.value[0] == "搜索结果全选") {
    employeeIds.value = [];
  }
}, { immediate: true, deep: true });

//表格选中
function selectTable() {
  return new Promise((reslove, reject) => {
    try {
      dataList.value.map((item, index) => {
        proxy.$refs["multipleTableRef"].value.toggleRowSelection(item, true);
      });
      reslove(true);
    } catch (error) {
      reject(error);
    }
  });
}
</script>

<style scoped lang="scss">
.pagination-box {
  position: relative;

  :deep(.pagination-container .el-pagination) {
    right: 20px;
    bottom: 20px;
  }
}

.export-btn {
  float: right;
  margin-right: 20px;
}

.asset-dimension {
  margin-top: 40px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.2), 0 0px 3px 0 rgba(0, 0, 0, 0.19);

  .dimension-title {
    background-color: #d5d7db;
    color: #3f3f3f;
    margin: 10px 0px 20px;
    height: 60px;
    font-size: 24px;
    line-height: 60px;
    font-weight: 700;

    .dot {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #409eff;
      margin: 0px 10px 0px 20px;
    }
  }

  .recoery-box {
    width: 75%;
    height: 20px;
    display: inline-block;
    position: relative;
    top: 5px;

    .recoery-back {
      height: 10px;
      width: 100%;
      margin-top: 5px;
      background-color: #e2e2e2;
      border-radius: 5px;
      position: absolute;
    }

    .recoery-sch {
      height: 10px;
      margin-top: 5px;
      background-color: #409eff;
      border-radius: 5px;
      position: absolute;
    }
  }

  .form-content {
    margin: 10px 0px;
  }

  .recoery-data {
    width: 25%;
    display: inline-block;
  }
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>
