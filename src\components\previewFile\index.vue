<template>
  <el-dialog
    title="预览"
    v-model="open"
    append-to-body
    width="1120px"
    :before-close="cancel"
  >
    <iframe class="iframe" v-if="iframeSrc" :src="iframeSrc"></iframe>
  </el-dialog>
</template>
<script setup>
import { getFileOnlinePreviewHost } from "@/api/menu.js";
import Base64 from "@/utils/base64.js";
const iframeSrc = ref(undefined);
const open = ref(false);
const opendialog = (originUrl) => {
  open.value = true;
  getFileOnlinePreviewHost().then((res) => {
    iframeSrc.value = `${res.data}onlinePreview?url=${encodeURIComponent(
      Base64.encode(originUrl)
    )}`;
  });
};
const cancel = () => {
  iframeSrc.value = undefined;
  open.value = false;
};
defineExpose({ opendialog });
</script>
<style lang="scss" scoped>
.iframe {
  min-height: 65vh;
  width: 100%;
}
</style>
