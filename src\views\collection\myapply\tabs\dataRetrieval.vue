<!--停催-->
<template>
  <el-form class="form-content h-50 mt20" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
    :inline="true" label-width="100px">
    <el-form-item label="案件ID" prop="caseId">
      <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="姓名" prop="clientName">
      <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="证件号码" prop="clientIdcard">
      <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
        @keyup.enter="antiShake(handleQuery)" />
    </el-form-item>
    <el-form-item label="申请时间" style="width: 308px">
      <el-date-picker v-model="queryParams.applyDate" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
    <el-form-item label="审核时间" style="width: 308px">
      <el-date-picker v-model="queryParams.updateTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
  </el-form>

  <div class="text-center">
    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
  </div>
  <el-row class="mb10 mt10 h32">
    <el-button v-hasPermi="['case:manage:recovery:data']" plain v-if="activeTab == '待审核'" :disabled="single || loading"
      @click="batchRevoke()">批量撤销申请
    </el-button>

    <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
  </el-row>

  <el-tabs class="mb8" type="card" v-model="activeTab" @tab-click="tabChange">
    <el-tab-pane v-for="item in examineStateTab" :key="item.key" :label="item.value" :name="item.key"></el-tab-pane>
    <el-tab-pane label="全部" name="全部"></el-tab-pane>
  </el-tabs>

  <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
    <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
    <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
      <template #default="scope">
        <div style="display: flex; align-items: center; justify-content: center">
          <el-tooltip v-if="scope.row.labelContent" placement="top">
            <template #content>{{ scope.row.labelContent }}</template>
            <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
          </el-tooltip>
          <span style="color: #409eff; cursor: pointer" type="text" v-if="scope.row.button == 1"
            @click="toDetails(scope.row.caseId, scope.$index)">{{ scope.row.caseId }}</span>
          <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible"
      :show-overflow-tooltip="true" />
    <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible" />
    <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[3].visible" />
    <el-table-column label="审核状态" align="center" v-if="columns[4].visible">
      <template #default="scope">
        <el-popover placement="bottom" :width="500" :ref="`popover-${scope.$index}`" trigger="click">
          <template #reference>
            <el-button @click="showPopover(scope.row)" type="text">{{
              scope.row.examineState
            }}</el-button>
          </template>
          <el-table :data="gridData">
            <el-table-column width="200" property="approveTime" label="处理时间" />
            <el-table-column width="100" property="reviewer" label="处理人" :show-overflow-tooltip="true" />
            <el-table-column width="100" property="approveStart" :formatter="approveStartFor" label="处理状态" />
            <el-table-column width="100" property="refuseReason" :formatter="reasonFor" label="原因"
              :show-overflow-tooltip="true" />
          </el-table>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column label="审核时间" width="150px" align="center" key="updateTime" prop="updateTime"
      v-if="columns[5].visible" :show-overflow-tooltip="true" />
    <el-table-column label="申请原因" align="center" key="reason" prop="reason" v-if="columns[6].visible">
      <template #default="scope">
        <el-tooltip placement="top">
          <template #content>
            <p style="max-width: 300px">{{ scope.row.reason }}</p>
          </template>
          <div>
            <span>{{
              scope.row.reason?.length > 15
                ? `${scope.row.reason?.substring(0, 15)}...`
                : scope.row.reason
            }}</span>
          </div>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column label="申请时间" width="120" align="center" key="applyDate" prop="applyDate" v-if="columns[7].visible"
      :show-overflow-tooltip="true" />

    <el-table-column label="操作" width="100" fixed="right">
      <template #default="scope">
        <div>
          <el-button type="text" v-if="scope.row.examineState == '待审核'" v-hasPermi="['collection:apply:recovery:data']"
            @click="revoke(scope.row)">
            撤销
          </el-button>
          <el-popover placement="top" :width="500" :ref="`popover-${scope.$index}`" trigger="click" v-if="
            scope.row.examineState == '已通过' &&
            scope.row.path !== null &&
            checkPermi(['collection:apply:check:data'])
          " :disabled="(scope.row.examineState == '已通过' && scope.row.fileExpiration == 1) ||
            scope.row.button == 0
            ">
            <template #reference>
              <el-button type="text" v-hasPermi="['collection:apply:check:data']" :disabled="(scope.row.examineState == '已通过' && scope.row.fileExpiration == 1) ||
                scope.row.button == 0
                ">
                查看资料
              </el-button>
            </template>
            <span>文件地址：</span>
            <span class="file-path text-blue" @click="setLink(scope.row.path)" :key="index">{{ scope.row.path }}</span>
          </el-popover>
          <span v-else>--</span>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
    @pagination="getList" />
</template>

<script setup>
import {
  examineStates,
  datumList,
  updateRetrievalRecord,
} from "@/api/collection/myapply.js";
import { selectApproveProceEight } from "@/api/case/aduit/aduit";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const router = useRouter();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    updateTime: [],
    applyDate: [],
  },
});
const rangfiles = ["updateTime", "applyDate"];
const examineStateTab = ref([]); //审核状态tab
const showSearch = ref(false);
const activeTab = ref("待审核");
const { queryParams } = toRefs(data);
const gridData = ref([]);
//选中的id集合
const ids = ref([]);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

const single = ref(true); //是否可操作
// 列显隐信息
const columns = ref([
  { key: 0, label: `案件ID`, visible: true },
  { key: 1, label: `产品类型`, visible: true },
  { key: 2, label: `姓名`, visible: true },
  { key: 3, label: `证件号码`, visible: true },
  { key: 4, label: `审核状态`, visible: true },
  { key: 5, label: `审核时间`, visible: true },
  { key: 6, label: `申请原因`, visible: true },
  { key: 7, label: `申请时间`, visible: true },
]);

//获取列表数据
function getList() {
  queryParams.value.examineState =
    activeTab.value == "全部" ? undefined : activeTab.value;

  loading.value = true;
  datumList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取审核状态
function getExamineStateList() {
  examineStates().then((res) => {
    examineStateTab.value = res.data;
  });
}
getExamineStateList();

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    updateTime: [],
    applyDate: [],
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
  const stutasEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 3: '已完成', 4: '已撤销', 5: '已退案关闭' }
  return stutasEnum[row.approveStart];
}

//气泡框展示
function showPopover(row) {
  let req = {
    applyId: row.id,
  };
  selectApproveProceEight(req)
    .then((res) => {
      gridData.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
}

//表格行能否选择
function checkSelectable() {
  return true;
}

//操作栏
function handlesCommand(command) {
  command.func(command.data);
}

//点击复制
function setLink(text) {
  proxy.copyUrl(text);
  window.open(text);
  proxy.$modal.msgSuccess("复制成功！");
}

//撤销
function revoke(data) {
  let req = [data.id];
  revokeSubmit(req);
}

//通过原因
function reasonFor(row) {
  return row.refuseReason ? row.refuseReason : `--`;
}

//批量撤销
function batchRevoke() {
  let req = ids.value;
  revokeSubmit(req);
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

//提交撤销
function revokeSubmit(data) {
  proxy.$modal
    .confirm("是否确认撤销该申请？撤销成功后该申请将失效，是否确认?")
    .then(function () {
      loading.value = true;
      return updateRetrievalRecord(data);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功！");
    }).catch(() => {
      loading.value = false;
    });;
}


</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

.text-blue {
  color: #409eff;
}

.text-blue:hover {
  color: #79bbff;
}

.file-path {
  margin: 10px;
  cursor: pointer;
}

.color-gray {
  color: #999;
}

// :deep(.el-table__header-wrapper .el-checkbox) {
//   display: none;
// }</style>
