import request from '@/utils/request'

//律函-新建发函-校验文件
export function getMessageList(query) {
    return request({
        url: '/case/selectListWithSignRecord',
        method: 'get',
        params: query
    })
}

//律函审核-通过
export function messagePass(data) {
    return request({
        url: '/case/updateSignRecord',
        method: 'post',
        data: data
    })
}

//律函审核-不通过
export function messageNotPass(data) {
    return request({
        url: '/case/updateSignRecord',
        method: 'post',
        data: data
    })
}

//律函审核-函件量-列表
export function getProce(query) {
    return request({
        url: '/case/selectApproveProceLawa',
        method: 'get',
        params: query
    })
}

//律函审核-函件量-列表
export function getPreviewUrl(query) {
    return request({
        url: '/case/sign/getPreviewUrl',
        method: 'get',
        params: query
    })
}
