<template>
    <div class="app-container">
        <el-radio-group class="mb20" v-model="radioType">
            <el-radio-button label="0" name="0" v-hasPermi="['case:approve:replyApprove']">回款审批</el-radio-button>
            <el-radio-button label="1" name="1" v-hasPermi="['case:approve:reduceApprove']">减免审批</el-radio-button>
            <el-radio-button label="2" name="2" v-hasPermi="['case:approve:stagesApprove']">分期审批</el-radio-button>
            <el-radio-button label="3" name="3" v-hasPermi="['case:approve:keepApprove']">留案审批</el-radio-button>
            <el-radio-button label="4" name="4" v-hasPermi="['case:approve:stopApprove']">停案审批</el-radio-button>
            <el-radio-button label="5" name="5" v-hasPermi="['case:approve:backApprove']">退案审批</el-radio-button>
            <el-radio-button label="6" name="6" v-hasPermi="['case:approve:outsideApprove']">外访审批</el-radio-button>
        </el-radio-group>
        <reply v-if="radioType == '0' && checkPermi(['case:approve:replyApprove'])" ref="replyRef" />
        <reduction v-if="radioType == '1' && checkPermi(['case:approve:reduceApprove'])" ref="reductionRef" />
        <stages v-if="radioType == '2' && checkPermi(['case:approve:stagesApprove'])" ref="stagesRef" />
        <keep v-if="radioType == '3' && checkPermi(['case:approve:keepApprove'])" ref="keepRef" />
        <back v-if="radioType == '5' && checkPermi(['case:approve:backApprove'])" ref="backRef" />
        <outside v-if="radioType == '6' && checkPermi(['case:approve:outsideApprove'])" ref="outsideRef" />
        <stopVue v-if="radioType == '4' && checkPermi(['case:approve:stopApprove'])" ref="stopRef" />
    </div>
</template>
<script setup>
import { checkPermi } from "@/utils/permission";
import reply from './tabPage/reply';
import reduction from './tabPage/reduction';
import stages from './tabPage/stages';
import keep from './tabPage/keep';
import back from './tabPage/back';
import outside from './tabPage/outside';
import stopVue from './tabPage/stop.vue';
const radioType = ref('0')
</script>