<template>
  <div class="app-container">
    <div class="app-content">
      <h3 class="title">物流跟踪</h3>
      <el-timeline v-if="message == 'ok'">
        <el-timeline-item v-for="(activity, index) in trackedInfoList" :key="index" :type="activity?.type"
          :color="activity?.color" :size="activity?.size" :hollow="activity?.hollow" :time="activity?.time">
          <time class="mr10">
            {{ formatDate(activity?.time) }}
          </time>
          <el-tag v-if="index === 0" round size="small" effect="light">
            最新
          </el-tag>
          <div :class="index == 0 ? 'last-data' : ''">
            {{ activity.context }}
          </div>
        </el-timeline-item>
      </el-timeline>
      <div v-else class="not-result">{{ message }}</div>
    </div>
  </div>
</template>
<script setup name="trackedDetail">
import { getExpressInfo } from "@/api/lawyer/lawyer";
import { ref } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
const trackedInfoList = ref([]);
const message = ref(undefined);
getTrackedInfo();
// 获取物流信息
function getTrackedInfo() {
  const lawyerInfo = JSON.parse(route.query?.lawyerInfo.replaceAll("&&", '"'));
  getExpressInfo(lawyerInfo)
    .then((res) => {
      if (res.data.message == "ok") {
        res.data.data[0].size = "large";
        res.data.data[0].type = "primary";
        res.data.data[0].hollow = true;
        trackedInfoList.value = res.data.data;
      }
      message.value = res.data.message || "查无此物流信息";
    })
    .catch((err) => {
      message.value = "查无此物流信息";
    });
}

function getTimeQuantum(time) {
  if (time) {
    time = time.split(" ")[1];
    const timestamp = Date.parse("1970-01-01 " + time) / 1000;
    let timeQuantum = undefined;
    if (timestamp >= -28800 && timestamp < -10800) {
      timeQuantum = "凌晨";
    } else if (timestamp >= -10800 && timestamp < 0) {
      timeQuantum = "早晨";
    } else if (timestamp >= 0 && timestamp < 10800) {
      timeQuantum = "上午";
    } else if (timestamp >= 10800 && timestamp < 18000) {
      timeQuantum = "中午";
    } else if (timestamp >= 18000 && timestamp < 28800) {
      timeQuantum = "下午";
    } else if (timestamp >= 28800 && timestamp < 39600) {
      timeQuantum = "傍晚";
    } else if (timestamp >= 39600 && timestamp < 57600) {
      timeQuantum = "晚上";
    }
    return timeQuantum;
  }
}

// 格式化日期
function formatDate(time) {
  const date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = repair0(date.getHours());
  const minutes = repair0(date.getMinutes());
  const seconds = repair0(date.getSeconds());
  return `${year}年${month}月${day}日 ${getTimeQuantum(
    time
  )} ${hours}:${minutes}:${seconds}`;
}
// 小于10，补0
function repair0(num) {
  if (num < 10 && num >= 0) {
    return `0${num}`;
  } else {
    return num;
  }
}
</script>

<style lang="scss" scoped>
.title {
  margin-left: 35px;
}

time {
  color: #999;
  font-size: 15px;
}

.last-data {
  color: var(--theme);
}

.el-tag {
  border-radius: 10px;
}

.not-result {
  margin-left: 55px;
  color: red;
}
</style>