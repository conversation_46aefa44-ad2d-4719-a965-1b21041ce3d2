<template>
  <div class="container">
    <div class="option" v-show="!isError">
      <span class="play" @click="handlePauseOrPlay">
        <el-icon v-show="paused"
          ><VideoPlay color="#3173e6" :size="40"
        /></el-icon>
        <el-icon v-show="!paused"><VideoPause :size="40" /></el-icon>
      </span>
    </div>
    <audio
      @canplay="getDuration"
      controls
      @timeupdate="updateTime"
      v-show="false"
      ref="audio"
      :src="audioSrc"
      @error="handleError"
    />
    <div class="card" v-show="!isError">
      <div class="time">
        <span class="startTime">{{ currentDuration }} / {{ duration }}</span>
      </div>
      <div
        class="progress"
        ref="progress"
        @click="clickProgress"
        @mouseup="handleMouseup"
      >
        <div class="currentProgress" ref="currentProgress">
          <span class="circle" ref="circle" @mousedown="handleMousedown"></span>
        </div>
      </div>
      <div class="tip">
        <span>{{ audioTip }}</span>
      </div>
    </div>
    <!-- 播放速度 -->
    <el-select v-show="isSpeed" v-model="speed" @change="changeSpeed" size="small" style="width: 108px;">
      <el-option label="1.0倍速(默认)" :value="1.0"></el-option>
      <el-option label="1.5倍速" :value="1.5"></el-option>
      <el-option label="2倍速" :value="2"></el-option>
    </el-select>
    <div v-show="isError" style="color: coral;">录音加载失败，请联系管理员！</div>
  </div>
</template>
<script setup>
//全局事件
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const emit = defineEmits();

const props = defineProps({
  audioSrc: {
    type: String,
    default: undefined,
  },
  audioTip: {
    type: String,
    default: "",
  },
  audioObj: {
    type: Object,
    default: {},
  },
  isSpeed: {
    type: Boolean,
    default: false,
  }
});
const isError = ref(false);
//音频参数
const duration = ref("00:00");
const currentDuration = ref("00:00");
const audio = ref("");
const paused = ref(true);
const isMoveIn = ref(false);
const speed = ref(1.0);

//文件加载失败
function handleError() {
  isError.value = true;
}
//播放或者暂停
function handlePauseOrPlay() {
  if (audio.value.paused) emit("stopCheck");
  audio.value.paused
    ? proxy.$refs["audio"].play()
    : proxy.$refs["audio"].pause();
  paused.value = !paused.value;
}
//视频在可以播放时触发
function getDuration() {
  duration.value = timeFormat(proxy.$refs["audio"].duration);
  audio.value = proxy.$refs["audio"];
  props.audioObj.duration = proxy.$refs["audio"].duration? Math.ceil(proxy.$refs["audio"].duration): 0;
}
//格式化时间
function timeFormat(number) {
  let minute = parseInt(number / 60);
  let second = Math.ceil(number % 60);
  minute = minute >= 10 ? minute : "0" + minute;
  second = second >= 10 ? second : "0" + second;
  return minute + ":" + second;
}
//跟新时间
function updateTime() {
  if (!proxy.$refs["progress"]) return;
  currentDuration.value = timeFormat(audio.value.currentTime);
  //如果不是正在移动 和 没有暂停播放就执行
  if (!isMoveIn.value || !audio.value.paused) {
    // 设置当前时间
    let MoveX =
      proxy.$refs["progress"].clientWidth *
      (audio.value.currentTime / audio.value.duration);
    //播放时更新距离
    proxy.$refs["currentProgress"].style.width = MoveX + "px";
    proxy.$refs["circle"].style.left =
      MoveX - proxy.$refs["circle"].clientWidth / 2 + "px";
  }
  if (currentDuration.value == duration.value) {
    paused.value = true;
  }
}
//点击进度条更新进度
function clickProgress(e) {
  //如果不是正在移动 和 没有暂停播放就执行
  if (!isMoveIn.value || !audio.value.paused) {
    updateProgress(e.offsetX);
  }
}
//跟新进度
function updateProgress(MoveX) {
  //当前移动的位置 = 当前移动的位置 / 当前进度条的可视长度    //this.$refs.progress.clientWidth 注意一定要拿总长度 否则会拿进度条已经走过的长度
  let clickProgress = MoveX / proxy.$refs["progress"].clientWidth;
  //设置播放的时间 = 总时长 * 当前点击的长度
  audio.value.currentTime = audio.value.duration * clickProgress;
  //设置移动的位置
  proxy.$refs["currentProgress"].style.width = MoveX + "px";
  proxy.$refs["circle"].style.left =
    MoveX - proxy.$refs["circle"].clientWidth / 2 + "px";
}
//鼠标弹起
function handleMouseup() {
  setTimeout(() => {
    audio.value.play();
    paused.value = false;
    isMoveIn.value = false;
  }, 200);
}
//滑块按下
function handleMousedown() {
  audio.value.pause();
  paused.value = true;
  isMoveIn.value = true;
  let progress = proxy.$refs["progress"];
  //进度条 左 边距离页面左边的距离 移动最小值
  let moveMin = progress.offsetParent.offsetLeft + progress.offsetLeft;
  //进度条 右 边距离页面左边的距离 移动最大值
  let moveMax =
    progress.offsetParent.offsetLeft +
    progress.offsetLeft +
    progress.clientWidth;
  //小圆圈的宽度
  let circleWidth = proxy.$refs["circle"].clientWidth / 2;
  let move = (move) => {
    if (move.pageX >= moveMax) {
      return;
    } else if (move.pageX <= moveMin) {
      return;
    }
    proxy.$refs["circle"].style.left =
      move.pageX - moveMin - circleWidth + "px";
    updateProgress(move.pageX - moveMin);
  };
  //获取当前鼠标的位置 X
  document.addEventListener("mousemove", move);
  //鼠标弹起来
  document.addEventListener("mouseup", () => {
    document.removeEventListener("mousemove", move);
  });
}

//强制暂停
function stopAudio() {
  proxy.$refs["audio"].pause();
  paused.value = true;
}

// 播放速度
function changeSpeed(value) {
  audio.value.playbackRate = value;
}

function getDurationTime() {
  return proxy.$refs["audio"]?.duration;
}

defineExpose({
  stopAudio,
  getDurationTime,
});
</script>
<style lang="scss" scoped>
.card {
  display: inline-block;
  width: 80%;
  padding: 5px 10px;
  border-radius: 10px;
  vertical-align: top;

  .progress {
    height: 7px;
    border-radius: 3px;
    margin-bottom: 5px;
    width: 100%;
    background-color: #dadfea;
    cursor: pointer;

    .currentProgress {
      position: relative;
      height: 100%;
      width: 0;
      background-color: #336de7;
      border-radius: 3px;

      .circle {
        position: absolute;
        right: -6px;
        top: -2px;
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: 1px solid #336de7;
        background-color: #fff;

        &:hover {
          width: 12px;
          height: 12px;
          top: -3px;
          border-radius: 50%;
        }
      }
    }
  }

  .time {
    display: flex;
    justify-content: space-between;
    color: #777a85;
    font-size: 12px;
  }
}
.container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.option {
  display: inline-block;
  // padding: 20px 1px 0 1px;

  .play,
  .pre,
  .next {
    display: flex;
    padding: 0 2px;
    align-items: center;
    cursor: pointer;
  }
}

.el-icon {
  width: 24px;
  height: 24px;
}
:deep(.el-icon svg) {
  width: 28px;
  height: 28px;
}
.tip {
  text-align: left;
  font-size: 12px;
  color: red;
  letter-spacing: 2px;
}
</style>
