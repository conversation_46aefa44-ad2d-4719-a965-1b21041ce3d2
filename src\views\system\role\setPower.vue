<template>
  <el-dialog title="设置权限" v-model="open" width="500px" append-to-body>
    <el-form
      :model="form"
      ref="formRef"
      label-position="right"
      :rules="rules"
      :label-width="100"
    >
      <el-form-item label="备注" prop="roleRemarks">
        <el-input
          v-model="form.roleRemarks"
          type="textarea"
          placeholder="请输入备注"
          maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="菜单权限">
        <el-checkbox
          v-model="menuCheckStrictly"
          @change="handleCheckedTreeConnect"
          >父子联动</el-checkbox
        >
        <el-tree
          ref="treeRef"
          class="tree-border"
          :data="powerList"
          show-checkbox
          :props="defaultProps"
          check-on-click-node
          :check-strictly="!menuCheckStrictly"
          :render-after-expand="false"
          node-key="id"
        />
      </el-form-item>
    </el-form>
    <div class="text-center mt20">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" :loading="loading" plain @click="submit"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script setup name="SetPower">
import { getmenulist } from "@/api/menu";
import { editrole } from "@/api/system/roles";
const emit = defineEmits(["getList"]);
//全局配置
const { proxy } = getCurrentInstance();
//树列表
const powerList = ref([]);
const menuCheckStrictly = ref(false);
//等待提交
const loading = ref(false);
//表单
const data = reactive({
  form: {
    id: undefined,
    roleRemarks: undefined,
    menuIds: [],
  },
  rules: {
    roleRemarks: [
      {
        required: true,
        pattern:  /^\S*$/,
        message: "请不要输入空格！",
        trigger: "blur",
      },{ required: true, message: "请填写备注!", trigger: "blur" }
    ],
  },
});
const { form, rules } = toRefs(data);
//开启弹窗
const open = ref(false);
//权限列表配置
const defaultProps = {
  children: "children",
  label: "label",
};

//获取权限菜单
function getList(roleId) {
  getmenulist(roleId)
      .then((res) => {
        powerList.value = res.menus;
        let checkedKeys = [
          ...new Set([...res.checkedKeys, ...getMessageIdList(res.menus)]),
        ];
        form.value.menuIds = res.checkedKeys;
        checkedKeys.forEach((v) => {
          nextTick(() => {
            proxy.$refs["treeRef"].setChecked(v, true, false);
          });
        });
      })
      .catch(() => {});
}

//打开弹窗
function opendialog(data) {
  form.value.id = data.id;
  form.value.roleName = data.roleName;
  form.value.roleRemarks = data.roleRemarks;
  open.value = true;
  menuCheckStrictly.value = data.menuCheckStrictly;
  getList(data.id);
}

//获取消息中心数据
function getMessageIdList(menus) {
  const messageCheckedKeys = [];
  const disabledArr = [{
    "label": "消息中心",
    "children": [
      {
        "label": "首页信息",
        "children": [
          { "label": "标记为已读" },
          { "label": "删除消息" },
          { "label": "详情" }
        ]
      },
    ]
  }];
  getIdList(menus, messageCheckedKeys, disabledArr);
  return messageCheckedKeys;
}

function getIdList(menus, messageCheckedKeys, disabledArr) {
  disabledArr.forEach(item => {
    menus.forEach(menu => {
      if (item.label == menu.label) {
        menu.disabled = true
        messageCheckedKeys.push(menu.id)
        if (menu.children) {
          getIdList(menu.children, messageCheckedKeys, item.children)
        }
      }
    })
  })
}

//获取权限信息
function openSet() {
  let checkedKeys = proxy.$refs["treeRef"].getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = proxy.$refs["treeRef"].getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}

//设置父子联动
function handleCheckedTreeConnect(value) {
  menuCheckStrictly.value = value ? true : false;
}

//提交
function submit() {
  let checkedNode = openSet();
  form.value.menuIds = checkedNode;
  form.value.menuCheckStrictly = menuCheckStrictly.value;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      editrole(form.value)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          cancel();
          emit("getList");
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

//取消
function cancel() {
  open.value = false;
  form.value.roleRemarks = undefined;
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.el-tree.tree-border {
  height: 50vh;
  overflow: auto;
}
</style>
