import request from '@/utils/request'

//查询
export function findAiVoiceTaskList(query) {
  return request({
    url: '/aiVoiceTask/findAiVoiceTaskList',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}

// 任务名称下拉
export function getAiTaskNameList(query) {
  return request({
    url: '/aiVoiceTask/getAiTaskNameList',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}

// 任务状态下拉 
export function getTaskStatus(query) {
  return request({
    url: '/aiVoiceTask/getTaskStatus',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}


// 获取话术模板
export function getAiVoiceTplList(query) {
  return request({
    url: '/aiVoiceTask/getAiVoiceTplList',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}

// 提交不执行
export function AiVoiceCaseSubmitTaskData(data) {
  return request({
    url: '/case/AiVoiceCaseSubmitTaskData',
    method: 'post',
    data: data,
    gateway: 'cis'
  })
}

//执行
export function aiExecuteTask(data) {
  return request({
    url: '/aiVoiceTask/executeAiTask',
    method: 'post',
    data: data,
    gateway: 'cis'
  })
}

//暂停
export function aiSuspendTask(data) {
  return request({
    url: '/aiVoiceTask/suspendAiTask',
    method: 'post',
    data: data,
    gateway: 'cis'
  })
}

//撤销
export function aiRevokeTask(data) {
  return request({
    url: '/aiVoiceTask/revokeAiTask',
    method: 'post',
    data: data,
    gateway: 'cis'
  })
}

// 查看报告
export function aiCallTaskStatistics(query) {
  return request({
    url: '/aiVoiceTask/AiCallTaskStatistics',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}
export function aiCallTaskDataList(query) {
  return request({
    url: '/aiVoiceTask/callTaskDataList',
    method: 'get',
    params: query,
    gateway: 'cis'
  })
}

// 查看报告数据列表删除
export function deleteAiTask(data) {
  return request({
    url: '/aiVoiceTask/deleteAiTask',
    method: 'delete',
    data: data,
    params: data,
    gateway: 'cis'
  })
}