import request from '@/utils/request'

//根据勾选案件查找对应有预测试外呼坐席的催员id集合-（案件管理）
export function verifyCaseList(data) {
    return request({
        url: '/case/verifyCaseList',
        method: 'post',
        data: data,
    })
}

//创建Ai语音外呼任务-（案件管理） 提交不执行
export function AiVoiceCaseSubmitTaskData(data) {
    return request({
        url: '/case/AiVoiceCaseSubmitTaskData',
        method: 'post',
        data: data,
    })
}
//创建Ai语音外呼任务-（案件管理） 提交并执行
export function AiVoiceCaseSubmitTaskDataAndExecute(data) {
    return request({
        url: '/case/AiVoiceCaseSubmitTaskDataAndExecute',
        method: 'post',
        data: data,
    })
}

//创建Ai语音外呼任务-（我的案件） 提交不执行
export function myAiVoiceCaseSubmitTaskData(data) {
    return request({
        url: '/collection/myAiVoiceCaseSubmitTaskData',
        method: 'post',
        data: data,
    })
}
//创建Ai语音外呼任务-（我的案件） 提交执行
export function myAiVoiceCaseSubmitTaskDataAndExecute(data) {
    return request({
        url: '/collection/myAiVoiceCaseSubmitTaskDataAndExecute',
        method: 'post',
        data: data,
    })
}
//创建Ai语音外呼任务-（团队案件） 提交不执行
export function teamAiVoiceCaseSubmitTaskData(data) {
    return request({
        url: '/teamCase/teamAiVoiceCaseSubmitTaskData',
        method: 'post',
        data: data,
    })
}
//创建Ai语音外呼任务-（团队案件） 提交执行
export function teamAiVoiceCaseSubmitTaskDataAndExecute(data) {
    return request({
        url: '/teamCase/teamAiVoiceCaseSubmitTaskDataAndExecute',
        method: 'post',
        data: data,
    })
}


//创建AI语音任务-（案件管理） 
export function caseSubmitTaskData(data) {
    return request({
        url: '/case/caseSubmitTaskData',
        method: 'post',
        data: data,
    })
}

//预测试外呼创建任务统计案件数量-（案件管理）
export function selectCaseNumber(data) {
    return request({
        url: '/case/selectCaseNumber',
        method: 'post',
        data: data,
    })
}

//根据勾选案件查找对应有预测试外呼坐席的催员id集合-（我的案件）
export function myVerifyCaseList(data) {
    return request({
        url: '/collection/myVerifyCaseList',
        method: 'post',
        data: data,
    })
}

//创建预测试外呼任务-（我的案件）
export function myCaseSubmitTaskData(data) {
    return request({
        url: '/collection/myCaseSubmitTaskData',
        method: 'post',
        data: data,
    })
}

//预测试外呼创建任务统计案件数量-（我的案件）
export function mySelectCaseNumber(data) {
    return request({
        url: '/collection/mySelectCaseNumber',
        method: 'post',
        data: data,
    })
}

//根据勾选案件查找对应有预测试外呼坐席的催员id集合-（我的团队）
export function teamVerifyCaseList(data) {
    return request({
        url: '/teamCase/teamVerifyCaseList',
        method: 'post',
        data: data,
    })
}

//创建预测试外呼任务-（我的团队）
export function teamCaseSubmitTaskData(data) {
    return request({
        url: '/teamCase/teamCaseSubmitTaskData',
        method: 'post',
        data: data,
    })
}

//预测试外呼创建任务统计案件数量-（我的团队）
export function teamSelectCaseNumber(data) {
    return request({
        url: '/teamCase/teamSelectCaseNumber',
        method: 'post',
        data: data,
    })
}

