<template>
      <el-dialog
    title="继续跟进"
    v-model="open"
    width="600px"
    :before-close="cancelOpen"
    append-to-body
  >
  <el-form
      :model="form"
      class="mt10"
      label-position="right"
      :rules="rules"
      ref="formRef"
    >
      <el-form-item class="mar0" label="跟进内容" prop="workFollowContent">
        <el-input v-model="form.workFollowContent"  maxlength="300"
          show-word-limit rows="4" type="textarea" />
      </el-form-item>
    </el-form>
     <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" :loading="subloading"  @click="submit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import {
addFollowUp
} from "@/api/collection/myworkorder";
const { proxy } = getCurrentInstance();

//弹窗信息
const open = ref(false);
const subloading = ref(false);
const emit = defineEmits();
//表单信息
const data = reactive({
    form: {},
    rules: {
        workFollowContent: [
          {
            required: true,
            pattern:  /^\S*$/,
            message: "请不要输入空格！",
            trigger: "blur",
          },
          { required: true, message: '请输入跟进内容！', trigger: 'blur'}
        ],
    },
})
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog(id){
    open.value = true;
    form.value.id = id;
}

//取消
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    workFollowContent: undefined,
  };
}

//取消审核不通过
function cancel() {
  reset();
  subloading.value = false;
  open.value = false;
}

function submit(){
  subloading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
     let req = JSON.parse(JSON.stringify(form.value));
     req.id = parseInt(form.value.id)
     addFollowUp(req)
        .then((res) => {
            proxy.$modal.msgSuccess("跟进成功！");
            cancel();
            emit('getdetails');
        })
        .finally(() => {
        subloading.value = false;
        });
    } 
  });
}

defineExpose({
    opendialog
})


</script>
<style lang="scss" scoped>

</style>