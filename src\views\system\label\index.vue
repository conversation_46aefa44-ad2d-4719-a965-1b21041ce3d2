<template>
  <div class="app-container">
    <div class="stamp-item" v-for="(item, index) in stamps" :key="index">
      <el-icon :color="color[index]" class="icon-flag"><flag /></el-icon>
      <el-input
        class="mr10 ml10"
        v-model="item.labelContent"
        style="width: 200px"
        :disabled="!checkPermi(['system:label:edit'])"
        @blur="edit(item,index,0)"
      ></el-input>
      <el-switch
        v-model="item.stateLabel"
        active-color="#409eff"
        :active-value="1"
        :inactive-value="0"
        :disabled="!checkPermi(['system:label:status'])"
        @change="edit(item,index,1)"
      ></el-switch>
    </div>
  </div>
</template>

<script setup name="Label">
import { findIdLabel, setLabel} from "@/api/system/menu";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const route = useRoute();
const color = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];
const stamps = ref([]);

//获取标签列表
function getList(){
  findIdLabel()
    .then((res) => {
      stamps.value = res.data;
  })
  .catch(() => {});
}
provide("getList", Function, true);
getList();

//保存修改标签
function edit(data,index,flag) {
  if(data.labelContent.length == 0 && flag == 1) {
    stamps.value[index].stateLabel = stamps.value[index].stateLabel == 0? 1: 0;
    return proxy.$modal.msgWarning("请先输入标签内容再进行开启或关闭操作！")
  };
  if(data.labelContent.length == 0 && flag == 0){
    data.labelContent = '未标签';
    stamps.value[index].labelContent = '未标签';
    proxy.$modal.msgWarning("请输入标签名称！")
  }
  setLabel([data])
    .then((res) => {
      getList()
    })
    .catch(() => {});
}
</script>

<style scoped>
.stamp-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 15px;
  margin-left: 20px;
}
.icon-flag {
  font-size: 16px;
}
</style>
