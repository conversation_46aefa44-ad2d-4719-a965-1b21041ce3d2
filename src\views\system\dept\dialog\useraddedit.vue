<template>
    <el-dialog :title="title" v-model="open" width="650px" @close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" v-if="enter">
            <el-form-item label="员工姓名" prop="employeeName">
                <el-input
                 v-model="form.employeeName"
                 placeholder="请输入员工姓名"
                 style="width: 240px"
                />
            </el-form-item>
            <el-form-item label="所属部门" prop="departmentId">
                <tree-select
                    v-model:value="form.departmentId"
                    v-model:name="form.departments"
                    :options="deptOptions"
                    placeholder="请选择所属部门"
                    :objMap="{ value: 'id', label: 'name', children: 'children' }"
                    style="width: 240px"
                />
            </el-form-item>
            <el-form-item label="登录账号" prop="loginAccount">
                <el-input
                 v-model="form.loginAccount"
                 placeholder="请输入登录账号"
                 style="width: 240px"
                 :disabled="accountSign"
                />
            </el-form-item>
             <el-form-item
              label="账户密码"
              prop="password"
              v-if="!accountSign"
            >
              <el-input
                v-model="form.password"
                placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
                type="password"
                maxlength="20"
                style="width: 240px"
                show-password
                oncut="return false"
                onpaste="return false"
                oncopy="return false"
                onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
                oninput="value=value.replace(/[^\x00-\xff]/g, '')"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="phoneNumber">
                <el-input
                 v-model="form.phoneNumber"
                 maxlength="11"
                 placeholder="请输入手机号码"
                 style="width: 240px"
                />
            </el-form-item>
            <el-form-item label="员工角色" prop="roleId">
                <el-select 
                    v-model="form.roleId"
                    placeholder="选择角色"
                    clearable
                    filterable
                    :reserve-keyword="false"
                    style="width: 240px"
                >
                    <el-option 
                        v-for="item in roleOptions"
                        :key="item.id"
                        :label="item.roleName"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="账号状态" prop="accountStatus">
                <el-radio-group v-model="form.accountStatus">
                    <el-radio :label="0">启用</el-radio>
                    <el-radio :label="1">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="工作状态" prop="workingState">
                <el-radio-group v-model="form.workingState">
                    <el-radio :label="0">在职</el-radio>
                    <el-radio :label="1">离职</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="部门负责人" prop="departmentHead">
                <el-radio-group v-model="form.departmentHead">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submitForm" v-if="enter">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
        <div class="text-center reset-pwd" v-if="success">
        <div class="step-icon">
         <el-icon :size="64" color="#67c23a"><CircleCheckFilled /></el-icon>
        </div>
        <h2>新建成功！</h2>
        <!-- <p>默认密码：zws123456</p> -->
      </div>
    </el-dialog>
</template>

<script setup>
    import { deptTree } from '@/api/system/dept'
    import { getRoleOptions } from '@/api/system/roles'
    import { addUser, editUser } from '@/api/system/user'

    const { proxy } = getCurrentInstance();
    const emit = defineEmits(['getList']);

    const loading = ref(false);
    const title = ref('');
    const open = ref(false);
    const deptOptions = ref([]);
    const roleOptions = ref([]);
    const success=ref(false);
    const enter=ref(true);
    const accountSign = ref(false)
    const data = reactive({
        form: {
            accountStatus: 0,
            workingState: 0,
            departmentHead:0
        },
        rules: {
            employeeName:[{ required: true, message: '请输入员工姓名！', trigger: 'blur'}],
            departmentId: [{ required: true, message: '请选择所属部门！', trigger: 'change'}],
            loginAccount:[{ required: true, message: '请输入登陆账号！', trigger: 'blur'}],
            password: [
                {
                    required: true,
                    pattern:  /^\S*$/,
                    message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
                    trigger: "blur",
                },{
                    required: true,
                    pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
                    message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
                    trigger: "blur",
                }
            ],
            phoneNumber: [{ required: true, pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }],
            roleId: [{ required: true, message: '请选择角色！', trigger: 'change'}],
            accountStatus: [{ required: true, message: '请选择账号状态！', trigger: 'change'}],
            workingState: [{ required: true, message: '请选择工作状态！', trigger: 'change'}],
            departmentHead: [{ required: true, message: '请选择是否是部门负责人！', trigger: 'change'}],
        }
    })
    const { form, rules } = toRefs(data);

    //提交
    function submitForm() {
        loading.value = true;
        proxy.$refs["formRef"].validate(valid => {
            if (valid) {
                let req = JSON.parse(JSON.stringify(form.value));
                let spval = form.value.departmentId.split(':');
                if (spval[0] == 'Create') {
                    req.departmentId = undefined;
                    req.departments = undefined;
                } else {
                    req.departmentId = spval[1]
                }
                for (let i = 0; i < roleOptions.value.length; i++) {
                    const item = roleOptions.value[i];
                    if (form.value.roleId == item.id) {
                        req.theRole = item.roleName;
                        break
                    }
                }
                if (form.value.id) { //编辑
                    editUser(req).then(res => {
                        proxy.$modal.msgSuccess('修改成功！');
                        cancel();
                        emit('getList');
                    }).finally(() => {
                        loading.value = false;
                    })
                } else { //新建
                    addUser(req).then(res => {
                        // proxy.$modal.msgSuccess('新建成功！');
                        success.value =true;
                        enter.value=false;
                        // cancel();
                        emit('getList');
                    }).finally(() => {
                        loading.value = false;
                    })
                }
            } else {
                loading.value = false;
            }
        })
    }

    //取消
    function cancel() {
        reset();
        open.value = false;
        loading.value = false;
        success.value = false;
        enter.value=true;
    }

    //重置表单
    function reset() {
        proxy.resetForm("formRef");
        form.value = {
            employeeName: undefined,
            departmentId: undefined,
            departments: undefined,
            loginAccount: undefined,
            phoneNumber: undefined,
            roleId: undefined,
            accountStatus: 0,
            workingState: 0,
            departmentHead:0
        }
    }

    //打开弹窗
    function opendialog(data) {
        deptTree().then(res => {
            deptOptions.value = res.data[0].children || [];
            getRoleOptions().then(res => {
                reset();
                roleOptions.value = res.data;
                if (data && data.id) {
                    title.value = '修改用户'
                    accountSign.value = true;
                    form.value = JSON.parse(JSON.stringify(data));
                    if (form.value.departmentId) {
                        form.value.departmentId = 'Dept:'+data.departmentId
                    } else {
                        form.value.departmentId = undefined;
                    }
                }else 
                if(data && data.departmentId){
                    accountSign.value = false;
                    form.value.departmentId = 'Dept:'+data.departmentId;
                    title.value = '新建用户'
                }else {
                    accountSign.value = false;
                    title.value = '新建用户'
                }
                open.value = true
            })
        })
    }

    defineExpose({
        opendialog
    })
</script>

<style lang="scss" scoped>
   
</style>