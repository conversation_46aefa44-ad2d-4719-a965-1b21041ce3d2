<template>
    <el-form :class="`${showSearch ? 'h-auto' : 'h-50'}`" :inline="props.inline" :model="props.form" :rules="rules"
        :label-width="props.labelWidth" ref="formRef">
        <el-form-item v-for="(item, index) in props.formArr" :key="index" :label="item?.label" :prop="item?.prop">
            <Fragment v-if="!item?.slot">
                <!-- 输入框 -->
                <el-input v-model="props.form[item?.prop]" v-if="item?.type == 'input'"
                    :disabled="item?.options?.disabled" :onkeyup="inputRestrictRule(item?.options?.ruleType)"
                    :oninput="inputRestrictRule(item?.options?.ruleType)" @change="item?.func?.change"
                    :placeholder="`请输入${item?.label}`" :style="item?.options?.style || props?.itemStyle" />
                <!-- 选择框-下拉框 -->
                <el-select v-model="props.form[item?.prop]" v-if="item?.type == 'select'"
                    :multiple="item?.options?.multiple" :disabled="item?.options?.disabled"
                    :placeholder="`请选择${item?.label}`" collapse-tags filterable clearable
                    :style="item?.options?.style || props?.itemStyle" @blur="item?.func?.blur"
                    @change="item?.func?.change" @focus="item?.func?.focus" @clear="item?.func?.clear"
                    @remove-tag="item?.func?.removeTag" @visible-change="item?.func?.visibleChange">
                    <el-option v-for="(item2, index2) in props.optionData[item?.prop]" :key="index2"
                        :label="item2[item?.optionEnum?.label] || item2"
                        :value="item2[item?.optionEnum?.value] || item2" />
                </el-select>
                <!-- 单选框 -->
                <el-radio-group v-model="props.form[item?.prop]" v-if="item?.type == 'radio'"
                    :disabled="item?.options?.disabled" @change="item?.func?.change">
                    <el-radio v-for="(item2) in props.optionData[item?.prop]" :key="item2[item?.optionEnum?.value]"
                        :label="item2[item?.optionEnum?.value]">
                        {{ item2[item?.optionEnum?.label] }}
                    </el-radio>
                </el-radio-group>
                <!-- 日期选择器 -->
                <el-date-picker v-model="props.form[item?.prop]" v-if="item?.type == 'picker'" start-placeholder="开始日期"
                    end-placeholder="结束日期" range-separator="-" :disabled="item?.options?.disabled"
                    @change="item?.func?.change" :placeholder="`请选择${item?.label}`"
                    :value-format="`${item.options.valueFormat ? item.options.valueFormat : 'YYYY-MM-DD'}`"
                    :style="item?.options?.style || props?.itemStyle" :type="item?.options?.type" />
                <!-- 开关 -->
                <el-switch v-model="props.form[item?.prop]" v-if="item?.type == 'switch'"
                    :style="item?.options?.style || props?.itemStyle" :disabled="item?.options?.disabled"
                    :active-value="item?.activeValue" :active-text="item?.options?.activeText"
                    :inactive-text="item?.options?.inactiveText" :inactive-value="item?.inactiveValue" />
                <!-- 级联选择 -->
                <el-cascader v-model="props.form[item?.prop]" v-if="item?.type == 'cascader'"
                    :multiple="item?.options?.multiple" :disabled="item?.options?.disabled"
                    :props="item?.cascader?.props" clearable collapse-tags filterable
                    :popper-class="item?.cascader?.popperClass" :placeholder="`请选择${item?.label}`"
                    :style="item?.options?.style || props?.itemStyle" :options="props.cascaderData[item?.prop]"
                    @blur="item?.func?.blur" @expand-change="item?.func?.expandChange" @change="item?.func?.change"
                    @focus="item?.func?.focus" @visible-change="item?.func?.visibleChange"
                    @remove-tag="item?.func?.removeTag" />
                <div class="range-scope" :style="item?.options?.style || props?.itemStyle"
                    v-if="item?.type == 'range-scope'">
                    <el-input v-model="props.form[item?.prop1]" />
                    <span>-</span>
                    <el-input v-model="props.form[item?.prop2]" />
                </div>
            </Fragment>
            <slot :name="item.prop" :data="item" />
        </el-form-item>
        <el-form-item v-if="props.isQuerySlot">
            <slot name="query" />
        </el-form-item>
    </el-form>
</template>
<script setup>
// 全局变量
const { proxy } = getCurrentInstance()
const props = defineProps({
    form: { type: Object, default: {} },// 表单获取的数据
    rules: { type: Object, default: {} }, // 表单校验规则
    inline: { type: Boolean, default: false },// 是否横向
    labelWidth: { type: String },// 表单中标签的宽度
    itemStyle: { type: Object, default: {} },// 表单中标签的宽度
    formArr: { type: Array, default: [] },// 表单的配置
    optionData: { type: Object, default: {} },// 下拉的数据
    cascaderData: { type: Object, default: {} },// 下拉的数据
    isQuerySlot: { type: Boolean, default: false },// 是否需要查询插槽
    showSearch: { type: Boolean, default: true }
})
// 校验规则
function checkRule() {
    return new Promise((resolve, reject) => {
        proxy.$refs['formRef'].validate((valid) => {
            if (valid) {
                resolve(true)
            }
        })
    })
}
// 输入框规则
function inputRestrictRule(ruleType) {
    const funcObj = {
        1: 'value = value.replace(/\\s/g, "")',// 去除空格
        2: "value = value.replace(/[^\x00-\xff]|\\s/g, '')", // 去除中文、空格
        3: "value = value.replace(/[^\x00-\xff]|[A-z]|\\s/g, '')", // 去除中文、字母、空格
        4: "value = value.replace(/[^\x00-\xff]|\\d|\\s/g, '')", // 去除中文、数字、空格
    }
    return funcObj[ruleType]
}

defineExpose({ checkRule })
</script>
<style lang="scss" scoped>
.range-scope {
    display: flex;
    align-items: center;

    &>span {
        margin: 0 10px;
    }

    .el-input {
        flex: 1;
    }
}

.h-50 {
    overflow: hidden;
    height: 50px;
}

.h-auto {
    height: auto !important;
}
</style>