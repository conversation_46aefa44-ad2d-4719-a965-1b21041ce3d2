<template>
  <div>
    <el-form
      :inline="true"
      :model="queryParams"
      ref="queryRef"
      label-width="80px"
    >
      <el-form-item label="主叫号码" prop="callingNumber">
        <el-input
          v-model="queryParams.callingNumber"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被叫号码" prop="calledNumber">
        <el-input
          v-model="queryParams.calledNumber"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="callType">
        <el-select
          v-model="queryParams.callType"
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option label="手动呼叫" value="callOutManual"></el-option>
          <el-option label="API点击呼叫" value="clickCallOut"></el-option>
          <el-option label="呼入" value="inbound"></el-option>
          <el-option label="预测试外呼" value="newauto"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="呼叫时间" prop="startTime">
        <el-date-picker
          v-model="startTime"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="通话时长">
        <div class="range-scope" style="width: 240px">
          <NumberInput v-model="queryParams.duration1" :decimals="0" />
          <span>-</span>
          <NumberInput v-model="queryParams.duration2" :decimals="0" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <div class="mb20">
      <el-radio-group v-model="engageType" @change="engageTypeChange">
        <el-radio-button label="y">接通</el-radio-button>
        <el-radio-button label="n">未接通</el-radio-button>
        <el-radio-button label="全部">全部</el-radio-button>
      </el-radio-group>
    </div>
    <div class="mb20">
      <el-alert
        title="注：默认只展示当月的通话记录。最多可查询近3个月内的明细记录！"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="案件ID" prop="caseId" align="center" />
      <el-table-column label="呼叫时间" prop="createTime" align="center" />
      <el-table-column
        label="通话时长"
        prop="duration"
        align="center"
        :formatter="callTime"
      />
      <el-table-column label="主叫号码" prop="callingNumber" align="center" />
      <el-table-column label="被叫号码" prop="calledNumber" align="center" />
      <el-table-column label="外显号码" prop="explicitNumber" align="center" />
      <el-table-column
        label="呼叫类型"
        prop="callType"
        align="center"
        :formatter="ChangeCallType"
      />
      <el-table-column label="操作" fixed="right" :width="200">
        <template #default="{ row, $index }">
          <musicPlayer
            v-if="row.recording && row.recording != ''"
            :ref="`audioRef${row.recording}`"
            :audioSrc="getaudioSrc(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { callRecordList } from "@/api/mediation/application";
const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    callingNumber: undefined,
    calledNumber: undefined,
    callType: undefined,
    duration1: undefined,
    duration2: undefined,
  },
});

const { queryParams } = toRefs(data);
const startTime = ref(undefined);
const engageType = ref("全部");

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const getList = () => {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value));
  if (startTime.value) {
    Object.assign(reqForm, {
      startTime1: startTime.value[0],
      startTime2: startTime.value[1],
    });
  }

  if (engageType.value !== "全部") {
    Object.assign(reqForm, {
      engageType: engageType.value,
    });
  }

  loading.value = true;
  callRecordList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    callingNumber: undefined,
    calledNumber: undefined,
    callType: null,
    duration1: undefined,
    duration2: undefined,
  };
  startTime.value = undefined;
  getList();
};

const engageTypeChange = () => {
  handleQuery();
}

//通话时长
const callTime = (row) => {
  let recordurl = row.recording;
  let val = row.duration;
  if (recordurl == "") {
    return "--";
  }
  let time = Number(val);
  let h, m, s;
  if (time > 60) {
    m = parseInt(time / 60);
    s = time % 60;
    if (m > 60) {
      h = parseInt(m / 60);
      m = m % 60;
    }

    h = h > 0 ? h + "时" : "";
    m = m > 0 ? m + "分" : s > 0 ? "0分" : "";
    s = s > 0 ? s + "秒" : "";
    return h + m + s;
  } else if (time <= 0) {
    return "--";
  } else {
    return time + "秒";
  }
};

const ChangeCallType = (row) => {
  // 手动呼叫：callOutManual，API点击呼叫：clickCallOut，呼入：inbound，预测试外呼：newauto
  return {
    callOutManual: "手动呼叫",
    clickCallOut: "API点击呼叫",
    inbound: "呼入",
    newauto: "预测试外呼",
  }[row.callType] || "--";
}

const getaudioSrc = (row) => {
  return import.meta.env.VITE_APP_RECORD_URL + row.recording;
};
</script>

<style scoped></style>
