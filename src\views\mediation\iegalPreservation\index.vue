<template>
  <div class="app-container">
    <el-form :model="queryParams" :loading="loading" ref="queryRef" :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
      inline label-width="110px">
      <el-form-item prop="caseId" label="案件ID">
        <el-input v-model="queryParams.caseId" style="width: 240px" placeholder="请输入案件ID" />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input v-model="queryParams.clientName" style="width: 240px" placeholder="请输入被告" />
      </el-form-item>
      <el-form-item prop="court" label="保全法院">
        <el-select placeholder="请选择保全法院" style="width: 240px" v-model="queryParams.court" @focus="getclosed">
          <el-option v-for="item in courtOptions" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="startDate" label="保全申请时间">
        <el-date-picker style="width: 240px" v-model="queryParams.startDate" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" unlink-panels />
      </el-form-item>
      <el-form-item label="申请保全金额">
        <div class="range-scope" style="width:240px">
          <el-input v-model="queryParams.applyAmount1" />
          <span>-</span>
          <el-input v-model="queryParams.applyAmount2" />
        </div>
      </el-form-item>
      <el-form-item label="实际保全金额">
        <div class="range-scope" style="width:240px">
          <el-input v-model="queryParams.actualAmount1" />
          <span>-</span>
          <el-input v-model="queryParams.actualAmount2" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area">
      <el-button v-if="checkPermi([setPermiss('download')])" :disabled="selectedArr.length == 0" type="primary"
        @click="downloadCase">批量导出</el-button>
      <el-button v-if="checkPermi([setPermiss('transfer')])" :disabled="selectedArr.length == 0" type="primary"
        @click="handleOpenDialog('transferCaseRef')">案件流转</el-button>
      <el-button v-if="checkPermi([setPermiss('sendNote')])" :disabled="selectedArr.length == 0" type="primary" 
        @click="handleOpenDialog('sendMessageRef')">发送短信</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <div class="mb10 hint">
      <selectedAll ref="SelectedAllRef" :dataList="dataList" v-model:all-query="allQuery" :selected-arr="selectedArr">
        <template #content>
          <div class="text-flex ml20">
            <span>案件数量：</span>
            <span class="text-danger mr10">{{ statistics?.caseNum || 0 }}</span>
            <span>保全金额：</span>
            <span class="text-danger mr10">
              {{ numFilter(statistics?.totalMoney || 0) }}
            </span>
          </div>
        </template>
      </selectedAll>
    </div>

    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="selectionChange">
      <el-table-column type="selection" :selectable="selectable" align="right" />
      <el-table-column v-if="columns[0].visible" label="案件ID" align="left" key="caseId" prop="caseId">
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
            </el-tooltip>
            <el-button :disable="!checkPermi([setPermiss('detail')])" type="text" @click="toDetails(row, $index)">
              {{ row.caseId }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[1].visible" label="被告" align="center" key="clientName" prop="clientName"
        :min-width="90" />
      <el-table-column v-if="columns[2].visible" label="手机号码" align="center" key="clientPhone" prop="clientPhone"
        width="110px">
        <template #default="{ row }">
          <div>
            <span>{{ row.clientPhone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
            <workPhoneVue :phoneNumber="row.clientPhone" :caseId="row.caseId" :borrower="row.clientName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[3].visible" label="身份证号码" align="center" key="clientIdcard" prop="clientIdcard"
        :width="180" />
      <el-table-column v-if="columns[4].visible" label="户籍地" align="center" key="clientCensusRegister"
        prop="clientCensusRegister" min-width="180px" />
      <el-table-column v-if="columns[5].visible" label="诉保状态" align="center" key="saveStage" prop="saveStage"
        width="150px" show-overflow-tooltip />
      <el-table-column v-if="columns[6].visible" label="申请保全金额" align="center" key="freezeAmount" prop="freezeAmount"
        :width="120">
        <template #default="{ row }">
          <span>{{ numFilter(row.freezeAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[7].visible" label="实际保全金额" align="center" key="actualFreezeAmount"
        prop="actualFreezeAmount" :width="120">
        <template #default="{ row }">
          <span>{{ numFilter(row.actualFreezeAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[8].visible" label="保全标的物" align="center" key="freezeAssets" prop="freezeAssets"
        min-width="150px" >
        <template #default="{ row }">
          <span>{{ numFilter(row.freezeAssets) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[9].visible" label="保全法院" align="center" key="court" prop="court" min-width="150px"
        show-overflow-tooltip />
      <el-table-column v-if="columns[10].visible" label="保全期限开始时间" align="center" key="startFreeze" prop="startFreeze"
        width="180px" />
      <el-table-column v-if="columns[11].visible" label="保全期限结束时间" align="center" key="endFreeze" prop="endFreeze"
        width="180px" />
      <el-table-column v-if="columns[12].visible" label="跟进人员" align="center" key="follower" prop="follower"
        :width="120" />
      <el-table-column v-if="columns[13].visible" label="最近一次跟进时间" align="center" key="followUpTime" prop="followUpTime"
        width="160px" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <transferCase :getList="getList" ref="transferCaseRef" />
    <!-- 发送短信 -->
    <sendMessage :getList="getList" ref="sendMessageRef" />
  </div>
</template>

<script setup name="IegalPreservation">
import { checkPermi } from "@/utils/permission";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import applyKeep from '@/views/mediation/dialog/applyKeep';
import transferCase from '@/views/mediation/dialog/transferCase';
import { formatParams } from "@/utils/common";
import { totalMoneyFreeze, getFreezeList } from "@/api/mediation/iegalPreservation";
import { getCourtOptions } from "@/api/common/common";
import { pageTypeEnum } from "@/utils/enum";

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const pageType = 'iegalPreservation'
const stageObj = {
  1: '材料提交',
  2: '待缴纳保证金',
  3: '已缴纳保证金,待上传回执单',
  4: '已财产保全',
}
const showSearch = ref(false)
const { queryParams } = toRefs(data);
const dataList = ref([]);
const allQuery = ref(false)
const selectedArr = ref([])
const rangFields = ['startDate'];

const courtOptions = ref([])
const statistics = ref({ totalMoney: 0, principal: 0, caseNum: 0 })
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '手机号码', visible: true },
  { key: 3, label: '身份证号码', visible: true },
  { key: 4, label: '户籍地', visible: true },
  { key: 5, label: '诉保状态', visible: true },
  { key: 6, label: '申请保全金额', visible: true },
  { key: 7, label: '实际保全金额', visible: true },
  { key: 8, label: '保全标的物', visible: true },
  { key: 9, label: '保全法院', visible: true },
  { key: 10, label: '保全期限开始时间', visible: true },
  { key: 11, label: '保全期限结束时间', visible: true },
  { key: 12, label: '跟进人员', visible: true },
  { key: 13, label: '最近一次跟进时间', visible: true },
])
//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = []
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.sign = pageTypeEnum[pageType]
  reqForm.saveStage = stageObj[route.meta.query]
  reqForm.condition = allQuery.value
  getFreezeList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}

function handleOpenDialog(refName, row) {
  const freezeIds = row ? [row.id] : selectedArr.value.map(item => item.id)
  const newAllQuery = row ? false : allQuery.value
  const query = { ...getReqParams(), allQuery: newAllQuery, freezeIds }
  delete query.ids
  delete query.caseIds
  const data = { query, ...row, freezeIds, allQuery: newAllQuery, pageType, isBatchSend: 1, caseId: row?.caseId }
  data.condition = row ? false : allQuery.value
  data.oneStatus = '诉讼保全'
  proxy.$refs[refName].openDialog(data)
}


//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  handleQuery();
}

// 选择
function selectionChange(selection) {
  selectedArr.value = selection; // 选中的列表
}

// 是否可选
function selectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      saveStage: stageObj[route.meta.query],
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: stageObj[route.meta.query], freezeId: row.id }
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}
//导出数据
function downloadCase() {
  const reqForm = getReqParams()
  proxy.downloadforjson('/freeze/exportWithFreeze', reqForm, `诉讼保全_${+new Date()}.xlsx`)
}

watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0, };
      reslove()
      return false
    }
    nextTick(() => {
      const reqForm = getReqParams()
      delete reqForm.ids
      delete reqForm.caseIds
      if (!allQuery.value) {
        reqForm.freezeIds = selectedArr.value.map(item => item.id)
      }
      totalMoneyFreeze(reqForm).then((res) => {
        statistics.value = {
          caseNum: res.data?.size,
          totalMoney: res.data?.money,
          principal: res.data?.principal,
        };
      }).finally(() => reslove());
    })
  })
}
getCourtOptionsFun()
function getCourtOptionsFun() {
  getCourtOptions().then(res => {
    courtOptions.value = res.data
  })
}

// 设置权限符
function setPermiss(val) {
  const permissBtn = {
    1: 'iegalPreservation:MaterialSubmission:',
    2: 'iegalPreservation:payASecurityDeposit:',
    3: 'iegalPreservation:depositPaid:',
    4: 'iegalPreservation:preservedProperty:',
  }
  return `${permissBtn[route.meta.query]}${val}`
}
const htrxCall = computed(() => store.getters.htrxCall);
// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.condition = allQuery.value
  reqForm.sign = pageTypeEnum[pageType]
  reqForm.saveStage = stageObj[route.meta.query]
  return reqForm
}
provide("getList", Function, true);

watch(() => route, () => {
  resetQuery()
}, { immediate: true, deep: true })

</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.table-title-question {
  display: inline-block;
  margin-left: 5px;
  color: var(--theme);
}
</style>
