import request from '@/utils/request'
// 根据条件查询机构信息
export function getCourtList(query) {
    return request({
        url: '/court/manage/list',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}
// 根据主键id查询机构信息
export function getCourtById(query) {
    return request({
        url: '/court/manage/listById',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}

// 添加机构
export function addCourt(data) {
    return request({
        url: '/court/manage/insert',
        method: 'post',
        data: data,
		gateway: 'sign'
    })
}

// 修改机构信息
export function updateCourt(data) {
    return request({
        url: '/court/manage/update',
        method: 'post',
        data: data,
		gateway: 'sign'
    })
}

// 删除机构信息
export function delCourt(data) {
    return request({
        url: '/court/manage/delete',
        method: 'post',
        data: data,
		gateway: 'sign'
    })
}

// 根据机构id查询文书模板信息
export function getWritTemplateByCourtId(query) {
    return request({
        url: '/court/manage/selectByCourtId',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}

// 查询启用的文书模板信息
export function listNotPaging(query) {
    return request({
        url: '/court/manage/listNotPaging',
        method: 'get',
        params: query,
		gateway: 'sign'
    })
}

// 根据条件查询机构信息
// export function getAgencyTypeOptions(query) {
//     return request({
//         url: '/agency/getAgencyTypeOptions',
//         method: 'get',
//         params: query
//     })
// }

