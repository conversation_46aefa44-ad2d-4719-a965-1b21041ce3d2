<template>
    <div class='wrap'>
        <!-- 分案 分配机构 -->
        <el-row class="mt10" :gutter="20">
            <el-col :span="6">
                <el-input class="mb8" v-model="filterText" placeholder="请输入关键字" />
                <el-tree ref="treeRef" class="filter-tree" show-checkbox :data="data" :props="defaultProps"
                    :expand-on-click-node="false" check-on-click-node :render-after-expand="false" node-key="id"
                    @check-change="getChecked" :filter-node-method="filterNode" />
            </el-col>
            <el-col :span="18">
                <div class="text-left" style="line-height: 32px">已选人员（{{ tableList.length }}）</div>
                <el-table :data="tableList" max-height="335" border>
                    <el-table-column v-if="point" label="员工工号" prop="jobNumber" align="center" />
                    <el-table-column label="处置人员姓名" prop="jobName" align="center" />
                    <el-table-column :label="colLable" align="center">
                        <template #default="{ row }">
                            <el-input v-if="isjointDebt" v-model="row.jointDebNumber" placeholder="请输入共债人数" />
                            <el-input v-else-if="type === 1" v-model="row.caseNum" placeholder="请输入案件数量" />
                            <el-input v-else-if="type === 2" v-model="row.caseNumPercentage" placeholder="请输入案件数量(%)" />
                            <el-input v-else-if="type === 3" v-model="row.caseMoney" placeholder="请输入案件金额" />
                            <el-input v-else-if="type === 4" v-model="row.caseMoneyPercentage"
                                placeholder="请输入案件金额(%)" />
                        </template>
                    </el-table-column>
                    <el-table-column v-if="isCase" label="委案批次号" width="180" align="center">
                        <template #default="{ row }">
                            <el-input v-model="row.entrustingCaseBatchNum" />
                        </template>
                    </el-table-column>
                    <el-table-column label="退案时间" width="240" align="center">
                        <template #default="{ row }">
                            <el-date-picker v-model="row.returnCaseDate" style="width:80%"
                                :disabled-date="disabledDateReturn" type="date" value-format="YYYY-MM-DD"
                                format="YYYY-MM-DD" placeholder="请选择退案时间" />
                        </template>
                    </el-table-column>
                    <el-table-column v-if="operation" label="操作" align="center">
                        <template #default="{ row }">
                            <el-button type="text" @click="openSet(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { getTeamTree } from '@/api/team/team'
const { proxy } = getCurrentInstance();
const typeFileds = ['', 'caseNum', 'caseNumPercentage', 'caseMoney', 'caseMoneyPercentage']; //提交数据key
const numKey = ['', 'caseNum', '', 'caseMoney']; //被分配数据量额度key

const props = defineProps({
    type: {
        required: true,
        type: Number
    },
    queryRes: {
        type: Object
    },
    isjointDebt: {
        type: Boolean,
        default: false
    },
    isCase: {
        type: Boolean,
        default: false
    },
    operation: {
        type: Boolean,
        default: false
    },
    point: {
        type: Boolean,
        default: false
    },
    unAssigned: {
        type: Number,
        required: true,
    },
    returnCaseDateRequired: {
        type: Boolean,
        default: false
    }
})

const filterText = ref('');
const defaultProps = {
    children: 'children',
    label: 'name',
}
const data = ref([]);//树结构
const filterData = ref([]);
const tableList = ref([]);
const totalData = ref(0); //分配总量数值


watch(filterText, (val) => {
    filterData.value = []
    filterDataFun(data.value, filterText.value)
    proxy.$refs["treeRef"].filter(val);
    nextTick(() => {
        if (tableList.value.length == 0) {
            proxy.$refs["treeRef"].setCheckedKeys([])
        } else {
            proxy.$refs["treeRef"].setCheckedKeys([])
            tableList.value.forEach(item => {
                proxy.$refs["treeRef"].setChecked(item.id, true)
            })
        }
    })
}, { deep: true })

watch(props, (newval) => {  //分配模式切换，数据变动
    if (props.isjointDebt) {
        totalData.value = newval.queryRes.jointDebtNum;
    } else if (newval.type == 2 || newval.type == 4) {
        totalData.value = 100;
    } else {
        totalData.value = props.unAssigned == 1 ? newval.queryRes['assignedNum'] : newval.queryRes[numKey[newval.type]];
    }
    autoWrite(newval);
})

//自动分配数量
function autoWrite(newval) {
    let amount = tableList.value.length
    if (amount > 0) {
        let average = Math.trunc(totalData.value / amount);
        let leftover = totalData.value % amount;
        leftover = Math.round((leftover + Number.EPSILON) * 100) / 100;
        tableList.value.forEach((item, index) => {
            if (props.isjointDebt) { //共债
                item.caseNum = undefined;
                item.caseNumPercentage = undefined;
                item.caseMoney = undefined;
                item.caseMoneyPercentage = undefined;

                item.jointDebNumber = 0;
                item.jointDebNumber += average;
                if (index === 0) {
                    item.jointDebNumber += leftover
                }
            } else {
                newval.type == 1 || (item.caseNum = undefined);
                newval.type == 2 || (item.caseNumPercentage = undefined);
                newval.type == 3 || (item.caseMoney = undefined);
                newval.type == 4 || (item.caseMoneyPercentage = undefined);

                item[typeFileds[newval.type]] = 0;
                item[typeFileds[newval.type]] += average
                if (index === 0) {
                    const decimalsNum = leftover - parseInt(leftover)
                    if (decimalsNum > 0) {
                        item[typeFileds[newval.type]] += decimalsNum
                    }
                }
            }
        })
        if (parseInt(leftover) > 0) {
            for (let i = 1; i <= parseInt(leftover); i++) {
                tableList.value[i - 1][typeFileds[newval.type]] += 1
            }
        }
    }
}

//表格label
const colLable = computed(() => {
    let title = props.isjointDebt ? '共债人数' : (props.type == 1 || props.type == 2 ? '案件数量' : '案件金额');
    title += props.isjointDebt ? '' : (props.type == 2 || props.type == 4 ? '(%)' : '');
    if (!props.isjointDebt) {
        tableList.value.map(item => {
            typeFileds.map((list, index) => {
                if (index > 0) {
                    item[list] = index == props.type ? item[list] : undefined;
                }
            })
        })
    }
    return title
})

getTeamTree().then(res => {
    data.value = res.data
})

//机构过滤
const filterNode = (value, treeData) => {
    return !value || treeData.name.indexOf(value) !== -1
}

// 获取员工数据
function filterDataFun(data, value) {
    data.forEach(i1 => {
        if (i1.id.indexOf('Dept:') == -1 && i1.name.indexOf(value) !== -1) {
            filterData.value.push(i1)
        }
        if (Array.isArray(i1.children) && i1.children.length > 0) {
            filterDataFun(i1.children, value)
        }
    })
}

//获取选中节点
function getChecked(node, ischecked) {
    if (node.id.indexOf('Dept') == -1) {
        if (ischecked) { //选中状态
            let obj = JSON.parse(JSON.stringify(node));
            typeFileds.map((item) => {
                obj[item] = undefined;
            })
            obj.jobName = node.name;
            obj.jobNumber = node.employeesWorking;
            obj.entrustingCaseBatchNum = node.label + (new Date().getTime()); //委案批次
            obj.jointDebNumber = undefined; //共债人数
            tableList.value.push(obj)
        } else { //取消选中
            tableList.value.map((item, index) => {
                if (item.id === node.id) {
                    tableList.value.splice(index, 1)
                }
            })
        }
    }
    autoWrite(props)
}

//验证数据
function validate() {
    return new Promise(reslove => {
        let num = 0;
        try {
            if (tableList.value.length === 0) {
                proxy.$modal.msgWarning('请选择左侧机构！')
                reslove(false)
                throw new Error("请选择左侧机构！");
            }
            let key = typeFileds[props.type];
            if (props.isjointDebt) {
                key = 'jointDebNumber'
            }
            tableList.value.forEach(item => {
                if (item[key] == 0) {
                    proxy.$modal.msgWarning('请填写大于0的整数！')
                    reslove(false)
                    throw new Error("请填写大于0的整数！");
                }
                if (!item[key]) {
                    proxy.$modal.msgWarning('请将表格数据填写完整！')
                    reslove(false)
                    throw new Error("请将表格数据填写完整！");
                } else {
                    const reg = /^[0-9]*[1-9][0-9]*$/;
                    const fixedMoneyReg = /^[0-9]+(\.[0-9]{1,2})?$|^0+[0-9]+(\.[0-9]{1,2})?$/
                    if (key == "caseMoney" && !fixedMoneyReg.test(item[key])) {
                        proxy.$modal.msgWarning('请输入正确金额，最多保留两位小数！')
                        reslove(false)
                        throw new Error("请输入正确金额，最多保留两位小数！");
                    } else if (key !== "caseMoney" && !reg.test(item[key])) {
                        proxy.$modal.msgWarning('请输入正整数！')
                        reslove(false)
                        throw new Error("请输入正整数！");
                    } else {
                        num += item[key] * 1
                    }
                }
                if (props.returnCaseDateRequired) {
                    if (!item.returnCaseDate) {
                        proxy.$modal.msgWarning('请输入退案日期！');
                        reslove(false);
                        throw new Error("请输入退案日期");
                    }
                }
                item.teamId = item.id.split(':')[1]
            })
        } catch (error) {
            if (error.message) {
                throw error
            }
        }
        if (num > totalData.value) {
            proxy.$modal.msgWarning('可分配案件案件数量不够了～')
            reslove(false)
        } else {
            reslove(true)
        }
    })
}

//数据暴露
async function exposeData() {
    let res = await validate();
    if (!res) return false;
    return new Promise(reslove => {
        console.log(tableList.value)
        reslove(tableList.value)
    })
}

function openSet(row) {
    let checkedNode = proxy.$refs['treeRef'].getCheckedKeys(false);
    let setNodeStatus = checkedNode.forEach(item => {
        if (item == row.id) {
            proxy.$refs['treeRef'].setChecked(item, false, false);
        }
    })
}
function disabledDateReturn(time) {
    return time.getTime() < Date.now() - 8.64e7;
}
defineExpose({ exposeData })
</script>

<style {row}d>
.wrap {
    min-height: 300px;
    padding-bottom: 20px;
}

.filter-tree {
    height: 319px;
    overflow: auto;
}
</style>