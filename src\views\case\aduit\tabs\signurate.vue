<template>
  <div class="mt20">
    <div class="form">
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="h-50" label-position="right"
        :class="{ 'h-auto': showSearch }" label-width="120px">
        <el-form-item label="模板类型" prop="classifyIds">
          <el-select v-model="classifyIdsList" placeholder="请选择模板类型" clearable filterable multiple collapse-tags
            collapse-tags-tooltip :reserve-keyword="false" @focus="getTemplate" style="width: 255px">
            <el-option v-for="item in classifyList" :key="item.dictLabel" :label="item.dictLabel"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板名称">
          <el-input v-model="queryParams.templateName" placeholder="模板名称" style="width: 255px" />
        </el-form-item>
        <el-form-item label="批次号">
          <el-input v-model="queryParams.batchNum" placeholder="批次号" style="width: 255px" />
        </el-form-item>
        <el-form-item label="签章状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择签章状态" clearable filterable style="width: 255px">
            <el-option v-for="item in signatureStatusList" :key="item.code" :label="item.info" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建人">
          <el-input v-model="queryParams.createBy" style="width: 255px" />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker v-model="queryParams.createTime" style="width: 255px" value-format="YYYY-MM-DD"
            type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
        </el-form-item>
      </el-form>
    </div>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">
        查询
      </el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <div class="mt10 mb10" v-if="activeTab == '2'">
      <el-button type="primary" :disabled="dataList.length == 0 || single" v-hasPermi="['signurate:aduitItem:pass']"
        plain @click="pass()">通过</el-button>
      <el-button type="primary" :disabled="dataList.length == 0 || single" v-hasPermi="['signurate:aduitItem:unpass']"
        plain @click="unpass()">不通过</el-button>
    </div>
    <div>
      <selectedAll :dataList="dataList" :selectedArr="selectedArr" v-model:allQuery="allQuery" />
      <right-toolbar :columns="columns" @queryTable="getList" v-model:showSearch="showSearch"></right-toolbar>
    </div>

    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane label="待处理" name="2"> </el-tab-pane>
      <el-tab-pane label="已同意" name="0"> </el-tab-pane>
      <el-tab-pane label="未同意" name="1"> </el-tab-pane>
      <el-tab-pane label="全部" name="all"> </el-tab-pane>
    </el-tabs>

    <el-table class="multiple-table" v-loading="loading" :data="dataList" ref="multipleTableRef"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="checkSelectable" align="center" />
      <el-table-column label="批次号" align="center" prop="batchNum" :width="180" v-if="columns[0].visible" />
      <el-table-column label="模板类型" align="center" prop="modelType" :width="150" v-if="columns[1].visible" />
      <el-table-column label="模板名称" align="center" prop="modelName" :width="180" v-if="columns[2].visible" />
      <el-table-column label="处理人" align="center" prop="reviewer" :width="120" v-if="columns[3].visible" />
      <el-table-column label="处理时间" align="center" prop="approveTime" :width="180" v-if="columns[4].visible" />
      <el-table-column label="审核状态" align="center" prop="examineState" :width="120" v-if="columns[5].visible">
        <template #default="{ row, $index }">
          <el-popover placement="bottom" :width="600" :ref="`popover-${$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(row)" type="text">{{ row.examineState }}</el-button>
            </template>
            <el-table :data="gridData" v-loading="gradLoading">
              <el-table-column width="200" property="approveTime" label="处理时间" />
              <el-table-column property="reviewer" label="处理人" show-overflow-tooltip />
              <el-table-column property="approveStartInfo" label="处理状态" :formatter="approveStartFor" />
              <el-table-column width="180" property="refuseReason" label="原因">
                <template #default="scope">
                  <Tooltip :content="scope.row.refuseReason" :length="10" />
                </template>
              </el-table-column>
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="签章状态" align="center" key="status" prop="status" :width="120" :formatter="statusFor"
        v-if="columns[6].visible" />
      <el-table-column label="签章文件" align="center" key="quantity" prop="quantity" :width="100"
        v-if="columns[7].visible">
        <template #default="{ row }">
          <el-button @click="toDetails(row)">{{ row.quantity }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" key="remarks" prop="remarks" width="200px" v-if="columns[8].visible">
        <template #default="{ row }">
          <Tooltip :content="row.remarks" :length="10" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" key="createBy" prop="createBy" show-overflow-tooltip
        v-if="columns[9].visible" />
      <el-table-column label="创建时间" align="center" width="250" key="createTime" prop="createTime" show-overflow-tooltip
        v-if="columns[10].visible" />
      <el-table-column fixed="right" align="center" width="250" label="操作">
        <template #default="{ row }">
          <el-button type="text" v-hasPermi="['caseApprove:signatureDocuments:downloadSignFile']" v-if="row.status == 2"
            @click="downloadSignFile(row)">下载签章文件</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 不通过弹窗 -->
    <unpassBox ref="unpassBoxRef" @notPass="notPass" />
    <!-- 添加预览 -->
    <previewFile ref="previewFileRef" />
  </div>
</template>

<script setup name="LawAduit">
import previewFile from "@/components/previewFile";
import unpassBox from "../dialog/unpass.vue";
import { downloadFile } from "@/api/signature/signatureDocuments";
import { getTemplateType } from "@/api/common/common";
import { getMessageList, messagePass, getProce, messageNotPass, } from "@/api/signature/aduit";
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const showSearch = ref(false);

const rangFields = ["createTime"]; //范围字段

const allQuery = ref(false);
// 列表显隐
const columns = ref([
  { key: 0, label: `批次号`, visible: true },
  { key: 1, label: `模版类型`, visible: true },
  { key: 2, label: `模版名称`, visible: true },
  { key: 3, label: `处理人`, visible: true },
  { key: 4, label: `处理时间`, visible: true },
  { key: 5, label: `审核状态`, visible: true },
  { key: 6, label: `签章状态`, visible: true },
  { key: 7, label: `签章文件`, visible: true },
  { key: 8, label: `备注`, visible: true },
  { key: 9, label: `创建人`, visible: true },
  { key: 10, label: `创建时间`, visible: true },
]);
//勾选信息
const single = ref(true);
const ids = ref([]);
const selectedArr = ref([]);
//多选信息
const classifyList = ref([]);
const classifyIdsList = ref([]);
const examineStateList = ref([]);
//tab切换
const activeTab = ref("2");

// 签章状态列表
const signatureStatusList = ref([
  { code: "0", info: "失败" },
  { code: "1", info: "进行中" },
  { code: "2", info: "完成" },
]);

//多选字段
const checkMoreList = ref(["classifyNames", "examineState"]);
const checkMoreName = ref([classifyIdsList, examineStateList]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    templateName: undefined,
    classifyNames: undefined,
    examineState: undefined,
    approveStarts: undefined,
  },
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
const gridData = ref([]);
const gradLoading = ref(false);
//获取列表
function getList() {
  loading.value = true;
  checkMoreList.value.forEach((item, index) => {
    if (checkMoreName.value[index].value.length === 0) {
      queryParams.value[item] = String(checkMoreName.value[index].value);
    }
  });
  queryParams.value.approveStarts =
    activeTab.value == "all" ? undefined : activeTab.value;
  getMessageList(proxy.addFieldsRange(queryParams.value, rangFields))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    }).finally(() => {
      loading.value = false;
    });
}
getList();

function toDetails(row) {
  const path = `/signature/SigDocsList/${row.letterId}`
  router.push({ path })
}

//获取模板数据
function getTemplate() {
  getTemplateType("model_type").then((res) => {
    classifyList.value = res.data;
  });
}

// 签章状态
function statusFor(row) {
  if (!row.status) return "--";
  return ["失败", "进行中", "完成"][row.status];
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//切换列表
function tabChange() {
  ids.value = [];
  getList();
}

//气泡框展示
function showPopover(row) {
  gradLoading.value = true;
  let req = { id: row.id };
  getProce(req).then((res) => {
    gridData.value = res.data;
  }).finally(() => {
    gradLoading.value = false;
  });
}

//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  selectedArr.value = selection;
}

//通过
function pass() {
  let req = {
    ids: ids.value,
    approveStart: 0,
    allQuery: allQuery.value
  };
  req.queryParam = {
    batchNum: queryParams.value.batchNum,
    templateName: queryParams.value.templateName,
    classifyNames: queryParams.value.classifyNames,
    examineState: queryParams.value.examineState,
  }
  proxy.$modal
    .confirm("是否确认审核？此操作将处理通过，是否确认？")
    .then(function () {
      messagePass(req).then((res) => {
        proxy.$modal.msgSuccess(res.msg);
        ids.value = [];
        getList();
      });
    })
}

//不通过弹窗
function unpass() {
  let req = { ids: ids.value };
  proxy.$refs["unpassBoxRef"].opendialog(req);
}

//不通过
function notPass(notPassData) {
  let req = JSON.parse(JSON.stringify(notPassData));
  req.queryParam = {
    batchNum: queryParams.value.batchNum,
    templateName: queryParams.value.templateName,
    classifyNames: queryParams.value.classifyNames,
    examineState: queryParams.value.examineState
  }
  req.allQuery = allQuery.value;
  req.approveStart = 1;
  messageNotPass(req).then(() => {
    proxy.$refs["unpassBoxRef"].loadingChage();
    proxy.$modal.msgSuccess("操作成功！");
    getList();
    ids.value = [];
    proxy.$refs["unpassBoxRef"].cancel();
  }).finally(() => {
    loading.value = false;
  });
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    batchNum: undefined,
    templateName: undefined,
    classifyNames: undefined,
    examineState: undefined,
    approveStarts: undefined,
  };
  selectedArr.value = [];
  ids.value = [];
  classifyIdsList.value = [];
  examineStateList.value = [];
  getList();
}

//下载签章文件
function downloadSignFile(row) {
  downloadFile({ batchNum: row.batchNum }).then((res) => {
    if (res && res.data && res.data[0]) {
      window.open(res.data[0].fileUrl);
    }
  });
}


//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
  return ["已同意", "未同意", "待处理", "已完成"][row.approveStart];
}
</script>

<style scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.query-reset-btn {
  margin-left: 40px;
}
</style>
