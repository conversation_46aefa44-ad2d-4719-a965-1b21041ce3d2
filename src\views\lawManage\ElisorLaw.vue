<template>
    <div class="fxn-body">
        <div class="fxn-search">
            <el-form :inline="true" :model="SearchForm" :rules="SearchRules" ref="SearchForm">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="案件ID" prop="id">
                            <el-input v-model="SearchForm.id" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户姓名" prop="case_name">
                            <el-input v-model="SearchForm.case_name" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="身份证" prop="case_idcard">
                            <el-input v-model="SearchForm.case_idcard" placeholder="请输入" maxlength="18"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="委托方" prop="entrust_id">
                            <el-select v-model="SearchForm.entrust_id" filterable>
                                <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="fxn-search-btn" >
                    <el-button @click="clearSearchForm">重置</el-button>
                    <el-button type="primary" @click="getLawSuitList(1,10,true)">查询</el-button>
                </div>
            </el-form>
        </div>
        <!-- <div class="fxn-operate">
            <el-button type="warning" @click="checkSuitCase">发起诉讼</el-button>
        </div> -->
        <div class="fxn-section">
            <el-table v-loading="loading" :data="lawListData" ref="lawSuitTable" :header-cell-style="{background:'#EEEFF4',color:'#888888',width: '100%'}">
                <template slot="empty">
                    <div v-if="!isSearch">请搜索案件！</div>
                    <div v-else-if="isSearch && lawListData.length == 0">暂无数据</div>
                </template>
                <el-table-column prop="id" label="案件ID" align="center" width="80">
                    <template slot-scope="scope">
                        <router-link :to="{path:'/CaseDetail',query: {id: scope.row.id,noncollector: true}}" class="fxn-text-blue" tag="a" target="_blank">{{scope.row.id}}</router-link>
                    </template>
                </el-table-column>
                <el-table-column prop="entrust_id" label="委托方" align="center" :formatter="BillNo"></el-table-column>
                <el-table-column prop="bill_no" label="批次号" align="center"></el-table-column>
                <el-table-column prop="case_name" label="客户姓名" align="center"></el-table-column>
                <el-table-column prop="case_idcard_asterisk" label="身份证号" align="center"></el-table-column>
                <el-table-column prop="new_entrust_money" label="委托金额" align="center"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" @click="checkSuitCase(scope.row.id)">诉讼登记</el-button>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="case_status" label="案件状态" align="center" :formatter="format"></el-table-column>
                <el-table-column prop="p_bill_no" label="资产批次号" align="center"></el-table-column>
                <el-table-column prop="bill_no" label="当前批次号" align="center"></el-table-column>
                <el-table-column prop="mem_primary_name" label="目前所属" align="center"></el-table-column> -->
            </el-table>
            <!-- Table End -->

            <div class="fxn-page">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="this.page" :page-sizes="[10, 50, 500, 1000]" :page-size="this.perPage" layout="total, sizes, prev, pager, next, jumper" :total="this.count"></el-pagination>
            </div>
            <!-- page End -->
        </div>
        
        <!-- 发起诉讼 -->
        <el-dialog title="发起诉讼" :visible.sync="launchLawBox" :close-on-click-modal="true">
            <el-form :model="launchLaw" :rules="launchLawRules" ref="launchLaw">
                <el-form-item label="诉讼状态" :label-width="formLabelWidth" prop="status">
                    <el-input v-model="launchLaw.status" :disabled="true" placeholder="请输入"></el-input>
                </el-form-item>
                <!-- <el-form-item label="被执行人" :label-width="formLabelWidth" prop="execute">
                    <el-radio-group v-model="launchLaw.execute">
                        <el-radio label="0">否</el-radio>
                        <el-radio label="1">是</el-radio>
                    </el-radio-group>
                </el-form-item> -->
                <el-form-item label="备注" :label-width="formLabelWidth" prop="mark">
                    <el-input type="textarea" v-model="launchLaw.mark" rows="5"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
				<el-button @click="launchLawBox = false">取 消</el-button>
				<el-button type="primary" @click="launchSuit">发 起</el-button>
			</div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            page: 1,
            perPage: 10,
            count: 0,
            loading: false,
            isSearch: false, //是否搜索

            //查询
            SearchForm: {
                case_name: '',
                entrust_id: '',
                case_idcard: '',
                id: '',
            },       
            SearchRules: {},
            queryForm:{},

            //诉讼状态
            lawStatus:[
                {id:1,title:'发起诉讼',disabled: true},
                {id:2,title:'网约立案',disabled: true},
                {id:3,title:'诉前调解',disabled: true},
                {id:4,title:'正式立案',disabled: true},
                {id:5,title:'受理通知',disabled: true},
                {id:6,title:'开庭传票',disabled: true},
                {id:7,title:'公告开庭',disabled: true},
                {id:8,title:'正常开庭',disabled: true},
                {id:9,title:'判决生效',disabled: true},
                {id:10,title:'申请执行',disabled: true},
                {id:11,title:'调解成功',disabled: true},
                {id:12,title:'录入失信人名单',disabled: true},
                {id:13,title:'结案',disabled: true},
                {id:14,title:'二审应诉',disabled: true},
            ],

            entrusts: [],  //委托方

            //诉讼列表
            lawListData: [], 
            SelectionChange: [],    //已选中列表 

            //发起诉讼
            formLabelWidth: "120px",
            launchLawBox: false,
            lawRecordData: [],         //诉讼记录列表
            launchLaw:{                //发起诉讼
                case_id:'',
                status: '发起诉讼', 
                // execute: '0',  //1:执行人，0：不是执行人（默认）
                mark: ''           
            },           
            launchLawRules:{
                status: [
                    { required: true, message: '请选择状态', trigger: 'blur'}
                ]
            },

        }
    },
    methods:{
        //司法诉讼列表
        getLawSuitList(page,perPage,type) {
            this.isSearch =true;
            this.loading = true;
            this.type = type;
            this.page = page ? page : 1;
            this.perPage = perPage ? perPage : 10;
            if (type) {
                this.queryForm = {
                    page: this.page,
                    perPage: this.perPage,
                    case_name: this.SearchForm.case_name,
                    entrust_id: this.SearchForm.entrust_id,
                    case_idcard: this.SearchForm.case_idcard,
                    id: this.SearchForm.id,
                }
            } else {
                this.queryForm = {
                    page: this.page,
                    perPage: this.perPage,
                }
            }
            //console.log(this.queryForm)
            this.get('/osapi/Cases/index',this.queryForm)
            .then(res => {
                let { code , msg , data } = res.data
                if( code != 1 ) {
                    this.loading = false;
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                } else {
                    this.lawListData = res.data.data.data
                    this.page = res.data.data.page;
                    this.perPage = res.data.data.perPage;
                    this.count = res.data.data.count;
                    this.loading = false;
                }
                //console.log(res)
            })
        },

        //清空查询
        clearSearchForm() {
            this.$refs['SearchForm'].resetFields();
            this.lawListData = [];
            this.page = 1;
            this.perPage = 10;
            this.count = 0;
            if ( this.isSearch == true ) {
                this.getLawSuitList()     
            }
            //this.getLawSuitList()
        },

        //每页显示数据量变更
        handleSizeChange: function(val) {
            this.perPage = val;
            this.getLawSuitList(this.page,this.perPage,this.type)
        },

        //页码变更
        handleCurrentChange: function(val) {
            this.page = val;
            this.getLawSuitList(this.page,this.perPage,this.type)
        },
        
        //检查发起诉讼案件
        checkSuitCase(id) {
            this.launchLaw.case_id = id;
           // console.log(arr)
            this.post('/osapi/Lawsuit/applysuitcheck',{
                case_id: String(id)
            }).then(res => {
                let { code , msg ,data } = res.data;
                if ( code != 1 ) {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                } else if (data.length == 0) {
                    this.$message({
                        type: 'error',
                        message: '已结清或已发起诉讼的案件不可（重复）发起诉讼！'
                    })
                    this.$refs.lawSuitTable.clearSelection();
                } else {
                    this.launchLawBox = true;
                    this.launchLaw.case_id = res.data.data;
                }
            })
        },

        //发起诉讼
        launchSuit() {
            
            this.post('/osapi/Lawsuit/applysuit',{
                case_id: String(this.launchLaw.case_id),
                warrant: 1,
                mark: this.launchLaw.mark,
            }).then( res => {
                //console.log(res)
                let { code , msg , data } = res.data;
                if ( code != 1 ) {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                } else {
                    this.launchLawBox = false;
                    const h = this.$createElement;
                    this.$message({
                        type: 'success',
                        message: h('div', { style: 'max-width: 400px;color: #67C23A;word-wrap:break-word;line-height:18px;' }, '案件：' + String(this.launchLaw.case_id) + '已成功发起诉讼！')
                    })
                    this.getLawSuitList(this.page,this.perPage,this.type);
                }
            })
           
        },

        //委托方转中文格式
        BillNo(row, col, cellValue){
            for (var key in this.entrusts) {
                if (cellValue == this.entrusts[key]['id']) {
                    return this.entrusts[key]['name'];
                } 
            }
        },

        //列表案件状态
        format (row,col){
            if(row.status == 1){
                return '未分配'
            }else if(row.status == 2){
                return '已分配'
            }else if(row.status == 3){
                return '暂停'
            }else if(row.status == 4){
                return '退案'
            }else if(row.status == 5){
                return '留案'
            }else if(row.status == 6){
                return '恢复'
            }
        },
    },
    mounted() {
        //this.getLawSuitList();
        //获取委托方(资产方)
        this.entrusts = JSON.parse(sessionStorage.getItem('entrustsAll'));
    },
    watch:{
        launchLawBox(val) {
            if (!val) {
                this.$refs['launchLaw'].resetFields();
                this.$refs.lawSuitTable.clearSelection();
            }
        },
    }
}
</script>
<style scoped>

</style>