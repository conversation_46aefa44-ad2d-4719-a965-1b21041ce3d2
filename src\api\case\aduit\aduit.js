import request from '@/utils/request'

//获取回款审批
export function selectRepaymentRecord(query) {
    return request({
        url: '/case/selectRepaymentRecord',
        method: 'get',
        params: query
    })
}

//获取减免审批
export function selectReductionRecord(query) {
    return request({
        url: '/case/selectReductionRecord',
        method: 'get',
        params: query
    })
}

//获取分期审批
export function selectStagingRecord(query) {
    return request({
        url: '/case/selectStagingRecord',
        method: 'get',
        params: query
    })
}

//获取留案审批
export function selectRetention(query) {
    return request({
        url: '/case/selectRetention',
        method: 'get',
        params: query
    })
}

//获取停催审批
export function selectStopUrging(query) {
    return request({
        url: '/case/selectStopUrging',
        method: 'get',
        params: query
    })
}

//获取退案审批
export function selectWithdrawal(query) {
    return request({
        url: '/case/selectWithdrawal',
        method: 'get',
        params: query
    })
}

//获取外访审批
export function selectOutsideRecord(query) {
    return request({
        url: '/case/selectOutsideRecord',
        method: 'get',
        params: query
    })
}

//获取资料调取
export function selectRetrievalRecord(query) {
    return request({
        url: '/case/selectRetrievalRecord',
        method: 'get',
        params: query
    })
}

//提交回款审批
export function WriteCollectionApproval(data) {
    return request({
        url: '/case/WriteCollectionApproval',
        method: 'post',
        data: data
    })
}

//提交减免审批
export function ExemptionApproval(data) {
    return request({
        url: '/case/ExemptionApproval',
        method: 'post',
        data: data
    })
}

//提交分期审批
export function InstallmentApproval(data) {
    return request({
        url: '/case/InstallmentApproval',
        method: 'post',
        data: data
    })
}

//提交留案审批
export function WriteRetention(data) {
    return request({
        url: '/case/WriteRetention',
        method: 'post',
        data: data
    })
}

//提交退案审批
export function WriteStopReminder(data) {
    return request({
        url: '/case/WriteStopReminder',
        method: 'post',
        data: data
    })
}

//提交停催审批
export function WriteBack(data) {
    return request({
        url: '/case/WriteBack',
        method: 'post',
        data: data
    })
}

//提交外访审批
export function updateOutsideRecordAs(data) {
    return request({
        url: '/case/updateOutsideRecordAs',
        method: 'post',
        data: data
    })
}

//提交资料调取审批
export function dataRetrievalApproval(data) {
    return request({
        url: '/case/dataRetrievalApproval',
        method: 'post',
        data: data
    })
}


//获取分期审批
export function selectStagingRecordById(query) {
    return request({
        url: `/case/selectStagingRecord/${query}`,
        method: 'get',
    })
}

//获取回款审核流程
export function selectApproveProceOne(query) {
    return request({
        url: `/case/selectApproveProceOne`,
        method: 'get',
        params:query
    })
}

//获取减免审核流程
export function selectApproveProceTwo(query) {
    return request({
        url: `/case/selectApproveProceTwo`,
        method: 'get',
        params:query
    })
}

//获取分期审核流程
export function selectApproveProceThree(query) {
    return request({
        url: `/case/selectApproveProceThree`,
        method: 'get',
        params:query
    })
}

//获取留案审核流程
export function selectApproveProceFour(query) {
    return request({
        url: `/case/selectApproveProceFour`,
        method: 'get',
        params:query
    })
}

//获取停催审核流程
export function selectApproveProceFive(query) {
    return request({
        url: `/case/selectApproveProceFive`,
        method: 'get',
        params:query
    })
}

//获取退案审核流程
export function selectApproveProceSix(query) {
    return request({
        url: `/case/selectApproveProceSix`,
        method: 'get',
        params:query
    })
}

//获取外访审核流程
export function selectApproveProceSeven(query) {
    return request({
        url: `/case/selectApproveProceSeven`,
        method: 'get',
        params:query
    })
}

//获取资料调取审批
export function selectApproveProceEight(query) {
    return request({
        url: `/case/selectApproveProceEight`,
        method: 'get',
        params:query
    })
}

//获取资料调取审批
export function getCaseCount(query) {
    return request({
        url: `case/caseCount`,
        method: 'get',
        params:query
    })
}





