<template>
  <el-dialog
    :title="`案件更新【${rowinfo.ownerName}】--${rowinfo.productName}`"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="110px">
      <el-form-item label="导入案件信息">
        【{{ rowinfo.ownerName }}】-- {{ rowinfo.productName }}
      </el-form-item>
      <el-form-item label="批次号">
        {{ assetInfo.batchNum }}
      </el-form-item>
      <el-form-item label="债权准入日期">
        {{ assetInfo.creditorAccessDate }}
      </el-form-item>
      <el-form-item label="请先下载现案件">
        <el-button type="primary"
            link @click="downTpl()">点击下载现案件</el-button>
      </el-form-item>
      <el-form-item label="案件模版" prop="fileUrl">
        <el-input v-show="false" v-model="form.fileUrl"></el-input>
        <el-upload
          ref="uploadRef"
          drag
          :limit="1"
          accept=".xls, .xlsx"
          :headers="upload.headers"
          :action="uploadUrl"
          :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange"
          :before-remove="handleRemove"
          :on-success="handleFileSuccess"
          :auto-upload="false"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div>
              <el-button type="success" @click="submitFile">上传到服务器</el-button>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {
  planBatch,
  planByAsset,
  getAssetDetail,
  updateCase,
} from "@/api/assets/asset/asset";
import { blobToOriginalData } from "@/utils/common";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["queryList"]);
const open = ref(false);
const loading = ref(false);
const assetInfo = ref({});
const rowinfo = ref({});

const data = reactive({
  form: {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  },
  rules: {
    fileUrl: [{ required: true, message: "请上传文件！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

const upload = reactive({
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/file/upload",
});
const uploadUrl = computed(() => {
  return upload.url.replace("/appeal", "");
}) 
const files = ref([]); //上传成功文件列表

//打开弹窗
function opendialog(row) {
  rowinfo.value = row;
  getAssetDetail(row.id).then((res) => {
    assetInfo.value = res.data;
    form.value.id = res.data.id;
    open.value = true;
  });
}

//下载文件
async function downTpl() {
  const result = await proxy.download(
    "/team/download/existingCases",
    { id: form.value.id },
    `tpl_现案件${assetInfo.value.batchNum}.xlsx`, { gateway: 'cis' }
  );
  ;

  blobToOriginalData(result).then(array => {
    proxy.$modal.alertWarning(
        `此导出操作已成功提交，请前往导出日志查看导出结果!\n
         任务文件名称：${array.data}`
    )
  }).catch(error => {
    proxy.$modal.alertWarning(`此导出操作已成功提交，请前往导出日志查看导出结果!`)
    console.error('转换出错：', error);
  });
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
}

/* 文件移除 */
function handleRemove(file, fileList) {
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.response.data.name;
    for (let i = 0; i < files.value.length; i++) {
      if (files.value[i].modifyName == name) {
        files.value.splice(i, 1);
        form.value.fileUrl = undefined;
        form.value.originalFilename = undefined;
        break;
      }
    }
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    form.value.fileUrl = data.url;
    form.value.originalFilename = file.name;
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: file.name,
      modifyName: data.name,
      fileUrl: data.url,
    };
    files.value.push(obj);
  }
};

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    id: undefined,
    fileUrl: undefined,
    originalFilename: undefined,
  };
  files.value = [];
  proxy.$refs["uploadRef"].clearFiles();
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      updateCase(form.value)
        .then((res) => {
          loading.value = false;
          // proxy.$modal.msgSuccess("操作成功");
          cancel();
          emit("queryList");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
