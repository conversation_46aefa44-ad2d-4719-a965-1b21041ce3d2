import request from '@/utils/request'

//提交任务
export function submitTask(data) {
    return request({
        url: '/intelligenceTask/submitTask',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//预测试外呼列表-查询
export function listIntelligenceTask(query) {
    return request({
        url: '/intelligenceTask/listIntelligenceTask',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}

//任务名称下拉
export function getTaskNameList() {
    return request({
        url: '/intelligenceTask/getTaskNameList',
        method: 'get',
        gateway: 'cis'
    })
}

//任务状态下拉
export function getTaskStatus() {
    return request({
        url: '/intelligenceTask/getTaskStatus',
        method: 'get',
        gateway: 'cis'
    })
}

//预测试外呼列表-执行
export function executeTask(data) {
    return request({
        url: '/intelligenceTask/executeTask',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//预测试外呼列表-暂停
export function suspendTask(data) {
    return request({
        url: '/intelligenceTask/suspendTask',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//预测试外呼列表-撤销
export function revokeTask(data) {
    return request({
        url: '/intelligenceTask/revokeTask',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//预测试外呼列表-未接通重呼
export function notConnectRecallTask(data) {
    return request({
        url: '/intelligenceTask/notConnectRecallTask',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//查看结果报告-数据统计
export function callTaskStatistics(query) {
    return request({
        url: '/intelligenceTask/callTaskStatistics',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}

//查看结果报告-数据列表
export function callTaskDataList(query) {
    return request({
        url: '/intelligenceTask/callTaskDataList',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}


//话单数据列表-导出
export function exportTaskCdr(data) {
    return request({
        url: '/intelligenceTask/exportTaskCdr',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//查询坐席状态-(预测试外呼坐席)
export function getSipState() {
    return request({
        url: '/intelligenceTask/getSipState',
        method: 'get',
        gateway: 'cis'
    })
}

//修改坐席状态-(预测试外呼坐席)
export function updateSipState(data) {
    return request({
        url: '/intelligenceTask/updateSipState',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}