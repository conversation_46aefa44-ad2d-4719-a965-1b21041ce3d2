<template>
  <!-- 分配坐席 -->
  <el-dialog v-if="open" title="分配坐席" v-model="open" width="650px" append-to-body :before-close="cancel">
    <el-transfer
      :titles="['坐席账号', '分配到员工账号']"
      v-model="sipNumbers"
      :data="dataList"
      @change="dataChange"
      :left-default-checked="ldefaultChecked"
      :right-default-checked="rdefaultChecked"
    ></el-transfer>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getUnassignedSipList, assignSip } from '@/api/system/user'

const { proxy } = getCurrentInstance();

const emits = defineEmits(["getList"]);

const open = ref(false);
const loading = ref(false);
const sipNumbers = ref([]);
const ldefaultChecked = ref([]); //默认选中的
const rdefaultChecked = ref([]); //默认选中的
const employeeIds = ref([]); //员工id
const dataList = ref([]);

//右侧变化
function dataChange(data) {
}

//打开弹窗
async function opendialog(user) {
  let form = {}
  form.allocate = 1
  sipNumbers.value = []
  await getUnassignedSipList(form).then(res => {
    res.data.map(item => {
      item.key = item.sipNumber
    })
    dataList.value = res.data;
  })

  getUnassignedSipList().then(res => {
    let all_seat = res.data;
    user.map(item => {
      employeeIds.value.push(item.id)
      if (item.sipNumber) {
        sipNumbers.value.push(item.sipNumber) //已分配的
        for (let i = 0; i < all_seat.length; i++) {
          let obj = all_seat[i];
          if (all_seat[i].sipNumber == item.sipNumber) {
            obj.key = obj.sipNumber
            dataList.value.push(obj)
            return
          }
        }
      }
    })
    open.value = true
  })
}

//提交
function submit() {
  loading.value = true;
  // if (sipNumbers.value.length === 0) {
  //   proxy.$modal.msgWarning('请分配坐席账号后再进行操作！')
  //   loading.value = false
  //   return
  // }
  let subform = {
    employeeIds: employeeIds.value,
    sipNumbers: sipNumbers.value
  }
  assignSip(subform).then(res => {
    proxy.$modal.msgSuccess('操作成功！');
    emits('getList');
    cancel()
  }).finally(() => {
    loading.value = false
  })
}

//重置
function reset() {
  employeeIds.value = [];
  sipNumbers.value = [];
}

//取消
function cancel() {
  reset();
  open.value = false
}

defineExpose({
  opendialog
})
</script>

<style scoped>
   
</style>