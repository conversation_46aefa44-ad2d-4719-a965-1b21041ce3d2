<template>
  <div class='app-container'>
    <el-form
      class="form-content h-50"
      :class="{ 'h-auto': showSearch }"
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" style="width: 308px">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select
          v-model="queryParams.taskStatus"
          placeholder="请选择"
          clearable
          @visible-change="getTaskStatusOptions"
          :loading="selectLoading"
          style="width: 240px"
        >
          <el-option v-for="item in taskStatusOptions" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb8 h32 hint">
      <!-- <el-tooltip effect="light" placement="bottom-start">
        <div>
          <svg-icon class="hint-item text-warning" icon-class="question" />
        </div>
        <template #content>
          <div class="info-tip">
            <el-icon class="info-tip-icon" :size="16">
              <warning-filled />
            </el-icon>
            <div>
              <p>
                注：任务完成时间一般为隔天9:00，有效期为创建时间起三天内！
              </p>
            </div>
          </div>
        </template>
      </el-tooltip> -->

      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
        :types="[1, 2, 3]"
      />
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="任务名称"
        align="center"
        key="taskName"
        prop="taskName"
        v-if="columns[0].visible"
      />
      <el-table-column
        label="创建时间"
        align="center"
        key="createTime"
        prop="createTime"
        v-if="columns[1].visible"
      />
      <el-table-column
        label="创建人"
        align="center"
        key="createBy"
        prop="createBy"
        v-if="columns[2].visible"
      />
      <el-table-column
        label="任务状态"
        align="center"
        key="taskStatusInfo"
        prop="taskStatusInfo"
        v-if="columns[3].visible"
      />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.taskStatus == 1" type="primary" v-hasPermi="['seat:downwork:down']" link @click="downLoad(scope.row.fileUrl)">下载</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Downtask">
import { downloadTaskList, getTaskStatus } from '@/api/seat/downtask'
const { proxy } = getCurrentInstance();

const showSearch = ref(false);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    createTime: [],
    createBy: undefined,
    taskStatus: undefined,
  },
});
const rangFields = ["createTime"];
const { queryParams } = toRefs(data);

const selectLoading = ref(false);
const taskStatusOptions = ref([]);

const columns = ref([
  { key: 0, label: `任务名称`, visible: true },
  { key: 1, label: `创建时间`, visible: true },
  { key: 2, label: `创建人`, visible: true },
  { key: 3, label: `任务状态`, visible: true },
]);

//获取列表
function getList() {
  loading.value = true;
  downloadTaskList(proxy.addFieldsRange(queryParams.value, rangFields))
      .then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
}
getList()

function getTaskStatusOptions(val) {
  if (val) {
    selectLoading.value = true;
    getTaskStatus().then(res => {
      taskStatusOptions.value = res.data
    }).finally(() => {
      selectLoading.value = false;
    })
  }
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    createTime: [],
    createBy: undefined,
    taskStatus: undefined,
  };
  getList();
}

//下载
function downLoad(url) {
  if (!url || url == '') {
    proxy.$modal.msgWarning('文件不存在，请联系后台管理人员！')
    return
  }
  window.open(url)
}

</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}
.h-50 {
  height: 50px;
}
.h-auto {
  height: auto !important;
}
:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}
.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}
.info-tip {
  border: unset;
}
</style>