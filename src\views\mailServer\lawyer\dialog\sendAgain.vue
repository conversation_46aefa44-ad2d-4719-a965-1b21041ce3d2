<template>
    <el-dialog title="重新发送" v-model="open" append-to-body @before-close="cancel" width="500px">
        <el-form :model="form" :rules="rules" label-width="120px" ref="formRef">
            <el-row :gutter="24">
                <el-col :span="24">
                    <el-form-item label="短信通道" prop="smsChannel">
                        <el-select v-model="form.smsChannel" style="width:100%" clearable placeholder="请选择短信通道">
                            <el-option v-for="item in smsChannelList" :label="item.repaymentMethod" :key="item.id"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="text-center">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="submitForm" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { resloadSendSms, getSmsChannel } from "@/api/note/noteLog.js";
const { proxy } = getCurrentInstance()
const props = defineProps({
    getList: { type: Function }
})
const open = ref(false)
const loading = ref(false)
const data = reactive({
    form: {
        ids: undefined,
        smsChannel: undefined,
    },
    rules: {
        smsChannel: [{ required: true, message: '请选择短信通道', trigger: 'blur' }]
    }
})
const smsChannelList = ref(undefined)
const { form, rules } = toRefs(data)
function submitForm() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            loading.value = true
            resloadSendSms(reqForm).then(res => {
                if (res.code == 200) {
                    cancel()
                    props.getList()
                    proxy.$modal.msgSuccess('操作成功！')
                }
            }).finally(() => loading.value = false)
        }
    })

}
function opendialog(data) {
    open.value = true
    form.value.ids = data.ids
}

function cancel() {
    proxy.resetForm('formRef')
    open.value = false
    form.value = {
        ids: undefined,
        smsChannel: undefined,
    }
}
defineExpose({
    opendialog
})
</script>