<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="登记时间" prop="complaintDate" key="complaintDate" align="center" />
    <el-table-column label="登记人" prop="registrant" key="registrant" align="center" />
    <el-table-column label="投诉内容" prop="complaintContent" key="complaintContent" align="center">
      <template #default="{ row }">
        <Tooltip :content="row.complaintContent" :length="15" />
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import { selectComplaintRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  params: { type: Object, default: {} },
  handleDownload: { type: Function },
});
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: route.params.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm, ...props.params }
  loading.value = true;
  selectComplaintRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();
defineExpose({ getList })
</script>

<style scoped></style>
