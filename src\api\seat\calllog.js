import request from '@/utils/request'

// 查询参列表
export function calllogs(query) {
  return request({
    url: '/call/record/list',
    method: 'get',
    params: query
  })
}

//呼叫类型下拉
export function getCallroters() {
  return request({
    url: '/call/record/getCallroters',
    method: 'get',
  })
}

//接听状态
export function getAnswerStatus() {
  return request({
    url: '/call/record/getAnswerStatus',
    method: 'get',
  })
}

//创建下载录音任务
export function createDownloadTask(data) {
  return request({
    url: '/call/record/createDownloadTask',
    method: 'post',
    data: data
  })
}