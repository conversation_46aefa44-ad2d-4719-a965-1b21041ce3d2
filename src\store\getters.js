const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  count: state => state.message.count,
  permission_routes: state => state.permission.routes,
  topbarRouters:state => state.permission.topbarRouters,
  defaultRoutes:state => state.permission.defaultRoutes,
  sidebarRouters: state => state.permission.sidebarRouters,
  showCallBtn: state => state.call.showCallBtn,
  callState: state => state.call.callState,
  callingPhone: state => state.call.callingPhone, 
  callTime: state => state.call.callTime,
  currentCallShowId: state => state.call.currentCallShowId,
  isRegistered: state => state.webrtc.isRegistered,
  iscalling: state => state.webrtc.iscalling,
  isincall: state => state.webrtc.isincall,
  htrxCall:state => state.webrtc.htrxCall,
  taskMessage: state => state.call.taskMessage,
  caseTaskMessage: state => state.call.caseTaskMessage,
  workPhoneMessage: state => state.call.workPhoneMessage,
  showWorkPhoneIcon: state => state.call.showWorkPhoneIcon
}
export default getters