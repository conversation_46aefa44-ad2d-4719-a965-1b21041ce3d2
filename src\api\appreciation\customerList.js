import request from '@/utils/request'

//下载导入客户资料模版--（客户资料）
export function customImportTemplate() {
    return request({
        url: '/callCustom/customImportTemplate',
        method: 'post',
        gateway: 'cis'
    })
}

//excel表格导入客户资料-(校验名单信息)
export function importDataCustom(data) {
    return request({
        url: '/callCustom/importDataCustom',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//返回团队分配了预测式外呼的人员列表树类型信息
export function DeptTreeType() {
    return request({
        url: '/callCustom/DeptTreeType',
        method: 'get',
        gateway: 'cis'
    })
}

//客户列表-查询
export function listCustomDetails(query) {
    return request({
        url: '/callCustom/listCustomDetails',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}

//客户列表-导出
export function exportCustomDetails(data) {
    return request({
        url: '/callCustom/exportCustomDetails',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//客户列表-批量删除
export function deleteCustomDetails(data) {
    return request({
        url: '/callCustom/deleteCustomDetails',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//客户列表-编辑
export function updateCustomDetails(data) {
    return request({
        url: '/callCustom/updateCustomDetails',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}

//来电弹屏
export function popOnScreens(query) {
    return request({
        url: '/callCustom/popOnScreen',
        method: 'get',
        params: query,
        gateway: 'cis'
    })
}

//来电弹屏-编辑备注
export function popOnScreenRemark(data) {
    return request({
        url: '/callCustom/popOnScreenRemark',
        method: 'post',
        data: data,
        gateway: 'cis'
    })
}


