<template>
  <div class="app-container">
    <el-radio-group class="mb20" v-model="activeTab">
      <el-radio-button
        v-hasPermi="['mediation:exeOfPaybacks:toBeClaimed:index']"
        label="0"
        name="0"
        >待领款</el-radio-button
      >
      <el-radio-button
        v-hasPermi="['mediation:exeOfPaybacks:haveClaimed:index']"
        label="1"
        name="1"
        >已领款</el-radio-button
      >
    </el-radio-group>
    <toBeClaimed v-if="activeTab == '0'" />
    <haveClaimed v-if="activeTab == '1'" />
  </div>
</template>

<script setup name="ExeOfPaybacks">
import toBeClaimed from "./toBeClaimed/index.vue";
import haveClaimed from "./haveClaimed/index.vue";
import { ref } from 'vue';

const activeTab = ref('0');
</script>

<style scoped></style>
