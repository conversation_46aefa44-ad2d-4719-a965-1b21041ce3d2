<template>
  <el-dialog title="诉讼记录" v-model="open" append-to-body @close="cancel">
    <el-table v-loading="subLoading" ref="urgedmultipleTableRef" :data="urgedList">
   <el-table-column
        prop="createTime"
        label="时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="lawsuitStatus"
        label="诉讼状态"
        align="center"
      ></el-table-column>
      <el-table-column prop="caseFilingNumber" label="立案号" align="center" />
      <el-table-column
        prop="propertyPreservationNumber"
        label="财保号"
        align="center"
      />
      <el-table-column
        prop="enforcementPreservationNumber"
        label="执保号"
        align="center"
      />
      <el-table-column
        prop="explain"
        label="说明"
        align="center"
      ></el-table-column>
      <el-table-column label="附件" align="center">
        <template #default="{ row }">
          <div class="fxn-detail-img-pdf">
            <span v-for="(item, index) in row.filePath" :key="index">
              <el-button
                v-if="getCaption(item) == 'pdf'"
                type="text"
                size="small"
                @click="clickImg(item)"
                >下载</el-button
              >
              <img
                v-else
                :src="item"
                alt=""
                width="20px"
                height="20px"
                @click="clickImg(item)"
              />
            </span>
            <el-button
              v-if="objectLen(row.filePath)"
              type="text"
              size="small"
              @click="bulkDownload(row.id)"
              >批量下载</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { getLawProgressListApi } from "@/api/selfLawList";
import { replaceNull } from "@/utils/common";

const urgedList = ref([]);
const caseId = ref(undefined);
const subLoading = ref(true);
const open = ref(false);
const { proxy } = getCurrentInstance();

function geturgedList() {
  subLoading.value = true;
  getLawProgressListApi({recordId:caseId.value})
      .then((res) => {
        urgedList.value = replaceNull(res.data);
        subLoading.value = false;
      })
      .catch(() => {
        subLoading.value = false;
      });
}

//打开弹窗
function opendialog(id) {
  reset();
  caseId.value = id;
  open.value = true;
  geturgedList(caseId.value);
}

//取消
function cancel() {
  caseId.value = undefined;
  open.value = false;
}

//重置
function reset() {
  caseId.value = undefined;
  open.value = true;
}

//协催状态 0-待协催，1-持续协催，2-完成协催，3-终止协催
function urgdStateFor(row) {
  return ["待协催", "持续协催", "完成协催", "终止协催"][parseInt(row.state)];
}

// 获取指定字符后面的内容
const getCaption = (url) => {
  var index = url.lastIndexOf(".");
  url = url.substring(index + 1, url.length);
  return url;
};

// 诉讼记录附件点击
const clickImg = (item) => {
  window.open(item);
};

// 批量下载是否显示
const objectLen = (url) => {
  return url != "" && url != undefined ? url.length > 1 : false;
};

// 批量下载
const bulkDownload = (id) => {
  proxy.downloadforjsonGet(
    `/lawsuitStatus/download/${id}`,
    {},                       // 无需参数时传空对象
    `download_${id}.zip`)
};

 defineExpose({
        opendialog
    })
</script>

<style scoped></style>
