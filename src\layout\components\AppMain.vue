<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
        <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>

<script setup>
import watermark from "@/utils/watermark";
import { getWatermark, exportSwtich } from "@/api/common/common";
import { watch } from "vue-demi";
let store = useStore();
//通话状态
const callState = computed(() => store.getters.callState);
const route = useRoute();
const router = useRouter();
const routeKey = ref(+new Date())
const callTimeObj = ref(undefined);
store.dispatch("tagsView/addCachedView", route);
const localStoragePath = ref([
  "mycase-detail",
  "caseIndex-detail",
  "teamIndex-detail",
  "aduit-detail",
]);
const cachedViews = computed(() => {
  return store.state.tagsView.cachedViews;
});
onMounted(() => {
  let callLoginState = localStorage.getItem(`callLoginState`) || JSON.stringify({
    state:false,
    name:undefined,
    pwd:undefined
  });
  if (window.name == "") {
    //首次加载
    window.name = "isRefresh";
  } else if (window.name == "isRefresh" && route.path.indexOf("mycase-detail") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForDetails()
  } else if (window.name == "isRefresh" && route.path.indexOf("caseIndex-detail") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForDetails()
  } else if (window.name == "isRefresh" && route.path.indexOf("teamIndex-detail") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForDetails()
  } else if (window.name == "isRefresh" && route.path.indexOf("aduit-detail") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForDetails()
  } else if (window.name == "isRefresh" && route.path.indexOf("rules") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForRules()
  } else if (window.name == "isRefresh" && route.path.indexOf("point") > -1) {
    //刷新页面且当前页面是 我的案件-》详情
    setLocalStorageForPoint()
  } else {
    //当前页不是 我的案件-》详情 保留对应缓存
    localStorage.clear();
  }
  localStorage.setItem(`callLoginState`, callLoginState);
  //刷新当前的通话时长
  localStorage.setItem(`callDuration`, 0);
});
//保存案件详情数据
function setLocalStorageForDetails() {
  let data = localStorage.getItem(`searchInfo/${route.params.caseId}`);
  localStorage.clear();
  localStorage.setItem(`searchInfo/${route.params.caseId}`, data);
}

//保留规则分案数据
function setLocalStorageForRules() {
  let data = localStorage.getItem(`rules/${route.params.time}`);
  localStorage.clear();
  localStorage.setItem(`rules/${route.params.time}`, data);
}

//保留指定分案数据
function setLocalStorageForPoint() {
  let data = localStorage.getItem(`point/${route.params.time}`);
  localStorage.clear();
  localStorage.setItem(`point/${route.params.time}`, data);
}

//设置水印
function getWaterMask() {
  watermark.out();
  getWatermark()
    .then((res) => {
      if (res.data.watermarkStatus == 1) {
        watermark.set(res.data.watermarkInformation, route.params.caseId);
      }
    })
    .catch(() => { });
}
getWaterMask();

// 设置导出按钮显示与隐藏
function getExportSwtich() {
  exportSwtich().then((res) => {
    for (const key in res.data) {
      if (Object.hasOwnProperty.call(res.data, key)) {
        localStorage.setItem(key, res.data[key])
      }
    }
  })
}
getExportSwtich()
watch(route, (value) => {
  getWaterMask();
})

//通话中信息请求
watch(() =>callState,() =>{
  let callDuration = localStorage.getItem("callDuration");
  if(callState.value == 1){
    callTimeObj.value = setInterval(() =>{
      callDuration++;
      if(callDuration > 10*60*1000){
        getWatermark();
        localStorage.setItem("callDuration",0);
      }else{
        localStorage.setItem("callDuration",callDuration);
      }
    },1000)
  }else{
    getWatermark();
    localStorage.setItem("callDuration",0);
    clearInterval(callTimeObj.value);
  }
},{deep:true})
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
