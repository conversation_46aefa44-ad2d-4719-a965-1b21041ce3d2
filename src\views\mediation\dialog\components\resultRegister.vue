<template>
    <div>
        <el-form-item prop="result" label="审核结果">
            <el-radio-group v-model="form.result">
                <el-radio label="1" value="1">审核通过</el-radio>
                <el-radio label="2" value="2">审核不通过</el-radio>
                <el-radio label="3" value="3">审核未通过但接受调解</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item prop="reason" v-if="form.result != 1" label="审核不通过原因">
            <el-input v-model="form.reason" type="textarea" :rows="4" placeholder="请输入诉讼案号" />
        </el-form-item>
    </div>
</template>
<script setup>
const props = defineProps({
    form: { type: Object, default: {} },
    rules: { type: Object, default: {} },
})
</script>