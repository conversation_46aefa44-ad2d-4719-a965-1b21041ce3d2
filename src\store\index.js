import { createStore } from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import message from './../api/common/webSocket'
import settings from './modules/settings'
import call from './modules/call'
import webrtc from './modules/webrtc'
import allCaseDetail from './modules/allCaseDetail'
import getters from './getters'

const store = createStore({
  modules: {
    app,
    user,
    tagsView,
    permission,
    message,
    webrtc,
    settings,
    call,
    allCaseDetail
  },
  getters
});


export default store