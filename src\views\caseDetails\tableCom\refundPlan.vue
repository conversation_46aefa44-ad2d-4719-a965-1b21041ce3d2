<template>
    <div>
        <el-table :data="dataList" :default-sort="{ prop: 'hkPeriodsNumber' }" @sort-change="getSortChange"
            max-height="450">
            <el-table-column label="还款期数" prop="hkPeriodsNumber" key="hkPeriodsNumber" sortable="custom" :width="100"
                align="center" />
            <el-table-column label="应还日期" prop="yhDate" key="yhDate" :width="120" align="center"
                :show-overflow-tooltip="true" />
            <el-table-column label="应还本金" prop="jhYhPrincipal" key="jhYhPrincipal" :width="120" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.jhYhPrincipal) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="应还利息" prop="jhYhInterest" key="jhYhInterest" :width="100" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.jhYhInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="应还罚息" prop="syYhSurplusInterest" key="syYhSurplusInterest" :width="100"
                align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhSurplusInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还金额" prop="syYhMoney" key="syYhMoney" :width="120" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhMoney) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还本金" prop="syYhPrincipal" key="syYhPrincipal" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhPrincipal) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还利息" prop="syYhInterest" key="syYhInterest" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="已偿还咨询费" prop="ychAdvice" key="ychAdvice" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychAdvice) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="已偿还还担保费" prop="ychGuarantee" key="ychGuarantee" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychGuarantee) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还咨询费" prop="syYhAdvice" key="syYhAdvice" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhAdvice) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还担保费" prop="syYhGuarantee" key="syYhGuarantee" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhGuarantee) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="计划应还咨询费" prop="jhYhAdvice" key="jhYhAdvice" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.jhYhAdvice) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="计划应还担保费" prop="jhYhGuarantee" key="jhYhGuarantee" :width="130" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.jhYhGuarantee) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余应还罚息" prop="syYhPenaltyInterest" key="syYhPenaltyInterest" :width="130"
                align="center">
                    <template #default="{ row }">
                        <span>{{ numFilter(row.syYhPenaltyInterest) }}</span>
                    </template>
            </el-table-column>
            <el-table-column label="实还本金" prop="ychPrincipal" key="ychPrincipal" :width="120" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychPrincipal) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="实还利息" prop="ychInterest" key="ychInterest" :width="100" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="实还罚息" prop="ychPenaltyInterest" key="ychPenaltyInterest" :width="100"
                align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychPenaltyInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="实还金额" prop="ychMoney" key="ychMoney" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.ychMoney) }}</span>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 10" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>
<script setup>
import { selectRepaymentPlan } from "@/api/caseDetail/detail";
const route = useRoute()
const data = reactive({
    queryParams: {
        caseId: route.params.caseId,
        pageNum: 1,
        pageSize: 10,
    }
})
const sortOrder = ref(undefined)
const dataList = ref([])
const total = ref(0)
const { queryParams } = toRefs(data)
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    reqForm.sortOrder = sortOrder.value
    selectRepaymentPlan(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}
//排序判断
function getSortChange(sortData) {
    if (sortData) {
        sortOrder.value = sortData.order == "ascending" ? 1 : 2;
    }
    getList()
}

</script>
<style lang="scss" scoped>
:deep(.el-pagination) {
    position: unset;
    float: right;
    margin-top: -20px;
}
</style>