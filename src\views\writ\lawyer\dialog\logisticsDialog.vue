<template>
  <el-dialog
    title="导入物流"
    v-model="open"
    width="900px"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <div class="step-item step1" v-show="stepActive == 0">
      <div v-if="!isimported">
        <div class="step-content mb20">
          <el-icon class="content-icon mr10"><upload-filled /></el-icon>
          <div class="text-box">
            <h3 class="title">填写导入数据信息</h3>
            <p class="tit">
              请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除。
            </p>
            <div>
              <el-button type="text" @click="downTpl">下载模板</el-button>
            </div>
          </div>
        </div>
        <div class="step-content-upload">
          <el-upload
            ref="uploadRef"
            drag
            :limit="1"
            accept=".xls, .xlsx"
            :headers="upload.headers"
            :action="upload.url"
            :before-upload="handleFileUploadBefore"
            :before-remove="handleRemove"
            :on-success="handleFileSuccess"
            :on-change="handleEditChange"
            :auto-upload="false"
            class="upload-box"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">上传填好的信息表</div>
            <div class="el-upload__tip mb10">
              <span class="mr10"> 上传格式仅支持：.xlsx .xls，文件大小不超过1M </span>
            </div>
            <div class="el-upload__em">选择文件</div>
          </el-upload>
        </div>
      </div>
      <div v-else class="step-data" style="width: 100%">
        <div class="msg-data">
          <div class="msg-icon">
            <el-icon :size="48" color="#fff"><Document /></el-icon>
          </div>
          <div class="msg-count">
            <div :class="resluts.errorNum > 0 ? 'mt12' : 'mt20'">
              <span class="text700">正常数量条数：</span>
              <span class="text-green">{{ resluts.successNum }}</span>
            </div>
            <div class="mt10">
              <span class="text700">异常数量条数：</span>
              <span class="text-danger">{{ resluts.errorDataList?.length }}</span>
            </div>
            <div
              class="mt10 text700"
              v-if="resluts.errorDataList?.length > 0"
              style="color: red"
            >
              注意：导入文件含有异常数据，只导入正常的数据，异常的数据请检查处理后重新导入或忽略！
            </div>
          </div>
        </div>
        <div class="err-msg">
          <div v-for="(item, index) in resluts.errorDataList" :key="index">
            {{ index + 1 }}、{{ item }}
          </div>
        </div>
      </div>
    </div>
    <div class="step-item step1" v-show="stepActive == 1">
      <div class="success-msg text-center">
        <div class="success-icon">
          <el-icon :size="64" color="#67c23a"><CircleCheckFilled /></el-icon>
        </div>
        <div class="success-text">
          <span style="font-size: 22px">批量导入成功</span>
          <span>您已成功导入{{ resluts.successNum }}条数据</span>
        </div>
        <div class="text-center mt20">
          <el-button type="primary" @click="cancel">确定</el-button>
        </div>
      </div>
    </div>
    <!-- 第四步 End -->
    <template #footer>
      <div class="dialog-footer" v-if="!isimported && stepActive != 1">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="submitFile"
          v-if="!isUpload"
          :disabled="!uploadFlag"
          >上传到服务器</el-button
        >
        <el-button type="primary" :loading="loading" @click="addFile" v-if="isUpload"
          >下一步</el-button
        >
      </div>
      <div class="dialog-footer" v-if="isimported && stepActive != 1">
        <el-button
          type="text"
          v-if="resluts.errorSize > 0"
          @click="errorImport"
          style="float: left"
          >异常导出</el-button
        >
        <el-button @click="backimport">返回上一步</el-button>
        <el-button
          type="primary"
          :loading="submitData"
          v-if="resluts.successNum > 0"
          @click="submit()"
          >提交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { logisticsVerifyFile, addByExcel } from "@/api/writ/lawyer";
const { proxy } = getCurrentInstance();
const emit = defineEmits({ getList: Function });
const open = ref(false);
const verifyId = ref(undefined);
const loading = ref(false);
const stepActive = ref(0); //当前步骤

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/document/message/upload",
});
const uploadFlag = ref(false); //是否打开文件
const isimported = ref(false); //文件是否导入完成
const isUpload = ref(false);
const resluts = ref({}); //文件结果
const fileUrl = ref(""); //上传成功的文件链接
const submitData = ref(false);

/* 文件移除 */
function handleRemove(file, fileList) {
  uploadFlag.value = false;
  isUpload.value = false;
}

const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    return false;
  }
  uploadFlag.value = true;
  return true;
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 10 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过10M!");
    fileList.pop();
    return false;
  }
  if (fileList.length > 0) uploadFlag.value = true;
  if (fileList.length == 0) {
    uploadFlag.value = false;
    isUpload.value = false;
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code === 9000) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    loading.value = false;
    fileList.length = 0;
    uploadFlag.value = false;
    return false;
  } else if (code !== 200) {
    proxy.$modal.msgError(msg ? msg : `文件《${file.name}》导入失败！`);
    file.status = "error";
    loading.value = false;
    fileList.length = 0;
    uploadFlag.value = false;
    return false;
  } else {
    proxy.$modal.msgSuccess("文件导入成功！");
    fileUrl.value = data.fileUrl[0];
    isUpload.value = true;
    loading.value = false;
  }
};

//上传文件
function submitFile() {
  loading.value = true;
  proxy.$refs["uploadRef"].submit();
}

//预览信息
function addFile() {
  loading.value = true;
  logisticsVerifyFile({ url: fileUrl.value })
    .then((res) => {
      resluts.value = res.data;
      isimported.value = true;
      loading.value = false;
      verifyId.value = res.data.verifyId;
    })
    .catch(() => {
      loading.value = false;
    });
}

//打开弹窗
function opendialog() {
  open.value = true;
}

//提交数据
function submit() {
  addByExcel({ verifyId: verifyId.value, type: 1 })
    .then((res) => {
      stepActive.value = 1;
      submitData.value = false;
      emit("getList");
    })
    .catch(() => {
      submitData.value = false;
    });
}

//模板下载
function downTpl() {
  proxy.download("/document/item/downloadTemplate", {}, `tpl_物流模板.xlsx`);
}

function cancel() {
  stepActive.value = 0;
  isimported.value = false;
  open.value = false;
  uploadFlag.value = false;
  isUpload.value = false;
  proxy.$refs["uploadRef"]?.clearFiles();
  emit("getList");
}

//重新导入
function backimport() {
  isimported.value = false;
  uploadFlag.value = false;
  isUpload.value = false;
  // proxy.$refs["uploadRef"]?.clearFiles()
}

function errorImport() {
  let req = {
    key: resluts.value.errorRouteKey,
  };
  exportError(req)
    .then((res) => {
      window.open(res.data.data.url, "_blank");
    })
    .catch(() => {
      submitData.value = false;
    });
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.step-item {
  width: 90%;
  min-height: 400px;
  margin: 0 auto;
  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;
    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;
      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

.step-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 20px;
  border: 1px solid #d9d9d9;
  padding: 20px;
  .content-icon {
    font-size: 68px;
    color: #a8abb2;
    transform: rotate(180deg);
  }
  .content-icon.not180 {
    transform: rotate(0deg);
  }
  .text-box > * {
    margin: 0;
    padding: 0;
  }
  .text-box {
    .tit {
      color: #888888;
      font-size: 12px;
      line-height: 20px;
      margin-top: 5px;
    }
  }
}
.err-msg {
  margin: 20px;
}
.err-msg > p:not(:first-child) {
  color: #888888;
  font-size: 14px;
  line-height: 20px;
}
.pro-hint-box {
  margin-top: 60px;
}
.pro-hint {
  font-size: 14px;
  color: #888888;
  margin-top: 50px;
}
.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;
  .text-primary {
    cursor: pointer;
    text-decoration: underline;
  }
  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;
    .check-icon {
      font-size: 34px;
    }
  }
  .step-icon.loading {
    background-color: #ffffff;
    // .is-loading{
    //     font-size:34px;
    //     color: #3CC556;
    // }
    width: 150px;
    height: 150px;
  }
  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }
  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}
:deep(.el-upload) {
  display: block !important;
  .el-upload-dragger {
    width: 100% !important;
    height: 120px;
    text-align: left;
    // border: 1px solid #d9d9d9;
    border-radius: 0px;
    .el-icon--upload {
      font-size: 67px;
      margin: 0px 0 0 20px;
      line-height: 50px;
      height: 100%;
      width: 67px;
    }
    .el-upload__text {
      display: inline-block;
      vertical-align: top;
      position: relative;
      top: 20px;
      left: 6px;
      font-size: 18px;
    }
    .el-upload__tip {
      display: inline-block;
      position: relative;
      top: -20px;
      left: -136px;
    }
    .el-upload__em {
      display: inline-block;
      position: relative;
      left: -394px;
      color: var(--el-color-primary);
      font-size: 14px;
      top: 5px;
    }
  }
}
.step-data {
  border: 1px solid #e9e9e9;
  .msg-data {
    border-bottom: 1px solid #e9e9e9;
    .msg-icon {
      background-color: #e6e6e6;
      text-align: center;
      vertical-align: top;
      width: 100px;
      height: 100px;
      display: inline-block;
      i {
        margin-top: 25px;
      }
    }
    .msg-count {
      display: inline-block;
      height: 100px;
      padding-left: 20px;
    }
  }
  .err-msg {
    height: 240px;
    overflow: auto;
  }
}
.success-text {
  margin-top: 20px;
  text-align: center;
  span {
    display: block;
    margin-top: 10px;
  }
}
.success-msg {
  position: relative;
  top: 80px;
}
.el-load-span {
  display: inline-block;
  position: relative;
  top: -48px;
  left: 170px;
  color: #409eff;
  font-size: 14px;
}
</style>
