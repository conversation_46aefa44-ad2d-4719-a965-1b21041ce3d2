<template>
    <div v-if="showWorkPhoneIcon">
        <el-icon :size="size" :color="color" @click="HandleOutboundCall"><Iphone /></el-icon>
    </div>
</template>

<script setup>
import { partnerCustomer } from "@/api/workPhone/index.js"
const { proxy } = getCurrentInstance();
const emits = defineEmits([]);

const store = useStore();
const showWorkPhoneIcon = computed(() => store.getters.showWorkPhoneIcon);

const props = defineProps({
    phoneNumber: {
        type: [String, Number, Boolean],
        default: false,
    },
    caseId: {
        type: [String, Number],
        default: undefined
    },
    borrower: {
        typr: String,
        default: undefined
    },
    size: {
        type: Number,
        default: 17
    },
    color: {
        type: String,
        default: "#296fe1"
    }
})

// 外呼
function HandleOutboundCall() {
    const req = {
        phone: props.phoneNumber,
        caseId: props.caseId,
        borrower: props.borrower,
        callModel: 6
    }
    partnerCustomer(req).then(((res) => {
        if(res.code == 200) {
            proxy.$modal.msgSuccess(res.msg);
        }
    }))
}

defineExpose({
})
</script>

<style lang="scss" scoped>
div {
    display: inline-block;
    :deep(.el-icon) {
        vertical-align: text-bottom;
        cursor: pointer;
    }
}
</style>
