import store from '@/store';
const getters = computed(() => store.getters);

let watermark = {}

let setWatermark = (str,caseId) => {
  let id = '1.23452384164.123412415'

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
  let can = document.createElement('canvas')
  can.width = 400
  can.height = 280

  let cans = can.getContext('2d')
  cans.rotate(-16 * Math.PI / 180)
  cans.font = '20px Cursive, serif'
  cans.fillStyle = 'rgba(50, 50, 50, 0.25)'
  cans.textAlign = 'center'
  cans.textBaseline = 'top'
  cans.fillText(str, 150, 90);
  if(caseId){
    cans.fillText(caseId, 160, 120)
  }

  let div = document.createElement('div')
  div.id = id
  div.style.pointerEvents = 'none'
  div.style.top = '0px'
  div.style.left = '230px'
  div.style.position = 'fixed'
  div.style.zIndex = '100000'
  div.style.width = document.documentElement.clientWidth  + 'px'
  div.style.height = document.documentElement.clientHeight  + 'px'
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
  document.body.appendChild(div)
  setTimeout(() => {
    // 防止用户在控制台修改删除水印
    let body = document.getElementsByTagName('body')[0]
    let options = {
      childList: true,
      attributes: true,
      characterData: true,
      subtree: true,
      attributeOldValue: true,
      characterDataOldValue: true
    }
    let observer = new MutationObserver(() => {
      div.id = id;
      div.style.pointerEvents = 'none'
      div.style.top = '0px'
      div.style.left = '230px'
      div.style.position = 'fixed'
      div.style.zIndex = '100000'
      div.style.width = document.documentElement.clientWidth  + 'px'
      div.style.height = document.documentElement.clientHeight + 'px'
      div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
      observer.disconnect();
      observer.observe(body, options)
      return false
    })
    observer.observe(body, options) // 监听body节点
  }, 1000);  // 防止在页面未渲染完成的时候找不到页面id
  return id
}

// 添加水印该方法只允许调用一次
watermark.set = (str,caseId) => {
  let id = setWatermark(str,caseId);
  setInterval(() => {
      if (document.getElementById(id) === null) {
          id = setWatermark(str,caseId);
      }
  }, 500);
  window.onresize = () => {
      setWatermark(str);
  };
}

const outWatermark = (id) => {
  if (document.getElementById(id) !== null) {
      const div = document.getElementById(id)
      div.style.display = 'none'
  }
}

watermark.out = () => {
  const str = '1.23452384164.123412416'
  outWatermark(str)
  // 去除水印
}

export default watermark