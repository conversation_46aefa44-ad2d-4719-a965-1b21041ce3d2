import request from '@/utils/request'

// 查询角色列表
export function repaysetList(query) {
    return request({
        url: '/teamSetup/list',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//更新状态 
export function editState(data) {
    return request({
        url: '/teamSetup/editState',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//获取必填字段下拉
export function getRequired() {
    return request({
        url: '/teamSetup/getRequired',
        method: 'get',
		gateway: 'cis'
    })
}

//添加
export function addRepayment(data) {
    return request({
        url: '/teamSetup/add',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//编辑 
export function editRepayment(data) {
    return request({
        url: '/teamSetup/edit',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}