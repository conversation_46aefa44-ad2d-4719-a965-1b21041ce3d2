import request from '@/utils/request'

//获取机构树结构 getTeamTree
export function getTeamTree() {
  return request({
    url: '/teamCase/DeptTreeType',
    method: 'get'
  })
}

//获取机构树结构(屏蔽登录人信息) DeptTreeTypeById
export function getTeamTreeById() {
  return request({
    url: '/teamCase/DeptTreeTypeById',
    method: 'get'
  })
}

//获取机构案件列表
export function selectTeamCase(query) {
  return request({
    url: '/teamCase/selectTeamCase',
    method: 'get',
    params: query
  })
}

//案件分配搜索案件量和案件结果
export function selectCaseManagesMoney(data) {
  return request({
    url: '/teamCase/selectCaseManagesMoney',
    method: 'post',
    data: data
  })
}

//退案
export function insertRetreat(data) {
  return request({
      url: '/teamCase/insertRetreat',
      method: 'post',
      data: data
  })
}

//留案
export function insertKeep(data) {
  return request({
      url: '/teamCase/insertKeep',
      method: 'post',
      data: data
  })
}

//留案
export function insertStop(data) {
  return request({
      url: '/teamCase/insertStop',
      method: 'post',
      data: data
  })
}

//标记案件
export function selectMarkCase(data) {
  return request({
      url: '/teamCase/selectMarkCase',
      method: 'post',
      data: data
  })
}

//分案获取可分配案件
export function selectCases(data) {
  return request({
      url: '/teamCase/selectCases',
      method: 'post',
      data: data
  })
}

//指定分案预览
export function specifyDivisionalData(data) {
  return request({
      url: '/teamCase/specifyDivisionalData',
      method: 'post',
      data: data
  })
}

//指定分案预览
export function updateCase(data) {
  return request({
      url: 'teamCase/updateCase',
      method: 'PUT',
      data: data
  })
}

//规则分案预览
export function ruleSplitPreview(data) {
  return request({
      url: 'teamCase/ruleSplitPreview',
      method: 'post',
      data: data
  })
}

//规则分案提交
export function writeRuleDivision(data) {
  return request({
      url: 'teamCase/writeRuleDivision',
      method: 'post',
      data: data
  })
}

// 导出案件
export function exportCase(data) {
  return request({
    url: '/teamCase/exportTeamCase',
    method: 'post',
    data: data
  })
}

// 放回导出催记字段
export function selectTickField() {
  return request({
    url: '/teamCase/selectTickField',
    method: 'get'
  })
}

// 导出催记
export function exportUrgeRecord(data) {
  return request({
    url: 'teamCase/exportUrgeRecord',
    method: 'post',
    data: data
  })
}


