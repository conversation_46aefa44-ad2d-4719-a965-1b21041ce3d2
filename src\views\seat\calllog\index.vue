<template>
  <div class="app-container">
      <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="录音列表" name="0" v-if="checkPermi(['seat:calllog:recordingList'])"></el-tab-pane>
          <el-tab-pane label="手机录音列表" name="1" v-if="checkPermi(['seat:calllog:phoneRecordingList'])"></el-tab-pane>
      </el-tabs>
      <div class="mt10" style="padding: 10px;">
          <recordingList v-if="activeTab == '0'"/>
          <phoneRecordingList v-if="activeTab == '1'" />
      </div>
  </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";

import recordingList from "./recordingList/index"
import phoneRecordingList from "./phoneRecordingList/index"
const activeTab = ref("0");

</script>

<style lang="scss" scoped></style>