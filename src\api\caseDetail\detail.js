import request from '@/utils/request'

//联系人列表
export function selectContact(query) {
  return request({
    url: '/collection/selectContact',
    method: 'get',
    params: query
  })
}

//新增联系人
export function insertInfoContact(data) {
  return request({
    url: '/collection/insertInfoContact',
    method: 'post',
    data: data
  })
}

//编辑联系人状态
export function updateInfoContact(data) {
  return request({
    url: '/collection/updateInfoContact',
    method: 'put',
    data: data
  })
}

//删除联系人
export function delInfoContact(data) {
  return request({
    url: '/collection/delInfoContact',
    method: 'delete',
    data: data
  })
}

//获取便签内容
export function selectNoteComplaint(id) {
  return request({
    url: '/collection/selectNoteComplaint/' + id,
    method: 'get'
  })
}

//编辑便签
export function editNote(data) {
  return request({
    url: '/collection/insertNote',
    method: 'post',
    data: data
  })
}

//编辑投诉
export function editComplaint(data) {
  return request({
    url: '/collection/insertComplaint',
    method: 'post',
    data: data
  })
}

//申请外访
export function insertOutsideRecord(data) {
  return request({
    url: '/collection/insertOutsideRecord',
    method: 'post',
    data: data
  })
}

//申请分期
export function insertAmortization(data) {
  return request({
    url: '/collection/insertAmortization',
    method: 'post',
    data: data
  })
}

//获取案件信息
export function getCaseInfo(id) {
  return request({
    url: '/collection/selectCase/' + id,
    method: 'get'
  })
}

//申请减免
export function insertReduction(data) {
  return request({
    url: '/collection/insertReduction',
    method: 'post',
    data: data
  })
}

//查询外访资源
export function selectOutsideResourceById(query) {
  return request({
    url: '/collection/selectOutsideResourceById',
    method: 'get',
    params: query
  })
}

//查询id查询回款信息
export function selectRepaymentPlan(query) {
  return request({
    url: '/collection/selectRepaymentPlan',
    method: 'get',
    params: query
  })
}

//根据案件id查询案件共债信息列表
export function selectQueryJointDebt(query) {
  return request({
    url: '/collection/selectQueryJointDebt',
    method: 'get',
    params: query
  })
}

//根据案件id查询案件共债信息
export function selectQueryAmountMoney(query) {
  return request({
    url: '/collection/selectQueryAmountMoney',
    method: 'get',
    params: query
  })
}

//根据案件id申请案件资料
export function applyCaseData(data) {
  return request({
    url: '/collection/insertRetrievalRecord',
    method: 'post',
    data: data
  })
}

// 根据案件id及搜索条件查看详情
export function agingDetailById(query) {
  return request({
      url: '/collection/selectTimeManage',
      method: 'get',
      params: query
  })
}
// 根据案件id及搜索条件查看详情
export function followOptionApi(query) {
  return request({
      url: '/collection/selectContactInformation',
      method: 'get',
      params: query
  })
}

//根据编辑收费
export function delCost(data) {
  return request({
    url: '/cost/record/remove',
    method: 'post',
    data: data
  })
}

//获取案件详情文书
export function selectWrit(query) {
  return request({
    url: '/team/letter/doc/getLitigationDoc',
    method: 'get',
    params: query
  })
}


//获取案件详情保全材料
export function selectSecurity(query) {
  return request({
    url: '/letter/team/letter/doc/getKeepIntactDoc',
    method: 'get',
    params: query
  })
}

//获取案件详情证据材料
export function selectLogistics(query) {
  return request({
    url: '/team/letter/doc/retrieval/getArchivalList',
    method: 'get',
    params: query
  })
}

//获取阶段表单
export function selectStageFrom(query) {
  return request({
    url: '/stage/register/getFormParam',
    method: 'get',
    params: query
  })
}
//获取阶段统计数据--我的诉讼案件
export function getMyCaseStageCount(query) {
  return request({
    url: '/team/dispose/getStageCountByMyCase',
    method: 'get',
    params: query
  })
}
//获取阶段统计数据
export function getStageCount(query) {
  return request({
    url: '/team/dispose/getStageCountByTeam',
    method: 'get',
    params: query
  })
}
//下载word格式文件
export function getWordFile(data) {
  return request({
    url: '/letter/document/template/getWord',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}


//获取款项类别的选项列表
export function getCostTypeOption(query) {
  return request({
    url: '/cost/record/getCostTypeOption',
    method: 'get',
    params: query
  })
}

//获取款项类别的选项列表
export function getPayMethodOption(query) {
  return request({
    url: '/cost/record/getPayMethodOption',
    method: 'get',
    params: query
  })
}

//根据添加收费
export function addCost(data) {
  return request({
    url: '/cost/record/add',
    method: 'post',
    data: data
  })
}

//根据编辑收费
export function editCost(data) {
  return request({
    url: '/cost/record/edit',
    method: 'post',
    data: data
  })
}

//获取档案资料列表
export function getArchivalList(query) {
  return request({
    url: '/management/retrieval/getArchivalList',
    method: 'get',
    params: query
  })
}

//获取文件在线预览主机地址
export function getFileOnlinePreviewHost(query) {
  return request({
    url: '/management/getFileOnlinePreviewHost',
    method: 'get',
    params: query
  })
}