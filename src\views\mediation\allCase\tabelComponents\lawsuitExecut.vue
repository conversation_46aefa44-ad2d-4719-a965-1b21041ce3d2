<template>
  <div>
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      :label-width="110"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
    >
      <el-form-item label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="被告" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入被告"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="结案方式" prop="closedMode">
        <el-select
          v-model="queryParams.closedMode"
          placeholder="请选择结案方式"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in closeWayEnum"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="案件涉及">
        <el-input
          v-model="queryParams.involvedWith"
          placeholder="请输入案件涉及"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="queryParams.executiveTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          style="width: 240px"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="执行法院" prop="executiveCourt">
        <el-select
          v-model="queryParams.executiveCourt"
          placeholder="请选择执行法院"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额" prop="syYhPrincipal">
        <div class="range-scope" style="width: 240px">
          <el-input
            type="text"
            v-model="queryParams.amount1"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
          <span>-</span>
          <el-input
            type="text"
            v-model="queryParams.amount2"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
        </div>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button
        :loading="loading"
        icon="Search"
        type="primary"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button
        :loading="loading"
        icon="Refresh"
        @click="antiShake(resetQuery)"
        >重置</el-button
      >
    </div>
    <div class="operation-revealing-area mb10">
      <!-- <el-button plain type="primary" @click="openCreateCaseTaskDialog" :disabled="single"
                v-hasPermi="['saasc:collection:createOutboundTasks']">创建智能外呼任务</el-button> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
      <el-tab-pane label="执行立案" name="执行立案" />
      <el-tab-pane label="判决审理" name="判决审理" />
      <el-tab-pane label="执行完成" name="执行完成" />
      <el-tab-pane label="执行撤案" name="执行撤案" />
      <el-tab-pane label="终本案件" name="终本案件" />
      <el-tab-pane label="全部" name="全部" />
    </el-tabs>
    <selectedAll
      ref="selectedAllRef"
      v-model:allQuery="queryParams.allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      :cusTableRef="proxy.$refs.multipleTableRef"
    >
      <template #content>
        <span class="case-data-list">
          案件数量：<i class="danger">{{ statistics.size || 0 }}</i>
        </span>
        <span class="case-data-list">
          初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
        </span>
        <span class="case-data-list">
          初始债权本金：<i class="danger">{{
            numFilter(statistics.concludeCaseAmount)
          }}</i>
        </span>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      class="multiple-table"
      @selection-change="handleSelectionChange"
      :data="dataList"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="center"
        prop="caseId"
        width="120"
      >
        <template #default="{ row, $index }">
          <el-button type="text" link @click="toDetails(row, $index)">{{
            row.caseId
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="被告"
        align="center"
        prop="clientName"
        width="120"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="标的额"
        align="center"
        prop="remainingDue"
        width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[3].visible"
        label="身份证号码"
        align="center"
        prop="clientIdcard"
        width="180px"
      />
      <el-table-column
        v-if="columns[4].visible"
        label="户籍地"
        align="center"
        prop="clientCensusRegister"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[5].visible"
        label="执行状态"
        align="center"
        prop="disposeStage"
        width="120"
      />
      <el-table-column
        v-if="columns[6].visible"
        label="结案标的额"
        align="center"
        prop="concludeCaseAmount"
        width="120"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.concludeCaseAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[7].visible"
        label="案件涉及"
        align="center"
        prop="involvedWith"
        width="120"
      />
      <el-table-column
        v-if="columns[8].visible"
        label="被执行人下落不明"
        align="center"
        prop="isMissing"
        :formatter="(row) => isNoEnum[row.isMissing]"
        width="130"
      />
      <el-table-column
        v-if="columns[9].visible"
        label="执行法院"
        align="center"
        prop="executiveCourt"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[10].visible"
        label="结案案由"
        align="center"
        prop="closedReason"
        width="120"
      />
      <el-table-column
        v-if="columns[11].visible"
        label="结案方式"
        align="center"
        prop="closedMode"
        width="100"
      />
      <el-table-column
        v-if="columns[12].visible"
        label="有无结案文书"
        align="center"
        key="isFile"
        prop="isFile"
        :width="120"
        :formatter="(row) => ({ 0: '是', 1: '否' }[row.isFile])"
      />
      <el-table-column
        v-if="columns[13].visible"
        label="跟进人员"
        align="center"
        prop="updateBy"
        width="120"
      />
      <el-table-column
        v-if="columns[14].visible"
        label="最近一次跟进时间"
        align="center"
        prop="updateTime"
        width="160px"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />
  </div>
</template>

<script setup>
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
import { getCourtOptions } from "@/api/common/common";
import {
  getExecuteList,
  totalMoneyExecute,
} from "@/api/mediation/lawsuitCarry";
import { formatParams2 } from "@/utils/common";
import { closeWayEnum, isNoEnum } from "@/utils/enum";
import { checkUserSip } from "@/api/system/user";

//全局数据
const router = useRouter();
const { proxy } = getCurrentInstance();

const total = ref(0);
//需要拆分的字段
const rangFiles = ["executiveTime"];
const activeTab = ref("全部");
const selectedArr = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
});
const statistics = ref({
  size: 0,
  money: 0,
  concludeCaseAmount: 0,
});
const loading = ref(false);
const dataList = ref([]);
const courtOptions = ref([]);
const showSearch = ref(false);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "被告", visible: true },
  { key: 2, label: "标的额", visible: true },
  { key: 3, label: "身份证号码", visible: true },
  { key: 4, label: "户籍地", visible: true },
  { key: 5, label: "执行状态", visible: true },
  { key: 6, label: "结案标的额", visible: true },
  { key: 7, label: "案件涉及", visible: true },
  { key: 8, label: "被执行人下落不明", visible: true },
  { key: 9, label: "执行法院", visible: true },
  { key: 10, label: "结案案由", visible: true },
  { key: 11, label: "结案方式", visible: true },
  { key: 12, label: "有无结案文书", visible: true },
  { key: 13, label: "跟进人员", visible: true },
  { key: 14, label: "最近一次跟进时间", visible: true },
]);

const single = ref(true);
const caseIds = ref([]);

// 获取列表
function getList() {
  const reqForm = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles))
  );
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["执行立案", "判决审理", "执行完成", "执行撤案", "终本案件"];
  reqForm.disposeStageList = String(reqForm.disposeStageList);
  reqForm.mineQuery = true;
  loading.value = true;
  getExecuteList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
getList();
//选择列表
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  single.value = !(selection.length != 0);
  caseIds.value = selection.map((item) => item.caseId);
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList();
  });
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList();
  });
}
function checkSelectable() {
  return !queryParams.value.allQuery;
}

//跳转案件详情
function toDetails(row, index) {
  // let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  // let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  // delete queryChange.pageNum;
  // delete queryChange.pageSize;
  // queryChange.executeStageList = activeTab.value != '全部' ? [activeTab.value] : ['执行立案', '判决审理', '执行完成', '执行撤案', '终本案件']
  // let searchInfo = {
  //     query: {
  //         manageQueryParam: queryChange, //查询参数
  //         pageNumber: pageNumber, //当前第几页(变动)
  //         pageSize: 1, //一页一条
  //         pageIndex: 0, //当前第一条
  //         caseIdCurrent: row.caseId, //当前案件id
  //     }, //查询参数
  //     type: "mycase",
  //     total: total.value, //查询到的案件总数
  // };
  // localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  // router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
  router.push({ path: `/case/allcase-detail/caseDetails/${row.caseId}` });
}
getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}
//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!queryParams.value.allQuery && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, concludeCaseAmount: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      reqForm.mineQuery = true;
      totalMoneyExecute(reqForm)
        .then((res) => {
          statistics.value = res.data;
        })
        .finally(() => reslove());
    });
  });
}

function getReqParams() {
  const reqParams = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles))
  );
  const reqForm = formatParams2(
    reqParams,
    selectedArr,
    queryParams.value.allQuery
  );
  reqForm.condition = queryParams.value.allQuery;
  reqForm.disposeStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : ["执行立案", "判决审理", "执行完成", "执行撤案", "终本案件"];
  reqForm.mineQuery = true;
  // reqForm.disposeStageList = String(reqForm.disposeStageList)
  return reqForm;
}

function openCreateCaseTaskDialog() {
  checkUserSip().then((res) => {
    if (res.data?.isPreTestSip) {
      //   const query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)));
      //   query.disposeStageList = activeTab.value != '全部' ? [activeTab.value] : ['执行立案', '判决审理', '执行完成', '执行撤案', '终本案件'];
      //   query.condition = query.allQuery;
      //   if (!query.allQuery) query.caseIds = caseIds.value;
      const query = getReqParams();
      query.executeStageList = query.disposeStageList;
      delete query.disposeStageList;
      query.mineQuery = true;
      proxy.$refs["createCaseTaskRef"].openDialog(query, 1);
    } else {
      proxy.$modal.msgWarning("未分配预测式外呼坐席");
    }
  });
}

watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);
defineExpose({ getList });
</script>

<style lang="scss" scoped></style>
