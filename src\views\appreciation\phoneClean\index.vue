<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" ref="queryRef">
      <el-form-item label="清洗批次" prop="batchName">
        <el-select
          :loading="seloading"
          v-model="queryParams.batchName"
          clearable
          filterable
          @visible-change="getBatchList"
          placeholder="请选择清洗批次"
          @clear="resetQuery"
        >
          <el-option
            v-for="item in BatchList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="antiShake(handleQuery)"
          >查询</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 统计数据 -->
    <div class="custom-tables">
      <div class="custom-table">
        <h3
          style="
            font-size: 14px;
            padding-left: 20px;
            border-bottom: 1px solid #e8e8e8;
            line-height: 34px;
          "
        >
          实时检测结果
        </h3>
        <div class="item" v-for="item in statisticsData" :key="item.status">
          <div class="title">
            {{ item.label }}
          </div>
          <div class="info">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <el-table v-loading="loading" :data="dataList" ref="multipleTableRef">
      <el-table-column prop="id" label="ID" width="100" align="center" />
      <el-table-column prop="caseNum" label="案件数量" align="center" />
      <el-table-column prop="num" label="清洗号码数量" align="center" />
      <el-table-column
        prop="status"
        label="清洗状态"
        :formatter="changeStatus"
        align="center"
      />
      <el-table-column prop="importTime" label="申请时间" align="center" />
      <!-- <el-table-column prop="money" label="清洗消费" align="center" /> -->
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button
            v-if="row.status == 2"
            type="text"
            @click="toResult(row.id)"
            >检测结果</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import {
  cleanBatchOptions,
  phoneCleanList,
  cleanBatchStatistics,
} from "@/api/appreciation/phoneClean";

const { proxy } = getCurrentInstance();
const router = useRouter();

const BatchList = ref([]);
const seloading = ref(false);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchName: undefined,
  },
});

const { queryParams } = toRefs(data);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const getBatchList = (val) => {
  if (val) {
    seloading.value = true;
    cleanBatchOptions()
      .then((res) => {
        BatchList.value = res.data;
      })
      .finally(() => {
        seloading.value = false;
      });
  }
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    batchName: undefined,
  };
  getList();
};

const getList = () => {
  loading.value = true;
  phoneCleanList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();

// 获取统计数据
// 1 正常 2 空号 3 通话中 4 不在网(空号) 5 关机 7 在网但不可用 13 停机 10 未知 9 服务器异常 12 不存在的号码
const statisticsData = ref([
  { label: "正常", status: 1, count: 0 },
  { label: "空号", status: 2, count: 0 },
  { label: "通话中", status: 3, count: 0 },
  { label: "不在网(空号)", status: 4, count: 0 },
  { label: "关机", status: 5, count: 0 },
  { label: "在网但不可用", status: 7, count: 0 },
  { label: "停机", status: 13, count: 0 },
  { label: "未知", status: 10, count: 0 },
  { label: "服务器异常", status: 9, count: 0 },
  { label: "不存在的号码", status: 12, count: 0 },
]);
const getStatistics = () => {
  statisticsData.value = [
    { label: "正常", status: 1, count: 0 },
    { label: "空号", status: 2, count: 0 },
    { label: "通话中", status: 3, count: 0 },
    { label: "不在网(空号)", status: 4, count: 0 },
    { label: "关机", status: 5, count: 0 },
    { label: "在网但不可用", status: 7, count: 0 },
    { label: "停机", status: 13, count: 0 },
    { label: "未知", status: 10, count: 0 },
    { label: "服务器异常", status: 9, count: 0 },
    { label: "不存在的号码", status: 12, count: 0 },
  ];
  cleanBatchStatistics().then((res) => {
    if (res.data.length > 0) {
      for (let i = 0; i < res.data.length; i++) {
        for (let j = 0; j < statisticsData.value.length; j++) {
          if (res.data[i].status === statisticsData.value[j].status) {
            statisticsData.value[j].count = res.data[i].count;
            break;
          }
        }
      }
    }
  });
};
getStatistics();

const changeStatus = (row) => {
  return ["", "检测中", "检测完成", "检测失败"][row.status] || "--";
};

const toResult = (id) => {
  router.push({
    path: `/ApplicationManagement/phoneClean-detail/results/${id}`,
  });
};
</script>

<style lang="scss" scoped>
.custom-tables {
  padding-bottom: 30px;
  overflow: hidden;
  line-height: 34px;
}

.custom-table {
  float: left;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.custom-table .item {
  float: left;
  width: auto;
  padding: 0 10px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
}

.custom-table .item:last-child {
  border-right: 0;
}

.custom-table .item .title {
  color: #888888;
  border-bottom: 1px solid #e8e8e8;
}

.custom-table .item .info {
  color: #3f3f3f;
  font-size: 18px;
  font-weight: bold;
}

.custom-table-status {
  float: right;
}
</style>
