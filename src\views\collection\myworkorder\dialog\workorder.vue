<template>
  <div class="app-container">
    <el-row class="mt10 mb10"
      ><el-button type="primary" @click="toBack"
        ><el-icon><ArrowLeft /></el-icon>返回列表</el-button
      ><span class="details-title">工单详情</span
      ><span :class="`details-status ${state == '待处理' ? 'text-red' : 'text-green'}`">{{
        state || "待处理"
      }}</span></el-row
    >
    <div class="details-content warp">
      <el-form :inline="true">
        <div class="flex">
          <div>
            <!-- <el-form-item label="委案团队:">{{ detailInfo.cname }}</el-form-item> -->
            <el-form-item label="处置人员:">{{ detailInfo.employeeName }}</el-form-item>
            <el-form-item label="发起人:">{{ detailInfo.nickName }}</el-form-item>
            <el-form-item label="发起时间:">{{ detailInfo.createTime }}</el-form-item>
          </div>
          <div>
            <el-form-item label="渠道来源:">{{ detailInfo.channelSource }}</el-form-item>
            <el-form-item label="问题类型:">{{ detailInfo.questionType }}</el-form-item>
          </div>
        </div>
        <div v-if="detailInfo.complaintLevel">
          <el-form-item label="投诉至:">{{ detailInfo.complaintLevel }}</el-form-item>
        </div>
        <div>
          <el-form-item label="工单内容:">{{ detailInfo.questionContent }}</el-form-item>
        </div>
        <div>
          <el-form-item label="来电号码:"><span>{{ detailInfo?.callNumber || `--`}}</span><callBarVue v-if="detailInfo?.callNumber" class="ml5" :workOrderId="route.params.id"  :phoneState="detailInfo?.phoneState || 0" :key="htrxCall" /></el-form-item>
        </div>
      </el-form>

      <div class="line"></div>

      <el-form
        v-if="
          (detailInfo.orderStatus == '待处理' && detailInfo.button == 1) ||
          (detailInfo.orderStatus == '处理中' && detailInfo.button == 1)
        "
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="78px"
      >
        <el-form-item label="跟进内容" prop="workFollowContent">
          <el-input
            v-model="form.workFollowContent"
            type="textarea"
            :rows="4"
            placeholder="请输入"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="附件" prop="accessory">
          <el-upload
            ref="uploadRef"
            accept=".xls, .xlsx, .docx, .doc, .mp3, .pdf, .zip, .jpg, .png "
            :headers="upload.headers"
            :action="upload.url"
            :before-upload="handleFileUploadBefore"
            :on-change="handleEditChange"
            :limit="5"
            :on-success="handleContractFileSuccess"
            :file-list="fileList"
            :auto-upload="false"
            :disabled="count >= 5"
          >
            <template #trigger>
              <el-button class="mr10" :disabled="count >= 5" type="primary"
                >选取文件</el-button
              >
            </template>
            <el-button class="ml10" type="success" @click="submitFile()"
              >上传到服务器</el-button
            >
            <template #tip>
              <div class="el-upload__tip">
                支持格式：.xls, .xlsx, .docx, .doc, .mp3, .pdf, .zip, .jpg, .png
                ，单个文件不能超过20MB,数量不超过5个；
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item class="mt10">
          <el-button type="primary" :loading="subloading" @click="submit"
            >提交跟进内容</el-button
          >
        </el-form-item>
      </el-form>
      <el-form label-width="78px">
        <el-form-item>
          <div class="follow-warp">
            <div>跟进内容：</div>
            <div class="item-warp" v-for="(item,i) in workFollowUp" :key="item.id">
              <div class="time" v-if="i == 0 || item.createdTime?.substring(0,10) != workFollowUp[i - 1]?.createdTime?.substring(0,10)">
                <span class="spot"></span>
                <div class="time-text">{{ item.createdTime?.substring(0,10) }}</div>
              </div>
              <div class="content">
                <span class="circle">
                  <span></span>
                </span>
                <div class="content-card">
                  <div class="flex">
                    <span>跟进人: {{ item.createdBy }}</span>
                    <span>{{ item.createdTime }}</span>
                  </div>
                  <div class="text">跟进内容: {{ item.workFollowContent }}</div>
                  <div class="file" v-if="item?.workAnnexs?.length > 0" >
                    <div class="file-span">附件:</div>
                    <div class="file-list">
                      <div class="file-item" v-for="(v,index) in item.workAnnexs" :key="index">
                        <div class="file-name">
                          <el-icon class="mt3" color="#909399" :size="16"
                            ><Tickets
                          /></el-icon>
                          <span class="name-span">{{v.fileName}}</span>
                        </div>
                        <div class="file-down">
                          <el-button
                            type="text"
                            @click="downFileByLink(v.fileName, v.fileUrl)"
                            >点击下载</el-button
                          >
                        </div>
                      </div>                
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <!-- <div
        v-for="(item, index) in orderName"
        :key="index"
        class="mb10 mt10 ml20 h32 item"
      >
        <div class="item-title" v-if="item != '投诉至'&& data[index]">{{ item }}:</div>
        <div class="ml10 item-label" v-if="data[index]">{{ data[index] || '无' }}</div>
      </div>
      <div class="mb10 mt10 ml20 h32 item" v-if="state !== '待处理'">
        <div class="item-title">跟进内容:</div>
        <div class="ml10 item-label" >
          <div v-for="(item, index) in workFollowUp" :key="index">
            <div style="text-align: left; line-height: 32px;vertical-align: top;">
            {{ item.workFollowContent }}
          </div>
          <div style="text-align: right; line-height: 32px;vertical-align: top;">跟进人：{{ item.createdBy }}{{`  ${item.createdTime}`}}</div>
          </div>
        </div>
      </div>
      <el-row>
       <el-button
            type="success"
             style="margin-left:40px;margin-top:40px"
            v-if="state == '待处理' && button == 1 || state == '处理中' && button == 1"
            @click="followUpWorkOrder()"
            >工单处理</el-button
          >
      </el-row> -->
    </div>
    <!-- 继续跟进 -->
    <followUp ref="followUpRef" @getdetails="getdetails" />
  </div>
</template>
<script setup name="CollectionWorkorderDetails">
import { getToken } from "@/utils/auth";
import { updateWorkOrder, getFollowUp, addFollowUp } from "@/api/collection/myworkorder";
import followUp from "./followUp.vue";
import callBarVue from "@/components/callBar/index.vue";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const emit = defineEmits();
const store = useStore();
const htrxCall = computed(() => store.getters.htrxCall);

const open = ref(false);
const orderName = ref([
  "委案团队",
  "处置人员",
  "发起人",
  "发起时间",
  "渠道来源",
  "问题类型",
  "投诉至",
  "工单内容",
]);
const orderList = ref([
  "cname",
  "employeeName",
  "nickName",
  "createTime",
  "channelSource",
  "questionType",
  "complaintLevel",
  "questionContent",
]);
const detailsList = ref([]);
const detailInfo = ref({});
const data = ref([]);
const subloading = ref(false);
const state = ref(undefined);
const workFollowUp = ref([]);
const button = ref(0);

const data_wcc = reactive({
  form: {
    workFollowContent: undefined,
    workAnnex: undefined,
  },
  rules: {
    workFollowContent: [
      {
        required: true,
        pattern:  /^\S*$/,
        message: "请不要输入空格！",
        trigger: "blur",
      },
      { required: true, message: "请填写跟进内容！", trigger: "blur" }
    ],
  },
});
const { form, rules } = toRefs(data_wcc);
//文件上传
const files = ref([]);
const count = ref(0);
const fileList = ref([]); //已上传文件列表
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/message/upload",
});

//打开弹窗
function getdetails() {
  let req = {
    id: route.params.id,
  };
  getFollowUp(req)
    .then((res) => {
      detailsList.value = [res.data];
      detailInfo.value = res.data;
      data.value = orderList.value.map((item) => detailsList.value[0][item]);
      state.value = res.data.orderStatus;
      workFollowUp.value = res.data?.workFollowUp || [];
      button.value = res.data.button;
    })
    .catch(() => {
      // loading.value = false;
    });
}
provide("getdetails", Function, true);
getdetails();

watch(()=>route.params?.id,(newval) =>{
  if(route.params?.id){
    getdetails()
  }
},{deep:true})

//继续跟进
function followUpWorkOrder() {
  let id = route.params.id;
  proxy.$refs["followUpRef"].opendialog(id);
}

//提交
function submit() {
  subloading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      form.value.id = route.params.id;
      let newFileList = [];
      files.value.forEach((item,index) => {
        let obj = {
          fileName:item.name,
          fileUrl:item.response.data.url
        };
        newFileList.push(obj)
      });
      form.value.workAnnex = newFileList;
      addFollowUp(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess("操作成功！");
          getdetails();
          reset();
        })
        .finally(() => {
          subloading.value = false;
        });
    } else {
      subloading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  proxy.$refs["uploadRef"]?.clearFiles();
  form.value = {
    workFollowContent: undefined,
    workAnnex:undefined
  };
  files.value = [];
  fileList.value = []
  count.value = 0
}

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

//文件上传前的处理
const handleFileUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    fileList.pop();
    return false;
  }
  count.value = fileList.length;
}

//委案合同上传文件上传成功处理
const handleContractFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    files.value = fileList;
  }
};

//文件下载
function downFileByLink(name,fileUrl){
    if (
      fileUrl.indexOf(".png") > -1 ||
      fileUrl.indexOf(".jpg") > -1 ||
      fileUrl.indexOf(".jpeg") > -1
    ) {
      let data = {
        url: fileUrl,
        type: "img",
        name: name,
      };
      downloadType(data);
    } else if (fileUrl.indexOf(".pdf") > -1) {
      let data = {
        url: fileUrl,
        type: "pdf",
        name: name,
      };
      downloadType(data);
    } else {
      downOtherFile(name,fileUrl)
      // window.open(fileUrl+`?attname=${name}`, "_blank");
    }
}

function downOtherFile(name,fileUrl){
   const url = fileUrl
    let x = new XMLHttpRequest();
    x.open("GET", url, true);
    x.responseType = "blob";
    x.onload = function() {
      //self.exportLoading = false;
      let url = window.URL.createObjectURL(x.response);
      let a = document.createElement("a");
      a.href = url;
      a.download = name;
      a.click();
    };
    x.send();
}

//判断下载类型
function downloadType(data) {
  switch (data.type) {
    case "img":
      return downloadImg(data);
    case "pdf":
      return downloadFile(data);
  }
}

function downloadFile(data) {
  fetch(data.url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], {
          type:
            data.type == "pdf"
              ? "application/pdf"
              : data.type == "word"
              ? "application/msword"
              : data.type == "xlsx"
              ? "application/vnd.ms-excel"
              : "",
        })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      window.open(data.url+`?attname=${data.name}`, "_blank");
    });
}

//下载图片
function downloadImg(data) {
  pathToBase64(data.url)
    .then((res) => {
      const link = document.createElement("a");
      link.href = res;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((err) => {
      console.log(err);
    });
}

//获取Base64
function pathToBase64(url) {
  return new Promise((resolve, reject) => {
    let image = new Image();
    image.onload = function () {
      let canvas = document.createElement("canvas");
      canvas.width = this.naturalWidth;
      canvas.height = this.naturalHeight;
      canvas.getContext("2d").drawImage(image, 0, 0);
      let result = canvas.toDataURL("image/png");
      resolve(result);
    };
    image.setAttribute("crossOrigin", "Anonymous");
    image.src = url;
    image.onerror = () => {
      reject(new Error("urlToBase64 error"));
    };
  });
}

//取消
const toBack = () => {
  const obj = { path: "/Workorder/myworkorder" };
  proxy.$tab.closeOpenPage(obj);
};

</script>
<style lang="scss" scoped>
.details-title {
  font-size: 26px;
  margin-left: 50px;
  vertical-align: top;
}
.details-status {
  font-size: 12px;
  padding: 4px 10px;
  margin-left: 20px;
  background-color: #f2f2f2;
  height: 28px;
  margin-top: 5px;
}
.details-content {
  padding: 20px;
  min-height: 800px;
  border: 1px solid #d7d7d7;
}
.item {
  padding: 8px 0px;
  .item-title {
    height: 100%;
    display: inline-block;
    vertical-align: top;
    width: 120px;
    text-align: right;
  }
  .item-label {
    height: 100%;
    display: inline-block;
    width: 70%;
    vertical-align: top;
    word-break: break-all;
    margin-left: 20px;
    color: #666;
    font-size: 15px;
  }
}
.text-green {
  color: #94c746;
}
.text-red {
  color: red;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .title {
    font-weight: 400;
    font-size: 18px;
  }
}

.warp {
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  .flex {
    justify-content: space-between;
  }
  .line {
    width: 100%;
    height: 0;
    border-top: 1px solid #dcdfe6;
    margin-bottom: 14px;
  }
}
:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: bold;
  color: #326fea;
}

.follow-warp {
  .item-warp {
    position: relative;
    &:after {
      content: "";
      display: block;
      position: absolute;
      left: 6px;
      top: 0px;
      width: 0px;
      border-left: 1px solid #dcdfe6;
      height: 100%;
      z-index: 1;
    }
  }
  .time {
    width: 100%;
    height: 42px;
    display: flex;
    justify-content: flex-start;
    position: relative;
    z-index: 2;
    .spot {
      width: 13px;
      height: 13px;
      background: #326fea;
      border: 2px solid #ffffff;
      border-radius: 50%;
    }
    .time-text {
      margin-left: 20px;
      line-height: 1;
      font-size: 14px;
    }
  }
  .content {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: flex-start;
    position: relative;
    z-index: 2;
    .circle {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 13px;
      height: 13px;
      border-radius: 50%;
      background-color: #ffffff;
      margin-left: 0.5px;
      span {
        display: inline-block;
        width: 9px;
        height: 9px;
        border: 2px solid #326fea;
        border-radius: 50%;
      }
    }
    .content-card {
      line-height: 1;
      margin-left: 20px;
      width: 100%;
      min-width: 500px;
      padding: 15px;
      background: rgba(64, 158, 255, 0.05);
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #326fea;
      margin-bottom: 20px;
      .text {
        margin-top: 16px;
      }
      .file {
        margin-top: 10px;
        vertical-align: top;
        .file-span {
          width: 50px;
          display: inline-block;
          line-height: 20px;
          height: 100%;
          vertical-align: top;
          margin-top: 5px;
        }
        .file-list{
          display: inline-block;
          height: 100%;
          width: 600px;
        }
        .file-item {
          display: inline-block;
          .file-name {
            display: inline-block;
            width: 500px;
            line-height: 20px;
            vertical-align: top;
            margin-top: 5px;
            .name-span {
              vertical-align: top;
              margin-left: 10px;
              word-break: break-all;
            }
          }
          .file-down {
            display: inline-block;
            width: 100px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
