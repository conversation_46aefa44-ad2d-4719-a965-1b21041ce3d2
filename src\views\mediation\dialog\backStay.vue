<template>
  <el-dialog :title="title" v-model="open" width="600px" :before-close="cancel" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item :label="label">
        <span class="text-primary">{{ caseNum }}</span>
      </el-form-item>
      <el-form-item :label="reasonLabel" prop="reason">
        <el-input type="textarea" v-model="form.reason" maxlength="300" show-word-limit placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { stayCase, backCase } from "@/api/collection/mycase";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);

const title = ref("");
const open = ref(false);
const loading = ref(false);
const label = ref("");
const caseNum = ref(0);
const reasonLabel = ref("");
const data = reactive({
  form: {},
  rules: {
    reason: [{ required: true, message: "请输入", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function openDialog(data) {
  reset();
  caseNum.value = data.total;
  if (data.type == 1) {
    label.value = "本次申请留案数量";
    title.value = "申请留案";
    reasonLabel.value = "留案原因";
  }
  if (data.type == 0) {
    label.value = "本次申请退案数量";
    title.value = "申请退案";
    reasonLabel.value = "退案原因";
  }
  form.value = { sign: data.sign, ...data.req }
  open.value = true;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
  };
  caseNum.value = 0;
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (title.value == "申请留案") {
        stayCase(form.value)
          .then((res) => {
            proxy.$modal.msgSuccess("申请提交成功！");
            cancel();
            emit("getList");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        backCase(form.value)
          .then((res) => {
            proxy.$modal.msgSuccess("申请提交成功！");
            cancel();
            emit("getList");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  openDialog,
});

</script>

<style scoped></style>
