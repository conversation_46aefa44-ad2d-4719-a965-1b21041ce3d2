<template>
  <el-dialog custom-class="pro-dialog" title="合规宣导" v-model="open" @close="cancel" append-to-body>
    <div class="content" v-html="content"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { lastLoginTime,selectPasswordTime } from "@/api/login";
import { ElMessageBox } from 'element-plus'

const store = useStore();
const router = useRouter();
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const content = ref("");

//打开弹窗
function opendialog(message) {
  if (message && message != "") {
    content.value = message;
    open.value = true;
  } else {
    router.push({ path: "/" });
    checkPassword();
  }
}

//确定
function submit() {
  loading.value = true;
  lastLoginTime()
    .then((res) => {
      loading.value = false;
        router.push({ path: "/" });
        checkPassword();
    })
    .catch(() => {
      loading.value = false;
    })
}


//获取用户密码时间
function checkPassword(){
  selectPasswordTime().then((res) =>{
    if(res.data.state == 1){
      ElMessageBox.alert(res.data.content, '提示', {
        confirmButtonText: '确定',
      }) 
    }
  })
}

//取消
function cancel() {
  open.value = false;
  content.value = "";
  store.dispatch('LogOut').then(() => {
    location.href = '/index';
  })
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped></style>
