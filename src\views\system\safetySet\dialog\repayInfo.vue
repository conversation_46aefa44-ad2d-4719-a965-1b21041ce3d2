<template>
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="134px">
            <el-form-item label="还款渠道名称" prop="repaymentMethod">
                <el-input v-model="form.repaymentMethod" placeholder="请输入还款渠道名称" />
            </el-form-item>
            <el-form-item label="登记回款必填字段" prop="registerPaymentArr">
                <el-select v-model="form.registerPaymentArr" multiple filterable :teleported="false"
                    placeholder="请输入或选择" style="width: 426px">
                    <el-option v-for="item in registerPaymentArr" :key="item" :disabled="item == '贷方发生额/元(收入)'"
                        :label="item" :value="item"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="自动对账匹配字段" prop="reconciliationArr">
                <el-select v-model="form.reconciliationArr" multiple filterable :reserve-keyword="false"
                    :teleported="false" placeholder="请输入或选择" style="width: 426px">
                    <el-option v-for="item in reconciliationArr" :key="item" :disabled="item == '贷方发生额/元(收入)'"
                        :label="item" :value="item"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" show-word-limit maxlength="300" placeholder="请输入" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getRequired, addRepayment, editRepayment } from "@/api/system/repaySet";
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const title = ref('添加还款渠道')
const open = ref(false)
const registerPaymentArr = ref(['']);
const reconciliationArr = ref([]);
const data = reactive({
    form: {
        repaymentMethod: undefined,
        registerPaymentArr: ['贷方发生额/元(收入)'],
        reconciliationArr: [],
        remark: undefined,
    },
    rules: {
        repaymentMethod: [
            { required: true, message: "请填写还款渠道名称！", trigger: "blur" },
        ],
        registerPaymentArr: [
            { required: true, message: "请选择登记回款必填字段！", trigger: "change" },
        ],
        reconciliationArr: [
            { required: true, message: "请选择自动对账匹配字段！", trigger: "change" },
        ],
    },
})
const loading = ref(false)
const { form, rules } = toRefs(data)

//提交
function submit() {
    proxy.$refs["formRef"].validate((valid) => {
        if (valid) {
            loading.value = true;
            let req = JSON.parse(JSON.stringify(form.value));
            req.registerPaymentArr =
                req.registerPaymentArr?.length === 0 ? undefined : req.registerPaymentArr;
            req.reconciliationArr =
                req.reconciliationArr?.length === 0 ? undefined : req.reconciliationArr;
            const reqApi = form.value.id ? editRepayment : addRepayment
            reqApi(req).then(() => {
                proxy.$modal.msgSuccess("修改成功！");
                props.getList && props.getList()
                cancel();
            }).finally(() => {
                loading.value = false;
            })
        }
    });
}
function openDialog(data) {
    open.value = true
    title.value = data.title
    getOptions(data.row)
}
function cancel() {
    open.value = false
    form.vlaue = {
        repaymentMethod: undefined,
        registerPaymentArr: ['贷方发生额/元(收入)'],
        reconciliationArr: ['贷方发生额/元(收入)'],
        remark: undefined,
    }
}
function getOptions(row) {
    getRequired().then((res) => {
        let data = res.data;
        registerPaymentArr.value = data.registerPaymentArr;
        reconciliationArr.value = data.reconciliationArr;
        if (row) {
            form.value.id = row.id;
            form.value.repaymentMethod = row.repaymentMethod;
            form.value.registerPaymentArr = row.registerPayment.split(";");
            form.value.reconciliationArr = row.reconciliation.split(";");
            form.value.remark = row.remark;
        }
    });
}
defineExpose({ openDialog })
</script>