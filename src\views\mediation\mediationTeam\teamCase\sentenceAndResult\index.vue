<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="side-edge">
        <div class="head-container mb10 pl20">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          团队判决排名
        </div>
        <div class="dept-list">
          <div class="dept-item" v-for="dept in allDept" :key="dept.id">
            <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
              {{ `${dept.name}(${dept.caseNum || 0})` }}
            </div>
            <!-- <div :class="`employ-item ${activeDept == employ.id ? 'active' : ''}`" v-for="employ in dept.children"
              :key="employ.id">
              <div @click="handleChangeRanke(employ.id, 1)">{{ `${employ.name}(${employ.caseNum || 0})` }}</div>
            </div> -->
          </div>
        </div>
      </el-col>

      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" :loading="loading" ref="queryRef" inline label-width="100px"
          :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
          <el-form-item prop="caseId" label="案件ID">
            <el-input v-model="queryParams.caseId" style="width: 280px" placeholder="请输入案件ID" />
          </el-form-item>
          <el-form-item prop="clientName" label="被告">
            <el-input v-model="queryParams.clientName" style="width: 280px" placeholder="请输入被告" />
          </el-form-item>
          <el-form-item prop="caseStatus" label="判决状态">
            <el-select placeholder="请选择判决状态" style="width: 280px" v-model="queryParams.caseStatus">
              <el-option label="已提交" value="已提交" />
              <el-option label="审核中" value="审核中" />
            </el-select>
          </el-form-item>
          <el-form-item prop="judgeTime" label="判决时间">
            <el-date-picker style="width: 280px" v-model="queryParams.judgeTime" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" unlink-panels />
          </el-form-item>
          <el-form-item prop="trialCourt" label="判决法院">
            <el-select placeholder="请选择判决法院" style="width: 280px" v-model="queryParams.trialCourt" @focus="getclosed">
              <el-option v-for="item in trialCourtOption" :label="item.info" :key="item.code" :value="item.info" />
            </el-select>
          </el-form-item>
          <el-form-item label="标的额">
            <div class="range-scope" style="width: 280px">
              <el-input v-model="queryParams.amount1" />
              <span>-</span>
              <el-input v-model="queryParams.amount2" />
            </div>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
          <el-button :disabled="selectedArr.length == 0" @click="handleExport" type="primary"
            v-if="checkPermi(['mediationTeam:sentenceAndResult:download'])">批量导出</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <el-tabs v-model="activeTab" @tab-click="tabChange">
          <el-tab-pane label="审理判决" name="审理判决" />
          <el-tab-pane label="撤案" name="诉讼撤案" />
          <el-tab-pane label="停诉" name="停止诉讼" />
          <el-tab-pane label="结案" name="诉讼结案" />
          <el-tab-pane label="全部" name="全部" />
        </el-tabs>
        <selectedAll ref="selectedAllRef" v-model:allQuery="allQuery" :selectedArr="selectedArr" :dataList="dataList"
          :cusTableRef="proxy.$refs.multipleTableRef">
          <template #content>
            <span class="case-data-list">
              客户数量：<i class="danger">{{ statistics.size || 0 }}</i>
            </span>
            <span class="case-data-list">
              初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
            </span>
            <span class="case-data-list">
              初始债权本金：<i class="danger">{{ numFilter(statistics.principal) }}</i>
            </span>
          </template>
        </selectedAll>
        <el-table v-loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" :data="dataList">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column v-if="columns[0].visible" label="案件ID" align="center" prop="caseId" width="100">
            <template #default="{ row }">
              <el-button type="text" link @click="toDetails(row)">{{ row.caseId }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="被告" align="center" prop="clientName" width="120" />
          <el-table-column v-if="columns[2].visible" label="标的额" align="center" prop="remainingDue" width="120" >
            <template #default="{ row }">
              <span>{{ numFilter(row.remainingDue) }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[3].visible" label="身份证号码" align="center" prop="clientIdNum" width="180px" />
          <el-table-column v-if="columns[4].visible" label="户籍地" align="center" prop="clientCensusRegister"
            width="160" />
          <el-table-column v-if="columns[5].visible" label="判决状态" align="center" prop="disposeStage" width="120" />
          <el-table-column v-if="columns[6].visible" label="判决时间" align="center" prop="judgeTime" width="160" />
          <el-table-column v-if="columns[7].visible" label="判决金额" align="center" prop="judgeSum" width="120" >
            <template #default="{ row }">
              <span>{{ numFilter(row.judgeSum) }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[8].visible" label="法院" align="center" prop="trialCourt" width="160" />
          <el-table-column v-if="columns[9].visible" label="判决描述" align="center" prop="judgeContent" width="180">
            <template #default="{ row }">
              <Tooltip :content="row.judgeContent" :length="15" />
            </template>
          </el-table-column>
          <el-table-column v-if="columns[10].visible" label="跟进人员" align="center" prop="updateBy" width="120" />
          <el-table-column v-if="columns[11].visible" label="最近一次跟进时间" align="center" prop="updateTime" width="160px" />
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="SentenceAndResult">
import { checkPermi } from "@/utils/permission";
import {
  getDeptTreeWithDisposeStage,
  getJudgeList,
  selectJudgeWithMoney,
} from "@/api/team/sentenceAndResult";
import { getCourtOptions } from "@/api/common/common"; //法院下拉
import { formatParams } from "@/utils/common";

const { proxy } = getCurrentInstance();
const router = useRouter();

//左侧团队树排名
const allDept = ref([]);
const showSearch = ref(false)
const activeDept = ref(undefined)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const trialCourtOption = ref([]);

const activeTab = ref("全部");
const dataList = ref([]);
const rangFiles = [];
const loading = ref(false);
const total = ref(0);
const selectedArr = ref([]);
const ids = ref([]);
const allQuery = ref(false);
const statistics = ref({
  size: 0,
  money: 0,
  principal: 0,
});
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '标的额', visible: true },
  { key: 3, label: '身份证号码', visible: true },
  { key: 4, label: '户籍地', visible: true },
  { key: 5, label: '判决状态', visible: true },
  { key: 6, label: '判决时间', visible: true },
  { key: 7, label: '判决金额', visible: true },
  { key: 8, label: '法院', visible: true },
  { key: 9, label: '判决描述', visible: true },
  { key: 10, label: '跟进人员', visible: true },
  { key: 11, label: '最近一次跟进时间', visible: true },
])

// 获取列表
function getList() {
  const reqForm = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)))
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['审理判决', '判决审理', '诉讼撤案', '停止诉讼', '诉讼结案', '申请执行立案']
  reqForm.smallStageList = String(reqForm.smallStageList)
  loading.value = true;
  getJudgeList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}
getList();
//获取团队排名
function getTeamTreeData() {
  let smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['审理判决', '判决审理', '诉讼撤案', '停止诉讼', '诉讼结案', '申请执行立案']
  smallStageList = String(smallStageList)
  getDeptTreeWithDisposeStage({ smallStageList }).then((res) => {
    allDept.value = res.data
  });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
  queryParams.value.stringDeptId = val
  activeDept.value = val
  queryParams.value.pageNum = 1
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}
// 获取机构列表
getCourts();
function getCourts() {
  getCourtOptions().then((res) => {
    trialCourtOption.value = res.data;
  });
}

// 搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// 重置
function resetQuery() {
  activeDept.value = undefined
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// 批量导出
function handleExport() {
  const reqForm = getReqParams()
  proxy.downloadforjson("/team/exportWithJudgeCase", reqForm, `判决与结果_${new Date().getTime()}.xlsx`);
}

//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0, };
      reslove()
      return false
    }
    selectJudgeWithMoney(getReqParams()).then((res) => {
      statistics.value = res.data;
    }).finally(() => reslove());
  })
}

// tab筛选
function tabChange() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//表格选择
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.caseId);
  selectedArr.value = selection;
}

// 表格是否可以选择
function checkSelectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId;
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = { query: queryChange };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
}
function getReqParams() {
  const reqParams = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)))
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.condition = allQuery.value
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['审理判决', '判决审理', '诉讼撤案', '停止诉讼', '诉讼结案', '申请执行立案']
  return reqForm
}
watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })
</script>

<style scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>


<style lang="scss" scoped>
.side-edge {
  height: calc(100vh - 125px);
  padding: 0 !important;
  border-right: 2px solid #eee;
}

.head-container {
  color: #333;
  font-weight: bold;
}

.dept-list {
  color: #5a5a5a;
  cursor: pointer;
  height: 80vh;
  overflow: auto;

  .active {
    border-right: 2px solid #60b2ff;
    background-color: #e6f7ff;
  }

  .dept-item {
    width: 100%;
    line-height: 44px;
    padding-left: 20px;
  }

  .employ-item {
    width: 100%;
    line-height: 44px;
    height: 44px;
    padding-left: 20px;
  }
}
</style>