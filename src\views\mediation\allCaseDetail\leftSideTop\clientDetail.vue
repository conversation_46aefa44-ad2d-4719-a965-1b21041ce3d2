<template>
  <div>
    <div class="add-wrap">
      <span>可用号码（{{ dataList.length }}）</span>
      <el-button type="primary" icon="Plus" @click="addContact"
        >新增联系人</el-button
      >
    </div>

    <!-- 新增联系人 -->
    <el-dialog title="新增联系人" v-model="open" width="500px" append-to-body>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input
            v-model="form.contactPhone"
            maxlength="15"
            show-word-limit
            type="text"
            placeholder="请输入联系电话"
            style="width: 240px"
          />
        </el-form-item>
        <!-- <el-form-item label="关系" prop="contactRelation">
          <el-select
            v-model="form.contactRelation"
            clearable
            placeholder="请选择"
            style="width: 240px"
          >
            <el-option label="本人" value="本人" />
            <el-option label="夫妻" value="夫妻" />
            <el-option label="父母" value="父母" />
            <el-option label="兄弟" value="兄弟" />
            <el-option label="姐妹" value="姐妹" />
            <el-option label="朋友" value="朋友" />
            <el-option label="同事" value="同事" />
            <el-option label="同学" value="同学" />
            <el-option label="亲属/家人" value="亲属/家人" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="号码类型" prop="label">
          <el-radio-group v-model="form.label">
            <el-radio label="本人号码">本人号码</el-radio>
            <el-radio label="家属号码">家属号码</el-radio>
            <el-radio label="非本人号码">非本人号码</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submit">
            确 定
          </el-button>
          <el-button :loading="subloading" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="lianxi-wrap">
      <el-scrollbar>
        <el-radio-group v-model="networkName" @change="changeNetWorkName">
          <el-radio-button
            v-for="item in networkNameDatas"
            :key="item.id"
            :label="item.networkName"
          />
        </el-radio-group>
      </el-scrollbar>
      <div class="lianxi-list" v-loading="loading">
        <div class="lianxi-item" v-for="item in dataList" :key="item.id">
          <div style="width: 150px">
            <div class="phone">{{ item.contactPhone }}</div>
            <div class="add-time" style="color: #409eff;font-size: 14px;font-weight: bold;">{{ changeCallTime(item.callTime) }}</div>
          </div>
          <el-select
            placeholder="打标记"
            v-model="item.label"
            @change="handleEdit(item)"
            style="width: 120px"
          >
            <el-option label="本人号码" value="本人号码" />
            <el-option label="家属号码" value="家属号码" />
            <el-option label="非本人号码" value="非本人号码" />
            <el-option label="暂停服务" value="暂停服务" />
            <el-option label="失联" value="失联" />
          </el-select>
          <div style="width: 20px">
            <callBarVue
              :contactId="item.id"
              :phoneState="item.phoneState"
              :key="htrxCall"
              :customRelay="customRelay"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="dengji-wrap">
      <el-radio-group v-model="dengjiType">
        <el-radio-button label="预估登记" />
        <el-radio-button label="出单登记" />
      </el-radio-group>
      <yuguDengji v-if="dengjiType === '预估登记'" />
      <chudanDengji v-if="dengjiType === '出单登记'" />
    </div>
  </div>
</template>

<script setup>
import yuguDengji from "./clientDetailDengji/yuguDengji.vue";
import chudanDengji from "./clientDetailDengji/chudanDengji.vue";
import {
  insertInfoContact,
  updateInfoContact,
} from "@/api/mediation/allCaseDetail";
import { selectContact } from "@/api/caseDetail/detail";

const { proxy } = getCurrentInstance();
// 接收父组件传值
const caseId = inject("caseId");
const store = useStore();
const emits = defineEmits(["authorizationStatus"]);

const networkNameDatas = computed(
  () => store.state.allCaseDetail.networkNameDatas
);
const customRelay = ref({});
const open = ref(false);
const subloading = ref(false);
const form = ref({
  caseId: caseId,
  phoneState: 0,
  contactPhone: "",
  // contactRelation: "",
  label: "本人号码",
});
const rules = ref({
  contactPhone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
  ],
  // contactRelation: [{ required: true, message: "请选择关系", trigger: "blur" }],
});

// 新增联系人
function addContact() {
  open.value = true;
}

// 新增联系人提交
function submit() {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      subloading.value = true;
      insertInfoContact(form.value)
        .then((res) => {
          getDataList();
          cancel();
          proxy.$modal.msgSuccess("操作成功！");
        })
        .finally(() => {
          subloading.value = false;
        });
    }
  });
}
// 新增联系人取消
function cancel() {
  open.value = false;
  form.value = {
    caseId: caseId,
    phoneState: 0,
    contactPhone: "",
    contactRelation: "",
    label: "本人号码",
  };
}

const htrxCall = computed(() => store.getters.htrxCall);
const networkName = ref("");

const queryParams = ref({
  caseId: caseId,
});
const loading = ref(false);
const dataList = ref([]);
// 通过线路获取联系人列表
function getDataList() {
  loading.value = true;
  selectContact(queryParams.value)
    .then((res) => {
      dataList.value = res.data.infoContacts;
      emits("authorizationStatus", res.data.authorizationStatus);
    })
    .finally(() => {
      loading.value = false;
    });
}
getDataList();

// 获取sip账号
watch(
  () => networkNameDatas.value,
  (newval) => {
    if (newval.length > 0) {
      const first = newval[0];
      networkName.value = first.networkName;
      customRelay.value = {
        trunkName: first.relaySign,
        trunkNumber: first.callFrom,
        callPrefix: first.outCallPrefix,
      };
      store.commit("allCaseDetail/SET_CUSTOM_RELAY", customRelay.value);
    }
  },
  { immediate: true, deep: true }
);

// 切换线路
function changeNetWorkName() {
  // 获取线路
  for (let i = 0; i < networkNameDatas.value.length; i++) {
    const item = networkNameDatas.value[i];
    if (item.networkName === networkName.value) {
      customRelay.value = {
        trunkName: item.relaySign,
        trunkNumber: item.callFrom,
        callPrefix: item.outCallPrefix,
      };
      store.commit("allCaseDetail/SET_CUSTOM_RELAY", customRelay.value);
      break;
    }
  }
}

// 呼叫时间转换
function changeCallTime(callTime) {
  if (!callTime) return ""
  // 获取当前时间
  const now = new Date();

  // callTime格式为yyyy-MM-dd HH:mm:ss，根据目前时间判断是多少天前，超过7天显示7 天前接通
  const callTimeDate = new Date(callTime);
  const diffTime = now.getTime() - callTimeDate.getTime();
  const diffDays = Math.floor(diffTime / (24 * 3600 * 1000));
  if (diffDays > 7) {
    return "7 天前接通";
  } else if (diffDays > 0) {
    return diffDays + " 天前接通";
  } else { //今天接通
    return "";
  }
}

// 编辑联系人
function handleEdit(item) {
  console.log(item);
  nextTick(() => {
    updateInfoContact(item)
      .then((res) => {
        proxy.$modal.msgSuccess("操作成功！");
      })
      .catch(() => {
        getDataList();
      });
  });
}

const dengjiType = ref("预估登记");
</script>

<style scoped>
.add-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}
.lianxi-list {
  margin-top: 16px;
  min-height: 200px;
}
.lianxi-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  padding: 10px;
  margin-bottom: 10px;
}
.lianxi-item .phone {
}
::v-deep(.lianxi-wrap .el-radio-group) {
  flex-wrap: nowrap;
}
</style>
