<template>
  <div class="app-container">
    <div style="padding: 10px 0">
      <el-button @click="toBack">返回列表</el-button>
    </div>
    <el-table v-loading="loading" :data="lawRecordData">
      <el-table-column
        prop="createTime"
        label="时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="lawsuitStatus"
        label="诉讼状态"
        align="center"
      ></el-table-column>
      <el-table-column prop="caseFilingNumber" label="立案号" align="center" />
      <el-table-column
        prop="propertyPreservationNumber"
        label="财保号"
        align="center"
      />
      <el-table-column
        prop="enforcementPreservationNumber"
        label="执保号"
        align="center"
      />
      <el-table-column
        prop="explain"
        label="说明"
        align="center"
      ></el-table-column>
      <el-table-column label="附件" align="center">
        <template #default="{ row }">
          <div class="fxn-detail-img-pdf">
            <span v-for="(item, index) in row.filePath" :key="index">
              <el-button
                v-if="getCaption(item) == 'pdf'"
                type="text"
                size="small"
                @click="clickImg(item)"
                >下载</el-button
              >
              <img
                v-else
                :src="item"
                alt=""
                width="20px"
                height="20px"
                @click="clickImg(item)"
              />
            </span>
            <el-button
              v-if="objectLen(row.filePath)"
              type="text"
              size="small"
              @click="bulkDownload(row.id)"
              >批量下载</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div
      v-if="!currentLawsuitStatus || currentLawsuitStatus !== '结案'"
      class="wcc-law-update fxn-margin-t-20"
    >
      <el-form
        :model="addLawStatus"
        :rules="addLawStatusRules"
        ref="addLawStatusRef"
      >
        <el-form-item label="状态" :label-width="formLabelWidth" prop="state">
          <el-select v-model="addLawStatus.state" placeholder="">
            <el-option
              v-for="item in nextSuitStatus"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明" :label-width="formLabelWidth" prop="explain">
          <el-input
            type="textarea"
            v-model="addLawStatus.explain"
            rows="5"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="附件"
          :label-width="formLabelWidth"
          prop="filePath"
        >
          <el-upload
            ref="uploadRef"
            accept=".png,.jpg,.pdf"
            :limit="5"
            :action="doUpload"
            :headers="headers"
            :on-change="fileChange"
            :before-upload="beforeAvatarUpload"
            :on-success="uploadSuccess"
            :on-remove="uploadRemove"
            :on-exceed="fileExceed"
            :file-list="fileList"
            :auto-upload="false"
          >
            <template #trigger>
              <el-button type="primary">选取文件</el-button>
            </template>
            <el-button
              style="margin-left: 10px"
              type="success"
              @click="submitUpload"
              >上传到服务器</el-button
            >
            <span style="margin-left: 10px"
              >附件数量最多不超过五个，支持jpg、png、pdf</span
            >
          </el-upload>
        </el-form-item>
      </el-form>
      <el-button
        class="wcc-margin-l-50"
        type="primary"
        :loading="btnLoading"
        @click="addSuitStatus"
        >更新进度</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {
  getLawProgressListApi,
  updateLawProgressApi,
} from "@/api/lawManage/selfLawList";
import {replaceNull} from "@/utils/common"
// 声明emit
const emit = defineEmits(["func"]);
const { proxy } = getCurrentInstance();

// 全局变量转为局部
let fileData = "";
let fileArray = [];

const currentLawsuitStatus = ref(undefined);

// 响应式状态
const loading = ref(false);
const LawInfo = reactive({});
const formLabelWidth = ref("50px");
const lawRecordData = ref([]);
const uploadRef = ref(null);
const addLawStatusRef = ref(null);
const hasFile = ref(0);
const btnLoading = ref(false);

const data = reactive({
  addLawStatus: {},
  addLawStatusRules: {
    state: [{ required: true, message: "请选择状态", trigger: "blur" }],
  },
});
const { addLawStatus, addLawStatusRules } = toRefs(data);

// 添加诉讼状态
const nextSuitStatus = ref([
  { id: 1, title: "发起诉讼" },
  { id: 2, title: "网上立案" },
  { id: 3, title: "执行局立案" },
  { id: 4, title: "诉讼立案" },
  { id: 5, title: "调解" },
  { id: 6, title: "判决生效" },
  { id: 7, title: "按撤诉处理" },
  { id: 8, title: "撤诉" },
  { id: 9, title: "强制执行" },
  { id: 10, title: "结案" },
]);
const fileList = ref([]);
const doUpload = ref(
  `${import.meta.env.VITE_APP_BASE_API}/lawsuitStatus/upload`
);
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileSuccess = ref([]);
// 返回
const toBack = () => {
  emit("func", false);
};

// 诉讼记录列表
const getSuitRecordList = (recordId) => {
  loading.value = true;
  getLawProgressListApi({ recordId: recordId })
    .then((res) => {
      lawRecordData.value = replaceNull(res.data) ;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 文件添加
const fileChange = (file, fileList) => {
  fileList.value = fileList;
  for (let i = 0, l = fileList.length; i < l; i++) {
    for (let j = i + 1; j < l; j++) {
      if (fileList[i].name === fileList[j].name) {
        fileList.splice(j, 1);
        j = ++i;
        proxy.$modal.msgWarning("文件已在上传列表中！");
      }
    }
  }
  hasFile.value = 1;
};

// 删除上传文件
const uploadRemove = (file, fileListParam) => {
  if (file.status === "success" && file.response.code === 200) {
    // 判断fileSuccess.value数组内是否有file.response.msg
    let index = fileSuccess.value.indexOf(file.response.msg);
    if (index !== -1) {
      fileSuccess.value.splice(index, 1);
    }
  }
  for (let i = 0; i < fileListParam.length; i++) {
    let obj = fileListParam[i];
    if (file.uid == obj.uid) {
      fileListParam.splice(i, 1);
      i--;
    }
  }
  fileList.value = fileListParam;
  if (fileListParam.length == 0) {
    hasFile.value = 0;
  } else {
    hasFile.value = 1;
  }
};

// 文件数量验证
const fileExceed = () => {
  proxy.$modal.msgWarning("上传文件数量不能超过五个！");
};

// 附件上传前的验证
const beforeAvatarUpload = (file) => {
  const arr = file.name.split(".");
  const type = arr[arr.length - 1]
  const extension = type === "jpg";
  const extension1 = type === "pdf";
  const extension2 = type === "png";
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!extension && !extension1 && !extension2) {
    proxy.$modal.msgWarning("上传文件只能是 jpg、png、pdf格式!");
  }

  if (!isLt10M) {
    proxy.$modal.msgWarning("上传文件大小不能超过 10MB!");
  }

  return (extension || extension1 || extension2) && isLt10M;
};

// 上传成功
const uploadSuccess = (response, file) => {
  fileData = response;
  if (fileData.code === 200) {
    fileSuccess.value.push(fileData.msg);
    proxy.$modal.msgSuccess("文件上传成功!");
  } else {
    proxy.$modal.msgWarning(file.msg);
    // 清除当前上传失败文件
    const index = fileList.value.findIndex(f => f.uid === file.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
      uploadRef.value.handleRemove(file);
    }
  }
};

// 上传文件
const submitUpload = () => {
  uploadRef.value.submit();
};

// 添加诉讼状态
const addSuitStatus = () => {
  if (fileSuccess.value.length > 0) {
    addLawStatus.value.filePath = fileSuccess.value.join(",");
  }
  addLawStatusRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true;
      updateLawProgressApi(addLawStatus.value)
        .then(() => {
          proxy.$modal.msgSuccess("添加成功！");
          getSuitRecordList(addLawStatus.value.recordId);
          uploadRef.value.clearFiles();
          fileList.value = [];
          addLawStatus.value.explain = "";
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      console.log("submit error");
    }
  });
};

// 获取指定字符后面的内容
const getCaption = (url) => {
  var index = url.lastIndexOf(".");
  url = url.substring(index + 1, url.length);
  return url;
};

// 诉讼记录附件点击
const clickImg = (item) => {
  window.open(item);
};

// 批量下载是否显示
const objectLen = (url) => {
  return url != "" && url != undefined ? url.length > 1 : false;
};

// 批量下载
const bulkDownload = (id) => {
    proxy.downloadforjsonGet(
    `/lawsuitStatus/download/${id}`,
    {},                       // 无需参数时传空对象
    `download_${id}.zip`)
    // window.open(
    //   TextUrl.value +
    //     "/lawsuitStatus/download" +
    //     id +
    //     "&token=" +
    //     JSON.parse(localStorage.getItem("token"))
    // );
};

function opendialog(data, lawsuitStatus) {
  // 打开弹窗
  currentLawsuitStatus.value = lawsuitStatus;
  getSuitRecordList(data.recordId);
  addLawStatus.value = data;
}

defineExpose({
  opendialog,
});
</script>

<style scoped>
.fxn-operate {
  padding-bottom: 1rem;
}
.wcc-law-update {
  margin: 0px auto;
  padding: 1rem;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(171, 186, 223, 0.2);
  border-radius: 0px 0px 2px 2px;
}
.wcc-margin-l-50 {
  margin-left: 50px;
}
</style>
