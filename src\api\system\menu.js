import request from '@/utils/request'

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: '/settings/selectMenu',
    method: 'get',
    params: query
  })
}

// 查询菜单详细
export function getMenu(menuId) {
  let query = {
    menuId: menuId
  }
  return request({
    url: '/settings/selectMenu',
    method: 'get',
    params: query
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/settings/insertMenu',
    method: 'post',
    data: data
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/settings/updateMenu',
    method: 'put',
    data: data
  })
}

// 删除菜单
export function delMenu(menuId) {
  return request({
    url: '/settings/deleteMenu/' + menuId,
    method: 'delete'
  })
}

//设置页面无法选择
export function selectRestrictedState() {
  return request({
    url: '/settings/selectRestrictedState',
    method: 'get'
  })
}

//获取标签信息
export function findIdLabel() {
  return request({
    url: '/settings/findIdLabel',
    method: 'get'
  })
}

//获取标签信息
export function setLabel(data) {
  return request({
    url: '/settings/label',
    method: 'PUT',
    data:data
  })
}

