<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="上传文件" description="根据模板修改后上传"></el-step>
        <el-step title="预览数据" description="预览并检查数据"></el-step>
        <el-step title="执行导入" description="数据导入至服务器"></el-step>
        <el-step title="导入成功" description="完成数据批量导入"></el-step>
      </el-steps>
    </div>

    <div class="step-item step1" v-show="stepActive === 0">
      <div v-if="!isimported">
        <div class="step-content mb20">
          <el-icon class="content-icon mr10"><upload-filled /></el-icon>
          <div class="text-box">
            <h3 class="title">填写导入员工的信息</h3>
            <p class="tit">
              请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除，单次导入的数据不超过1000条。
            </p>
            <div>
              <el-button type="text" @click="downTpl">下载模板</el-button>
            </div>
          </div>
        </div>
        <div class="step-content-upload">
          <el-upload
            ref="uploadRef"
            drag
            :limit="1"
            accept=".xls, .xlsx"
            :headers="upload.headers"
            :action="upload.url"
            :before-remove="remove"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            class="upload-box"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">上传填好的员工信息表</div>
            <div class="el-upload__tip mb10">
                <span class="mr10"
                  >文件后缀名必须为xls 或xls （即Excel格式），文件大小不得大于10M</span
                >
              </div>
              <span class="el-upload__em">点击上传</span>
          </el-upload>
        </div>
        <div class="step-content">
          <el-icon class="content-icon not180 mr10"><lock /></el-icon>
          <div class="text-box">
            <h3 class="title">账号密码为导入（密码长度为8-20位，其中需同时包含字母、数字和符号）</h3>
            <!-- <p class="tit">账号密码为导入（密码长度为8-12位，其中需同时包含字母、数字和符号）</p> -->
          </div>
        </div>
        <div class="mt20 text-center">
          <el-button type="primary" @click="submitFile">下一步</el-button>
        </div>
      </div>
      <div v-else style="width: 70%; margin: 0 auto; padding: 20px">
        <div class="mb8">
          <span>正常数量条数：</span>
          <span class="text-success">{{ resluts.successNum }}</span>
          <span class="ml10">异常数量条数：</span>
          <span class="text-danger">{{ resluts.errorNum }}</span>
        </div>
        <div class="err-msg">
          <p>异常提示：</p>
          <p v-for="item in resluts.errorMsg" :key="item">{{ item }}</p>
        </div>
        <div class="text-center">
          <el-button plain @click="backimport">重新导入</el-button>
          <el-button
            type="primary"
            plain
            :disabled="!resluts.successNum || resluts.successNum == 0"
            @click="nextStep(1)"
            >下一步</el-button
          >
        </div>
      </div>
    </div>
    <!-- 第一步 End -->

    <div class="step-item step2" v-show="stepActive === 1">
      <el-form class="mt20" :model="form" :rules="rules" ref="formRef">
        <el-table :data="form.tableData" border width="100%">
          <el-table-column label="员工姓名" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.employeeName`"
                :rules="rules.employeeName"
              >
                <el-input v-model="row.employeeName" placeholder="员工姓名"></el-input>
              </el-form-item>
              <span v-else>{{ row.employeeName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="登录账号" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.loginAccount`"
                :rules="rules.loginAccount"
              >
                <el-input v-model="row.loginAccount" placeholder="登录账号"></el-input>
              </el-form-item>
              <span v-else>{{ row.loginAccount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属部门" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.loginAccount`"
              >
                <tree-select
                  v-model:value="row.departmentId"
                  v-model:name="row.departments"
                  :options="deptOptions"
                  placeholder="所属部门"
                  :objMap="{ value: 'id', label: 'name', children: 'children' }"
                  style="width: 240px"
                />
              </el-form-item>
              <span v-else>{{ row.departments }}</span>
            </template>
          </el-table-column>
          <el-table-column label="员工角色" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.roleId`"
              >
                <el-select
                  v-model="row.roleId"
                  placeholder="选择角色"
                  clearable
                  filterable
                  :reserve-keyword="false"
                  @change="getRoleName(row, $index)"
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.id"
                    :label="item.roleName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <span v-else>{{ row.theRole }}</span>
            </template>
          </el-table-column>
          <el-table-column label="员工工号" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.employeesWorking`"
              >
                <el-input v-model="row.employeesWorking" :disabled="true" />
              </el-form-item>
              <span v-else>{{ row.employeesWorking }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手机号码" width="120" prop="phoneNumber" align="center">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="currentRowIndex === $index"
                :prop="`tableData.${$index}.phoneNumber`"
              >
                <el-input v-model="row.phoneNumber" type="number" placeholder="手机号码" />
              </el-form-item>
              <span v-else>{{ row.phoneNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ $index }">
              <el-form-item
                v-if="currentRowIndex !== undefined && currentRowIndex === $index"
              >
                <el-button type="text" @click="edit(undefined)">保存</el-button>
              </el-form-item>
              <el-button v-else type="text" @click="edit($index)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="text-center mt20">
        <el-button plain @click="backimport">重新导入</el-button>
        <el-button type="primary" plain @click="nextStep(2)">下一步</el-button>
      </div>
    </div>
    <!-- 第二步 End -->

    <div class="step-item step3" v-show="stepActive === 2">
      <div class="pro-hint-box">
        <el-progress
          :percentage="100"
          :duration="6"
          :indeterminate="true"
          :show-text="false"
          :stroke-width="12"
        />
        <div class="pro-hint text-center mb20">正在导入数据，请勿关闭或刷新页面</div>
      </div>
    </div>
    <!-- 第三步 End -->

    <div class="step-item step4" v-show="stepActive === 3">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <!-- <p>{{count}}账号默认密码：zws123456</p> -->
      </div>
      <div class="text-center mt30">
        <el-button @click="toBack">返回</el-button>
      </div>
    </div>
    <!-- 第四步 End -->
  </div>
</template>

<script setup name="ImportUser">
import { getToken } from "@/utils/auth";
import { getRoleOptions } from "@/api/system/roles";
import { deptTree } from "@/api/system/dept";
import { insertEmployeesBatch } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const stepActive = ref(0);
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/settings/importData",
});

const isimported = ref(false); //文件是否导入完成
const resluts = ref({}); //文件结果
const currentRowIndex = ref(undefined); //正在编辑的行
const deptOptions = ref([]); //部门
const roleOptions = ref([]); //角色
const count = ref('');
const data = reactive({
  form: {
    tableData: [],
  },
  rules: {
    employeeName: [{ required: true, message: "请填写员工姓名！", trigger: "blur" }],
    loginAccount: [{ required: true, message: "请填写登录账号！", trigger: "blur" }],
    phoneNumber: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
      // 这个只能验证手机号码
      // { pattern:/^0{0,1}(13[0-9]|15[7-9]|153|156|18[7-9])[0-9]{8}$/, message: "请输入合法手机号码", trigger: "blur" }
      // {
      //   pattern: /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/,
      //   message: "请输入合法手机号码/电话号",
      //   trigger: "blur",
      // },
    ],
  },
});
const { form, rules } = toRefs(data);

// 文件移除
function remove(file, fileList) {
  //form.value.fileUrl = ''
}

// 文件上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.tableData = data.employees;
    resluts.value = data;
    isimported.value = true;
  }
};

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

//模板下载
function downTpl() {
  proxy.download("settings/export", {}, `tpl_员工导入.xlsx`);
}

//重新导入
function backimport() {
  isimported.value = false;
  stepActive.value = 0;
  //proxy.$refs["uploadRef"].clearFiles()
}

//编辑表格
function edit(type) {
  currentRowIndex.value = type;
}

//下一步
function nextStep(next) {
  if (next === 1) {
    deptTree().then((res) => {
      deptOptions.value = res.data[0].children || [];
      getRoleOptions().then((res) => {
        roleOptions.value = res.data;
        stepActive.value = next;
      });
    });
  }

  if (next === 2) {
    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        stepActive.value = next;
        let req = JSON.parse(JSON.stringify(form.value.tableData))
        req?.forEach(item => {
          if(item?.departmentId){
            item.departmentId = item?.departmentId?.indexOf('Dept') == 0? item.departmentId.split(':')[1]:item.departmentId
          }
        });
        insertEmployeesBatch(req)
          .then((res) => {
            stepActive.value++;
            count.value = res?.data?`成功导入员工数量${res?.data}条，`:``;
          })
          .catch(() => {
            stepActive.value--;
          });
      }
    });
  }
}

//获取角色名称
function getRoleName(row, $index) {
  for (let i = 0; i < roleOptions.value.length; i++) {
    let item = roleOptions.value[i];
    if (item.id === row.roleId) {
      row.theRole = item.roleName;
      break;
    }
  }
}

//返回
const toBack = () => {
  const obj = { path: "/system/dept" };
  proxy.$tab.closeOpenPage(obj);
};
</script>


<style lang="scss" scoped>
.step-item {
  width: 90%;
  margin: 0 auto;
  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;
    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;
      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

.step-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 20px;
  border: 1px solid #d9d9d9;
  padding: 20px;
  .content-icon {
    font-size: 68px;
    color: #a8abb2;
    transform: rotate(180deg);
  }
  .content-icon.not180 {
    transform: rotate(0deg);
  }
  .text-box > * {
    margin: 0;
    padding: 0;
  }
  .text-box {
    .tit {
      color: #888888;
      font-size: 12px;
      line-height: 20px;
    }
  }
}
.err-msg > p:not(:first-child) {
  color: #888888;
  font-size: 14px;
  line-height: 20px;
}
.pro-hint-box {
  margin-top: 60px;
}
.pro-hint {
  font-size: 14px;
  color: #888888;
  margin-top: 50px;
}
.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;
  .text-primary {
    cursor: pointer;
    text-decoration: underline;
  }
  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;
    .check-icon {
      font-size: 34px;
    }
  }
  .step-icon.loading {
    background-color: #ffffff;
    // .is-loading{
    //     font-size:34px;
    //     color: #3CC556;
    // }
    width: 150px;
    height: 150px;
  }
  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }
  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}
:deep(.el-upload){
  display: block !important;
  .el-upload-dragger{
    width: 100% !important;
    height: 120px;
    text-align: left;
    // border: 1px solid #d9d9d9;
    border-radius: 0px;
    .el-icon--upload{
      font-size: 67px;
      margin: 0px 0 0 20px;
      line-height: 50px;
      height: 100%;
      width: 67px;
    }
    .el-upload__text{
      display: inline-block;
      vertical-align: top;
      position: relative;
      top: 25px;
      left: 10px;
      font-size: 18px;
    }
    .el-upload__tip{
      display: inline-block;
      position: relative;
      top: -30px;
      left: -170px;
    }
    .el-upload__em{
      display: inline-block;
      position: relative;
      left: -552px;
      color: var(--el-color-primary);
      font-size: 14px;
    }
  }
}
</style>
