import request from '@/utils/request'

//获取树
export function cisSetting() {
    return request({
        url: '/cisSetting/selectId',
        method: 'get',
		gateway: 'cis'
    })
}

//修改安全设置开关状态
export function changeSafeStatus(data) {
    return request({
      url: '/cisSetting/updateStates',
      method: 'put',
      data: data,
	  gateway: 'cis'
    })
  }

  //脱敏设置列表开关 
export function changSecrecys(data) {
    return request({
      url: '/cisSetting/updateDesensitization',
      method: 'put',
      data: data,
	  gateway: 'cis'
    })
  }

//水印字段修改
export function changeWatermark(data) {
    return request({
      url: '/cisSetting/updateWatermark',
      method: 'put',
      data: data,
	  gateway: 'cis'
    })
  }


//导出数据修改
export function editMenuStatus(data) {
    return request({
      url: '/cisSetting/updateTeamExport',
      method: 'put',
      data: data,
	  gateway: 'cis'
    })
  }

  
//获取导出设置列表
export function getTeamExportList(query) {
    return request({
      url: '/cisSetting/getTeamExportList',
      method: 'get',
      params: query,
	  gateway: 'cis'
    })
  }
  