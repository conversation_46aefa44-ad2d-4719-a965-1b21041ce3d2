{"name": "my-vue-app", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --host 0.0.0.0", "build:prod": "vite build", "dev:ghtj": "vite --host 0.0.0.0 --mode ghtj", "dev:zcax": "vite --host 0.0.0.0 --mode zcax", "build:ghtj-prod": "vite build --mode ghtj-prod", "build:zcax-prod": "vite build --mode zcax-prod", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "0.2.6", "@tinymce/tinymce-vue": "^4.0.2", "@vueuse/core": "^9.12.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "aieditor": "^1.0.14", "axios": "0.24.0", "echarts": "5.2.2", "element-plus": "2.0.1", "file-saver": "2.0.5", "fingerprintjs2": "^2.1.4", "html2canvas": "^1.4.1", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "jspdf": "^2.5.1", "nprogress": "0.2.0", "tinymce": "^5.8.0", "vue": "3.2.26", "vue-cropper": "1.0.2", "vue-demi": "^0.13.11", "vue-pdf-embed": "^1.1.4", "vue-router": "4.0.12", "vue3-pdfjs": "^0.1.6", "vuex": "4.0.2", "xss": "^1.0.11"}, "devDependencies": {"@vitejs/plugin-vue": "^2.2.0", "sass": "1.45.0", "unplugin-auto-import": "0.5.3", "vite": "^2.8.0", "vite-plugin-compression": "0.3.6", "vite-plugin-html": "^3.2.2", "vite-plugin-inspect": "^0.4.3", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.1.0"}}