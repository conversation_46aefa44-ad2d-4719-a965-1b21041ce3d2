<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="登录时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.accessTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="用户名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录IP" prop="ipaddr">
        <el-input
          v-model="queryParams.ipaddr"
          placeholder="请输入登录IP"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
     
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

     <el-table v-loading="loading" :data="userList">
        <el-table-column
          label="登录时间"
          align="center"
          key="accessTime"
          prop="accessTime"
        />
        <el-table-column
          label="用户名称"
          align="center"
          key="name"
          prop="name"
        />
        <el-table-column
          label="终端类型"
          align="center"
          key="terminalType"
          prop="terminalType"
        />
        <el-table-column
          label="浏览器/终端版本"
          align="center"
          key="terminalVersion"
          prop="terminalVersion"
        />
        <el-table-column label="操作系统" align="center" key="os" prop="os" width="300px" />
        <el-table-column
          label="服务器的设备名称"
          align="center"
          key="devicename"
          prop="devicename"
        />
        <el-table-column label="登录IP" align="center" key="ipaddr" prop="ipaddr" />
        <el-table-column
          label="所在地区"
          align="center"
          key="loginArea"
          prop="loginArea"
        />
      </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Logininfor">
import { selectEmployeesLogin} from "@/api/system/user";

const { proxy } = getCurrentInstance();

const userList = ref([]);
const loading = ref(true);
const total = ref(0);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  ipaddr: undefined,
  name: undefined,
  accessTime: [],
});
const rangfiles = ['accessTime']

/** 查询登录日志列表 */
//获取用户列表
function getList() {
  loading.value = true;
  selectEmployeesLogin(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      userList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = ({
    pageNum: 1,
    pageSize: 10,
    ipaddr: undefined,
    name: undefined,
    accessTime: [],
  });
  handleQuery();
}

</script>
