<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="记录时间" prop="logTime" key="logTime" align="center" />
    <el-table-column label="调诉阶段" prop="mediateState" key="mediateState" align="center" />
    <el-table-column label="内容" prop="createTime" key="createTime" align="center">
      <template #default="{ row }">
        <div v-if="row.law">选择机构：{{ row.law }}</div>
        <div v-if="row.documentNumber">立案号：{{ row.documentNumber }}</div>
        <div v-if="row.mediation">调诉材料：<span
            @click="downloadFile(row.mediationUrl, row.mediation)">{{ row.mediation }}</span></div>
        <div v-if="row.material">物料信息：{{ row.material }}</div>
        <div v-if="row.uploadBack">上传回执：<span
            @click="downloadFile(row.uploadBackUrl, row.uploadBack)">{{ row.uploadBack }}</span></div>
      </template>
    </el-table-column>
    <el-table-column label="操作人员" prop="creater" key="creater" align="center" />
  </el-table>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
  listData: {
    type: Array,
    default: [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

//下载操作
function downloadFile(url, name) {
  fetch(url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], {
          type: "application/pdf"
        })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      window.open(url + `?attname=${name}`, "_blank");
    });
  window.open(url)
}
defineExpose({ getList })
</script>

<style scoped></style>
