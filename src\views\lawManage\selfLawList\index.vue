<template>
  <div class="app-container">
    <div v-show="!showLawProgress">
      <el-form inline :model="queryParams" ref="queryRef">
        <el-form-item label="案件ID" prop="caseId">
          <el-input
            v-model="queryParams.caseId"
            placeholder="请输入"
            style="width: 240px"
            @keyup.enter="antiShake(handleQuery)"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户姓名" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入"
            style="width: 240px"
            @keyup.enter="antiShake(handleQuery)"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证" prop="idCard">
          <el-input
            v-model="queryParams.idCard"
            placeholder="请输入"
            maxlength="18"
            style="width: 240px"
            @keyup.enter="antiShake(handleQuery)"
          ></el-input>
        </el-form-item>
        <el-form-item label="发起时间" prop="createTime">
          <el-date-picker
            style="width: 240px"
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="委托方" prop="client">
          <el-select
            filterable
            v-model="queryParams.client"
            style="width: 240px"
          >
            <el-option
              v-for="item in entrusts"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="text-center">
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)"
          >重置</el-button
        >
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px 0;
        "
      >
        <el-radio-group v-model="statusActive" @change="handleClick">
          <el-radio-button :label="1">进行中</el-radio-button>
          <el-radio-button :label="2">已完成</el-radio-button>
          <el-radio-button label="全部">全部</el-radio-button>
        </el-radio-group>

        <div>
          <span>诉讼进度&nbsp;&nbsp;</span>
          <el-select
            v-model="lawsuitStatus"
            @change="handleLawsuitStatusChange"
          >
            <el-option
              v-for="item in lawStatusing"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </div>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column prop="caseId" label="案件ID" align="center" width="80">
          <template #default="{ row }">
            <router-link
              :to="{
                path: '/CaseDetail',
                query: { id: row.caseId },
              }"
              >{{ row.caseId }}</router-link
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="lawsuitStatus"
          label="诉讼进度"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="caseFilingNumber"
          label="立案号"
          align="center"
        />
        <el-table-column
          prop="propertyPreservationNumber"
          label="财保号"
          align="center"
        />
        <el-table-column
          prop="enforcementPreservationNumber"
          label="执保号"
          align="center"
        />
        <el-table-column
          prop="batchNumber"
          label="批次号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="client"
          label="委托方"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="customerName"
          label="客户姓名"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="entrustedAmount"
          label="委托金额"
          align="center"
        />
        <el-table-column
          prop="withdrawalReason"
          label="撤销原因"
          align="center"
        />
        <el-table-column
          prop="status"
          label="状态"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="OpenlawProgressBox(row)"
                >诉讼进度</el-button
              >
            </div>
            <div v-if="row.status == '正常'">
              <el-button type="text" @click="openCancelBox(row.id)"
                >撤销诉讼</el-button
              >
            </div>
            <div v-if="row.fileList.length > 0">
                <el-popover
                  placement="left-start"
                  width="500"
                  trigger="click"
                  :show-arrow="false"
                >
                  <template #reference>
                    <el-button type="text" @click="openFileDialog(row)">查看附件</el-button>
                  </template>
                  <div style="max-height: 300px; overflow-y: auto;">
                    <viewAttachment ref="fileDialogRef"></viewAttachment>
                  </div>
                </el-popover>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 撤销诉讼 -->
      <el-dialog
        title="撤销原因"
        v-model="cancelBox"
        :close-on-click-modal="true"
        append-to-body
      >
        <el-form
          :model="cancelForm"
          :rules="cancelFormRules"
          ref="cancelFormRef"
          label-width="70px"
        >
          <el-form-item label="原因" prop="reason">
            <el-input
              type="textarea"
              v-model="cancelForm.reason"
              rows="5"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelBox = false">取 消</el-button>
            <el-button type="primary" :loading="subloading" @click="cancelSuit">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <LawProgress
      v-show="showLawProgress"
      ref="lawProgressRef"
      @func="getMsgFormSon"
    ></LawProgress>
    <viewAttachment ref="fileDialogRef" />
  </div>
</template>
<script setup>
import { getLawListApi, cancelSuitApi } from "@/api/lawManage/selfLawList";
import { assetOwnerList } from "@/api/assets/assetside";
import LawProgress from "./LawProgress";
import {replaceNull} from "@/utils/common"
import viewAttachment from "./dialog/viewAttachment.vue"

const { proxy } = getCurrentInstance();
const queryRef = ref(null);
const cancelFormRef = ref(null);

// 查询
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    customerName: undefined,
    idCard: undefined,
    client: undefined,
    createTime: [],
  },
});
const { queryParams } = toRefs(data);
const total = ref(0);
const loading = ref(false);
const reqForm = ref({});
// const createTime = ref([]);
const entrusts = ref([]); //委托方（资产方）
const dataList = ref([]); // 诉讼列表
// Tabs
const statusActive = ref("全部");

// 诉讼状态
const lawsuitStatus = ref(0);
const lawStatusing = reactive([
  { id: 0, title: "全部" },
  { id: 1, title: "发起诉讼" },
  { id: 2, title: "网约立案" },
  { id: 3, title: "执行局立案" },
  { id: 4, title: "诉讼立案" },
  { id: 5, title: "调解" },
  { id: 6, title: "判决生效" },
  { id: 7, title: "按撤诉处理" },
  { id: 8, title: "撤诉" },
  { id: 9, title: "强制执行" },
  { id: 10, title: "结案" },
]);

// 诉讼进度
const showLawProgress = ref(false);
const LawProgressInfo = reactive({
  recordId: undefined,
  state: undefined,
  explain: undefined,
  filePath: undefined,
});

// 撤销诉讼
const subloading = ref(false);
const cancelBox = ref(false);
const cancelForm = reactive({
  reason: undefined,
  recordId: undefined,
});
const cancelFormRules = reactive({
  reason: [{ required: true, message: "请填写撤销原因！", trigger: "blur" }],
});

// 方法
const handleClick = () => {
  lawsuitStatus.value = 0;
  getList();
};

const handleLawsuitStatusChange = () => {
  getList()
};

// 在 data 对象中添加日期范围字段配置
const rangFields = ["createTime"];

const getList = () => {
  loading.value = true;
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  
  getLawListApi(reqForm).then((res) => {
    
    // 根据实际返回的数据结构调整
    dataList.value =  replaceNull(res.rows) || [];
    total.value = res.data?.total  || 0;
  }).finally(() => {
    loading.value = false;
  });
};
getList();

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    customerName: undefined,
    idCard: undefined,
    client: undefined,
    createTime: [],
  };
  // createTime.value = [];
  statusActive.value = "全部";
  lawsuitStatus.value = 0;
  getList();
};

// 获取子组件传回的值
const getMsgFormSon = (data) => {
  showLawProgress.value = data;
  if (!data) {
    getList();
  }
};

// 诉讼记录
const OpenlawProgressBox = (row) => {
  showLawProgress.value = true;
  LawProgressInfo.recordId = row.id;
  proxy.$refs.lawProgressRef.opendialog(LawProgressInfo, row.lawsuitStatus);
};

// 撤销诉讼
const openCancelBox = (recordId) => {
  cancelBox.value = true;
  cancelForm.recordId = recordId;
};

// 查看附件
function openFileDialog (row) {
  proxy.$refs["fileDialogRef"].openDialog(row.fileList);
}

const cancelSuit = () => {
  cancelFormRef.value.validate(async (valid) => {
    if (valid) {
      subloading.value = true;
      cancelSuitApi(cancelForm).then((res) => {
        proxy.$modal.msgSuccess("撤销成功！");
        getList();
        cancelBox.value = false; //关闭弹框
      }).finally(() => {
        subloading.value = false;
      });
    } else {
      console.log("submit error");
    }
  });
};

// 监听撤销诉讼原因
watch(cancelBox, (val) => {
  if (!val) {
    cancelFormRef.value?.resetFields();
  }
});

//获取转让方列表
function getAssetOwnerList() {
  assetOwnerList().then((res) => {
    console.log("res.data",res.data)
    entrusts.value = res.data;
  });
}
getAssetOwnerList();
</script>
<style scoped>
.fxn-body {
  padding: 20px;
}
.fxn-detail-img-pdf {
  display: block;
}
.fxn-detail-img-pdf img {
  margin-right: 0.25rem;
  display: inline-block;
  cursor: pointer;
}
.fxn-detail-img-pdf span {
  color: #f66b2e !important;
}
.fxn-remark {
  font-size: 10px;
  color: #999999;
  padding-left: 10px;
}
.fxn-search-btn {
  text-align: center;
}
</style>
