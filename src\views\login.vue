<template>
  <div class="login">
    <div class="login-icon">
      <img v-if="sign === 'ghtj'" style="width:100%;" src="../assets/images/login-icon.png" />
      <img v-else-if="sign === 'zcax'" style="width:100%;" src="../assets/images/login-icon_zcax.png" />
      <img v-else style="width:100%;" src="../assets/images/login-icon_saas.png" />
    </div>
    <div class="login-box">
      <div class="login-img">
        <img style="width:100%;height:100%" src="../assets/images/<EMAIL>" />
      </div>
      <el-form ref="loginRef" v-if="!finUsernameCheck" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title" style="text-align:center">{{ systemTitle }}</h3>
        <el-form-item prop="username">
          <el-input v-model.trim="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model.trim="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
            show-password @keyup.enter="handleLogin">
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaOnOff">
          <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" style="width: 63%"
            @keyup.enter="handleLogin">
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <div v-loading="codeloading" style="width: 100%; height: 100%">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">记住密码</el-checkbox>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="large" type="primary" style="width: 100%;background-color:#326FEA"
            @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      <!-- 手机验证 -->
      <el-form v-if="openPhone == 0 && finUsernameCheck" ref="loginRef" :model="loginForm" :rules="loginRules"
        class="login-form">
        <h3 class="title" style="text-align:center">调解管理系统</h3>
        <div style="text-align:center" class="mb20">安全验证</div>
        <el-form-item prop="phonenumber" class="mt10">
          <el-input v-model="loginForm.phonenumber" type="text" size="large" auto-complete="off" disabled
            placeholder="手机号码">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="smsCode" class="mt10">
          <el-input v-model="loginForm.smsCode" size="large" auto-complete="off" placeholder="短信验证码" style="width: 63%"
            @keyup.enter="handleLoginByphone">
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <el-button class="ml10 phone-code-buttton" v-if="second == 0"
              :disabled="!loginForm.phonenumber || (loginForm.phonenumber && loginForm.phonenumber?.length == 0)"
              type="primary" @click="getSmsCode()">获取验证码</el-button>
            <el-button class="ml10 phone-code-buttton" v-else type="info" disabled>{{ second }}秒后再获取</el-button>
          </div>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="large" type="primary" style="width: 100%;background-color:#326FEA"
            @click.prevent="handleLoginByphone">
            <span v-if="!phoneLoading">验证并登录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
        <el-form-item style="width: 100%;margin-top:10px" @click="backUserName()">
          <el-icon :size="14" color="#999">
            <ArrowLeft />
          </el-icon><span class="phone-back">返回账号登录页面</span>
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>为了更好的网站体验，建议您使用谷歌、火狐高版本的浏览器</span>
      <span>© 深圳市债卫士信息科技有限公司|{{sign === 'ghtj' ? '粤ICP备2025418058号-1' : '粤ICP备2025436755号'}}，官方客服************</span>
    </div>

    <!-- 合规宣导 -->
    <Propaganda ref="propagandaRef" />
  </div>
</template>

<script setup>
import { getFingerprint, getIpAddress } from '@/utils/common.js';
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import Propaganda from "@/components/Propaganda/index.vue";
import { getCodeImg, sendSmsCode } from "@/api/login";
import { getLoginPersuade, selectPasswordTime } from "@/api/login";
import { ElMessageBox } from 'element-plus'

const systemTitle = computed(() => import.meta.env.VITE_APP_TITLE)
const sign = computed(() => import.meta.env.VITE_APP_SIGN)
const store = useStore();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  phonenumber: undefined,
  smsUid: undefined,
  smsCode: undefined,
  uid: undefined,
  internalIp: undefined,// 内网IP
  browserFingerprint: undefined, // 浏览器指纹
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
  phonenumber: [{ required: true, trigger: "change", message: "请输入手机号码" }],
  smsCode: [{ required: true, trigger: "change", message: "请输入验证码" }],
};
const loading = ref(false);
// 验证码开关
const captchaOnOff = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

const codeUrl = ref("");
const codeloading = ref(false);
//短信开关
const openPhone = ref(0);
const second = ref(0);
const phoneLoading = ref(false);
const finUsernameCheck = ref(false);
//定时器
const cookieTimer = ref(undefined);

//获取记录的时间
function getCookieSecond() {
  const count = Cookies.get("appeal-loginSecond");
  second.value = count > 0 ? count : 0;
  reciprocalTime()
}
getCookieSecond()

function getCode() {
  codeloading.value = true;
  getCodeImg().then((res) => {
    codeloading.value = false;
    captchaOnOff.value = res.captchaOnOff === undefined ? true : res.captchaOnOff;
    if (captchaOnOff.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  }).finally(() => {
    codeloading.value = false;
  });
}
getCode();
function getCookie() {
  const username = Cookies.get("appeal-username");
  const password = Cookies.get("appeal-password");
  const rememberMe = Cookies.get("appeal-rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}
getCookie();

function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      Cookies.set("appeal-username", loginForm.value.username, { expires: 30 });
      // 勾选了需要记住密码设置在cookie中设置记住用户明和名命
      if (loginForm.value.rememberMe) {
        Cookies.set("appeal-password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("appeal-rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        // Cookies.remove("appeal-username");
        Cookies.remove("appeal-password");
        Cookies.remove("appeal-rememberMe");
      }
      // 调用action的登录方法
      if (openPhone.value == 1) {
        finUsernameCheck.value = true;
        loading.value = false;
      }
      const reqForm = {
        username: loginForm.value.username.trim(),
        password: loginForm.value.password,
        code: loginForm.value.code,
        internalIp: loginForm.value.internalIp,
        browserFingerprint: loginForm.value.browserFingerprint,
        uuid: loginForm.value.uuid,
        teamLevelType: 1
      }
      store.dispatch("Login", reqForm).then((res) => {
        webrtcSDK.setIsFirstLogin && webrtcSDK.setIsFirstLogin(true)
        loginForm.value.phonenumber = res.data?.phonenumber;
        loginForm.value.smsUid = res.data?.smsUid;
        openPhone.value = res.data?.securityVerification ?? 1;
        if (openPhone.value == 0) {
          finUsernameCheck.value = true;
          loading.value = false;
        } else {
          getLoginPersuade()
            .then((res) => {
              if (res.data.propaganda) {
                proxy.$refs["propagandaRef"].opendialog(res.data.propaganda);
              } else {
                router.push({ path: redirect.value || "/" });
                checkPassword()
              }
            })
            .catch(() => {
              // 重新获取验证码
              if (captchaOnOff.value) {
                getCode();
              }
            })
            .finally(() => {
              loading.value = false;
            });
        }

      })
        .catch(() => {
          loading.value = false;
          if (captchaOnOff.value) {
            getCode();
          }
        });
    }
  });
}

//获取用户密码时间
function checkPassword() {
  selectPasswordTime().then((res) => {
    if (res.data.state == 1) {
      ElMessageBox.alert(res.data.content, '提示', {
        confirmButtonText: '确定',
      })
    }
  })
}

//获取短信验证码
function getSmsCode() {
  let req = {
    smsUid: loginForm.value.smsUid
  }
  sendSmsCode(req).then((res) => {
    loginForm.value.uid = res.data.uid;
    second.value = 60;
    reciprocalTime()
  }).catch(() => {
    second.value = 0;
  })
}

//60秒倒数
function reciprocalTime() {
  cookieTimer.value = setInterval(() => {
    if (second.value > 0) {
      second.value--;
      Cookies.set("appeal-loginSecond", second.value, { expires: 30 });
    } else {
      second.value == 0;
      Cookies.set("appeal-loginSecond", second.value, { expires: 30 });
      clearInterval(cookieTimer.value)
    }
  }, 1000)
}

//返回账号登陆
function backUserName() {
  loginForm.value.phonenumber = undefined;
  loginForm.value.smsCode = undefined;
  loginForm.value.smsUid = undefined;
  loginForm.value.uid = undefined;
  loginForm.value.internalIp = undefined;
  loginForm.value.browserFingerprint = undefined;
  loginForm.value.code = "";
  proxy.resetForm('loginRef');
  finUsernameCheck.value = false;
  getCode()
}

//手机登录
function handleLoginByphone() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      phoneLoading.value = true
      // 调用action的登录方法
      store
        .dispatch("SmsLogin", loginForm.value)
        .then(() => {
          webrtcSDK.setIsFirstLogin && webrtcSDK.setIsFirstLogin(true)
          phoneLoading.value = false
          getLoginPersuade()
            .then((res) => {
              if (res.data.propaganda) {
                proxy.$refs["propagandaRef"].opendialog(res.data.propaganda);
              } else {
                router.push({ path: redirect.value || "/" });
                checkPassword()
              }
            })
            .catch(() => {
              // 重新获取验证码
              if (captchaOnOff.value) {
                getCode();
              }
            })
            .finally(() => {
              phoneLoading.value = false;
            });
          //router.push({ path: redirect.value || "/" });
        })
        .catch(() => {
          phoneLoading.value = false;
          // 重新获取验证码
          if (captchaOnOff.value) {
            getCode();
          }
        }).catch(() => {
          phoneLoading.value = false;
        });
    }
  });
}

function redirectToBeian() {
  const beianURL = "https://beian.miit.gov.cn/#/Integrated/index";
  window.open(beianURL, "_blank");
}

// 判断IP地址是否为内网IP。
function isInternalIp(ip) {
  // 定义内网IP段范围
  var internalRanges = [
    /^10\./,                      // 10.x.x.x
    /^172\.(?:[1-9]\d?|1[6-9])\./,   // 172.16.x.x - 172.31.x.x
    /^192\.168\.\d{1,3}\.\d{1,3}$/   // 192.168.x.x
  ];

  for (var i = 0; i < internalRanges.length; i++) {
    if (internalRanges[i].test(ip)) {
      return true;
    }
  }

  return false;
}

// 获取内网IP和浏览器指纹
onMounted(async () => {
  const res1 = await getFingerprint()
  const res2 = await getIpAddress()
  loginForm.value.internalIp = isInternalIp(res2[0]) ? res2[0] : undefined
  loginForm.value.browserFingerprint = res1.murmur
})

onBeforeUnmount(() => {
  Cookies.remove("appeal-loginSecond");
  clearInterval(cookieTimer.value)
})
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: linear-gradient(180deg, #2698f4 0%, #326fea 100%);
  // background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  font-size: 30px;
  font-family: MicrosoftYaHei;
  line-height: 38px;
  color: #161514;
  opacity: 1;
}

.login-box {
  width: 52%;
  height: 420px;
  background: #ffffff;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  opacity: 1;
  border-radius: 20px;
  margin: 0 auto;
  padding: 50px;
}

.login-img {
  width: 58%;
  height: 100%;
  display: inline-block;
  margin-right: 15px;
}

.login-icon {
  width: 240px;
  height: 100px;
  position: absolute;
  top: 50px;
  left: 42px;

  span {
    line-height: 148px;
    vertical-align: top;
    color: #fff;
    font-size: 24px;
    font-weight: 700;
  }
}

.login-form {
  border-radius: 6px;
  width: 38%;
  vertical-align: top;
  display: inline-block;
  padding: 5px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  position: fixed;
  bottom: 58px;
  width: 100%;
  text-align: center;
  font-size: 14px;
  font-family: MicrosoftYaHei;
  line-height: 24px;
  color: #ffffff;
  opacity: 0.8;

  span {
    display: block;
  }
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.phone-code-buttton {
  line-height: 40px;
  height: 40px;
  width: 115px;
}

.phone-back {
  color: #999;
  font-size: 12px;
  cursor: pointer;
}

.phone-back:hover {
  color: #b4b3b3
}

//适配2560屏幕宽度
@media screen and (min-width: 2560px) {
  .login-box {
    width: 1180px;
    height: 500px;
  }

  .login-img {
    // width: 620px;
    height: 100%;

    img {
      object-fit: contain;
    }
  }

  .login-form {
    // width: 440px;
    padding: 40px 25px 5px 25px;
  }

  .title {
    line-height: 55px;
  }
}

//适配1400屏幕宽度
@media screen and (min-width: 1920px) {
  .login-box {
    padding: 30px 25px;
    width: 55%;
  }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
  .login-box {
    padding: 30px 25px;
    width: 60%;
  }
}

@media screen and (min-width: 1280px) and (max-width: 1440px) {
  .login-box {
    padding: 30px 25px;
    width: 70%;
  }
}

//适配1440屏幕宽度
@media screen and (min-width: 1080px) and (max-width: 1280px) {
  .login-box {
    padding: 30px 25px;
    width: 80%;
  }
}

@media screen and (min-width: 0px) and (max-width: 1080px) {
  .login-box {
    padding: 30px 25px;
    width: 980px !important;
  }

  .login {
    width: 1080px;
  }
}
</style>
