<template>
  <!-- 添加直播活动 -->
  <el-dialog :title="title" width="600px" v-model="open" @close="cancel">
    <el-form
      @submit.native.prevent
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-width="120"
    >
      <el-form-item label="参数名称" prop="variableName">
        <el-input
          v-model="form.variableName"
          placeholder="请输入名称，不能超过20个字；"
          style="width: 400px"
          type="text"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="AddParams">
import { addVariable, editVariable } from "@/api/writ/parametric";
//获取全局变量
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const title = ref("添加参数");
//接口名称
const typeInfo = ref([addVariable, editVariable]);
const type = ref();
//开启标志
const open = ref(false);
//标签
const dynamicTags = ref([]);
const inputValue = ref("");
const inputVisible = ref(false);
//提交
const data = reactive({
  form: {
    variableName: undefined,
    status: 0,
  },
  rules: {
    variableName: [
      { required: true, message: "请输入参数名称！", trigger: "blur" },
      {
        required: true,
        pattern: /^\S*$/,
        message: "不能输入空格",
        trigger: "blur",
      },
    ],
    status: [{ required: true, message: "请选择状态！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
//加载信息
const loading = ref(false);

//开启弹窗
function opendialog(row) {
  if (row) {
    form.value = row;
    type.value = 1;
    title.value = "编辑参数";
  } else {
    title.value = "添加参数";
    type.value = 0;
  }
  open.value = true;
}

//提交信息
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req = JSON.parse(JSON.stringify(form.value));
      typeInfo.value[type.value](req)
        .then((res) => {
          proxy.$modal.msgSuccess(`设置成功！`);
          cancel();
          emits("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    variableName: undefined,
    status: 0,
  };
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped></style>
