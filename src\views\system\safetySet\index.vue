<template>
  <div class="app-container content-box">
    <div class="left">
      <el-tabs class="mytabs" tab-position="left" v-model="tabActive">
        <el-tab-pane v-if="checkPermi(['system:safetySet:desensitization'])" label="信息脱敏" name="信息脱敏" />
        <!-- <el-tab-pane v-if="checkPermi(['system:safetySet:outsideAuth']) && false" label="外访授权" name="外访授权" />
        <el-tab-pane v-if="checkPermi(['system:safetySet:repaySet']) && false" label="还款方式" name="还款方式" />
        <el-tab-pane v-if="checkPermi(['system:safetySet:exportSet'])" label="导出设置" name="7"></el-tab-pane> -->
        <el-tab-pane v-if="checkPermi(['system:safetySet:watermark'])" label="水印设置" name="水印设置"></el-tab-pane>
        <el-tab-pane v-if="checkPermi(['system:safetySet:pagelimit'])" label="页面限制" name="页面限制"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="right">
      <infoDesensitization v-if="tabActive == '信息脱敏' && checkPermi(['system:safetySet:desensitization'])"
        ref="infoDesensitizationRef" />
      <!-- <outsideAuth v-if="tabActive == '外访授权' && checkPermi(['system:safetySet:outsideAuth'])" ref="outsideAuthRef" />
      <repaySet v-if="tabActive == '还款方式' && checkPermi(['system:safetySet:repaySet'])" ref="repaySetRef" />
      <exportSet v-if="tabActive == '7'" v-model:status="states.exportSettingStatus" /> -->
      <v-watermark v-if="tabActive == '水印设置'" v-model:status="states.settingStatus" />
      <pagelimit v-if="tabActive == '页面限制'" v-model:status="states.restrictedState" />
    </div>
  </div>
</template>
<script setup>
import { checkPermi } from "@/utils/permission";
import { cisSetting } from "@/api/safetySet/index"
import outsideAuth from './tabPage/outsideAuth';
import repaySet from './tabPage/repaySet';
import vWatermark from "./tabPage/watermark";
import exportSet from "./tabPage/exportSet";
import pagelimit from "./tabPage/pagelimit";
import infoDesensitization from './tabPage/infoDesensitization';
const tabActive = ref('信息脱敏');
const states = ref({
  informationStatus: 0,
  settingStatus: 0,
  restrictedState: 0,
  whitelistStatus: 0,
  authorizationStatus: 0,
  authenticationStatus: 0,
  safetyCheckStatus: 0,
  securityVerificationStatus: 0,
  exportSettingStatus: 0,
  pushAppStatus: 0
});
const desensitization = ref([
  { filed: "dname", fieldname: "姓名", status: 0, rules: "只展示姓名第一个字，例：李**" },
  {
    filed: "numbers",
    fieldname: "手机号码",
    status: 0,
    rules: "脱敏中间四位数字，例:134****4532",
  },
  {
    filed: "cardId",
    fieldname: "证件号码",
    status: 0,
    rules: "前6位和后4位不脱敏，中间脱敏，例:333344********7232",
  },
  {
    filed: "bankCard",
    fieldname: "银行卡号",
    status: 0,
    rules: "除前6位和后4位不脱敏，中间脱敏，例：632243******6543",
  },
  {
    filed: "qq",
    fieldname: "QQ",
    status: 0,
    rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
  },
  {
    filed: "weChat",
    fieldname: "微信",
    status: 0,
    rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
  },
  {
    filed: "households",
    fieldname: "户籍地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "unitAddress",
    fieldname: "单位详细地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "residentialAddress",
    fieldname: "居住地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "homeAddress",
    fieldname: "家庭地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "entityName",
    fieldname: "单位名称",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
]);
const _desensitization = ref({}); //脱敏
provide("desensitization", desensitization);
provide("_desensitization", _desensitization);

const waterMdata = ref({});
provide("watermark", waterMdata);

//导出设置
const teamExport = ref({})
provide("teamExport", teamExport);

const state = computed(() => states.value);
provide("state", state);


function getTeamSafe() {
  return new Promise((reslove, reject) => {
    cisSetting().then((res) => {
      console.log(res.data);
      states.value = res.data.state;
      waterMdata.value = res.data.watermark;
      teamExport.value = res.data?.teamExport || {};
      _desensitization.value = JSON.parse(JSON.stringify(res.data.desensitization));

      let obj = res.data.desensitization;
      desensitization.value.map((item) => {
        for (const key in obj) {
          if (item.filed == key) {
            item.status = obj[key];
            break;
          }
        }
      });
      reslove()
    });
  })
}
getTeamSafe();
provide("getTeamSafe", getTeamSafe);
</script>
<style lang="scss" scoped>
.content-box {
  display: flex;

  .right {
    flex: 1;
    margin-left: 10px;
  }
}
</style>