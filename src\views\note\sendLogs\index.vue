<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" inline label-width="82px"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="发起时间" style="width: 340px">
        <el-date-picker v-model="queryParams.sendTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="关系" prop="relationship">
        <el-input v-model="queryParams.relationship" placeholder="请输入关系" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="发送人" prop="createdBy">
        <el-input v-model="queryParams.createdBy" auto-complete="off" @input="inputCreatedBy" placeholder="请输入发送人"
          clearable style="width: 240px" />
      </el-form-item>
      <el-form-item label="是否回复" prop="sendNum">
        <el-select v-model="queryParams.sendNum" placeholder="请选择是否回复" clearable filterable :reserve-keyword="false"
          style="width: 240px">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
          <el-option label="全部" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="回复时间" prop="recvtime">
        <el-date-picker v-model="queryParams.recvtime" style="width: 240px" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area">
      <el-button class="btn-right" icon="Download" v-if="checkPermi([`note:sendLogs:export`])"
        :disabled="dataList?.length == 0" @click="exportData">导出</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <div class="mt10 mb10">
      <el-tabs class="mb8 demo-tabs" v-model="activeTab" @tab-click="tabChange">
        <el-tab-pane :label="`全部  ${getStutasData('all') || 0}`" name="all"></el-tab-pane>
        <el-tab-pane :label="`发送成功  ${getStutasData('2') || 0}`" name="2"></el-tab-pane>
        <el-tab-pane :label="`发送失败  ${getStutasData('1') || 0}`" name="1"></el-tab-pane>
        <el-tab-pane :label="`发送中  ${getStutasData('0') || 0}`" name="0"></el-tab-pane>
      </el-tabs>
    </div>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" class="mt20">
      <el-table-column v-if="columns[0].visible" label="案件ID" align="center" key="caseId" prop="caseId" :width="100"
        show-overflow-tooltip />
      <el-table-column v-if="columns[1].visible" label="发送人" align="center" key="createdBy" prop="createdBy"
        :width="120" />
      <el-table-column v-if="columns[2].visible" label="姓名" align="center" key="name" prop="name" :width="120" />
      <el-table-column v-if="columns[3].visible" label="关系" align="center" key="relationship" prop="relationship"
        :width="120" />
      <el-table-column v-if="columns[4].visible" label="手机号码" width="120" align="center" key="phoneNumber"
        prop="phoneNumber" />
      <el-table-column v-if="columns[5].visible" label="短信内容" align="left" key="smsContent" prop="smsContent">
        <template #default="{ row }">
          <Tooltip :content="row.smsContent" :length="50" />
        </template>
      </el-table-column>
      <el-table-column v-if="columns[6].visible" label="发送状态" align="center" key="sendStatus" prop="sendStatus"
        :width="180" :formatter="sendStatusFor" show-overflow-tooltip />
      <el-table-column v-if="columns[7].visible" label="发送时间" align="center" key="sendTime" prop="sendTime" :width="180"
        show-overflow-tooltip />
      <el-table-column v-if="columns[8].visible" label="回复内容" align="center" key="replyContent" prop="replyContent">
        <template #default="{ row, $index }">
          <el-popover placement="bottom" :width="550" :ref="`popover-${$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(row)" :disabled="!row.sendNum || row.sendNum == 0" type="text">
                {{ row.sendNum || 0 }}
              </el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column property="replyContent" label="回复内容" />
              <el-table-column width="200" property="recvtime" label="回复时间" />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 成功弹窗 -->
    <exportTip ref="exportTipRef" :exportType="`短信记录`" />
  </div>
</template>
<script setup name="SendLogs">
import { checkPermi } from "@/utils/permission";
import { selectSendRecords, getProce, selectRecordNumber, exportCsPage } from "@/api/note/sendLogs";
//全局配置
const { proxy } = getCurrentInstance();
//查询参数
const statusList = ref([
  { name: '全部', numbers: 0 },
  { name: '发送成功', numbers: 0 },
  { name: '发送失败', numbers: 0 },
  { name: '发送中', numbers: 0 },
]);
const showSearch = ref(false)
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    name: undefined,
    relationship: undefined,
    phoneNumber: undefined,
    sendStatus: undefined,
    sendTime: undefined,
    recvtime: undefined,
    createdBy: undefined,
    sendNum: undefined,
  },
});
const { queryParams } = toRefs(data);
const gridData = ref([]);
const rangfiles = ["sendTime", "recvtime"];
//表格参数
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const activeTab = ref("all");

const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '发送人', visible: true },
  { key: 2, label: '姓名', visible: true },
  { key: 3, label: '关系', visible: true },
  { key: 4, label: '手机号码', visible: true },
  { key: 5, label: '短信内容', visible: true },
  { key: 6, label: '发送状态', visible: true },
  { key: 7, label: '发送时间', visible: true },
  { key: 8, label: '回复内容', visible: true },
])
//获取列表
function getList() {
  loading.value = false;
  let req = proxy.addFieldsRange(queryParams.value, rangfiles)
  req.sendNum = queryParams.value.sendNum == "all" ? undefined : queryParams.value.sendNum;
  req.sendStatus = activeTab.value == 'all' ? undefined : activeTab.value;
  selectSendRecords(req).then((res) => {
    loading.value = false;
    dataList.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    loading.value = false;
  });
}
provide("getList", Function, true);
getList();

//获取发送状态
function getContactsList() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
  reqForm.sendNum = queryParams.value.sendNum == "all" ? undefined : queryParams.value.sendNum;
  delete reqForm.pageNum
  delete reqForm.pageSize
  delete reqForm.sendStatus
  selectRecordNumber(reqForm).then((res) => {
    const sum = res.data.reduce((s, item) => s += item.numbers, 0)
    statusList.value = [{ numbers: sum, sendStatus: "all" }, ...res.data]
  });
}
getContactsList()

// 获取显示状态显示数据
function getStutasData(stutas) {
  return statusList.value.find(item => item.sendStatus == stutas)?.numbers
}

//tab却换
function tabChange() {
  queryParams.value.sendStatus = activeTab.value == 'all' ? undefined : activeTab.value;
  getList()
}

function inputCreatedBy(value) {
  queryParams.value.createdBy = value?.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g, '');
}

//气泡框展示
function showPopover(row) {
  let req = {
    id: row.id,
  };
  gridData.value = []
  getProce(req)
    .then((res) => {
      gridData.value = res.rows;
    })
    .catch(() => {
      loading.value = false;
    });
}

//查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
  getContactsList()
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    name: undefined,
    relationship: undefined,
    phoneNumber: undefined,
    sendStatus: undefined,
    sendTime: undefined,
    recvtime: undefined,
    createdBy: undefined,
    sendNum: undefined,
  };
  getList();
  getContactsList()
}

//导出记录
function exportData() {
  proxy.$modal.loading("正在导出数据，请稍候...");
  let req = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)))
  req.sendNum = queryParams.value.sendNum == "all" ? undefined : queryParams.value.sendNum;
  req.sendStatus = activeTab.value == 'all' ? undefined : activeTab.value;
  exportCsPage(req).then((res) => {
    proxy.$modal.closeLoading();
    proxy.$refs['exportTipRef'].opendialog(res.data);
  }).catch((error) => {
    proxy.$modal.closeLoading();
  })
}

//发送状态整理
function sendStatusFor(row) {
  return ["发送中", "发送失败", "发送成功"][row.sendStatus];
}

// 脱敏号码
function dsensitizationNum(val) {
  if (!val) {
    return '--'
  }
  const replaceNum = val.substring(3, 7)
  return val.replace(replaceNum, '****')
}

</script>
<style lang="scss" scoped>
.btn-right {
  float: right;
  margin-right: 150px;
}
</style>
