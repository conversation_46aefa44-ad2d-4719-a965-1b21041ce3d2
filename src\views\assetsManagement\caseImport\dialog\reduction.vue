<template>
    <el-dialog title="减免设置" v-model="open" width="800px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="106px">
            <el-form-item label="开启自动减免" prop="autoReduction">
                <el-radio-group v-model="form.autoReduction">
                    <el-radio :label="0">是</el-radio>
                    <el-radio :label="1">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="自动减免公式" prop="reductionId" v-if="form.autoReduction == 0">
                <el-select
                 v-model="form.reductionId" 
                 placeholder="请选择" 
                 style="width:240px"
                @change="changeReductionSetupId()"
                 >
                    <el-option v-for="item in formulas" :key="item.code" :label="item.info" :value="item.code"></el-option>
                </el-select>
                <span class="ml20" v-if="form.reductionId">{{returnText}}</span>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading"  @click="submit">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
    import { reductionOptions } from '@/api/system/tactic'
    import { updateReduction ,  getReductionInfo} from '@/api/assets/asset/asset'
    const { proxy } = getCurrentInstance();
    const emit = defineEmits(["queryList"]);
    const returnText = ref('');
    const open = ref(false);
    const loading = ref(false);
    const data = reactive({
        form: {
            id: undefined,
            autoReduction: 0,
            reductionId: undefined
        },
        rules: {
            autoReduction: [{ required: true, message: '是否开启自动减免！', trigger: 'change'}],
            reductionId: [{ required: true, message: '请选择减免公式！', trigger: 'change'}],
        }
    })
    const { form , rules } = toRefs(data);

    const formulas = ref([]);

    //打开弹窗
    function opendialog(id,reductionId,autoReduction) {
        reductionOptions().then(res => {
            formulas.value = res.data;
        })
        form.value.id = id;
        form.value.reductionId = reductionId || reductionId === 0 ? String(reductionId) : undefined;
        form.value.autoReduction = Number(autoReduction);
        open.value = true;
        if( form.value.reductionId){
            changeReductionSetupId()
        }
    }

    function changeReductionSetupId(){
        getReductionInfo({reductionId:form.value.reductionId}).then((res) => {
            returnText.value = res.msg	
        });
    }

    //提交
    function submit() {
        loading.value = true;
        proxy.$refs['formRef'].validate(valid => {
            if (valid) {
                updateReduction(form.value).then(res => {
                    proxy.$modal.msgSuccess('设置成功！')
                    loading.value = false;
                    emit('queryList')
                    cancel()
                }).catch(() => {
                    loading.value = false;
                })
            } else {
                loading.value = false;
            }
        })
    }

    //重置
    function reset() {
        proxy.resetForm('formRef');
        form.value = {
            id: undefined,
            autoReduction: 0,
            reductionId: undefined
        }
    }

    //取消
    function cancel() {
        reset()
        open.value = false
    }

    defineExpose({
        opendialog
    })
</script>

<style scoped>
   
</style>