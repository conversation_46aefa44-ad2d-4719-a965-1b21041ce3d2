<template>
  <el-input
    v-model="inputVal"
    :clearable="clearable"
    style="width: 100%"
    @change="change"
    :disabled="disabled"
    @input="handleInput"
    @blur="handleBlur"
    :maxlength="maxlength"
    :placeholder="placeholder"
  >
    <!-- 继承el-input所有slot -->
    <template #prefix v-if="$slots.prefix">
      <slot name="prefix"></slot>
    </template>
    <template #suffix v-if="$slots.suffix">
      <slot name="suffix"></slot>
    </template>
    <template #prepend v-if="$slots.prepend">
      <slot name="prepend"></slot>
    </template>
    <template #append v-if="$slots.append">
      <slot name="append"></slot>
    </template>
  </el-input>
</template>

<script setup>
const emits = defineEmits(["update:modelValue"]);
const props = defineProps({
  placeholder: { type: String, default: "请输入" },
  decimals: { type: [String, Number], default: 2 },
  maxlength: { type: [String, Number], default: 20 },
  modelValue: { type: [String, Number], default: null },
  change: { type: Function, default: () => {} },
  disabled: { type: Boolean, default: false },
  clearable: { type: Boolean, default: false },
  regex: { type: [RegExp, null], default: null },
});
const inputVal = ref(null);

function handleInput(val) {
  nextTick(() => {
    const regex = props.regex;
    if (regex && regex instanceof RegExp) {
      val = val.replace(regex, "");
      inputVal.value = val;
    } else {
      // Step 1: 只允许负号开头、数字和小数点
      let newVal = val.replace(/[^-.\d]/g, "");

      // Step 2: 处理负号格式
      // 确保只能有一个负号且在开头
      if (newVal.startsWith("-")) {
        newVal = "-" + newVal.substr(1).replace(/-/g, "");
      } else {
        newVal = newVal.replace(/-/g, "");
      }

      // Step 3: 处理小数点
      const decimalIndex = newVal.indexOf(".");
      if (decimalIndex >= 0) {
        // 处理多个小数点的情况
        newVal =
          newVal.substr(0, decimalIndex + 1) +
          newVal.substr(decimalIndex + 1).replace(/\./g, "");

        // 限制小数位数
        const decimalPart = newVal.split(".")[1] || "";
        if (decimalPart.length > props.decimals) {
          newVal = newVal.substr(0, decimalIndex + 1 + +props.decimals);
        }
      }

      // 最终值处理
      const validFormat = /^-?\d*\.?\d{0,2}$/.test(newVal);
      if (validFormat) {
        inputVal.value = newVal;
      } else {
        inputVal.value = inputVal.value; // 保留合法值
      }
    }
    emits("update:modelValue", parseFloat(inputVal.value) || 0);
  });
}
function handleBlur() {
  // 若不符合规则，则清空输入框
  console.log(inputVal.value)
  const validFormat = /^-?\d*\.?\d{0,2}$/.test(inputVal.value);
  if (validFormat) {
    inputVal.value = parseFloat(inputVal.value) || 0;
    emits("update:modelValue", parseFloat(inputVal.value) || 0);
  }
}
watch(
  () => props.modelValue,
  () => {
    inputVal.value = props.modelValue;
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped></style>
