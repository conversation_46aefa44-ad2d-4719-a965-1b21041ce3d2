<template>
  <div class="app-container">
    <div class="flex mb8">
      <el-button type="primary" icon="ArrowLeft" plain @click="toBack"
        >返回列表</el-button
      >
      <div class="title ml20">
        工单详情
        <el-tag
          class="ml5"
          :type="['warning', 'success'][detailInfo.orderStatusCode]"
          size="small"
          >{{ detailInfo.orderStatus }}</el-tag
        >
      </div>
    </div>
    <div class="warp">
      <el-form label-width="96px" :model="form" :rules="rules" ref="formRef">
        <el-row>
          <el-col :span="4" :pull="1">
            <el-form-item label="处置人员:">{{ detailInfo.odvName || "--" }}</el-form-item>
          </el-col>
          <el-col :span="3" :pull="1">
            <el-form-item label="发起人:">{{ detailInfo.createBy }}</el-form-item>
          </el-col>
          <el-col :span="7" >
            <el-form-item label="发起时间:">{{ detailInfo.createTime }}</el-form-item>
          </el-col>
          <el-col :span="5" :push="3">
            <el-form-item label="渠道来源:">{{ detailInfo.channelSource }}</el-form-item>
          </el-col>
          <el-col :span="5" :push="2">
            <el-form-item label="问题类型:">{{ detailInfo.questionType }}</el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工单内容:">{{ detailInfo.questionContent }}</el-form-item>

       <el-form-item label="处理状态:">
       <el-radio-group v-model="form.orderState">
          <el-radio :label="0">处理中</el-radio>
          <el-radio :label="1">已完成</el-radio>
        </el-radio-group>
       </el-form-item>
        <el-form-item label="跟进内容:">
           <el-input v-model="form.workFollowContent" type="textarea" :rows="4" placeholder="请输入" maxlength="300" show-word-limit />
        </el-form-item>
        <el-form-item>
          <el-button
            v-if="detailInfo.orderStatusCode != 1"
            type="primary"
            @click="submit"
            >提交跟进内容</el-button
          >
        </el-form-item>
      </el-form>

      <div class="text-center">
        <el-button Plain @click="toBack">关 闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="OrderDetail">
import {updateWorkOrder} from "@/api/collection/myworkorder";
const { proxy } = getCurrentInstance();
const route = useRoute();
const detailInfo = ref({});
const againData = ref([]);
const loading = ref(false);
const data = reactive({
  form:{
    orderState:0,
    workFollowContent:"",
  },
})
const { form , rules } = toRefs(data);

// 提交
function submit(){

    loading.value = true;
   proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      let req={
           orderId:route.params.orderId,
           orderState:form.value.orderState,
           workFollowContent:form.value.orderState,
      };
      followUpOrder(req).then(res => {
        proxy.$modal.msgSuccess('操作成功！')
        reset()
      }).finally(() => {
        loading.value = false
      })
    } else {
      loading.value = false;
    }
  })
}

// 取消
function reset() {
  proxy.resetForm('formRef');
  form.value = {
    orderState: 0,
    workFollowContent: '',
  }
}

//返回
const toBack = () => {
  const obj = { path: "/Workorder/teamworkorder" };
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .title {
    font-weight: 400;
    font-size: 18px;
  }
}
.warp {
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.again-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px 20px;
  width: 100%;
  .again-box-warp {
    padding-right: 140px;
    position: relative;
    line-height: 20px;
    .time {
      text-align: right;
    }
  }
  .again-box-warp:not(:first-child) {
    padding-top: 20px;
  }
  .again-box-warp:not(:last-child) {
    border-bottom: 1px solid #dcdfe6;
  }
}
  .progressBar{
    margin-top:50px;
    margin-left:50px;
    span{
     font-size:15px;
    }
  }
  .barContent{
    margin-bottom:20px;
  }
  .content-people{
    margin-bottom:20px;
  }
</style>