<template>
  <!-- 退案留案弹窗 -->
  <el-dialog
    :title="title"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item label="申请原因" prop="reason">
        <el-input
          type="textarea"
          v-model="form.reason"
          placeholder="请输入申请原因"
          maxlength="300"
          rows="4"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { applyCaseData } from "@/api/caseDetail/detail";
//传输数据
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
const title = ref("申请案件资料");
//提交数据
const data = reactive({
  form: {
    caseId: undefined,
    reason: undefined,
  },
  rules: {
    reason: [{ required: true, message: "请输入申请原因", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
function opendialog(id) {
  form.value.caseId = id;
  open.value = true;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let data = JSON.parse(JSON.stringify(form.value));
      applyCaseData(JSON.stringify(data))
        .then((res) => {
          proxy.$modal.msgSuccess("操作成功！");
          cancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>
<style scoped></style>
