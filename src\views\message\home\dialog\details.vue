<template>
  <div class="app-container">
    <el-row class="mt10 mb10"><el-button @click="toBack">返回上一级</el-button></el-row>
    <el-row class="mt10 mb10 message-title">{{ messageList.messageTitle }}</el-row>
    <el-row class="mt10 mb10 message-subheading">
      <span class="mr20">发布时间：{{ messageList.creationtime }}</span>
    </el-row>
    <el-divider />
    <el-row class="mt10 mb10">
      <div class="text-html" v-html="messageList.messageContent"></div>
    </el-row>
  </div>
</template>
<script setup name="DetailsMessage">
import { getUserNotice } from "@/api/message/home";
import { webSocketContent } from "@/api/common/websocket";
//全局配置
const { proxy } = getCurrentInstance();
const route = useRoute();
//详情列表
const messageList = ref({});
//列表参数
const queryParams = ref({
  id: route.params.id,
});

//获取列表
function getList() {
  getUserNotice(queryParams.value)
    .then((res) => {
      messageList.value = res?.data;
      if (res?.data?.delete == 1) {
        setTimeout(function () {
          toBack()
        }, 2000)
        return proxy.$modal.msgWarning('该消息详情已被删除，将自动跳回系统消息页面！');
      }
    })
    .catch(() => {
      messageList.value.messageContent = ''
    });
}
getList();
watch(() => route.params.id, async value => {
  if (value) {
    await webSocketContent()
  }

}, { deep: true, immediate: true })

//返回
const toBack = () => {
  const obj = { path: route.query.path || '/' };
  proxy.$tab.closeOpenPage(obj);
};

//测试语句
</script>
<style lang="scss" scoped>
.message-title {
  text-align: center;
  display: block;
  font-size: 18px;
  font-weight: 600;
}

.message-subheading {
  text-align: center;
  display: block;
  font-size: 14px;
}

.text-html {
  width: 100%;

  :deep(a) {
    color: #409eff !important;
    text-decoration: underline !important;
  }

  :deep(p) {
    word-break: break-all;
  }
}
</style>
