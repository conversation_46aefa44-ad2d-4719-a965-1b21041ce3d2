import request from '@/utils/request'

// 电话调解 - 查询
export function getPhoneList(query) {
    return request({
        url: '/phone/list',
        method: 'get',
        params: query
    })
}

// 电话调解 - 添加调节号
export function addPhoneNum(data) {
    return request({
        url: '/phone/addNum',
        method: 'post',
        data
    })
}

// 电话调解 - 添加司法公示链接
export function addPhoneLink(data) {
    return request({
        url: '/phone/addLink',
        method: 'post',
        data
    })
}

// 电话调解 - 电话调解批量发送生成短信模板短信
export function previewTemplatePhoneLink(data) {
    return request({
        url: '/phone/previewTemplate',
        method: 'post',
        data
    })
}

// 电话调解 - 电话调解批量发送短信
export function sendMessagesPhoneLink(data) {
    return request({
        url: '/phone/sendAppealMessages',
        method: 'post',
        data
    })
}

// 电话调解 - 电话调解批量发送短信
export function selectAllSmsTemplate(query) {
    return request({
        url: '/phone/selectAllSmsTemplate',
        method: 'get',
        params: query
    })
}
// 电话调解 - 电话调解批量发送短信
export function sendAppealMessageSingle(data) {
    return request({
        url: '/phone/sendAppealMessage',
        method: 'post',
        data
    })
}
