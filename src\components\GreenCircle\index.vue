<template>
    <div class="mr5 ml5">
        <el-tooltip placement="top">
            <template #content>{{ `${props.data.startFreeze.split(' ')[0]} 至 ${props.data.endFreeze.split(' ')[0]}` }}</template>
            <div class="circle"></div>
        </el-tooltip>
    </div>
</template>
<script setup>
const props = defineProps({
    data: { type: Object, default: {} }
})
</script>
<style lang="scss" scoped>
.circle {
    width: 12px;
    height: 12px;
    cursor: pointer;
    border-radius: 50%;
    background-color: #67c23a;
}
</style>