<template>
  <!-- 退案留案弹窗 -->
  <el-dialog
    :title="title"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item :label="`本次${type == 0?'退案':'留案'}数量`">
        <span class="">{{ caseNum }}</span>
      </el-form-item>
      <el-form-item label="申请原因" prop="reason">
        <el-input
          type="textarea"
          v-model="form.reason"
          placeholder="请输入申请原因"
          maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { insertRetreat, insertKeep} from "@/api/team/team";
//传输数据
const props = defineProps({
  caseNum: {
    type: Number,
  },
});
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
const title = ref('');
const queryParams = ref({});
const emit = defineEmits(["getList"]);
const teamInfo = ref([insertRetreat, insertKeep]);
const type = ref(0);
//提交数据
const data = reactive({
  form: {
    reason: undefined,
  },
  rules: {
    reason: [{ required: true, message: "请输入申请原因", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
async function opendialog(opType,query){
  open.value = true;
  title.value = opType == 0?`申请退案`:`申请留案`;
  queryParams.value = query;
  type.value = opType;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let data = JSON.parse(JSON.stringify(queryParams.value));
      data.reason = form.value.reason;
      teamInfo.value[type.value](JSON.stringify(data))
        .then((res) => {
          proxy.$modal.msgSuccess( `${type.value == 0?'退案':'留案'}登记成功！`);
          cancel();
          emit("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

 defineExpose({
        opendialog
    })

</script>
<style scoped></style>
