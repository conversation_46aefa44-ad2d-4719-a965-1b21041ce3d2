<template>
  <el-table :data="listData" :loading="loading">
    11111
    <el-table-column label="催记时间" prop="createTime" key="createTime" align="center" :width="190" />
    <el-table-column label="联系人" prop="liaison" key="liaison" align="center" :width="100" />
    <el-table-column label="联系方式" prop="contactMode" key="contactMode" align="center" :width="110" />
    <el-table-column label="跟进状态" prop="followUpState" key="followUpState" align="center" :width="100" />
    <el-table-column label="催收状态" prop="urgeState" key="urgeState" align="center" :width="100" />
    <el-table-column label="沟通内容" align="center" :width="300">
      <template #default="{ row }">
        <Tooltip :content="row.content" />
      </template>
    </el-table-column>
    <el-table-column label="承诺还款信息/备注" prop="remarks" key="remarks" align="center">
      <template #default="{ row }">
        <Tooltip :content="row.remarks" :length="8" />
      </template>
    </el-table-column>
    <el-table-column label="处置人员" prop="odvName" key="odvName" align="center" />
    <el-table-column label="联系渠道" prop="contactMedium" key="contactMedium" align="center" />
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import { selectUrgeRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  caseId: { type: [String, Number], default: undefined }
});
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    urgeTpye: 1,
    caseId: props.caseId,
    webSide: 1
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm }
  loading.value = true;
  
  selectUrgeRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();
defineExpose({ getList })
</script>

<style scoped></style>
