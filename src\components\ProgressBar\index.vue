<template>
  <div class="over-box-data-sub">
    <div class="sub-title">
      回收总额占比：<span>{{ zeroizeTwo(props.schedule) }}%</span>
    </div>
    <div class="sub-content">
      <div class="sub-back">
        <div class="sub-pro" :style="`width:${props.schedule}%`"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
//全局配置
const { proxy } = getCurrentInstance();
const props = defineProps({
  schedule: {
    type: Number,
    default: 0,
  },
});
function zeroizeTwo(num) {
  const strNum = String(num);
  if (strNum.includes(".") && strNum.split(".")[1].length > 2) {
    const index = strNum.indexOf(".");
    return strNum.substring(0, index + 3);
  } else if (strNum.includes(".") && strNum.split(".")[1].length == 1) {
    return strNum + "0";
  } else if (!strNum.includes(".")) {
    return strNum + ".00";
  } else {
    return strNum;
  }
}
</script>

<style scoped lang="scss">
.over-box-data-sub {
  width: 100%;
  height: 100px;
  .sub-title {
    font-size: 22px;
    height: 50px;
    span {
      color: #409eff;
    }
  }
  .sub-content {
    height: 50px;
    width: 90%;
    position: relative;
    .sub-back {
      width: 100%;
      height: 20px;
      position: absolute;
      background-color: #dcdfe6;
      border-radius: 10px;
      overflow: hidden;
    }
    .sub-pro {
      height: 20px;
      position: absolute;
      background-color: #409eff;
    }
  }
}
</style>
