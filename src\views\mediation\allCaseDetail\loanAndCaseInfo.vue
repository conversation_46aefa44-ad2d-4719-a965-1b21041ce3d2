<template>
  <div>
    <el-radio-group v-model="infotype" @change="changeType">
      <el-radio-button value="贷款信息" label="贷款信息" />
      <el-radio-button value="案件信息" label="案件信息" />
    </el-radio-group>
    <span v-if="infotype === '案件信息'" class="caseinfo-text"
      >合计合同数量 {{ caseInfoAmount?.caseTotal }} 笔 &nbsp;&nbsp;累计欠款金额
      {{ numFilter(caseInfoAmount?.entrustMoneyTotal) }} 元 &nbsp;&nbsp;累计剩余本金{{
        numFilter(caseInfoAmount?.syYhPrincipalTotal)
      }}
      元</span
    >

    <formCom
      v-if="infotype === '贷款信息'"
      :form="formList"
      :formList="infoBaseList"
      v-loading="loading"
    />
    <div v-if="infotype === '案件信息'">
      <el-table :data="caseInfo" v-loading="loading" class="mt10">
        <el-table-column prop="contractNo" label="合同号" align="center" />
        <el-table-column prop="productName" label="产品名称" align="center" />
        <el-table-column prop="overdueStarts" label="逾期天数" align="center" />
        <el-table-column
          prop="entrustMoney"
          label="总欠款金额"
          align="center"
          :formatter="(row) => numFilter(row.entrustMoney)"
        />
        <el-table-column
          prop="syYhPrincipal"
          label="总剩余本金"
          align="center"
          :formatter="(row) => numFilter(row.syYhPrincipal)"
        />
        <el-table-column
          prop="residualPrincipal"
          label="借款金额"
          align="center"
          :formatter="(row) => numFilter(row.residualPrincipal)"
        />
        <el-table-column
          prop="alreadyPrincipal"
          label="总已归还本金"
          align="center"
          :formatter="(row) => numFilter(row.alreadyPrincipal)"
        />
        <el-table-column prop="accountPeriod" label="账龄" align="center" />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="changeType"
        layout="prev, pager, next"
      />
    </div>
  </div>
</template>

<script setup>
import { format } from "echarts";
import formCom from "./components/formCom";

import {
  selectCaseDetails,
  selectSameBatchCases,
  selectSameBatchAmountMoney,
} from "@/api/mediation/allCaseDetail";
import { numFilter } from "@/utils/common";
const { proxy } = getCurrentInstance();
const caseId = inject("caseId");

const infotype = ref("贷款信息");
const infoBaseList = ref([
  { code: "projectName", info: "项目名称" },
  { code: "productName", info: "产品名称" },
  { code: "productType", info: "产品类型" },
  { code: "loanPrincipal", info: "贷款金额", isNum: true },
  { code: "batchNo", info: "批次号" },
  { code: "overdueStarts", info: "逾期天数" },
  { code: "accountPeriod", info: "账期" },
  { code: "loanInstitution", info: "贷款机构" },
  { code: "customerNo", info: "客户号" },
  { code: "alreadyPeriods", info: "已还期数" },
  { code: "notPeriods", info: "未还期数" },
  { code: "loanPeriods", info: "贷款期数" },
  { code: "entrustMoney", info: "委托金额", isNum: true },
  { code: "residualPrincipal", info: "贷款本金", isNum: true },
  { code: "syYhPrincipal", info: "剩余本金", isNum: true },
  { code: "syYhInterest", info: "剩余利息", isNum: true },
  { code: "overdueStart", info: "逾期日期" },
  { code: "entrustingCaseDate", info: "委案日期" },
  { code: "returnCaseDate", info: "退案日期" },
  { code: "registerChannel", info: "申请渠道" },
]);
const formList = ref([]);
const loading = ref(true);
const caseInfo = ref([]);
const caseInfoAmount = ref({});
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  caseId: caseId,
});
const total = ref(0);

function changeType(val) {
  loading.value = true;
  if (infotype.value === "贷款信息") {
    selectCaseDetails({ caseId: caseId })
      .then((res) => {
        formList.value = res.data;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    selectSameBatchCases(queryParams.value)
      .then((res) => {
        caseInfo.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
    if (val === "案件信息") {
      selectSameBatchAmountMoney({ caseId: caseId })
      .then((res) => {
        caseInfoAmount.value = res.data;
      })
      .finally(() => {
        loading.value = false;
      });
    }
    
  }
}
changeType(infotype.value);
</script>

<style scoped>
.caseinfo-text {
  margin-left: 10px;
  vertical-align: bottom;
  font-size: 14px;
}
::v-deep(.pagination-container) {
  position: relative;
  margin-top: 0;
}
</style>
