<template>
  <el-dialog
    title="分期还款申请"
    v-model="open"
    width="600px"
    append-to-body
    :before-close="cancel"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="分期期数" prop="stagingNum">
        <el-input
          v-model="form.stagingNum"
          placeholder="请输入"
          style="width: 240px"
        ></el-input>
      </el-form-item>
      <el-form-item label="每期还款时间" prop="repaymentDate">
         <el-input
          v-model="form.repaymentDate"
          placeholder="请输入1~31"
          style="width: 240px"
        ></el-input>
      </el-form-item>
      <el-form-item label="每期还款金额" prop="repaymentMonthly">
        <el-input
          type="number"
          v-model="form.repaymentMonthly"
          placeholder="请输入"
          style="width: 240px"
        ></el-input>
      </el-form-item>
      <el-form-item label="申请原因" prop="reason">
        <el-input
          type="textarea"
          show-word-limit
          v-model="form.reason"
          maxlength="300"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { insertAmortization } from "@/api/caseDetail/detail";
const { proxy } = getCurrentInstance();
const getcaseDetailInfo = inject("getcaseDetailInfo");

const loading = ref(false);
const open = ref(false);
const data = reactive({
  form: {},
  rules: {
    stagingNum: [{ required: true, message: "请输入分期期数！", trigger: "blur" }],
    repaymentDate: [
      { required: true, message: "请选择每期还款时间！", trigger: "blur" },
      { pattern: /^[1-9]$|^[1-2][0-9]$|^3[0-1]$/, message: '请输⼊1~31' }
    ],
    repaymentMonthly: [
      { required: true, message: "请输入每期还款金额！", trigger: "blur" },
      { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
    ],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog(caseId) {
  reset();
  form.value.caseId = caseId;
  open.value = true;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    stagingNum: undefined,
    repaymentDate: undefined,
    repaymentMonthly: undefined,
    reason: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      insertAmortization(form.value)
        .then(() => {
          proxy.$modal.msgSuccess("申请提交成功！");
          cancel();
          getcaseDetailInfo();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
