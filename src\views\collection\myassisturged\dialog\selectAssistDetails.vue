<template>
  <el-dialog title="协催记录" v-model="open" append-to-body @close="cancel">
    <el-table v-loading="subLoading" ref="urgedmultipleTableRef" :data="urgedList">
      <el-table-column label="时间" align="center" key="createTime" prop="createTime" />
      <el-table-column
        label="协催状态"
        align="center"
        key="state"
        prop="state"
        :formatter="urgdStateFor"
      />
      <el-table-column
        label="协催内容"
        align="center"
        key="assistContent"
        prop="assistContent"
      />
    </el-table>
  </el-dialog>
</template>

<script setup>
import { selectAssistDetails } from "@/api/collection/myassisturged";

const urgedList = ref([]);
const caseId = ref(undefined);
const subLoading = ref(true);
const open =  ref(false);

//打开弹窗
function opendialog(id) {
  reset();
  caseId.value = id;
  open.value = true;
  geturgedList();
}

function geturgedList() {
  subLoading.value = true;
  selectAssistDetails(caseId.value)
    .then((res) => {
      urgedList.value = res.rows;
      subLoading.value = false;
    })
    .catch(() => {
      subLoading.value = false;
    });
}

//取消
function cancel() {
  caseId.value = undefined;
  open.value = false;
}

//重置
function reset() {
  caseId.value = undefined;
  open.value = true;
}

//协催状态 0-待协催，1-持续协催，2-完成协催，3-终止协催
function urgdStateFor(row) {
  return ["待协催", "持续协催", "完成协催", "终止协催"][parseInt(row.state)];
}

 defineExpose({
        opendialog
    })

</script>

<style scoped></style>
