<template>
  <el-radio-group v-model="value">
    <div v-for="item in labels" :key="item.id">
      <el-radio v-if="item.stateLabel == 1" :label="item.code" :value="value">
        <labelVue :code="item.code" />
        <span class="mr10">{{ item.labelContent }}</span>
      </el-radio>
    </div>
    <div>
      <el-radio label="7">取消标签</el-radio>
    </div>
  </el-radio-group>
</template>

<script setup>
import labelVue from "./index.vue";

const { proxy } = getCurrentInstance();
const props = defineProps({
  labels: {
    type: Array,
    required: true,
  },
  code: {
    type: [String, Number],
    default: "",
  },
});
const emit = defineEmits();
const value = computed({
  get: () => props.code,
  set: (val) => {
    emit("update:code", val);
  },
});
</script>

<style lang="scss" scoped>
:deep(.el-radio__label) {
  display: flex;
  align-items: center;
}
</style>
