import request from '@/utils/request'
//获取机构树结构
export function DeptTreeWithExecute(params) {
  return request({
    url: '/team/DeptTreeWithExecute',
    method: 'get',
    params
  })
}

// 根据条件查询列表信息
export function getExecuteList(query) {
  return request({
    url: '/team/getExecuteList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectExecuteWithMoney(data) {
  return request({
    url: '/team/selectExecuteWithMoney',
    method: 'post',
    data: data,
  })
}
