<template>
  <div class="app-container">
    <el-form class="form-content h-50" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
      :inline="true" :rules="rules" label-width="96px">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="UID" prop="uid">
        <el-input v-model="queryParams.uid" placeholder="请输入uid" clearable style="width: 240px"
          onkeyup="value=value.replace(/[^A-z0-9/g, '')" oninput="value=value.replace(/[^A-z0-9]/g, '')"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" type="number" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="转让方" prop="entrustingPartyId">
        <el-select v-model="queryParams.entrustingPartyId" placeholder="请输入或选择转让方" clearable filterable
          :reserve-keyword="false" @focus="OwnerList" style="width: 240px">
          <el-option v-for="item in entrusts" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="户籍地" prop="clientCensusRegister">
        <el-cascader :options="areas" filterable ref="clientCensusRegisterRef" v-model="clientCensusRegisterList"
          collapse-tags-tooltip collapse-tags @change="changeOption" placeholder="请输入或选择地区" clearable
          @focus="CensusRegisters" style="width: 240px" :props="{ multiple: true, value: 'label' }" />
      </el-form-item>
      <el-form-item label="退案日期">
        <el-date-picker v-model="queryParams.returnCaseDate" value-format="YYYY-MM-DD" type="daterange"
          style="width: 240px" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="跟进日期">
        <el-date-picker v-model="queryParams.followUpAst" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          style="width: 240px" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="标签" prop="label">
        <el-select v-model="queryParams.label" placeholder="请输入或选择标签" clearable filterable :reserve-keyword="false"
          @focus="LabelList" style="width: 240px">
          <el-option v-for="item in tags" :key="item.code" :label="item.labelContent" :value="item.code">
            <div>
              <el-icon :color="lableColor[item.code]" class="icon-flag">
                <flag />
              </el-icon>
              {{ item.labelContent }}
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="剩余应还本金" style="width: 336px" prop="syYhPrincipal">
        <el-row>
          <el-col :span="11">
            <el-input type="text" v-model="queryParams.syYhPrincipal1" @blur="validatePrincipalRange"
              @input="value => value.replace(/[^\d]/g, '')" clearable />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input type="text" v-model="queryParams.syYhPrincipal2" @blur="validatePrincipalRange"
              @input="value => value.replace(/[^\d]/g, '')" clearable />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <!-- Search End -->

    <el-row class="mb10 mt10 h32">
      <el-button type="primary" plain :disabled="single" @click="openBackStay(0)"
        v-hasPermi="['saasc:collection:insertRetreat']">申请退案
      </el-button>
      <!-- <el-button type="success" plain :disabled="single" @click="openhelpUrge"
        v-hasPermi="['saasc:collection:insertAssistRecord']">申请协案
      </el-button> -->
      <el-button type="info" plain :disabled="single" @click="openBackStay(1)"
        v-hasPermi="['saasc:collection:insertKeep']">申请留案
      </el-button>
      <el-button type="warning" plain :disabled="single" @click="openstopUrge"
        v-hasPermi="['saasc:collection:insertStop']">申请停案
      </el-button>
      <el-button type="danger" plain :disabled="single" @click="openLabelCase"
        v-hasPermi="['saasc:collection:MarkCase']">标记案件
      </el-button>
      <el-button type="danger" plain :disabled="single" @click="openSendMessage()">批量发送短信</el-button>
      <el-button class="ml10" type="primary" :disabled="single" @click="batchCreateWork()">批量生成文书</el-button>
      <el-dropdown class="ml10" @command="downloadWorkZip">
        <el-button type="primary" :disabled="single" icon="arrow-down">批量下载文书</el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :disabled="single" command="0">PDF格式</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>
    <!-- 操作栏 End -->

    <el-row class="mb10 hint">
      <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
        <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
          :indeterminate="item.indeterminate" :disabled="caseList.length === 0" />
      </el-checkbox-group>

      <div class="text-flex ml20">
        <span>剩余应还债权金额：</span>
        <span class="text-danger mr10">{{ proxy.setNumberToFixed(statistics.money) }}</span>
        <span>剩余债权本金：</span>
        <span class="text-danger mr10">{{ proxy.setNumberToFixed(statistics.principal) }}</span>
        <span>案件数量：</span>
        <span class="text-danger">{{ statistics.size }}</span>
      </div>

      <div class="top-right-btn">
        <el-radio-group v-model="settlementStatus" @change="settleStateChange">
          <el-radio label="1">已结清</el-radio>
          <el-radio label="0">未结清</el-radio>
          <el-radio label="全部">全部</el-radio>
        </el-radio-group>
      </div>
    </el-row>

    <el-tabs class="mb8" v-model="followUpState" @tab-click="tabChange">
      <el-tab-pane v-for="item in follows" :key="item" :label="item" :name="item">
      </el-tab-pane>
      <el-tab-pane label="全部" name="全部"></el-tab-pane>
    </el-tabs>
    <!-- tabs End -->

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList" @selection-change="handleSelectionChange"
      @cell-mouse-enter="callCellEnter" @sort-change="sortChange">
      <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible" width="80">
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
            </el-tooltip>
            <el-button type="text" @click="toDetails(row.caseId, $index)">{{ row.caseId }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="UID" align="center" key="uid" prop="uid" :width="100" v-if="columns[1].visible" />
      <el-table-column label="案件状态" align="center" key="caseState" prop="caseState" :formatter="caseStateChange"
        v-if="columns[2].visible" />
      <el-table-column label="调解阶段" align="center" key="disposeState" prop="disposeState" :width="90"
        v-if="columns[3].visible" />
      <el-table-column label="跟进调诉员" align="center" key="prosecutor" prop="prosecutor" :width="100"
        v-if="columns[4].visible" />
      <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[5].visible"
        :width="100" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[6].visible"
        :width="90" />
      <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" v-if="columns[7].visible"
        :width="120">
        <template #default="{ row }">
          <span>{{ row.clientPhone }}</span>
          <callBarVue class="ml5" :caseId="row.caseId" :phoneState="row.phoneState" :odvId="row.odvId"
            :key="htrxCall" />
          <span class="send-icon ml5" v-if="row.phoneState == 0" @click="openSendMessage(row)"><el-icon color="#409eff"
              :size="16">
              <ChatLineSquare />
            </el-icon></span>
          <el-icon class="note-icon" v-else color="#b6b4b4" :size="18">
            <ChatLineSquare />
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column label="出生日期" align="center" key="clientBirthday" prop="clientBirthday"
        v-if="columns[8].visible" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" :width="90"
        v-if="columns[9].visible" />
      <el-table-column label="证件号码【户籍地】" align="center" key="clientIdcard" prop="clientIdcard"
        v-if="columns[10].visible" :width="180">
        <template #default="{ row }">
          {{ `${row.clientIdcard}` }}<br />
          {{ `【${row.clientCensusRegister || "--"}】` }}
        </template>
      </el-table-column>
      <el-table-column label="债权总金额" align="center" :width="180" key="clientMoney" prop="clientMoney"
        sortable="clientMoney" v-if="columns[11].visible">
        <template #default="{ row }">
          <span>{{ row.clientMoney ? proxy.setNumberToFixed(row.clientMoney) : `--` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余应还债权金额" width="140" align="center" key="remainingDue" prop="remainingDue"
        v-if="columns[12].visible" />
      <el-table-column label="剩余应还本金" align="center" key="syYhPrincipal" prop="syYhPrincipal" width="130"
        v-if="columns[13].visible" />
      <el-table-column label="逾期日期（末次）" align="center" key="overdueDays" prop="overdueDays" width="140"
        v-if="columns[14].visible" />
      <el-table-column label="分配时间" align="center" key="allocatedTime" prop="allocatedTime"
        :show-overflow-tooltip="true" v-if="columns[15].visible" width="180" />
      <el-table-column label="跟进日期" align="center" key="followUpAst" prop="followUpAst" sortable="followUpAst"
        v-if="columns[16].visible" width="180" />
      <el-table-column label="跟进状态" align="center" key="followUpState" prop="followUpState"
        v-if="columns[17].visible" />
      <el-table-column label="催收状态" align="center" key="urgeState" prop="urgeState" v-if="columns[18].visible" />
      <el-table-column fixed="right" width="200" label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="openBackStay(1, row.caseId)" v-hasPermi="['saasc:collection:insertKeep']">申请留案
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 申请留案、退案 -->
    <backStayVue @getList="getList" ref="backStayRef" />

    <!-- 申请协催 -->
    <helpUrgeVue @getList="getList" ref="helpUrgeRef" />

    <!-- 申请停催 -->
    <stopUrgeVue @getList="getList" ref="stopUrgeRef" />

    <!-- 标记案件 -->
    <leabelCaseVue @getList="getList" ref="leabelCaseRef" />

    <!-- 发送短信 -->
    <sendMessageVue @getList="getList" @openResult="openResult" ref="sendMessageVueRef" />

    <!-- 发送结果 -->
    <sendResult ref="sendResultRef" />

    <!-- 批量生成文书 -->
    <batchImportWritVue ref="batchImportWritVueRef" />

  </div>
</template>

<script setup name="Mycase">
import { ElMessage } from "element-plus";
import backStayVue from "./dialog/backStay.vue";
import helpUrgeVue from "./dialog/helpUrge.vue";
import stopUrgeVue from "./dialog/stopUrge.vue";
import leabelCaseVue from "./dialog/leabelCase.vue";
import callBarVue from "@/components/callBar/index.vue";
import sendMessageVue from "./dialog/sendMessage.vue";
import sendResult from "./dialog/sendResult.vue";
import batchImportWritVue from "@/views/mediation/dialog/batchImportWrit";
import {
  batchOptions,
  assetOwnerOptions,
  labelOptions,
  mycaseList,
  selectMoneySizeById,
} from "@/api/collection/mycase";
import { getFollowUpState, getProvinces } from "@/api/common/common";

const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
//是否有底部组件显示
const showFoot = computed(() => store.getters.callshowFoot);
const htrxCall = computed(() => store.getters.htrxCall);

const showSearch = ref(false);
const loading = ref(false);
const caseList = ref([]);
const total = ref(0);
const selectLoading = ref(false);
const follows = ref([]);
const clientCensusRegisterList = ref([]);

const checkedType = ref(["本页选中"]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
const single = ref(true); //操作栏是否可操作
const caseIds = ref([]); //选中id集合
const selectedArr = ref([]); //列表选中集合
const statistics = ref({
  //统计
  size: 0,
  money: 0,
  principal: 0,
});
const settlementStatus = ref("0"); //结清

const queryParams = ref({
  pageNum: 1,
  pageSize: 50,
  condition: false,
  caseId: undefined,
  clientName: undefined,
  clientPhone: undefined,
  clientIdcard: undefined,
  entrustingCaseBatchNum: undefined,
  entrustingPartyId: undefined,
  clientCensusRegister: undefined,
  clientMoney1: undefined,
  clientMoney2: undefined,
  entrustingCaseDate: [],
  returnCaseDate: [],
  followUpAst: [],
  uid: undefined,
  notFollowUpDays1: undefined,
  notFollowUpDays2: undefined,
  label: undefined,
  area: undefined,
  orderBy: undefined, // 排序字段
  sortOrder: undefined, // 排序（1-正序；2-倒序）
});
const rules = ref({
  clientPhone: [
    { required: false, message: "请输入手机号码", trigger: "blur" },
  ],
  clientIdcard: [{ required: false, message: "请输入证件号码", trigger: "blur" }],
});
const rangFields = ["entrustingCaseDate", "returnCaseDate", "followUpAst"]; //区间字段

const batchs = ref([]); //委案批次号下拉
const entrusts = ref([]); //转让方下拉
const areas = ref([]); //户籍地下拉
const tags = ref([]); //标签下拉

const followUpState = ref("全部"); //跟进状态tab
const lableColor = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];
//列显隐信息
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: 'UID', visible: true },
  { key: 2, label: '案件状态', visible: true },
  { key: 3, label: '调解阶段', visible: true },
  { key: 4, label: '跟进调诉员', visible: true },
  { key: 5, label: '产品类型', visible: true },
  { key: 6, label: '姓名', visible: true },
  { key: 7, label: '手机号码', visible: true },
  { key: 8, label: '出生日期', visible: true },
  { key: 9, label: '证件类型', visible: true },
  { key: 10, label: '证件号码【户籍地】', visible: true },
  { key: 11, label: '债权总金额', visible: true },
  { key: 12, label: '剩余应还债权金额', visible: true },
  { key: 13, label: '剩余应还本金', visible: true },
  { key: 14, label: '逾期日期（末次）', visible: true },
  { key: 15, label: '分配时间', visible: true },
  { key: 16, label: '跟进日期', visible: true },
  { key: 17, label: '跟进状态', visible: true },
  { key: 18, label: '催收状态', visible: true },
]);


//获取列表
function getList() {
  loading.value = true;
  queryParams.value.followUpState =
    followUpState.value == "全部" ? undefined : followUpState.value;
  queryParams.value.settlementStatus =
    settlementStatus.value == "全部" ? undefined : settlementStatus.value;
  mycaseList(proxy.addFieldsRange(queryParams.value, rangFields))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//获取跟进状态
function FollowUpState() {
  getFollowUpState().then((res) => {
    follows.value = res.data.genjin;
  });
}
FollowUpState();

//获取户籍地下拉
function CensusRegisters() {
  selectLoading.value = true;
  getProvinces()
    .then((res) => {
      areas.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}
CensusRegisters()

//获取批次号
function BatchList() {
  selectLoading.value = true;
  batchOptions()
    .then((res) => {
      batchs.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

//获取转让方
function OwnerList() {
  selectLoading.value = true;
  assetOwnerOptions()
    .then((res) => {
      entrusts.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { syYhPrincipal1, syYhPrincipal2 } = queryParams.value;
  // 检测输入是否是数字
  if (syYhPrincipal1 && !Number.isFinite(Number(syYhPrincipal1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal1 = undefined;
  }
  if (syYhPrincipal2 && !Number.isFinite(Number(syYhPrincipal2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal2 = undefined;
  }
  if (!syYhPrincipal1 || !syYhPrincipal2) return;
  const principal1 = parseFloat(syYhPrincipal1);
  const principal2 = parseFloat(syYhPrincipal2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.syYhPrincipal2 = undefined;
  }
};

// 排序
function sortChange({ prop, order }) {
  const orderByObj = {
    clientMoney: 3,
    followUpAst: 5,
  };
  let orderBy = orderByObj[prop];
  queryParams.value.sortOrder = proxy.orderEnum[order]
  queryParams.value.orderBy = orderBy;
  getList();
}

//拼接字段
function changeOption() {
  if (clientCensusRegisterList.value?.length > 0) {
    let checkedMap = proxy.$refs['clientCensusRegisterRef'].getCheckedNodes();
    let checkedMapLabel = checkedMap.map(item => item.label)
    let provinceList = clientCensusRegisterList.value.map((item, index) => {
      let prolabel = ""
      if (checkedMapLabel.includes(item?.[0])) {
        prolabel = item?.[0]
      }
      return prolabel
    })
    let clientCensusRegisterFormat = Array.from(new Set(provinceList.filter(item => item?.length > 0))).toString()
    clientCensusRegisterList.value.forEach((item, index) => {
      if (provinceList.includes(item?.[0])) {
        return true
      } else {
        let text = "";
        item.forEach((v, h) => {
          text = text + v;
        })
        clientCensusRegisterFormat = clientCensusRegisterFormat + `${index == 0 && clientCensusRegisterFormat.length == 0 ? '' : (text.length > 0 ? ',' : '')}${text}`
      }
    })
    queryParams.value.clientCensusRegister = clientCensusRegisterFormat
  } else {
    queryParams.value.clientCensusRegister = undefined;
  }
}
//批量生成文书
function batchCreateWork() {
  proxy.$refs['batchImportWritVueRef'].opendialog(caseIds.value)
}


//批量下载文书
function downloadWorkZip(downloadWork) {
  let reqForm = { ids: caseIds.value }
  if (checkedType.value[0] == '搜索结果全选') {
    reqForm = JSON.parse(JSON.stringify(queryParams.value))
    reqForm.condition = true
    delete reqForm.pageNum
    delete reqForm.pageSize
  }
  // 0 pdf格式  1 word格式
  const func = { 0: downloadPdfFormat, 1: downloadWordFormat }
  func[downloadWork](reqForm)
}

// 下载pdf格式文件
function downloadPdfFormat(reqForm) {

  proxy.downloadforjson("/zip/downloadMediate", reqForm, `批量PDF文件_${new Date().getTime()}.zip`);
}
// 下载word格式文件
function downloadWordFormat(reqForm) {
  proxy.downloadforjson("/zip/downloadWordDocumentsMediate", reqForm, `批量WORD文件_${new Date().getTime()}.zip`);
}


//获取标签下拉
function LabelList() {
  selectLoading.value = true;
  labelOptions().then((res) => {
    tags.value = res.data;
  }).finally(() => {
    selectLoading.value = false;
  });
}

//查询
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 50,
    caseId: undefined,
    clientName: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    clientCensusRegister: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    followUpAst: [],
    uid: undefined,
    notFollowUpDays1: undefined,
    notFollowUpDays2: undefined,
    label: undefined,
    area: undefined,
    condition: false, //是否搜索结果全选
    orderBy: undefined, // 排序字段
    sortOrder: undefined, // 排序（1-正序；2-倒序）
  };
  clientCensusRegisterList.value = [];
  getList();
}

//打开弹窗
function opendialog() { }

//打开退案留案
function openBackStay(type, caseId) {
  let data = {
    type: type, //0-申请退案, 1-申请留案
  };
  if (checkedType.value[0] == "本页选中" || checkedType.value.length === 0) {
    data.req = {
      caseIds: caseId ? [caseId] : caseIds.value,
      condition: false,
    };
    data.total = caseId ? 1 : caseIds.value.length;
  } else {
    data.req = proxy.addFieldsRange(queryParams.value, rangFields);
    data.req.pageNum = undefined;
    data.req.pageSize = undefined;
    (data.req.caseIds = caseId ? [caseId] : undefined),
      (data.total = caseId ? 1 : total.value);
    if (caseId) data.req.condition = false;
  }
  proxy.$refs["backStayRef"].opendialog(data);
}

//打开申请协催
function openhelpUrge() {
  let data = {};
  if (checkedType.value[0] == "本页选中") {
    data = {
      caseIds: caseIds.value,
      condition: false,
    };
  } else {
    data = proxy.addFieldsRange(queryParams.value, rangFields);
    data.pageNum = undefined;
    data.pageSize = undefined;
  }
  proxy.$refs["helpUrgeRef"].opendialog(data);
}

//打开停催
function openstopUrge() {
  let data = {};
  if (checkedType.value[0] == "本页选中") {
    data = {
      caseIds: caseIds.value,
      condition: false,
    };
  } else {
    data = proxy.addFieldsRange(queryParams.value, rangFields);
    data.pageNum = undefined;
    data.pageSize = undefined;
  }
  proxy.$refs["stopUrgeRef"].opendialog(data);
}

//打开标记案件
function openLabelCase() {
  let data = {};
  if (checkedType.value[0] == "本页选中") {
    data = {
      caseIds: caseIds.value,
      condition: false,
    };
  } else {
    data = proxy.addFieldsRange(queryParams.value, rangFields);
    data.pageNum = undefined;
    data.pageSize = undefined;
  }
  proxy.$refs["leabelCaseRef"].opendialog(data);
}

//打开短信结果
function openResult(data) {
  proxy.$refs['sendResultRef'].opendialog(data)
}

//结清状态
function settleStateChange() {
  handleQuery();
}

//全选类型
function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    proxy.$refs["multipleTableRef"].clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else if (checkedType.value?.[0] == "搜索结果全选") {
    nextTick(() => {
      caseList.value.length > 0 &&
        caseList.value.map((item) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
    });
  } else {
    caseList.value.length > 0 &&
      caseList.value.map((item) => {
        proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.condition = true;
  } else {
    queryParams.value.condition = false;
  }
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;

  if (selectedArr.value.length) {
    checkedType.value = queryParams.value.condition ? ["搜索结果全选"] : ["本页选中"];
    checkStatus.value.map((item) => {
      //全选/半选样式变化
      if (item.label === checkedType.value[0]) {
        item.indeterminate =
          selectedArr.value.length > 0 &&
          selectedArr.value.length < caseList.value.length;
      }
    });
  } else {
    checkedType.value = [];
  }
}

//单元格hover
function callCellEnter(row, column, cell, event) {
  if (showFoot.value) {
    return;
  }
  var arr = [];
  if (column?.label == "手机号码") {
    arr = document.getElementsByClassName(column.id);
    for (let i = 0; i < arr.length; i++) {
      const obj = arr[i];
      if (obj != cell) {
        obj.classList.remove("zIndex4");
      } else {
        obj.classList.add("zIndex4");
      }
    }
  }
}

//表格行能否选择
function checkSelectable() {
  if (checkedType.value[0] === "本页选中" || checkedType.value.length === 0) {
    return true;
  } else {
    return false;
  }
}

//发送短信
function openSendMessage(row) {
  let req = {
    caseIds: row ? [row.caseId] : caseIds.value,
  };
  let query = {};
  if (!row) {
    query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFields)));
    query.followUpState = followUpState.value == "全部" ? undefined : followUpState.value;
    query.settlementStatus = settlementStatus.value == "全部" ? undefined : settlementStatus.value;
    query.pageNum = undefined;
    query.pageSize = undefined;
  } else {
    query.condition = false;
  }
  Object.assign(query, req);
  let isBatchSend = row ? 0 : 1;
  proxy.$refs["sendMessageVueRef"].openDialog({ isBatchSend, query, pageType: 'case' })
}

//tab
function tabChange(val) {
  handleQuery();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

//案件状态 0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
function caseStateChange(row) {
  return ["未分配", "已分配", "停催", "留案", "退案", "回收案件", "案件结清"][
    row.caseState
  ];
}

watch(caseList, (newval, preval) => {
  proxy.$refs["multipleTableRef"].toggleAllSelection();
});

watch([selectedArr, checkedType], (newval) => {
  nextTick(() => {
    if (!loading.value && checkedType.value?.length > 0) {
      let req = {};
      loading.value = true;
      if (!checkedType.value[0] || checkedType.value[0] == "本页选中") {
        queryParams.value.condition = false;
      }
      req = proxy.addFieldsRange(queryParams.value, rangFields);
      req.pageNum = undefined;
      req.pageSize = undefined;
      if (!req.condition) req.caseIds = caseIds.value;
      selectMoneySizeById(req).then((res) => {
        statistics.value = res.data || {
          size: 0,
          money: 0,
          principal: 0,
        };
      }).finally(() => loading.value = false);
    } else {
      statistics.value = {
        size: 0,
        money: 0,
        principal: 0,
      };
    }
  });
});

</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h32 {
  height: 32px !important;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-table .el-table__cell.zIndex4) {
  z-index: 4 !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__bar) {
  z-index: 4;
}

.send-icon {
  position: relative;
  top: 2px;
  cursor: pointer;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
