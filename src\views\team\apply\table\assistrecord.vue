<template>
  <div class="assistrecord">
    <el-form
      class="form-content h-50 mt20"
      :class="{ 'h-auto': showSearch }"
      :model="queryParams"
      label-position="right"
      :label-width="100"
      ref="queryRef"
      :inline="true"
    >
      <el-form-item label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="客户姓名" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入姓名"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="clientIdcard">
        <el-input
          v-model="queryParams.clientIdcard"
          placeholder="请输入身份证号"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <!-- <el-form-item label="委托方" prop="entrustingPartyId">
        <el-select
          v-model="queryParams.entrustingPartyId"
          placeholder="请输入或选择转让方"
          clearable
          filterable
          :reserve-keyword="false"
          @focus="OwnerList"
          style="width: 328px"
        >
          <el-option
            v-for="item in entrusts"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="发起时间" style="width: 336px">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb40 mt10">
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <div class="fxn-tabs">
      <el-tabs
        class="mb8"
        v-model="lawsuitTabs"
        @tab-click="tabChange"
        style="flex: 1"
      >
        <el-tab-pane
          v-for="item in tab"
          :key="item.id"
          :label="item.label"
          :name="item.id"
        >
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="columns[0].width"
        v-show="columns[0].visible"
      />
        <!-- <template #default="scope">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <el-tooltip v-show="scope.row.labelContent" placement="top">
              <template #content>{{ scope.row.labelContent }}</template>
              <case-label
                class="ml5"
                v-show="scope.row.label && scope.row.label != 7"
                :code="scope.row.label"
              />
            </el-tooltip>
            <span
              style="color: #409eff; cursor: pointer"
              type="text"
              v-show="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)"
              >{{ scope.row.caseId }}</span
            >
            <span v-show="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        label="委托方"
        align="center"
        prop="entrustingPartyName"
        v-if="columns[1].visible"
        width="80"
      />
    <el-table-column
      label="批次号"
      align="center"
      prop="batchNo"
      v-if="columns[2].visible"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      label="客户姓名"
      align="center"
      prop="clientName"
      v-if="columns[3].visible"
    />
    <el-table-column
      label="委托金额"
      align="center"
      prop="entrustMoney"
      v-if="columns[4].visible"
    />
        <el-table-column
      label="申请原因"
      align="center"
      prop="reason"
      v-if="columns[5].visible"
    />
        <el-table-column
      label="申请人"
      align="center"
      prop="employeeName"
      v-if="columns[6].visible"
    />
    <el-table-column
      label=" 协助调解人"
      align="center"
      prop="applyTargetName"
      v-if="columns[7].visible"
    />
    <el-table-column
      label="申请时间"
      width="120"
      align="center"
      prop="createTime"
      v-if="columns[8].visible"
      :show-overflow-tooltip="true"
    >
    </el-table-column>
    <el-table-column
      label="审核状态"
      prop ="status"
      v-if="columns[9].visible"
      align="center"
    />
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button type="text" @click="urgedRecord(scope.row)" v-if="false"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList()"
    />

    <!-- 协催记录 -->
    <selectAssistDetails ref="selectAssistDetailsRef" />
  </div>
</template>
<script setup>
import selectAssistDetails from "../dialog/selectAssistDetails.vue";
import { getTeamApplyList } from "@/api/approval";
import {replaceNull} from "@/utils/common"
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表格配置数据
const loading = ref(false);
const total = ref(0);
// 列显隐信息
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 80 },
  { key: 1, label: `委托方`, visible: true, width: 100 },
  { key: 2, label: `批次号`, visible: true , width: 100 },
  { key: 3, label: `客户姓名`, visible: true , width: 100 },
  { key: 4, label: `委托金额`, visible: true, width: 100  },
  { key: 5, label: `申请原因`, visible: true , width: 100 },
  { key: 6, label: `申请人`, visible: true, width: 100  },
  { key: 7, label: `协助调解人`, visible: true, width: 100  },
  { key: 8, label: `申请时间`, visible: true, width: 100  },
  { key: 9, label: `审核状态`, visible: true , width: 100 },
]);
//表单配置信息
const tab = ref([
  { label: "进行中", id: "0", count: 0 },
  { label: "已完成", id: "1", count: 0 },
  { label: "全部", id: "all", count: 0 },
]);
//列表切换字段
const showSearch = ref(false);
const lawsuitTabs = ref("all");

//表格数据
const caseList = ref([]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    lawsuitProcess: undefined,
    createTime: [],
  },
});
//需要拆分的字段
const rangfiles = ["createTime"];
const { queryParams } = toRefs(data);

//获取列表
function getList(deptId) {
  queryParams.value.approveProcessState = 2;
  queryParams.value.approveState = 2;
  loading.value = true;
  let req = {
    approveCode: "teamwork",
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    approveProcessState: queryParams.value.approveProcessState,
    approveState: queryParams.value.approveProcessState,
    approveData: {
      stringDeptId: deptId,
      ...proxy.addFieldsRange(queryParams.value, rangfiles),
    },
  };
  getTeamApplyList(req)
    .then((res) => {
      caseList.value = replaceNull(res.rows);
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//协催记录
function urgedRecord(row) {
  proxy.$refs["selectAssistDetailsRef"].opendialog(row.caseId);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    createTime: [],
  };
  getList();
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNumber =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange, //查询参数
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({
    path: `/case/teamIndex-detail/teamCaseDetails/${caseId}`,
    query: { type: "caseManage" },
  });
}

//tab切换
function tabChange() {
  getList();
}

defineExpose({
  getList,
});
</script>
<style lang="scss" scoped>
.form-content {
  .el-form-item {
    width: 30% !important;

    .el-select .el-select__tags .el-tag--info {
      max-width: 100px;
      overflow: hidden;
    }
  }
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.filter-tree {
  height: 70%;
  overflow: auto;
}

.top-right-btn {
  z-index: 1;
}
.fxn-tabs {
  position: relative;
  .fxn-status {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
