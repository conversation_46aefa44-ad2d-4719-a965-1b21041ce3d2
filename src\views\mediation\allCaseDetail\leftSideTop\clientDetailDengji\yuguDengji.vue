<template>
  <div class="yugu-dengji">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="108px">
      <el-form-item prop="customerAttribute" label="客户属性">
        <el-radio-group v-model="form.customerAttribute">
          <el-radio label="短信+工单回复">短信+工单回复</el-radio>
          <el-radio label="自流水">自流水</el-radio>
          <el-radio label="电话">电话</el-radio>
          <el-radio label="上期留案">上期留案</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="promiseRepaymentMoney" label="预期还款金额">
        <NumberInput v-model="form.promiseRepaymentMoney" :decimals="2" style="width: 220px;">
          <template #append>元</template>
        </NumberInput>
      </el-form-item>
      <el-form-item prop="promiseRepaymentTime" label="预期还款日期">
        <el-date-picker
          v-model="form.promiseRepaymentTime"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
           style="width: 220px;"
        />
      </el-form-item>
    </el-form>
    <div style="text-align: right;">
      <el-button @click="onSubmit" :loading="loading">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { nextTick, onMounted } from 'vue';

const { proxy } = getCurrentInstance()
const store = useStore();
const loading = computed(() => store.state.allCaseDetail.loading);
const caseId = inject('caseId');
const form = ref({
  customerAttribute: '短信+工单回复',
  promiseRepaymentMoney: "",
  promiseRepaymentTime: '',
})
const rules = ref({
  customerAttribute: [
    { required: true, message: '请选择客户属性', trigger: 'blur' },
  ],
  promiseRepaymentMoney: [
    { required: true, message: '请输入预期还款金额', trigger: 'blur' },
    { type: 'number', message: '请输入数字', trigger: 'blur' },
  ],
  promiseRepaymentTime: [
    { required: true, message: '请选择预期还款日期', trigger: 'blur' },
  ],
})

onMounted(() => {
  store.commit('allCaseDetail/SET_YUGU_FORM_REF', proxy.$refs.formRef)
})

function onSubmit() { 
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      const reqForm = {
        preTransaction: 1
      }
      Object.assign(reqForm, form.value)
      store.dispatch('allCaseDetail/updateYuguForm', reqForm)
    } else {
      console.log('error submit!!')
      return false
    }
  })
  
}
</script>

<style scoped>
.yugu-dengji {
  padding: 10px;
  margin-top: 10px;
  border: 1px solid #e2e2e2;
}
</style>
