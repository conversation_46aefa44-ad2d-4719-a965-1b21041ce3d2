
import Fingerprint2 from "fingerprintjs2"
import { ref, watch } from 'vue'

/*
* 全局公共方法
*/

// /**
//  * 数字保留小数位数
//  * @param {string,number} val 被转换的值
//  * @param {number} bit 保留的位数
//  * @returns {number} 
//  */
// export function numFilter(val, bit = 2) {
//   if (typeof val === 'number') {
//     return parseFloat(val).toFixed(bit)
//   } else {
//     if (typeof val === 'string' && Number(val) != NaN) {
//       return parseFloat(val).toFixed(bit)
//     } else {
//       // console.log(val + '不是纯数字')
//       return '--'
//     }
//   }
// }

// 数据千分号
export function formatAmountWithComma(amount) {
  // 验证输入是否为字符串或数字类型
  if (typeof amount !== 'string' && typeof amount !== 'number') {
    // console.error("错误: 输入必须是字符串或数字.");
    return '0';
  }

  // 将输入金额转换为字符串
  let amountStr = typeof amount === 'string' ? amount : amount.toString();

  // 验证金额格式是否正确
  if (!/^\d+(\.\d{0,3})?$/.test(amountStr)) {
    // console.error("错误: 金额格式无效。请提供有效的号码.");
    return '--';
  }

  // 判断是否含有小数位
  const hasDecimal = /\.\d{2,3}$/.test(amountStr);

  // 将整数部分按每三位添加逗号
  amountStr = amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // 处理小数部分
  if (hasDecimal) {
    const decimalIndex = amountStr.indexOf('.');
    const integerPart = amountStr.slice(0, decimalIndex);
    let decimalPart = amountStr.slice(decimalIndex);

    // 如果小数部分不足三位，则补足三位
    /*if (decimalPart.length === 2) {
      decimalPart += '00';
    } else if (decimalPart.length === 3) {
      decimalPart += '0';
    }*/

    return integerPart + decimalPart;
  }

  return amountStr;
}

/**
 * 数字保留小数位数
 * @param {string,number} val 被转换的值
 * @param {number} bit 保留的位数
 * @returns {number} 
 */
export function numFilter(val, bit = 2) {
  if (typeof val === 'number') {
    return formatAmountWithComma(parseFloat(val).toFixed(bit))
  } else {
    if (typeof val === 'string' && !isNaN(Number(val))) {
      return formatAmountWithComma(parseFloat(val).toFixed(bit))
    } else {
      return '--'
    }
  }
}

/**
 * 数字保留
 * @param {string,number} val 被转换的值
 * @param {number} bit 保留的位数
 * @returns {number} 
 */
export function bankNumFilter(num) {
  let logo = "";//用于记录是正值还是负值
  if (!num) return undefined;
  if (num < 0) {
    logo = "-";
    num = -num;//将负数转正,如果不转正，则下面获取它的length时，会由于有个负号，使得长度+1，最终加逗号位置出错
  }

  let result = num.toString().split("");//将数字转化为了数组，便于使用数组中的splice方法插入逗号
  let position = result.indexOf(".");//获取小数点的位置，根据有无小数点确定position最终值进入添加逗号环节
  position = position !== -1 ? position -= 1 : result.length - 1;//因为只需考虑整数部分插入逗号，所以需要考虑有没有逗号。有逗号则不等于-1，减去逗号位置，则是下标0~position就是整数部分；若不是小数，则num原本就是整数，直接取其length即可
  while (position > 2) {//只要下标值大于2，说明前面还可以插入“,”，则继续循环
    position -= 3;//下标前移3位，然后在这个下标对应的元素后面插入逗号
    result.splice(position + 1, 0, ",");
  }
  return logo + result.join("");//数组转换为字符串,前面+logo，若为负数则拼接个符号，否则拼接空字符
}

//js强制保留小数点后两位小数
export function setNumberToFixed(num) {
  if (!num) return "0.00";
  let value = Math.round(parseFloat(num) * 100) / 100;
  let xsd = value.toString().split(".");
  if (xsd.length == 1) {
    value = value.toString() + ".00";
    return value;
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      value = value.toString() + "0";
    }
    return value;
  }
}

// 获取浏览指纹
export function getFingerprint() {
  return new Promise((reslove, reject) => {
    const createFingerprint = () => {
      const compData = {
        values: undefined,
        murmur: undefined,
      }
      Fingerprint2.get((components) => {
        compData.values = components.map(component => component.value) // 配置的值的数组
        compData.murmur = Fingerprint2.x64hash128(compData.values.join(""), 31).toUpperCase() // 生成浏览器指纹
        reslove(compData)
      })
    }
    if (window.requestIdleCallback) {
      requestIdleCallback(() => {
        createFingerprint()
      })
    } else {
      setTimeout(() => {
        createFingerprint()
      }, 600)
    }
  })
}

// 获取网络IP地址
export function getIpAddress(timeout = 1000) {

  const ipv4Regex =
    /\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/;

  const ipv6Regex = /\b(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}\b/i;

  // prettier-ignore
  // @ts-expect-error
  globalThis.RTCPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection
  const ipSet = new Set();

  const onicecandidate = (ice) => {
    const candidate = ice?.candidate?.candidate;
    if (candidate) {
      for (const regex of [ipv4Regex, ipv6Regex]) {
        const [ip] = candidate.match(regex) ?? [];
        if (ip) {
          ipSet.add(ip);
        }
      }
    }
  };

  return new Promise((resolve, reject) => {
    const conn = new globalThis.RTCPeerConnection({
      iceServers: [
        {
          urls: 'stun:stun.l.google.com:19302',
        },
      ],
    });
    conn.addEventListener('icecandidate', onicecandidate);
    conn.createDataChannel('');
    conn.createOffer().then((offer) => conn.setLocalDescription(offer), reject);

    setTimeout(() => {
      try {
        conn.removeEventListener('icecandidate', onicecandidate);
        conn.close();
      } catch {
        // ignore
      }

      resolve([...ipSet]);
    }, timeout);
  });
};


export function loadBaiduMapScript(apiKey) {
  if (!apiKey) return false
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = `https://api.map.baidu.com/api?v=3.0&ak=${apiKey}&s=1&callback=initialize`
  document.body.appendChild(script);
}

// 格式化参数
export function formatParams(reqForm, selectedArr, allQuery, filedName = 'caseId') {
  let reqParams = JSON.parse(JSON.stringify(reqForm))
  if (allQuery.value) {
    for (const key in reqParams) {
      if (Object.hasOwnProperty.call(reqParams, key)) {
        if (Array.isArray(reqParams[key])) {
          reqParams[key] = String(reqParams[key])
        }
      }
    }
    delete reqParams.pageNum
    delete reqParams.pageSize
    delete reqParams.ids
  } else {
    reqParams = {
      ids: selectedArr.value.map(item => item[filedName]),
      caseIds: selectedArr.value.map(item => item[filedName])
    }
  }
  reqParams.allQuery = allQuery.value
  return reqParams
}
// 格式化参数
export function formatParams2(reqForm, selectedArr, allQuery, filedName = 'caseId') {
  let reqParams = JSON.parse(JSON.stringify(reqForm))
  if (allQuery) {
    for (const key in reqParams) {
      if (Object.hasOwnProperty.call(reqParams, key)) {
        if (Array.isArray(reqParams[key])) {
          reqParams[key] = String(reqParams[key])
        }
      }
    }
    delete reqParams.pageNum
    delete reqParams.pageSize
    delete reqParams.ids
  } else {
    reqParams = {
      ids: selectedArr.value.map(item => item[filedName]),
      caseIds: selectedArr.value.map(item => item[filedName])
    }
  }
  reqParams.allQuery = allQuery
  return reqParams
}

// blob 转换为原始数据
export function blobToOriginalData(blob) {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const originalData = reader.result;
        if (JSON.parse(originalData)) {
          if (JSON.parse(originalData).code == 200) {
            mymessage.ClosePrompt();
            resolve(JSON.parse(originalData));
          }
          console.error(JSON.parse(originalData).deta);
        }
      };
      reader.onerror = reject;
      reader.readAsText(blob);
    });
  } catch (error) {
    console.error(error);
  }
}

/**
 * 将数组中的所有对象的 null或者undefined 值替换为 "__"
 * 原始数据不会改变，返回的是处理后的新数组
 * @param {Array} data - 要处理的数据数组，数组元素应为对象
 * @returns {Array} 处理后的新数组，所有 null 值已被替换为 "__"
 * @throws {Error} 如果输入参数不是数组类型，则抛出错误
 */
export function replaceNull(data) {
  if (!Array.isArray(data)) {
    throw new Error('不是数组类型');
  }
  const result = JSON.parse(JSON.stringify(data));
  result.forEach(item => {
    const keys = Object.keys(item);
    keys.forEach(key => {
      if (item[key] == null) {
        item[key] = '--';
      }
    });
  });
  return result;
}