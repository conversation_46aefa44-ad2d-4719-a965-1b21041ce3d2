<template>
  <el-dialog title="申请外访" v-model="open" width="700px" top="70px" lock-scroll>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="外访日期" prop="outsideStart">
        <el-date-picker v-model="form.outsideStart" type="date" value-format="YYYY-MM-DD" placeholder="请选择外访时间" />
      </el-form-item>
      <el-form-item label="外访地址" class="mt5" prop="outsideAddress">
        <el-input class="mt5" v-model="form.outsideAddress" placeholder="请输入地址">
          <template #append>
            <el-button @click="BaiduMap(null, form.outsideAddress, 'OutboundMap')">搜索地址</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label=" ">
        <div class="mt10" v-loading="maploading" id="OutboundMap"></div>
      </el-form-item>
      <el-form-item label="申请原因" prop="reason">
        <el-input type="textarea" v-model="form.reason" :rows="4" show-word-limit maxlength="500" />
      </el-form-item>
      <el-form-item label="选择处置人员" prop="odvId" style="height:240px">
        <el-input v-show="false" v-model="form.odvId" />
        <selectUser v-model:selected="expeditingRecord" class="mt10" ref="selectUserRef" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import selectUser from "@/components/selectUser/index";
import { insertOutsideRecord } from "@/api/caseDetail/detail";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getcaseDetailInfo"]);

//搜索地址校验
const validateAddress = (rule, value, callback) => {
  if (!value || value == "") {
    callback(new Error("请输入外访地址搜索选择！"));
  } else {
    if (!form.value.longitudeAtitudeStart || form.value.longitudeAtitudeStart == "") {
      callback(new Error("请点击搜索后选择地图中的详细地点！"));
    } else {
      callback();
    }
  }
};

const open = ref(false);
const loading = ref(false);
const maploading = ref(false);
const expeditingRecord = ref([]);
const data = reactive({
  form: {},
  rules: {
    outsideStart: [{ required: true, message: "请选择外访时间！", trigger: "change" }],
    outsideAddress: [{ required: true, validator: validateAddress, trigger: "blur" }],
    reason: [{ required: true, message: "请填写申请原因！", trigger: "blur" }],
    odvId: [{ required: true, message: "请选择处置人员！", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog(data) {
  reset();
  form.value.caseId = data.caseId;
  open.value = true;
  BaiduMap(null, null, "OutboundMap");
}

//提交
function submit() {
  let odvId = [];
  let odvName = [];
  if (expeditingRecord.value.length > 0) {
    expeditingRecord.value.map((item) => {
      odvId.push(item.id);
      odvName.push(item.name);
    });
    form.value.odvId = String(odvId);
    form.value.odvName = String(odvName);
  }
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      insertOutsideRecord(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess("申请提交成功！");
          cancel();
          emit("getcaseDetailInfo");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    outsideStart: undefined,
    outsideAddress: undefined,
    longitudeAtitudeStart: undefined,
    reason: undefined,
    odvId: undefined,
    odvName: undefined,
  };
  proxy.$refs["selectUserRef"] && proxy.$refs["selectUserRef"].clearSelected();
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//持续获取
function getDiv(value) {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      var div = document.getElementById(value);
      if (!div) {
        setTimeout(() => {
          getDiv(value);
        });
        return;
      }
      resolve(div);
    });
  });
}

//百度地图
async function BaiduMap(Win, huji_address, div, result) {
  //Win-弹窗窗口,huji_address-地址，div-渲染容器，result-检索渲染容器
  huji_address = huji_address || "北京";
  maploading.value = true;
  await getDiv(div);
  var map = new BMap.Map(div); // 创建Map实例
  var geoc = new BMap.Geocoder({ extensions_town: true }); //地址解析对象
  map.centerAndZoom(new BMap.Point(116.331398, 39.897445), 11);
  map.centerAndZoom("北京", 11);
  map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
  var myKeys = [huji_address];
  if (result) {
    let local = new BMap.LocalSearch(map, {
      renderOptions: { map: map, panel: result },
      pageCapacity: 2,
    });
    local.searchInBounds(myKeys, map.getBounds());
    this.maploading = false;
  } else {
    let options = {
      onSearchComplete: function (results) {
        var opts = {
          width: 250, // 信息窗口宽度
          height: 100, // 信息窗口高度
          title: "", // 信息窗口标题
        };
        var pointArr = [];
        if (huji_address != "北京") {
          for (var i = 0; i < results.getCurrentNumPois(); i++) {
            var pointObj = results.getPoi(i);
            var pointA = new BMap.Point(pointObj.point.lng, pointObj.point.lat);
            pointArr.push(pointA);
            //var marker = new BMapGL.Marker(new BMapGL.Point(point.lng, point.lat));
            var marker = new BMap.Marker(
              new BMap.Point(pointObj.point.lng, pointObj.point.lat)
            ); // 创建点
            map.addOverlay(marker); //增加点
            marker.setAnimation(BMAP_ANIMATION_BOUNCE); //跳动的动画
            addClickHandler("地址：" + pointObj.address, marker, pointObj.title);
            //s.push(results.getPoi(i).title + ", " + results.getPoi(i).address);
          }
          map.setViewport(pointArr); //显示搜索到的所有标注
        }
        function addClickHandler(content, marker, title) {
          marker.addEventListener("click", function (e) {
            //给标注添加点击事件
            openInfo(content, e, title);
          });
        }
        function openInfo(content, e, title) {
          opts.title = title;
          var p = e.target;
          let lng_lat = [p.getPosition().lng, p.getPosition().lat];
          form.value.longitudeAtitudeStart = String(lng_lat);
          var point = new BMap.Point(p.getPosition().lng, p.getPosition().lat);
          var infoWindow = new BMap.InfoWindow(content, opts); // 创建信息窗口对象
          map.openInfoWindow(infoWindow, point); //开启信息窗口
          const lat = point.lat
          const lng = point.lng
          map.centerAndZoom(new BMap.Point(lat, lng), 11);
          // 创建地理编码实例, 并配置参数获取乡镇级数据
          var geoc = new BMap.Geocoder({ extensions_town: true });
          // 根据坐标得到地址描述    
          geoc.getLocation(point, function (result) {
            if (result) {
              let address = infoWindow.D.title;
              const { city, province, town, district } = result.addressComponents
              address = address.replaceAll(province, '')
              address = address.replaceAll(city, '')
              address = address.replaceAll(town, '')
              form.value.outsideAddress = `${province}${province == city ? '' : city}${district || town}${address}`
            }
          });

        }
        maploading.value = false;
      },
    };
    var local = new BMap.LocalSearch(map, options);
    local.search(huji_address);
  }
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
#OutboundMap {
  width: 100%;
  height: 250px;
  border-radius: 8px;
}

:deep(.el-form-item--default) {
  margin-bottom: 10px;
}

:deep(.el-dialog__body) {
  max-height: 800px;
}
</style>
