import { createWebHistory, createRouter } from 'vue-router'
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/report/dataOverview/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },
    ]
  },
  {
    path: '/system/dept-user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'importUser',
        noCache: true,
        component: () => import('@/views/system/dept/importUser'),
        name: 'ImportUser',
        meta: { title: '导入员工' }
      }
    ]
  },
  {
    path: '/case/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'rules/:time(\\d+)',
        component: () => import('@/views/case/caseManage/rules/rules'),
        name: 'Rules',
        meta: { title: '规则分案', activeMenu: '/case/caseManage' }
      }
    ]
  },
  {
    path: '/case/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'point/:time(\\d+)',
        component: () => import('@/views/case/caseManage/point/point'),
        name: 'Point',
        meta: { title: '指定分案', activeMenu: '/case/caseManage' }
      }
    ]
  },
  {
    path: '/mediationTeam/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'rules/:time(\\d+)',
        component: () => import('@/views/mediation/mediationTeam/rules/rules.vue'),
        name: 'mediationTeamRules',
        meta: { title: '规则分案' }
      }
    ]
  },
  {
    path: '/mediationTeam/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'point/:time(\\d+)',
        component: () => import('@/views/mediation/mediationTeam/point/point.vue'),
        name: 'mediationTeamPoint',
        meta: { title: '指定分案' }
      }
    ]
  },
  {
    path: '/case/caseManage-handle',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'batchHandle',
        noCache: true,
        component: () => import('@/views/case/caseManage/batchhandle/batchhandle'),
        name: 'BatchHandle',
        meta: { title: '案件批量操作' }
      }
    ]
  },
  {
    path: '/message/system',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'postMessage',
        noCache: true,
        component: () => import('@/views/message/system/dialog/post'),
        name: 'postMessage',
        meta: { title: '发送信息', activeMenu: '/message/system' }
      }
    ]
  },
  {
    path: '/user/system',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'editPassword',
        noCache: true,
        component: () => import('@/views/system/editpassword/edit'),
        name: 'editPassword',
        meta: { title: '修改密码', activeMenu: '/user/system' }
      }
    ]
  },
  {
    path: '/message/system',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'editMessage/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/message/system/dialog/edit'),
        name: 'editMessage',
        meta: { title: '修改信息', activeMenu: '/message/system' }
      }
    ]
  },
  {
    path: '/message/system',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detailsMessage/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/message/system/dialog/details'),
        name: 'systemDetailsMessage',
        meta: { title: '消息详情', activeMenu: '/message/system' }
      }
    ]
  },
  {
    path: '/message/home',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detailsMessage/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/message/home/<USER>/details'),
        name: 'detailsMessage',
        meta: { title: '消息详情', activeMenu: '/message/home' }
      }
    ]
  },
  {
    path: '/collection/mycase-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'caseDetails/:caseId(\\d+)',
        noCache: true,
        component: () => import('@/views/caseDetails/index'),
        name: 'CaseDetails',
        meta: { title: '案件详情' }
      }
    ]
  },
  {
    path: '/case/allcase-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'caseDetails/:caseId(\\d+)',
        noCache: true,
        component: () => import('@/views/mediation/allCaseDetail/index'),
        name: 'AllCaseDetail',
        meta: { title: '案件详情' }
      }
    ]
  },
  {
    path: '/case/caseIndex-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'manageDetails/:caseId(\\d+)',
        noCache: true,
        component: () => import('@/views/caseDetails/index'),
        name: 'ManageDetails',
        meta: { title: '案件详情' }
      }
    ]
  },
  {
    path: '/case/teamIndex-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'teamCaseDetails/:caseId(\\d+)',
        noCache: true,
        component: () => import('@/views/caseDetails/index'),
        name: 'teamCaseDetails',
        meta: { title: '案件详情' }
      }
    ]
  },
  {
    path: '/case/aduit-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'aduitDetails/:caseId(\\d+)',
        noCache: true,
        component: () => import('@/views/caseDetails/index'),
        name: 'AduitDetails',
        meta: { title: '案件详情', activeMenu: '/case/aduit' }
      }
    ]
  },
  {
    path: '/collection/workorder',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/collection/myworkorder/dialog/workorder'),
        name: 'collectionWorkorderDetails',
        meta: { title: '工单详情', activeMenu: '/collection/myworkorder' }
      }
    ]
  },
  {
    path: '/team/workorder',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/team/workorder/dialog/workorder'),
        name: 'workorderDetails',
        meta: { title: '工单详情', activeMenu: '/team/teamworkorder' }
      }
    ]
  },
  {
    path: '/setting/user-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'userDetails/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/system/dept/userDetails'),
        name: 'userDetails',
        meta: { title: '员工详情', activeMenu: '/system/userDetails' }
      }
    ]
  },
  {
    path: '/teamcase/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'point/:time(\\d+)',
        component: () => import('@/views/team/case/page/point'),
        name: 'TeamPoint',
        meta: { title: '指定分案', activeMenu: '/team/case' }
      }
    ]
  },
  {
    path: '/teamcase/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'rules/:time(\\d+)',
        component: () => import('@/views/team/case/page/rules'),
        name: 'TeamRules',
        meta: { title: '规则分案', activeMenu: '/team/case' }
      }
    ]
  },
  {
    path: '/team/workorder-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'orderDetail/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/team/workorder/dialog/orderDetail'),
        name: 'orderDetail',
        meta: { title: '工单详情', activeMenu: '/team/teamworkorder' }
      }
    ]
  },
  {
    path: '/case/brokerage-previewPDF',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'previewPDF',
        noCache: true,
        component: () => import('@/views/case/brokerage/page/previewPDF'),
        name: 'previewPDF',
        meta: { title: '预览PDF文件', activeMenu: '/case/previewPDF' }
      }
    ]
  },
  {
    path: '/signature',
    component: Layout,
    hidden: true,
    children: [
      // /:id(\d+)
      {
        path: 'addLawyer',
        component: () => import('@/views/signature/signatureDocuments/page/addLawyer'),
        name: 'AddLawyer',
        meta: { title: '发函操作', activeMenu: '/signature/addLawyer/' }
      },
      {
        path: 'SigDocsList/:id(\\d+)',
        component: () => import('@/views/signature/signatureDocuments/page/SigDocsList'),
        name: 'SigDocsList',
        meta: { title: '签章文件详情', activeMenu: '/signature/SigDocsList/' }
      },
    ]
  },
  {
    path: '/writ',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addLawyer',
        component: () => import('@/views/writ/lawyer/page/addLawyer'),
        name: 'writ-addLawyer',
        meta: { title: '新建文书', activeMenu: '/writ/lawyerIndex' }
      },
      {
        path: 'lawyerDetails/:id(\\d+)',
        component: () => import('@/views/writ/lawyer/page/lawyerDetails'),
        name: 'writ-lawyerDetails',
        meta: { title: '文书量', activeMenu: '/writ/lawyerIndex' }
      },
      {
        path: 'trackedDetail/:id(\\d+)',
        component: () => import('@/views/writ/lawyer/page/trackedDetail'),
        name: 'writ-trackedDetail',
        meta: { title: '物流跟踪', activeMenu: '/writ/lawyerIndex' }
      },
      {
        path: 'aduitDetails/:id(\\d+)',
        component: () => import('@/views/writ/aduit/page/aduitBatchNum'),
        name: 'writ-aduitDetails',
        meta: { title: '文书量', activeMenu: '/writ/aduit' }
      },
    ]
  },
  {
    path: '/writ-template',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addTemplate',
        component: () => import('@/views/writ/template/page/addTemplate'),
        name: 'writ-addTemplate',
        meta: { title: '创建模板', activeMenu: '/writ/templateIndex' }
      },
      {
        path: 'editTemplate/:id(\\d+)',
        component: () => import('@/views/writ/template/page/addTemplate'),
        name: 'writ-editTemplate',
        meta: { title: '编辑模板', activeMenu: '/writ/templateIndex' }
      },
    ]
  },

  {
    path: '/lawyer',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addLawyer',
        component: () => import('@/views/mailServer/lawyer/page/addLawyer'),
        name: 'addLawyer',
        meta: { title: '新建发函', activeMenu: '/mailServer/lawyerIndex' }
      },
      {
        path: 'lawyerDetails/:id(\\d+)',
        component: () => import('@/views/mailServer/lawyer/page/lawyerDetails'),
        name: 'lawyerDetails',
        meta: { title: '函件量', activeMenu: '/mailServer/lawyerIndex' }
      },
      {
        path: 'trackedDetail/:id(\\d+)',
        component: () => import('@/views/mailServer/lawyer/page/trackedDetail'),
        name: 'trackedDetail',
        meta: { title: '物流跟踪', activeMenu: '/mailServer/lawyerIndex' }
      },
      {
        path: 'aduitDetails/:id(\\d+)',
        component: () => import('@/views/mailServer/aduit/page/aduitBatchNum'),
        name: 'aduitDetails',
        meta: { title: '函件量', activeMenu: '/mailServer/aduit' }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addTemplate',
        component: () => import('@/views/mailServer/template/page/addTemplate'),
        name: 'addTemplate',
        meta: { title: '创建模板', activeMenu: '/mailServer/templateIndex' }
      },
      {
        path: 'editTemplate/:id(\\d+)',
        component: () => import('@/views/mailServer/template/page/addTemplate'),
        name: 'editTemplate',
        meta: { title: '编辑模板', activeMenu: '/mailServer/templateIndex' }
      },
    ]
  },
  {
    path: '/assets/assetside-add',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addproduct/:ownerId*',
        component: () => import('@/views/assetsManagement/transferor/page/addProduct.vue'),
        name: 'AddProduct',
        meta: { title: '创建产品' }
      },
    ]
  },
  {
    path: '/assets/assetside-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'editProduct/:proId(\\d+)',
        component: () => import('@/views/assetsManagement/transferor/page/editProduct.vue'),
        name: 'EditProduct',
        meta: { title: '编辑产品' }
      }
    ]
  },
  {
    path: '/assets/asset-import',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'importCase/:productid(\\d+)',
        component: () => import('@/views/assetsManagement/caseImport/page/importCase.vue'),
        name: 'ImportCase',
        meta: { title: '导入案件' }
      }
    ]
  },
  {
    path: '/assets/oftentpl-handle',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'handeloftenTpl',
        component: () => import('@/views/assetsManagement/commonTemplate/page/oftenTpl.vue'),
        name: 'HandeloftenTpl',
        meta: { title: '模板添加/编辑', activeMenu: '/assets/oftentpl' }
      }
    ]
  },
  {
    path: '/callOut',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'resultsReport',
        component: () => import('@/views/appreciation/callOut/page/resultsReport.vue'),
        name: 'resultsReport',
        meta: { title: '查看结果报告',}
      }
    ]
  },
  {
    path: '/voiceTask',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'resultsReport',
        component: () => import('@/views/appreciation/voiceTask/page/resultsReport.vue'),
        name: 'voiceTaskResultsReport',
        meta: { title: '查看结果报告', }
      }
    ]
  },
  {
    path: '/ApplicationManagement/phoneClean-detail',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'results/:id(\\d+)',
        noCache: true,
        component: () => import('@/views/appreciation/phoneClean/details'),
        name: 'PhoneCleanDetail',
        meta: { title: '检测结果' }
      }
    ]
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
});

export default router;
