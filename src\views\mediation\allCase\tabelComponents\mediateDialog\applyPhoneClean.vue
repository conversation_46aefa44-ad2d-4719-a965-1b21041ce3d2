<template>
  <div>
    <el-dialog
      title="申请号码清洗"
      v-model="open"
      :before-close="cancel"
      width="650px"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="清洗对象" prop="ownerOnly">
          <el-radio-group v-model="form.ownerOnly" @change="CheckOwnerOnly">
            <el-radio :label="true">本人</el-radio>
            <el-radio :label="false">全部（本人，通讯录）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="清洗信息">
          <span style="font-weight: bold;"
            >案件量：{{ PhoneData.caseIds }}
            <span style="margin-left: 20px;"
              >清洗号码数：{{ PhoneData.phoneNumbers }}</span
            ></span
          >
        </el-form-item>
        <!-- <el-form-item label="清洗原因" prop="reason">
        <el-input
          type="textarea"
          rows="5"
          v-model="form.reason"
        ></el-input>
      </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submit"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="openFinish" append-to-body>
      <div style="text-align: center;">
        <img src="@/assets/images/import.gif" alt="" />
        <p>
          号码正在清洗中，请稍后在应用管理下的号码清洗报告页面中查看清洗进度……
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openFinish = false">OK,知道了!</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { applyPhoneClean, getPhoneCleanInfo } from "@/api/appreciation/phoneClean";
import { nextTick } from "vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
const open = ref(false);
const data = reactive({
  form: {
    ownerOnly: true,
    reason: undefined,
  },
  rules: {
    ownerOnly: [
      { required: true, message: "请选择清洗对象", trigger: "change" },
    ],
    // reason: [
    //   { required: true, message: "请输入清洗原因", trigger: "blur" },
    //   { min: 10, message: "清洗原因不能少于10个字", trigger: "blur" },
    // ],
  },
});
const { form, rules } = toRefs(data);
const PhoneData = ref({
  case_num: 0,
  phone_num: 0,
});
const loading = ref(false);

const openFinish = ref(false);

// 获取案件数量和清洗号码数量
function getPhoneData() {
  getPhoneCleanInfo(form.value)
   .then((res) => {
      PhoneData.value = res.data;
    })
   .catch((err) => {
      console.log(err);
    });
}

function CheckOwnerOnly() {
  nextTick(() => {
    getPhoneData();
  })
}

function reset() {
  proxy.resetForm("formRef");
  form.value = {
    ownerOnly: true,
    reason: undefined,
  };
}

function cancel() {
  reset();
  open.value = false;
}

function submit() {
  loading.value = true;
  applyPhoneClean(form.value)
    .then(() => {
      reset();
      open.value = false;
      openFinish.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
}

function opendialog(data) {
  Object.assign(form.value, data);
  open.value = true;
  getPhoneData()
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
