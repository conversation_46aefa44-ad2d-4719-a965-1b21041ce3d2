import request from '@/utils/request'

//获取机构树结构 getTeamTree
export function getTeamTree(params) {
  return request({
    url: '/team/DeptTreeType',
    method: 'get',
    params
  })
}

//获取调解案件列表
export function phoneMediationList(query) {
  return request({
    url: '/team/phoneMediationList',
    method: 'get',
    params: query
  })
}

// 债权统计 
export function selectMediationWithMoney(data) {
  return request({
    url: '/team/selectMediationWithMoney',
    method: 'post',
    data: data
  })
}