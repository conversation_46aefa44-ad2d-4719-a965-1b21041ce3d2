<template>
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
            <el-form-item label="部门名称" prop="deptName">
                <el-input v-model="form.deptName" placeholder="请输入部门名称" style="width: 240px" />
            </el-form-item>
            <el-form-item label="上级部门" prop="parentId">
                <tree-select v-model:value="form.parentId" :props="defaultProps" :options="deptOptionsCom"
                    placeholder="请选择上级部门"
                    :objMap="{ value: 'id', label: 'name', disabled: 'disabled', children: 'children' }"
                    style="width: 240px" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { addDept, updateDept } from '@/api/system/dept'
import { ref } from 'vue';
const { proxy } = getCurrentInstance();

const props = defineProps({
    deptOptions: {
        type: Array,
        default: []
    }
})

const deptOptionsCom = ref([])
const emit = defineEmits(['getDeptTree', 'getList']);

const title = ref('');
const open = ref(false);
const loading = ref(false);
const data = reactive({
    form: {},
    rules: {
        deptName: [{ required: true, message: '请输入部门名称', trigger: 'blur' }]
    }
})
const { form, rules } = toRefs(data);

//打开
function opendialog(data) {
    reset();
    if (data && data.hasOwnProperty('id')) {
        form.value = data;
        title.value = '修改部门'
        deptOptionsCom.value = machineDept(data.id, JSON.parse(JSON.stringify(props.deptOptions))) || []
    } else {
        form.value.parentId = data.parentId ? data.parentId : undefined;
        deptOptionsCom.value = props.deptOptions;
        title.value = '新建部门'
    }
    open.value = true;
}

function machineDept(deptId, arr) {
    const newArr = []
    for (let i = 0; i < arr.length; i++) {
        const id = arr[i].id.split(':')[1]
        if (id != deptId) {
            newArr.push(arr[i])
            if (Array.isArray(arr[i].children) && arr[i].children.length > 0) {
                arr[i].children = machineDept(deptId, arr[i].children)
            }
        }
    }

    return newArr
}

//提交
function submitForm() {
    loading.value = true;
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            let req = JSON.parse(JSON.stringify(form.value));
            let keys = form.value?.parentId?.split(":") ?? [];
            req.parentId = keys[0] == 'Create' ? undefined : keys[1];
            if (form.value.id) {
                updateDept(req).then(res => {
                    proxy.$modal.msgSuccess('修改成功！');
                    cancel();
                    emit('getDeptTree')
                    emit('getList')
                }).finally(() => {
                    loading.value = false;
                })
            } else {
                addDept(req).then(res => {
                    proxy.$modal.msgSuccess('新建成功！');
                    cancel();
                    emit('getDeptTree')
                }).finally(() => {
                    loading.value = false;
                })
            }
        } else {
            loading.value = false;
        }
    })
}

//重置表单
function reset() {
    form.value = {
        deptName: undefined,
        parentId: undefined
    }
    proxy.resetForm("formRef");
}

//取消
function cancel() {
    reset();
    open.value = false
}

defineExpose({
    opendialog
})
</script>

<style scoped></style>