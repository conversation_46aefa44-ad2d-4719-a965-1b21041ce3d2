<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
      label-width="100px"
    >
      <el-form-item label="案件ID" prop="caseId">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="被告">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入被告"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="判决时间">
        <el-date-picker
          v-model="queryParams.judgeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="判决法院">
        <el-select
          v-model="queryParams.trialCourt"
          placeholder="请选择判决法院"
          style="width: 240px"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :value="item.code"
            :label="item.info"
          >
            {{ item.info }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 240px">
          <el-input
            type="text"
            v-model="queryParams.amount1"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
          <span>-</span>
          <el-input
            type="text"
            v-model="queryParams.amount2"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <!-- Search End -->
    <div class="operation-revealing-area">
      <el-button
        v-if="checkPermi([setPermiss('download')])"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleDownload()"
        >批量导出</el-button
      >
      <el-button
        v-if="
          checkPermi([setPermiss('add')]) && ['1', '2', '3'].includes(route.meta.query)
        "
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('addJudicationRef')"
        >新增判决</el-button
      >
      <!-- <el-button
        v-if="checkPermi([setPermiss('keep')])"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('applyKeepRef')"
        >申请保全</el-button
      > -->
      <el-button
        v-if="checkPermi([setPermiss('transfer')])"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('transferCaseRef')"
        >案件流转</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('createWirt')])"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('batchImportWritRef')"
        >批量生成文书</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('register')]) && route.meta.query == 1"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('importRegisterRef')"
        >批量登记</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('close')]) && route.meta.query == 5"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('batchCloseRef')"
        >批量结案</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('execute')]) && route.meta.query == 6"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('applyRecordRef')"
        >申请执行立案</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('sendNote')])"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        @click="handleOpenDialog('sendMessageRef')"
        >发送短信</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <selectedAll
      v-model:allQuery="allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      class="mt10 mb10"
    >
      <template #content>
        <div class="selected-all-content ml10">
          <span>案件数量：</span>
          <i class="danger">{{ statistics.caseNum || 0 }}</i>
          <span class="ml10">标的额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.totalMoney || 0) }}
          </i>
          <span>执行金额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.principal || 0) }}
          </i>
        </div>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        align="center"
        prop="caseId"
        label="案件ID"
        :width="110"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <GreenCircle v-if="row.isFreeze == 1" :data="row" />
            <el-button
              :disable="!checkPermi([setPermiss('detail')])"
              type="text"
              @click="toDetails(row, $index)"
            >
              {{ row.caseId }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        align="center"
        prop="clientName"
        label="被告"
        :width="100"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[2].visible"
        align="center"
        prop="clientPhone"
        label="手机号码"
        :width="110"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.clientPhone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
            <workPhoneVue :phoneNumber="row.clientPhone" :caseId="row.caseId" :borrower="row.clientName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[3].visible"
        align="center"
        prop="remainingDue"
        label="标的额"
        :width="110"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[4].visible"
        align="center"
        prop="clientIdNum"
        label="身份证号码"
        :width="180"
      />
      <el-table-column
        v-if="columns[5].visible"
        align="center"
        prop="clientCensusRegister"
        label="户籍地"
        :min-width="160"
      />
      <el-table-column
        v-if="columns[6].visible && route.meta.query == 5"
        align="center"
        prop="lastStatus"
        label="结案状态"
        :min-width="110"
      />
      <el-table-column
        v-if="columns[7].visible && route.meta.query == 5"
        align="center"
        prop="undertakingLawyer"
        label="承办律师"
        :min-width="110"
      />
      <el-table-column
        v-if="columns[8].visible"
        align="center"
        prop="disposeStage"
        label="判决状态"
        :min-width="110"
      />
      <el-table-column
        v-if="columns[9].visible && route.meta.query != 6"
        align="center"
        prop="judgeTime"
        label="判决时间"
        :width="160"
      />
      <el-table-column
        v-if="columns[10].visible && route.meta.query != 6"
        align="center"
        prop="judgeSum"
        label="判决金额"
        :width="110"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.judgeSum) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[11].visible && route.meta.query != 6"
        align="center"
        prop="trialCourt"
        label="法院"
        :min-width="110"
      />
      <el-table-column
        v-if="columns[12].visible && route.meta.query != 6"
        align="center"
        prop="judgeContent"
        label="判决描述"
        :width="180"
      >
        <template #default="{ row }">
          <Tooltip :content="row.judgeContent" :length="15" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[13].visible && route.meta.query == 6"
        align="center"
        prop="filingTime"
        label="立案时间"
        :width="110"
      />
      <el-table-column
        v-if="columns[14].visible && route.meta.query == 6"
        align="center"
        prop="executeNo"
        label="执行案号"
        :width="110"
      />
      <el-table-column
        v-if="columns[15].visible && route.meta.query == 6"
        align="center"
        prop="executiveCourt"
        label="执行法院"
        :min-width="110"
      />
      <el-table-column
        v-if="columns[16].visible && route.meta.query == 6"
        align="center"
        prop="contractor"
        label="承办员"
        :width="110"
      />
      <el-table-column
        v-if="columns[17].visible && route.meta.query == 6"
        align="center"
        prop="clerk"
        label="书记员"
        :width="110"
      />
      <el-table-column
        v-if="columns[18].visible"
        align="center"
        prop="updateBy"
        label="跟进人员"
        :width="110"
      />
      <el-table-column
        v-if="columns[19].visible"
        align="center"
        prop="updateTime"
        label="最近一次跟进时间"
        :width="160"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <addJudication :getList="getList" ref="addJudicationRef" />
    <applyRecord :getList="getList" ref="applyRecordRef" />
    <batchClose :getList="getList" ref="batchCloseRef" />
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <transferCase :getList="getList" ref="transferCaseRef" />
    <batchImportWrit :getList="getList" ref="batchImportWritRef" />
    <importRegister :getList="getList" ref="importRegisterRef" />
    <!-- 发送短信 -->
    <sendMessage :getList="getList" ref="sendMessageRef" />
  </div>
</template>

<script setup name="JudgmentResult">
import { checkPermi } from "@/utils/permission";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import applyKeep from "@/views/mediation/dialog/applyKeep";
import transferCase from "@/views/mediation/dialog/transferCase";
import addJudication from "./dialog/addJudication";
import applyRecord from "./dialog/applyRecord";
import batchClose from "@/views/mediation/dialog/batchClose";
import importRegister from "@/views/mediation/dialog/importRegister";
import batchImportWrit from "@/views/mediation/dialog/batchImportWrit";
import { formatParams } from "@/utils/common";
import { getCourtOptions } from "@/api/common/common";
import { pageTypeEnum } from "@/utils/enum";
import { getJudgeList } from "@/api/mediation/judgmentResult";
import { selectJudgeWithMoney } from "@/api/team/sentenceAndResult";
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const store = useStore();
const pageType = "judgmentResult";
const stageObj = {
  1: "审理判决",
  2: "判决审理",
  3: "诉讼撤案",
  4: "停止诉讼",
  5: "诉讼结案",
  6: "申请执行立案",
};
const loading = ref(false);
const allQuery = ref(false);
const judgmentStatus = ref([
  { id: 1, name: "已提交" },
  { id: 2, name: "审核中" },
]);
const total = ref(0);
const selectedArr = ref([]);
const dataList = ref([]);
const caseIds = ref([]);
const single = ref(false);
const showSearch = ref(false);
const statistics = ref({});
const courtOptions = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  condition: false,
});
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "被告", visible: true },
  { key: 2, label: "手机号码", visible: true },
  { key: 3, label: "标的额", visible: true },
  { key: 4, label: "身份证号码", visible: true },
  { key: 5, label: "户籍地", visible: true },
  { key: 6, label: "结案状态", visible: true },
  { key: 7, label: "承办律师", visible: true },
  { key: 8, label: "判决状态", visible: true },
  { key: 9, label: "判决时间", visible: true },
  { key: 10, label: "判决金额", visible: true },
  { key: 11, label: "法院", visible: true },
  { key: 12, label: "判决描述", visible: true },
  { key: 13, label: "立案时间", visible: true },
  { key: 14, label: "执行案号", visible: true },
  { key: 15, label: "执行法院", visible: true },
  { key: 16, label: "承办员", visible: true },
  { key: 17, label: "书记员", visible: true },
  { key: 18, label: "跟进人员", visible: true },
  { key: 19, label: "最近一次跟进时间", visible: true },
]);
const rangFields = ["judgeTime"]; //区间字段
getList();
function getList() {
  loading.value = true;
  selectedArr.value = [];
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.condition = allQuery.value;
  getJudgeList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
//查询
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  getList();
}

// 导出
function handleDownload() {
  const reqForm = getReqParams();
  proxy.downloadforjson(
    "/team/exportWithJudgeCase",
    reqForm,
    `判决与结果_${+new Date()}.xlsx`
  );
}

function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const newAllQuery = row ? false : allQuery.value;
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds };
  const data = {
    query,
    ...row,
    caseIds,
    allQuery: newAllQuery,
    pageType,
    isBatchSend: 1,
    caseId: row?.caseId,
  };
  data.condition = row ? false : allQuery.value;
  data.oneStatus = "判决与结果";
  proxy.$refs[refName].openDialog(data);
}
//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      disposeStage: stageObj[route.meta.query],
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: stageObj[route.meta.query] };
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}
//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}
// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery);
  reqForm.condition = allQuery.value;
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.sign = pageTypeEnum[pageType];
  return reqForm;
}
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && caseIds.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      selectJudgeWithMoney(getReqParams())
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.principal,
          };
        })
        .finally(() => reslove());
    });
  });
}

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { amount1, amount2 } = queryParams.value;
  // 检测输入是否是数字
  if (amount1 && !Number.isFinite(Number(amount1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount1 = undefined;
  }
  if (amount2 && !Number.isFinite(Number(amount2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
  if (!amount1 || !amount2) return;

  const principal1 = parseFloat(amount1);
  const principal2 = parseFloat(amount2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
};
//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

// 设置权限符
function setPermiss(val) {
  const permissBtn = {
    1: "judgmentResult:Judication:",
    2: "judgmentResult:hear:",
    3: "judgmentResult:withdrawalAction:",
    4: "judgmentResult:abatementAction:",
    5: "judgmentResult:caseClosed:",
    6: "judgmentResult:putOnRecord:",
  };
  return `${permissBtn[route.meta.query]}${val}`;
}
const htrxCall = computed(() => store.getters.htrxCall);
getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}
watch(
  () => route,
  () => {
    resetQuery();
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h32 {
  height: 32px !important;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-table .el-table__cell.zIndex4) {
  z-index: 4 !important;
}

:deep(.el-table__body-wrapper .el-scrollbar__bar) {
  z-index: 4;
}

.send-icon {
  position: relative;
  top: 2px;
  cursor: pointer;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
