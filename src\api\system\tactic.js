import request from '@/utils/request'


//获取分案策略列表
export function strategyList(query) {
    return request({
        url: '/caseManage/case/strategy/list',
        method: 'get',
        params: query
    })
}

//查看策略详情
export function detailStrateg(id) {
    return request({
        url: '/caseManage/case/strategy/selectById/'+id,
        method: 'get'
    })
}

//新增分案策略
export function addStrategy(data) {
    return request({
        url: '/caseManage/case/strategy/add',
        method: 'post',
        data: data
    })
}

//编辑分案策略
export function editStrategy(data) {
    return request({
        url: '/caseManage/case/strategy/edit',
        method: 'post',
        data: data
    })
}

//删除分案策略
export function deleteStrategy(id) {
    const data = {
        id: id
    }
    return request({
        url: '/caseManage/case/strategy/deleted',
        method: 'post',
        data: data
    })
}

//获取省份
export function getProvinces() {
    return request({
        url: '/caseManage/case/strategy/getProvinces',
        method: 'get'
    })
}

//获取数据规则字段
export function getFields() {
    return request({
        url: '/caseManage/case/strategy/getFields',
        method: 'get'
    })
}

//获取数据规则-操作符
export function getOperators() {
    return request({
        url: '/caseManage/case/strategy/getOperators',
        method: 'get'
    })
}


/**
 * 减免规则
 * */ 

//减免方式
export function getReductionModes(query) {
    return request({
        url: '/caseManage/setup/reduction/getReductionModes',
        method: 'get'
    })
}

//减免规则列表
export function reducetionList(query) {
    return request({
        url: '/caseManage/setup/reduction/list',
        method: 'get',
        params: query
    })
}

//自动减免公式下拉
export function reductionOptions() {
    return request({
        url: '/caseManage/setup/reduction/getReductionOptions',
        method: 'get',
    })
}

//添加减免规则
export function addReducetion(data) {
    return request({
        url: '/caseManage/setup/reduction/add',
        method: 'post',
        data: data
    })
}

//编辑减免规则
export function editReducetion(data) {
    return request({
        url: '/caseManage/setup/reduction/edit',
        method: 'post',
        data: data
    })
}

//删除减免规则
export function deleteReducetion(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/setup/reduction/deleted',
        method: 'post',
        data: data
    })
}

/**
 * 债权总金额策略
 * */

//债权总金额策略列表
export function getentrustList(query) {
    return request({
        url: '/caseManage/setup/entrustMoney/list',
        method: 'get',
        params: query
    })
}

//债权总金额公式下拉
export function entrustMoneyOptions() {
    return request({
        url: '/caseManage/setup/entrustMoney/getEntrustMoneyOptions',
        method: 'get',
    })
}

//添加债权总金额策略
export function addEntrust(data) {
    return request({
        url: '/caseManage/setup/entrustMoney/add',
        method: 'post',
        data: data
    })
}

//编辑债权总金额策略
export function editEntrust(data) {
    return request({
        url: '/caseManage/setup/entrustMoney/edit',
        method: 'post',
        data: data
    })
}

//删除债权总金额策略
export function deleteEntrust(id) {
    let data = {
        id: id
    }
    return request({
        url: '/caseManage/setup/entrustMoney/deleted',
        method: 'post',
        data: data
    })
}