<template>
  <div>
    <div class="list-wrap" v-loading="loading">
      <div class="list-item" v-for="item in resDataList" :key="item.recording">
        <div class="mb10">
          沟通时间：{{ item.callTime }}&nbsp;&nbsp;调解员：{{ item.odvName }}
        </div>
        <div class="content">
          <!-- @stopCheck="stopCheck(scope.$index)" -->
          <!-- :durationOrigin="scope.row.agentDuration" -->
          <musicPlayer
            :ref="`audioRef${item.recording}`"
            :audioSrc="getaudioSrc(item)"
            :isSpeed="true"
          />
        </div>
      </div>
      <div
        v-show="dataList.length === 0"
        style="
          text-align: center;
          font-weight: bold;
          color: #888888;
          line-height: 100px;
        "
      >
        无录音文件
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        layout="prev, pager, next"
      />

      <div style="text-align: center">
        <el-button
          v-show="dataList.length > 1"
          type="text"
          @click="isExpend = !isExpend"
          >{{ isExpend ? "收缩列表" : "展开查看历次沟通情况" }}</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { selectListByNetworkName } from "@/api/mediation/allCaseDetail";
const props = defineProps({
  networkName: {
    type: String,
    required: true,
  },
  names: {
    type: Array,
    required: true,
    default: () => [],
  },
});
const caseId = inject("caseId");
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  caseId: caseId,
  networkName: props.networkName,
});
const total = ref(0);
const dataList = ref([]);
const loading = ref(false);
const isExpend = ref(false);
const resDataList = computed(() => {
  if (isExpend.value) {
    return dataList.value;
  } else {
    return dataList.value.length > 0 ? [dataList.value[0]] : [];
  }
});
function getList() {
  loading.value = true;
  selectListByNetworkName(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

function getaudioSrc(item) {
  return import.meta.env.VITE_APP_RECORD_URL + item.recording;
}

// 监听networkName变化，重新请求列表
watch(
  () => props.networkName,
  (newval) => {
    const name_arr = props.names.map((item) => item.networkName);
    if (name_arr.includes(newval)) {
      queryParams.value.networkName = newval;
      getList();
    }
  }
);
</script>

<style lang="scss" scoped>
.list-wrap {
  min-height: 200px;
}
.list-item {
  background-color: #e8e8e8;
  padding: 15px;
  border-radius: 4px;
  &:not(:last-child) {
    margin-bottom: 10px;
  }
}
.content {
  background-color: #ffffff;
  padding: 10px 20px;
  border-radius: 4px;
}
::v-deep(.pagination-container) {
  position: relative;
  margin-top: 0;
}
</style>
