import request from '@/utils/request';

//诉讼保全列表查询
export function selectFreezeList(query) {
    return request({
        url: '/team/selectFreezeList',
        method: 'get',
        params: query
    })
}

//诉讼保全统计剩余应还债权金额 计算
export function selectFreezeWithMoney(data) {
    return request({
        url: '/team/selectFreezeWithMoney',
        method: 'post',
        data
    })
}

//团队诉讼保全
export function deptTreeWithSaveStage(query) {
    return request({
        url: '/team/DeptTreeWithSaveStage',
        method: 'get',
        params: query
    })
}