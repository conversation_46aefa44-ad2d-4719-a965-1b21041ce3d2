<template>
    <div>
        <div class="joint-count" v-if="count > 0">
            <span>共债欠款总额：{{ numFilter(jointData?.syYhArrearsTotal) }}，</span>
            <span>欠款总本金：{{ numFilter(jointData?.syYhPrincipal) }}，</span>
            <span>欠款总利息：{{ numFilter(jointData?.syYhInterest) }}，</span>
            <span>欠款总罚息：{{ numFilter(jointData?.syDefaultInterest) }}，</span>
            <span>欠款总费用：{{ numFilter(jointData?.syYhFees) }}</span>
        </div>
        <el-table :data="dataList" max-height="745">
            <el-table-column label="案件ID" prop="caseId" key="caseId" align="center" min-width="100px">
                <template #default="{ row }">
                    <span v-if="route.params.caseId == row.caseId">{{ row.caseId }}(本案)</span>
                    <el-button v-else type="text" @click="toOtherCase(row.caseId)">
                        {{ row.caseId }}
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="借据号" prop="contractNo" key="contractNo" align="center" />
            <el-table-column label="姓名" prop="clientName" key="clientName" align="center" />
            <el-table-column label="处置人员" prop="odvName" key="odvName" align="center" />
            <el-table-column label="产品类型" prop="productName" key="productName" align="center" />
            <el-table-column label="贷款金额（元）" prop="loanMoney" key="loanMoney" width="120px" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.loanMoney) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="逾期天数【账期】" prop="overdueDays" key="overdueDays" width="130px" align="center">
                <template #default="scope">
                    {{
                        `${scope.row.overdueStarts > 0 ? scope.row.overdueStarts : "--"}
                    【${scope.row.accountPeriod || "--"}】`
                    }}
                </template>
            </el-table-column>
            <el-table-column label="剩余欠款本金" prop="syYhPrincipal" key="syYhPrincipal" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhPrincipal) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余欠款利息" prop="syYhInterest" key="syYhInterest" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.syYhInterest) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="剩余欠款总额" prop="remainingDue" key="remainingDue" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.remainingDue) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="债权机构" prop="loanInstitution" key="loanInstitution" align="center" />
            <el-table-column label="贷款期数 | 已还期数 | 待还期数" prop="loanPeriods" key="loanPeriods" align="center"
                width="205px" :show-overflow-tooltip="true">
                <template #default="scope">
                    {{
                        `${scope.row.loanPeriods || 0} | ${scope.row.alreadyPeriods || 0} | ${scope.row.notPeriods || 0
                        }`
                    }}
                </template>
            </el-table-column>
            <el-table-column label="案件地区" prop="caseRegion" key="caseRegion" align="center" />
            <el-table-column label="债权总金额" width="120" prop="entrustMoney" key="entrustMoney" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.entrustMoney) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="贷款本金" prop="loanPrincipal" key="loanPrincipal" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.loanPrincipal) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="债权利息" prop="interestMoney" key="interestMoney" align="center">
                <template #default="{ row }">
                    <span>{{ numFilter(row.interestMoney) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="逾期开始时间" prop="overdueStart" key="overdueStart" align="center"
                :show-overflow-tooltip="true" width="110px" />
        </el-table>
        <pagination v-show="total > 10" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>
<script setup>
import { selectQueryJointDebt, selectQueryAmountMoney, } from "@/api/caseDetail/detail";
defineProps({ count: { type: [String, Number] } })
const route = useRoute()
const router = useRouter()
const dataList = ref([]);
const total = ref(0)
const data = reactive({
    queryParams: {
        caseId: route.params.caseId,
        pageNum: 1,
        pageSize: 10,
    }
})
const { queryParams } = toRefs(data)
//共债总数
const jointData = ref({
    syYhArrearsTotal: 0,
    syYhPrincipal: 0,
    syYhInterest: 0,
    syYhFees: 0,
    syDefaultInterest: 0,
});
//获取欠款总额
getCount()
function getCount() {
    let req = { caseId: route.params.caseId };
    selectQueryAmountMoney(req).then((res) => {
        jointData.value = res.data;
    })
}
//跳转其他案件详情 /collection/mycase-detail/caseDetails/${row.caseId}
function toOtherCase(caseId) {
    let searchInfo = { type: "details" };
    localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
    router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
}

//获取共债信息
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    selectQueryJointDebt(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}

</script>
<style lang="scss" scoped>
.joint-count {
    padding: 20px 25px;
    color: #ff1e10;
    font-size: 15px;
}

:deep(.el-pagination) {
    position: unset;
    float: right;
    margin-top: -20px;
}
</style>