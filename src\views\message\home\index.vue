<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container mb20" style="color: #888888">
          <el-icon class="mr5">
            <HomeFilled color="#888888" />
          </el-icon>
          站内消息
        </div>
        <div class="nav-box">
          <span :class="{ active: isActive === index }" v-for="(item, index) in navList" :key="item.id"
            @click="navChange(index)">{{ item.label }}</span>
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-row class="mb10 mt10">
          <el-button plain v-hasPermi="['message:home:read']" @click="maskAsRead()"
            :disabled="loading">标记为已读</el-button>
          <el-button plain v-hasPermi="['message:home:delete']" @click="remove()" :disabled="loading">删除消息</el-button>
        </el-row>
        <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
          <el-tab-pane label="全部" name="all"> </el-tab-pane>
          <el-tab-pane label="业务进度提醒" name="101"> </el-tab-pane>
          <el-tab-pane label="审批提醒" name="102"> </el-tab-pane>
          <el-tab-pane label="工单反馈" name="103"> </el-tab-pane>
          <el-tab-pane label="系统消息" name="0"> </el-tab-pane>
        </el-tabs>
        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="标题内容" align="center">
            <template #default="scope">
              <el-tooltip placement="top">
                <template #content>
                  <p style="max-width:300px;">{{ scope.row.messageContent }}</p>
                </template>
                <div>
                  <i v-if="scope.row.read != 1" class="reTip"></i>
                  <span>{{ scope.row.messageContent?.length > 15 ? `${scope.row.messageContent?.substring(0, 15)}...` : scope.row.messageContent }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" align="center" key="createTime" prop="createTime" />
          <el-table-column label="类型" align="center" key="messageType" prop="messageType" :formatter="messageTypeFor"
            :show-overflow-tooltip="true" />
          <el-table-column fixed="right" width="250" label="操作">
            <template #default="scope">
              <el-button v-if="scope.row.delFlag !== 1" type="text" v-hasPermi="['message:home:detail']"
                @click="detailsMessage(scope.row)">详情</el-button>
              <span v-if="scope.row.delFlag == 1">消息已删除</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>
<script setup name="HomeMessage">
import { getMessageList, markRead, deteleMessage } from "@/api/message/home";
//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
//表格配置数据
const loading = ref(false);
const total = ref(0);
const activeTab = ref("all");
const multipleTableRef = ref();
//id集合
const messageIds = ref([]);
//导航配置
const isActive = ref(0);
//导航配置
const navList = ref([
  { id: 0, label: "全部消息" },
  { id: 1, label: "未读消息" },
  { id: 2, label: "已读消息" },
]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    read: undefined,
    messageType: undefined,
  },
});
const { queryParams } = toRefs(data);
//表格数据
const dataList = ref([]);

//获取数据
function getList() {
  queryParams.value.messageType = activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.read = isActive.value == "0" ? undefined : isActive.value - 1;
  loading.value = true;
  getMessageList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//列表切换
function tabChange() {
  getList();
}

//导航切换
function navChange(index) {
  isActive.value = index;
  getList();
}

//案件选择
function handleSelectionChange(selection) {
  messageIds.value = selection.map((item) => item.id);
}

//监听选择发生改变
watch(dataList, (newval, preval) => {
  multipleTableRef.value.toggleAllSelection();
});

//标记成已读
function maskAsRead() {
  proxy.$modal
    .confirm(`是否批量将选择的消息标记为已读？已读的消息不再展示红点`)
    .then(() => {
      loading.value = true;
      markRead(messageIds.value)
        .then((res) => {
          getList();
        })
        .catch(() => {
          loading.value = false;
        });
    }).catch(() => { loading.value = false; })
}

//删除消息
function remove() {
  proxy.$modal
    .confirm(`批量删除选中的信息？是否继续？`)
    .then(() => {
      loading.value = true;
      deteleMessage(messageIds.value)
        .then((res) => {
          getList();
        })
        .catch(() => {
          loading.value = false;
        });
    }).catch(() => { loading.value = false; })
}

//案件状态 0-未分配 1-已分配 2-停催 3-留案 4-退案 5-回收案件 6-案件结清
function messageTypeFor(row) {
  if (row.messageType == "101") {
    return '业务进度提醒' + '-' + row.messageTitle
  } else if (row.messageType == "102") {
    return '审批提醒' + '-' + row.messageTitle
  } else if (row.messageType == "103") {
    return '工单反馈' + '-' + row.messageTitle
  } else {
    return '系统信息'
  }
}

//消息详情
function detailsMessage(row) {
  const query = { path: route.path }
  router.push({ path: `/message/home/<USER>/${row.id}`, query });
}
</script>
<style lang="scss" scoped>
.nav-box {
  margin: 20px 0px;
  width: 100%;

  span {
    display: block;
    padding: 10px 20px;
    font-size: 14px;
    color: #666;
  }

  span:hover {
    background-color: rgb(230 247 255);
  }

  .active {
    border-right: 2px solid rgb(24 144 255);
    background-color: #e9f6fe;
  }
}

.red-point {
  display: inline-block;
  font-size: 48px;
  vertical-align: top;
  line-height: 20px;
  color: red;
  border-radius: 50%;
}

.reTip {
  display: inline-block;
  background: #f00;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: relative;
  z-index: 4;
  margin-right: 5px;
}
</style>
