<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" :label-width="130"
            class="form-content" :class="{ 'h-auto': showSearch }">
            <el-form-item label="文件名称" prop="originalFileName">
                <el-input v-model="queryParams.originalFileName" placeholder="请输入" clearable style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="联系电话" prop="casePhone">
                <el-input v-model="queryParams.casePhone" placeholder="请输入" clearable style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="姓名" prop="caseName">
                <el-input v-model="queryParams.caseName" placeholder="请输入" clearable style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="所属坐席" prop="employeeIdAndName">
                <el-input v-model="queryParams.employeeIdAndName" placeholder="请输入" clearable style="width: 240px" @keyup.enter="antiShake(handleQuery)" />
            </el-form-item>
            <el-form-item label="备注时间" prop="noteTime">
                <el-date-picker v-model="queryParams.noteTime" style="width: 240px" value-format="YYYY-MM-DD"
                type="daterange" range-separator="-" start-placeholder="开始日期"
                end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item label="导入时间" prop="createTime">
                <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="YYYY-MM-DD"
                type="daterange" range-separator="-" start-placeholder="开始日期"
                end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
        </el-form>

        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>

        <el-row class="mb10 mt10" style="min-height: 26px">
            <el-button plain :disabled="single" @click="exportCustomDetailsHandle" v-if="checkPermi(['appreciation:customerList:exportCustomDetails'])">导出客户资料</el-button>
            <el-button plain :disabled="single" @click="deleteCustomDetailsHandle" v-if="checkPermi(['appreciation:customerList:deleteCustomDetails'])">批量删除</el-button>
        </el-row>

        <el-row class="mb10">
            <el-checkbox-group v-model="checkedType" @change="checkedTypeChange" :disabled="dataList.length === 0">
                <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
                :indeterminate="item.indeterminate" />
            </el-checkbox-group>
        </el-row>

        <el-table v-loading="loading" :data="dataList" ref="multipleTableRef" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
            <el-table-column label="编号" align="center" key="id" prop="id"  />
            <el-table-column label="文件名称" align="center" prop="originalFileName" :min-width="100">
              <template #default="{ row }">
                <Tooltip :content="row.originalFileName" :length="8" />
              </template>
            </el-table-column>
            <el-table-column label="联系电话" align="center" prop="casePhone" :width="160">
                <template #default="{ row }">
                    <span>{{ row.casePhone }}</span>
                    <callBarVue class="ml5" :clickCallOut="true" :phoneNumber="row.casePhone"
                      :key="htrxCall" />
                    <!-- <svg-icon icon-class="phone-call" :style="'font-size:' + size + 'px'" /> -->
                </template>
            </el-table-column>
            <el-table-column label="所属坐席" align="center">
                <template #default="{ row }">
                    {{ row.userNo }}{{ row.memberName ? "(" + row.memberName + ")" : '' }} 
                </template>
            </el-table-column>
            <el-table-column label="姓名" align="center" prop="caseName"  />
            <el-table-column label="产品名称" align="center" prop="productName" />
            <el-table-column label="债权总金额" align="center" prop="entrustMoney">
              <template #default="{ row }">
                  {{ numFilter(row.entrustMoney) }} 
              </template>
            </el-table-column>
            <el-table-column label="剩余应还债权金额" align="center" prop="remainingDueMoney">
              <template #default="{ row }">
                  {{ numFilter(row.remainingDueMoney) }} 
              </template>
            </el-table-column>
            <el-table-column label="剩余应还本金" align="center" prop="remainMoney">
              <template #default="{ row }">
                  {{ numFilter(row.remainMoney) }} 
              </template>
            </el-table-column>
            <el-table-column label="贷款时间" align="center" prop="loanTime" :min-width="100"/>
            <el-table-column label="逾期时间" align="center" prop="overTime" :min-width="100"/>
            <el-table-column label="备注" align="center" prop="remark" :min-width="100">
              <template #default="{ row }">
                <Tooltip :content="row.remark" :length="8" />
              </template>
            </el-table-column>
            <el-table-column label="备注时间" align="center" prop="noteTime" :min-width="100"/>
            <el-table-column label="备注人" align="center" prop="noteBy" />
            <el-table-column label="导入时间" align="center" prop="createTime" :min-width="100"/>
            <el-table-column fixed="right" width="250" label="操作">
                <template #default="{ row }">
                    <el-button type="text" @click="deleteCustomDetailsHandle(row)" v-if="checkPermi(['appreciation:customerList:deleteCustomDetails'])">删除</el-button>
                    <el-button type="text" @click="openEditCustomerListDialog(row)" v-if="checkPermi(['appreciation:customerList:updateCustomDetails'])">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <editCustomerList ref="editCustomerListRef" @editFinish="getList" />
    </div>
</template>
  
<script setup>
import editCustomerList from "@/views/appreciation/dialog/editCustomerList.vue"
import { listCustomDetails, exportCustomDetails, deleteCustomDetails } from "@/api/appreciation/customerList";
import callBarVue from '@/components/callBar/index'
import { onMounted } from "vue";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const ids = ref([]);
const selectedArr = ref([]);

const dataList = ref([]);
const checkedType = ref([]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

// 是否搜索结果全选
const condition = ref(false);

//全选类型
function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    proxy.$refs["multipleTableRef"].clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else {
    dataList.value.length > 0 &&
      dataList.value.map((item) => {
        proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
      });
  }
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    condition.value = true;
  } else {
    condition.value = false;
  }
}

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        originalFileName: undefined,
        casePhone: undefined,
        caseName: undefined,
        employeeIdAndName: undefined,
        noteTime: undefined,
        createTime: undefined,
    },
});
//需要拆分的字段
const { queryParams } = toRefs(data);

//勾选信息
const single = ref(true);

// onMounted(() => {
//     console.log("11111111111")
// })

const rangFields = ["noteTime","createTime"];

const total = ref(undefined);

const loading = ref(false);
//获取列表数据
const getList = () => {
    loading.value = true;
    const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
    if (reqForm.noteTime1 && reqForm.noteTime2) {
      reqForm.noteTime1 = `${reqForm.noteTime1} 00:00:00`;
      reqForm.noteTime2 = `${reqForm.noteTime2} 23:59:59`;
    }
    if (reqForm.createTime1 && reqForm.createTime2) {
      reqForm.createTime1 = `${reqForm.createTime1} 00:00:00`;
      reqForm.createTime2 = `${reqForm.createTime2} 23:59:59`;
    }
    listCustomDetails(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    }).finally(() => loading.value = false);
}
getList();

const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
}

const exportCustomDetailsHandle = () => {
    let query = proxy.addFieldsRange(queryParams.value, rangFields);
    if (query.noteTime1 && query.noteTime2) {
      query.noteTime1 = `${query.noteTime1} 00:00:00`;
      query.noteTime2 = `${query.noteTime2} 23:59:59`;
    }
    if (query.createTime1 && query.createTime2) {
      query.createTime1 = `${query.createTime1} 00:00:00`;
      query.createTime2 = `${query.createTime2} 23:59:59`;
    }
    delete query.pageNum;
    delete query.pageSize;
    if (condition.value) {
        query.condition = condition.value;
    } else {
        query.condition = condition.value;
        query.ids = ids.value;
    }
    // proxy.$modal.loading("正在导出数据，请稍候...");
    // exportCustomDetails(query).then(res => {
    //   // proxy.$modal.closeLoading();
    //   console.log(res);
    //   proxy.$modal.exportTip()
    // });

    proxy.downloadforjson(
      "/callCustom/exportCustomDetails",
      query,
      `客户资料_${new Date().getTime()}.xlsx`,
      { gateway: 'cis'}
    );


}

const deleteCustomDetailsHandle = (row) => {
    let query = proxy.addFieldsRange(queryParams.value, rangFields);
    if (query.noteTime1 && query.noteTime2) {
      query.noteTime1 = `${query.noteTime1} 00:00:00`;
      query.noteTime2 = `${query.noteTime2} 23:59:59`;
    }
    if (query.createTime1 && query.createTime2) {
      query.createTime1 = `${query.createTime1} 00:00:00`;
      query.createTime2 = `${query.createTime2} 23:59:59`;
    }
    if (row && row.id) {
        query.condition = condition.value;
        query.ids = [row.id];
    } else {
        if (condition.value) {
            query.condition = condition.value;
        } else {
            query.condition = condition.value;
            query.ids = ids.value;
        }
    }
    proxy.$modal
            .confirm("删除后数据将丢失，确定要删除该信息吗？")
            .then(function () {
                deleteCustomDetails(query).then((res) => {
                    proxy.$modal.msgSuccess(res.msg);
                    getList();
                });
            })
            .catch((err) => { });
}


/** 多选框选中数据 */
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  if (checkedType.value[0] != "搜索结果全选") {
    if (selectedArr.value.length == dataList.value?.length) {
      checkedType.value[0] = "本页选中";
    } else {
      checkedType.value = [];
    }
  }
}

watch([selectedArr, checkedType], (newval) => {
  nextTick(async () => {
    let type = newval[1][0] || checkedType.value[0];
    let select = newval[0];
    let pageIds = select.map((item) => item.id).filter((item) => item);
    //添加选择的数据
    let caseTableIds = dataList.value.map((item) => item.id);
    // console.log(select, "11111111111111111");
    ids.value = ids.value.filter((item) => !caseTableIds.includes(item));
    ids.value = [...new Set([...ids.value, ...pageIds])];
    single.value = !(ids.value?.length > 0);
  });
});


watch(dataList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (condition.value) {
      // allQuery.value = false;
      nextTick(() => {
        dataList.value.forEach((item, index) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
      });
    } else {
      nextTick(() => {
        dataList.value?.forEach((item, index) => {
          if (ids.value?.includes(item.id)) {
            proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
          }
        });
      });
    }
  }
});

const openEditCustomerListDialog = (row) => {
    proxy.$refs["editCustomerListRef"].openDialog(row);
}

//重置操作
const resetQuery = () => {
    proxy.$refs["queryRef"].resetFields();
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        taskName: undefined,
        statusStr: undefined,
        createTime: undefined 
    };
    selectedArr.value = [];
    ids.value = [];
    getList();
}

//表格行能否选择
function checkSelectable() {
  if (condition.value) {
    return false;
  } else {
    return true;
  }
}
</script>
<style lang="scss" scoped>

.h-50 {
    overflow: hidden;
    height: 50px;
}

.h-auto {
    height: auto !important;
}
  
</style>
  