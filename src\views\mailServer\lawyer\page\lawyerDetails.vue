<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :class="{ 'form-h50': !showSearch }" label-width="120px" inline>
      <el-form-item label="函件单号">
        <el-input v-model="queryParams.serialNo" placeholder="请输入函件单号" style="width: 240px" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status" placeholder="请选择状态" style="width: 240px">
          <el-option label="待审核" :value="0" />
          <el-option label="审核中" :value="1" />
          <el-option label="已签章" :value="2" />
          <el-option label="已驳回" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="短信发送状态">
        <el-select v-model="queryParams.sendStatusList" placeholder="请选择短信发送状态" clearable multiple
          @focus="getSendStatus" style="width: 240px">
          <el-option v-for="s in sendList" :key="s.code" :label="s.info" :value="s.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="寄送方式">
        <el-select v-model="queryParams.deliveryWayList" placeholder="请选择寄送方式" clearable multiple style="width: 280px"
          @focus="getDeliveryWayList()">
          <el-option v-for="item in deliveryWayList" :key="item.dictValue" :label="item.dictValue"
            :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="operation-revealing-area mb10 mt10">
      <el-button type="primary" plain :disabled="ids.length === 0" @click="downSigntrue()">导出函件内容</el-button>
      <el-button type="primary" plain @click="downSigntrueList()">导出函件列表</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column v-if="columns[0].visible" label="函件单号" align="center" prop="serialNo" width="200px" />
      <el-table-column v-if="columns[1].visible" label="审核状态" align="center" prop="status" show-overflow-tooltip
        :formatter="statusFor">
        <template #default="{ row }">
          <el-popover placement="bottom" :width="500" :ref="`popover-${$index}`" trigger="click">
            <template #reference>
              <el-button @click="showPopover(row)" type="text">{{ statusFor(row) }}</el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column width="120" property="approveStart" label="处理状态" :formatter="approveStartFor" />
              <el-table-column width="220" property="approveTime" label="处理时间" />
              <el-table-column width="150" property="reviewer" label="处理人" :show-overflow-tooltip="true" />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[2].visible" label="模板名称" align="center" width="120px" prop="templateName"
        show-overflow-tooltip />
      <el-table-column v-if="columns[3].visible" label="短信发送状态" align="center" prop="sendStatus" width="110px"
        show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip v-if="row.respdesc" class="box-item" effect="dark" trigger="click" :content="row.respdesc"
            placement="top">
            <el-button type="text"> {{ smsStatus(row.sendStatus) }} </el-button>
          </el-tooltip>
          <span v-else> {{ smsStatus(row.sendStatus) }} </span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[4].visible" label="寄送方式" align="center" prop="deliveryWay" width="100px"
        show-overflow-tooltip />
      <el-table-column v-if="columns[5].visible" label="物流跟踪" align="center" prop="mailingStatus">
        <template #default="{ row }">
          <div>
            <el-button type="text" :text="true" v-if="checkPermi(['lawyer:signtrue:express'])"
              :disabled="row.mailingStatus == 0" :style="{ color: row.mailingStatus == 3 ? '#f56c6c' : '#409eff', }"
              @click="toTrackDetail(row)"> {{ trackStatus(row.mailingStatus) }} </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[6].visible" label="快递单号" align="center" prop="expressNumber" width="220px" />
      <el-table-column v-if="columns[7].visible" label="收件地址" align="center" prop="address" width="180px"
        show-overflow-tooltip />
      <el-table-column v-if="columns[8].visible" label="访问次数" align="center" prop="visitNum" width="160px"
        show-overflow-tooltip />
      <el-table-column v-if="columns[9].visible" label="最后处理人" align="center" prop="examineBy" width="120px"
        show-overflow-tooltip />
      <el-table-column v-if="columns[10].visible" label="最后处理时间" align="center" prop="examineTime" width="180px">
        <template #default="{ row }"> {{ parseTime(row.examineTime) }} </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250px" fixed="right">
        <template #default="{ row }">
          <div>
            <el-button type="text" @click="previewSigntrue(row)">预览</el-button>
            <el-button type="text" v-if="row.status == 2" @click="batchCreate(row)">签章文件</el-button>
            <el-tooltip class="box-item" v-if="row.fileLink" effect="light" placement="top" trigger="click">
              <template #content>
                <div>
                  文件访问地址：<el-button type="text" @click="setLink(row.fileLink)">{{ row.fileLink }}</el-button>
                </div>
              </template>
              <el-button type="text">文件访问地址</el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-area">
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 详情 -->
    <templateDetailsBox ref="templateDetailsBoxRef" />
  </div>
</template>

<script setup name="LawyerDetails">
import { getLetterList, getProceList, getSignFile, } from "@/api/lawyer/lawyer";
import { getStatusOption } from "@/api/note/noteLog.js";
import templateDetailsBox from "./templateDetails.vue";
import { parseTime } from "@/utils/ruoyi.js";
import { listData } from "@/api/system/dict/data.js";
import { checkPermi } from "@/utils/permission";

//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const ids = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    status: undefined,
    deliveryWay: undefined,
  },
});
//查询之后的参数
const queryData = ref({
  serialNo: undefined,
  status: undefined,
  sendStatusList: undefined,
  deliveryWayList: undefined,
  letterId: route.params.id,
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
const sendList = ref([]);
//审核信息
const gridData = ref([]);
//禁用
const single = ref(true);

// 寄送列表
const deliveryWayList = ref([]);
const showSearch = ref(false)
const columns = ref([
  { key: 0, label: `函件单号`, visible: true },
  { key: 1, label: `审核状态`, visible: true },
  { key: 2, label: `模板名称`, visible: true },
  { key: 3, label: `短信发送状态`, visible: true },
  { key: 4, label: `寄送方式`, visible: true },
  { key: 5, label: `物流跟踪`, visible: true },
  { key: 6, label: `快递单号`, visible: true },
  { key: 7, label: `收件地址`, visible: true },
  { key: 8, label: `访问次数`, visible: true },
  { key: 9, label: `最后处理人`, visible: true },
  { key: 10, label: `最后处理时间`, visible: true },
])

//获取列表
function getList() {
  loading.value = true;
  const req = JSON.parse(JSON.stringify(queryParams.value));
  req.sendStatusList = req.sendStatusList?.join(",");
  req.deliveryWayList = req.deliveryWayList?.join(",");
  getLetterList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

// 获取寄送列表
function getDeliveryWayList(params) {
  listData({ dictType: "delivery_method" }).then((res) => {
    deliveryWayList.value = res.rows.filter((item) => item.status != "1");
  });
}

// 获取短信发送状态
function getSendStatus() {
  getStatusOption().then((res) => {
    sendList.value = res.data;
  });
}

/** 多选框选中数据 */
function downSigntrue(row) {
  proxy.downloadforjson(
    "/letter/item/export",
    { letterId: route.params.id, ids: ids.value },
    `签章文件_${new Date().getTime()}.zip`
  );
}
// 导出函件列表
async function downSigntrueList() {
  const req = ids.value.length > 0 ? { ids: ids.value } : queryData.value;
  await proxy.downloadforjson(
    "/letter/item/exportExcel",
    req,
    `函件列表_${new Date().getTime()}.xlsx`
  );
}

// 跳转到物流跟踪
function toTrackDetail(row) {
  let str = JSON.stringify({
    lettersNumber: row.serialNo,
    expressNumber: row.expressNumber,
    address: row.address,
  });
  router.push({
    path: `/lawyer/trackedDetail/${row.id}`,
    query: { lawyerInfo: str.replaceAll('"', "&&") },
  });
}

//状态枚举
function statusFor(row) {
  return ["待审核", "审核中", "已签章", "已驳回"][row.status];
}
// 物流状态
function trackStatus(status) {
  // 待邮寄0、已邮寄1、已签收2 、已拒收3
  return ["待邮寄", "已邮寄", "已签收", "已拒收"][status];
}
// 短信状态
function smsStatus(status) {
  return ["已发送", "发送失败", "发送成功"][status];
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  queryData.value.serialNo = queryParams.value.serialNo;
  queryData.value.status = queryParams.value.status;
  queryData.value.deliveryWay = queryParams.value.deliveryWay;
  queryData.value.sendStatus = queryParams.value.sendStatus;
  queryData.value.letterId = queryParams.value.letterId;
  getList();
}

//审核状态
function approveStartFor(row) {
  return ["待处理", "通过", "不通过"][row.approveStart];
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  let signStatusList = selection.map((item) => item.status);
  //判断通过不通过按钮状态
  let statusFlag = false;
  if (signStatusList.length > 0) {
    signStatusList.forEach((item, index) => {
      if ([1].includes(item)) {
        single.value = true;
        statusFlag = true;
      }
    });
    if (!statusFlag) {
      single.value = false;
    }
  } else {
    single.value = true;
  }
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    status: undefined,
  };
  getList();
}

//气泡框展示
function showPopover(row) {
  let req = {
    id: row.id,
  };
  getProceList(req)
    .then((res) => {
      gridData.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

//模板详情
function previewSigntrue(row) {
  proxy.$refs["templateDetailsBoxRef"].opendialog(row.id);
}

// 复制辨真伪网址
function setLink(url) {
  proxy.copyUrl(url);
  proxy.$modal.msgSuccess("复制成功！");
}

//生成签章
function batchCreate(row) {
  let req = {
    id: row.id,
  };
  getSignFile(req).then((res) => {
    window.open(res.data.url, "_blank");
  });
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
