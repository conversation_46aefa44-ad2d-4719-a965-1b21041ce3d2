<template>
  <div class="app-container">
    <el-tabs tab-position="left" v-model="tabActive" class="demo-tabs">
      <el-tab-pane
        name="0"
        label="我的回款"
        v-if="checkPermi(['collect:myapply:replay'])"
      >
        <!-- 我的回款 -->
        <repaymentVue v-if="tabActive == '0'" ref="repaymentRef" />
      </el-tab-pane>
      <el-tab-pane
        name="1"
        label="我的减免"
        v-if="checkPermi(['collect:myapply:reduct'])"
      >
        <!-- 我的减免 -->
        <reductionVue v-if="tabActive == '1'" ref="reductionRef" />
      </el-tab-pane>
      <el-tab-pane
        name="2"
        label="我的分期"
        v-if="checkPermi(['collect:myapply:stage'])"
      >
        <!--     我的分期    -->
        <stagingVue v-if="tabActive == '2'" />
      </el-tab-pane>
      <el-tab-pane
        name="3"
        label="我的留案"
        v-if="checkPermi(['collect:myapply:stay'])"
      >
        <!--  我的留案-->
        <staycaseVue v-if="tabActive == '3'" />
      </el-tab-pane>
      <el-tab-pane
        name="4"
        label="我的停案"
        v-if="checkPermi(['collect:myapply:stop'])"
      >
        <stopurgingVue v-if="tabActive == '4'" />
      </el-tab-pane>
      <el-tab-pane
        name="5"
        label="我的退案"
        v-if="checkPermi(['collect:myapply:back'])"
      >
        <sendbackVue v-if="tabActive == '5'" />
      </el-tab-pane>
      <el-tab-pane
        name="6"
        label="我的外访"
        v-if="checkPermi(['collect:myapply:outsize'])"
      >
        <outsideVue v-if="tabActive == '6'" />
      </el-tab-pane>
      <el-tab-pane
        name="7"
        label="我的协案"
        v-if="checkPermi(['collect:myapply:assist'])"
      >
        <assistVue v-if="tabActive == '7'" />
      </el-tab-pane>
      <el-tab-pane
        name="8"
        label="我的诉讼"
        v-if="checkPermi(['collect:myapply:lawsuit'])"
      >
        <lawsuitVue v-if="tabActive == '8'" />
      </el-tab-pane>
      <!-- <el-tab-pane name="8" label="资料调取" v-if="checkPermi(['collect:myapply:data'])">
        <dataRetrievalVue v-if="tabActive == '8'" />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script setup name="Myapply">
import { checkPermi } from "@/utils/permission";
import repaymentVue from "./tabs/repayment.vue";
import reductionVue from "./tabs/reduction.vue";
import stagingVue from "./tabs/staging.vue";
import staycaseVue from "./tabs/staycase.vue";
import stopurgingVue from "./tabs/stopurging.vue";
import sendbackVue from "./tabs/sendback.vue";
import outsideVue from "./tabs/outside.vue";
import assistVue from "./tabs/assist.vue";
import lawsuitVue from "./tabs/lawsuit.vue";
import dataRetrievalVue from "./tabs/dataRetrieval.vue";
const tabActive = ref("0");

// 检测权限页面
function checkPermiTab() {
  if (checkPermi(["collect:myapply:replay"])) {
    tabActive.value = "0";
  } else if (checkPermi(["collect:myapply:reduct"])) {
    tabActive.value = "1";
  } else if (checkPermi(["collect:myapply:stage"])) {
    tabActive.value = "2";
  } else if (checkPermi(["collect:myapply:stay"])) {
    tabActive.value = "3";
  } else if (checkPermi(["collect:myapply:stop"])) {
    tabActive.value = "4";
  } else if (checkPermi(["collect:myapply:back"])) {
    tabActive.value = "5";
  } else if (checkPermi(["collect:myapply:outsize"])) {
    tabActive.value = "6";
  } else if (checkPermi(["collect:myapply:assist"])) {
    tabActive.value = "7";
  } else if (checkPermi(["collect:myapply:data"])) {
    tabActive.value = "8";
  } else {
    tabActive.value = undefined;
  }
}
checkPermiTab();
</script>

<style scoped></style>
