<template>
  <div class="app-container">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="92px" style="width: 90%; margin: 0 auto">
      <el-form-item label="模版名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入模版名称" style="width: 350px" maxlength="30" show-word-limit />
      </el-form-item>
    </el-form>
    <TplField />
    <div class="text-center mt20">
      <el-button type="primary" :loading="loading" plain @click="submit">保存，并返回列表</el-button>
      <el-button @click="toBack">返回</el-button>
    </div>
  </div>
</template>

<script setup name="HandeloftenTpl">
import TplField from "@/components/TplField";

import { getTplFields } from "@/api/assets/assetside";
import { addOftenTpl, getTplinfo, editOftenTpl } from "@/api/assets/oftentpl";

const { proxy } = getCurrentInstance();
const route = useRoute();
const store = useStore();
const data = reactive({
  form: {
    name: undefined,
  },
  rules: {
    name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

const loading = ref(false);
const tplFields = ref(); //字段信息
const checkedres = ref({}); //字段选择数据
provide("tplFields", tplFields);
provide("checkedres", checkedres);

onMounted(() => {
  if (route.query.id) {
    //编辑
    form.value.id = route.query.id;
    getTplinfo(form.value.id).then((res) => {
      tplFields.value = res.data.templateInfo;
      form.value.name = res.data.name;
    });
  } else {
    //添加
    getTplFields().then((res) => {
      provide("checkedres", checkedres);
      form.value.name = "";
      tplFields.value = res.data;
    });
  }
});

onActivated(() => {
  if (route.query.id) {
    //编辑
    form.value.id = route.query.id;
    getTplinfo(form.value.id).then((res) => {
      tplFields.value = res.data.templateInfo;
      form.value.name = res.data.name;
    });
  } else {
    //添加
    getTplFields().then((res) => {
      provide("checkedres", checkedres);
      form.value.name = "";
      tplFields.value = res.data;
    });
  }
})

//提交
function submit() {
  loading.value = true;
  try {
    Object.keys(tplFields.value).map((key) => {
      //获取选中数据
      if (key != "additionalInfo") {
        tplFields.value[key].columns.map((item) => {
          if (checkedres.value[key].indexOf(item.label) > -1) {
            item.choose = true;
          } else {
            item.choose = false;
          }
        });
      }
    });
    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        form.value.template = tplFields.value;
        if (form.value.id) {
          editOftenTpl(form.value)
            .then(() => {
              proxy.$modal.msgSuccess("保存成功！");
              store.dispatch("tagsView/delCachedView", { name: "Oftentpl" });
              toBack();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addOftenTpl(form.value)
            .then((res) => {
              proxy.$modal.msgSuccess("创建成功！");
              store.dispatch("tagsView/delCachedView", { name: "Oftentpl" });
              toBack();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
}

//返回
const toBack = () => {
  const obj = { path: route.query.path };
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style scoped></style>
