<template>
  <div class="handle-urage caseinfo-wrap pd20">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="144px">
      <el-form-item label="保全阶段" prop="saveFieldName">
        <el-radio-group v-model="form.saveFieldName">
          <el-radio v-for="item in securityList" :key="item.code" :label="item.info">
            {{ item.info }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 表单配置 -->
    <formComponents ref="formComponentsRef" :key="form.saveFieldName" :formOption="formOption"
      v-model:worksheetForm="worksheetForm" />
    <div class="mt20" style="margin-left:160px">
      <el-button type="primary" plain :loading="loading" @click="saveLawsuit">保存</el-button>
    </div>
  </div>
</template>

<script setup name="lawsuitUrge">
import formComponents from "@/components/formComponents/index";
import { selectSecurity, selectContactChannel, insertComplaint } from "@/api/caseDetail/urge";
import { selectStageFrom } from "@/api/caseDetail/detail";
import { stateOnlineFilling } from "@/api/mediation/onlineFiling";
const { proxy } = getCurrentInstance();
//表单设置
const emit = defineEmits(["getUrgeList"]);
const route = useRoute()
const loading = ref(false);
const data = reactive({
  form: {
    registerType: 3,
    saveFieldName: route.query?.twoStage,
    saveStage: undefined,
  },
  rules: {
    saveFieldName: [{ required: true, message: "请选择调诉阶段", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//调诉阶段
const securityList = ref([]);
const formOption = ref([]);
//自定义表单
const worksheetForm = ref([]);
const props = defineProps({
  caseId: {
    type: [Number, String],
  },
});

//获取保全阶段
function getContactChannel() {
  let req = { type: 3, caseId: route.params.caseId };
  stateOnlineFilling(req).then((res) => {
    securityList.value = res.data;
  });
}
getContactChannel();

//查询阶段表单
function getStageFrom() {
  let req = { stageTwoName: form.value.saveFieldName, disposeWay: 3, };
  selectStageFrom(req).then((res) => {
    formOption.value = res.data;
  });
}

//保存催记
function saveLawsuit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = {
        caseId: props.caseId,
        disposeWay: 3,
        stageId: form.value.contactMedium,
        registerType: form.value.registerType,
        saveStage: form.value.saveFieldName,
        saveFieldName: form.value.saveFieldName,
        id: route.query.freezeId,
        registerType: 3
      };
      proxy.$refs["formComponentsRef"].submitForm();
      Object.assign(req, worksheetForm.value);
      insertComplaint(req)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          emit('getUrgeList')
          reset();
        })
        .catch((erroe) => {
          loading.value = false;
        });
    }
  });
}

//清空表单方法
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    registerType: 3,
    saveFieldName: route.query.twoStage,
    saveStage: undefined,
  };
  proxy.$refs["formComponentsRef"].resetForm();
}
watch(() => form.value, () => {
  nextTick(() => {
    getStageFrom()
  })
}, { immediate: true, deep: true })
</script>

<style></style>
