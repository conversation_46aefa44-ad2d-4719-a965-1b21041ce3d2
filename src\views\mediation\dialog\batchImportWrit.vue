<template>
    <el-dialog v-model="open" title="批量生成文书" :before-close="cancel" width="940px">
        <el-steps :active="activeStep" class="mb20" align-center>
            <el-step title="选择法院和文书" />
            <el-step title="预览完成" />
        </el-steps>
        <div class="import-step-one" v-if="activeStep == 0">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
                <el-row :gutter="24">
                    <el-col :span="24">
                        <el-form-item label="选择法院" prop="lawAgencyId">
                            <el-select placeholder="请选择法院" v-model="form.lawAgencyId" clearable @change="selectCourt">
                                <el-option v-for="item in courtList" :label="item.info" :key="item.code"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="选择文书" prop="documentTemplateIds">
                            <div style="width:100%;" class="select-writ-content">
                                <div class="select-box">
                                    <div style="width:50%;">
                                        <el-input v-model="writText" placeholder="可搜索你需要的文书" />
                                    </div>
                                </div>
                                <div v-if="form.lawAgencyId" style="width:98%;">
                                    <el-checkbox-group :loading="loading" v-model="form.documentTemplateIds">
                                        <el-row :gutter="24">
                                            <el-col :span="12" v-for="item in filterWritList()"
                                                :key="item.documentTemplateId">
                                                <el-checkbox :label="item.documentTemplateId">
                                                    {{ item.documentTemplateName }}
                                                </el-checkbox>
                                            </el-col>
                                        </el-row>
                                    </el-checkbox-group>
                                </div>
                                <div style="width:98%" class="no-writ-content"
                                    v-if="form.lawAgencyId && filterWritList().length == 0">
                                    该机构没有文书，请重新选择
                                </div>
                                <div style="width:98%" class="no-writ-content" v-if="!form.lawAgencyId">
                                    请选择法院
                                </div>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="import-step-two" v-else>
            <div class="left">
                <el-button type="text" :loading="loading" :disabled="writShowSetp == 0" size="large" @click="handleLeft"
                    link icon="ArrowLeftBold" />
            </div>
            <div class="content">
                <!-- <template v-for="(item, index) in preivewForm" :key="item.fileUrl">
                    <previewPdf ref="previewPdfRef" class="preview-pdf" v-show="index == writShowSetp"
                        :pdfSrc="item.fileUrl" :total="item.count" />
                    
                </template> -->
                <previewPdf v-if="currentPdf" ref="previewPdfRef" class="preview-pdf" :pdfSrc="currentPdf.fileUrl" :total="currentPdf.count"/>
            </div>
            <div class="right">
                <el-button type="text" :loading="loading" :disabled="writShowSetp == preivewForm.length - 1"
                    size="large" @click="handleRight" link icon="ArrowRightBold" />
            </div>
        </div>
        <template #footer>
            <div>
                <div class="text-center" v-if="activeStep == 0">
                    <el-button :loading="loading" @click="cancel">取消</el-button>
                    <el-button :loading="loading" type="primary" @click="nextSetp">下一步</el-button>
                </div>
                <div class="text-center" v-else>
                    <el-button :loading="loading" @click="backSetp">上一步</el-button>
                    <el-button :loading="loading" type="primary" @click="submitForm">提交</el-button>
                </div>
            </div>

        </template>
    </el-dialog>
</template>
<script setup>
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
import { getWritTemplateBylawAgencyId } from '@/api/courtManage/courtManage';
import { documentPreview, batchAddLitigationDoc } from "@/api/collection/mycase";
import { getCourtOptions } from "@/api/common/common";
import { pageTypeEnum } from "@/utils/enum";
import { ElMessageBox } from 'element-plus'
import { nextTick } from "vue";
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const uid = ref(undefined)
const courtList = ref([])
const writList = ref([])
const writShowSetp = ref(0)
const currentPdf = ref(undefined);
const activeStep = ref(0)
const templateType = ref(undefined)
const writText = ref(undefined)
const data = reactive({
    form: {
        lawAgencyId: undefined,
        caseIds: undefined,
        documentTemplateIds: undefined,
        courtName: undefined,
    },
    preivewForm: [],
    rules: {
        lawAgencyId: [{ required: true, message: '请选择', trigger: 'change' }],
        documentTemplateIds: [{ required: true, message: '请选择', trigger: 'change' }],
    },
})
const { form, rules, preivewForm } = toRefs(data)

// 提交
function submitForm() {
    loading.value = true
    if (loading.value) {
        const court = courtList.value.find(item => item.code == form.value.lawAgencyId)
        form.value.courtName = court?.courtName
        const reqForm = JSON.parse(JSON.stringify(form.value))
        reqForm.courtName = court?.courtName
        reqForm.uid = uid.value
        batchAddLitigationDoc(reqForm).then((res) => {
            if (res.code == 200) {
                cancel()
                const htmlContent = `
                <div>
                     <div>已提交电子签章文书审核，可以在案件详情查看文书材料</div>   
                     <div>待审核人员审核通过，即可以查看或下载带电子签章的文书</div>   
                </div>
                `
                ElMessageBox.alert(htmlContent, '提交审核', {
                    confirmButtonText: '确定',
                    dangerouslyUseHTMLString: true,
                })

            }
        }).finally(() => loading.value = false)
    }

}
// 打开
function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data };
}
// 关闭
function cancel() {
    open.value = false
    activeStep.value = 0
    proxy.resetForm('formRef')
    form.value = {}
    preivewForm.value = [];
    currentPdf.value = undefined
}

// 上一步
function backSetp() {
    activeStep.value--
    writShowSetp.value = 0
}

// 下一步
function nextSetp() {
    if (activeStep.value == 0) {
        proxy.$refs['formRef'].validate((vaild) => {
            if (vaild) {
                loading.value = true
                const court = courtList.value.find(item => item.code == form.value.lawAgencyId) || {}
                form.value.courtName = court?.courtName
                const reqForm = JSON.parse(JSON.stringify(form.value))
                reqForm.courtName = court?.courtName
                documentPreview(reqForm).then(res => {
                    if (res.code == 200) {
                        uid.value = res.data.uid
                        preivewForm.value = res.data.pdfFilePojos
                        currentPdf.value = preivewForm.value[0];
                        writShowSetp.value = 0
                        activeStep.value++
                        console.log(preivewForm.value);
                    }
                }).finally(() => loading.value = false)
            }
        })
    }

}

function handleLeft() {
    if (writShowSetp.value > 0) {
        nextTick(() => {
            writShowSetp.value--;
            currentPdf.value = preivewForm.value[writShowSetp.value]
            console.log(currentPdf.value);
        })
    }
}

function handleRight() {
    if (writShowSetp.value < preivewForm.value?.length - 1) {
        nextTick(() => {
            writShowSetp.value++;
            currentPdf.value = preivewForm.value[writShowSetp.value]
            console.log(currentPdf.value);
        })
    }
}

// 获取机构列表
getCourts()
function getCourts() {
    getCourtOptions().then((res) => {
        courtList.value = res.data
    })
}
function selectCourt() {
    form.value.documentTemplateIds = []
    nextTick(() => {
        getWritTemplate()
    })
}
// 根据机构查询文书模板
function getWritTemplate() {
    getWritTemplateBylawAgencyId({ agencyId: form.value.lawAgencyId }).then(res => {
        writList.value = res.data
    })
}

// 过滤文书
function filterWritList() {
    let arr = JSON.parse(JSON.stringify(writList.value))
    const newTypeList = templateType.value ? JSON.parse(JSON.stringify(templateType.value)) : []
    const typeList = Array.isArray(templateType.value) && newTypeList.map(item => item.join('/'))
    arr = typeList.length > 0 ? arr.filter(item => typeList.includes(item.classifyLabel)) : arr
    arr = writText.value ? arr.filter(item => item.templateName == writText.value) : arr
    return arr
}

defineExpose({ openDialog })

</script>

<style lang="scss" scoped>
.import-step-two {
    display: flex;
    align-items: center;

    .content {
        width: 794px;
        height: 50vh;
        overflow-y: auto;
        border: 1px solid #ccc;

        .writ-content {
            min-height: 1124px;
            border: 1px solid transparent;
        }
    }

    .left,
    .right {
        flex: 1;

        .el-button {
            :deep(.el-icon) {
                font-size: 35px;
            }
        }
    }
}

.no-writ-content {
    font-size: 18px;
    margin-top: 20px;
    text-align: center;
    color: #9f9f9f;
}

.select-writ-content {
    min-height: 150px;
    max-height: 300px;
    overflow-y: auto;

    .select-box {
        display: flex;
    }
}

:deep(.el-step__title.is-process),
:deep(.el-step.is-center .el-step__description) {
    color: #326fea;
}

:deep(.is-process .el-step__head.is-process),
:deep(.is-finish .el-step__head.is-finish) {
    color: #fff;
    border-color: #326fea;
    background-color: #326fea;
}

:deep(.is-process .el-step__icon.is-text),
:deep(.is-finish .el-step__icon.is-text) {
    color: #fff;
    border-color: #326fea;
    background-color: #326fea;
}

:deep(.el-step__icon) {
    width: 34px;
    height: 34px;
}

:deep(.el-step.is-horizontal .el-step__line) {
    top: 50%;
    width: 80%;
    left: 60%;
    transform: translateY(-50%);
}

:deep(.is-finish .el-step__line) {
    background-color: #326fea;
}
</style>
<style lang="scss">
.preview-pdf {
    canvas {
        height: auto !important;
        width: 792px !important;
    }
}
</style>