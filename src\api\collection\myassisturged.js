import request from "@/utils/request";

//获取列表
export function selectAssistRecordHelperId(query) {
  return request({
    url: "/collection/selectAssistRecordHelperId",
    method: "get",
    params: query,
  });
}

//根据案件id查询协催记录
export function selectAssistDetails(query) {
  return request({
    url: `/collection/selectAssistDetails/${query}`,
    method: "get",
  });
}

//写入协催申请记录详情表
export function insertAssistDetails(data) {
  return request({
    url: "/collection/insertAssistDetails",
    method: "post",
    data: data,
  });
}

//获取协催数量
export function getAssistCount(query) {
  return request({
    url: "/collection/selectAssistRecordNumber",
    method: "get",
    params: query,
  });
}

