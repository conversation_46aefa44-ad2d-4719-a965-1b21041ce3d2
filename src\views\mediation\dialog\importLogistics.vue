<template>
    <el-dialog title="批量导入物流单号" v-model="open" width="550px" :before-close="cancel" append-to-body>
        <el-form :model="form" ref="formRef" :rules="rules" label-width="140px" inline>
            <el-form-item label="下载导入模板">
                <el-button :loading="loading" type="primary" @click="handleDownloadTpl">下载模板</el-button>
            </el-form-item>
            <el-form-item label="上传填好的信息表" prop="url">
                <FileUpload drag v-model:fileList="fileList" :fileType="['xls', 'xlsx']" gateway="sign"
                    uploadFileUrl="/letter/message/upload" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-right">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import FileUpload from '@/components/FileUpload';
import { logisticsVerifyFile, addByExcel } from "@/api/writ/lawyer";
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const data = reactive({
    form: {},
    rules: {
        url: [{ required: true, message: '请上传文件', trigger: 'bluer' }]
    },
})
const fileList = ref([])
const { form, rules } = toRefs(data)
function submit() {
    form.value.url = fileList.value[0].response.data.fileUrl[0]
    form.value.fileName = fileList.value[0]?.response.data.modifyName[0]
    nextTick(() => {
        proxy.$refs['formRef'].validate(valid => {
            if (valid) {
                try {
                    loading.value = true
                    const urlReqForm = JSON.parse(JSON.stringify(form.value))
                    logisticsVerifyFile(urlReqForm).then(res => {
                        addByExcel({ verifyId: res.data.verifyId, type: 0, stage: form.value.stage }).then((res) => {
                            if (res.code == 200) {
                                proxy.$modal.msgSuccess('操作成功')
                                cancel()
                            }
                        }).finally(() => loading.value = false)
                    }).finally(() => loading.value = false)
                } catch (error) {
                    loading.value = false
                }
            }
        })
    })
}
function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data }
    form.value.stage = `${data.query.sign}-${data.query.mediatedStage}`
}
function cancel() {
    form.value = {}
    fileList.value = []
    open.value = false
}

// 下载模板
function handleDownloadTpl() {
    proxy.download("/document/item/downloadTemplate", {}, `tpl_导入物流单号模板.xlsx`, { gateway: 'sign' });
}

defineExpose({ openDialog })
</script>