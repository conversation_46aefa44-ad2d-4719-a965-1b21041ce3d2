import request from '@/utils/request'

// 获取路由
export const getRouters = () => {
  return request({
    url: '/settings/selectMenuRoute',
    method: 'get'
  })
}
//获取菜单列表
export function getmenulist(roleId) {
    return request({
        url: `/settings/roleMenuTreeselect/${roleId}`,
        method: 'get'
    })
}

//获取文件在线预览主机地址
export function getFileOnlinePreviewHost(query) {
  return request({
    url: '/filePreview/getFileOnlinePreviewHost',
    method: 'get',
    params: query
  })
}