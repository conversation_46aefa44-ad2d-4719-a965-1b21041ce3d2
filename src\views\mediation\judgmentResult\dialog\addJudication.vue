<template>
  <el-dialog title="新增判决" v-model="open" width="750px" :before-close="cancel" append-to-body>
    <el-form :model="form" ref="formRef" :rules="rules" inline label-width="100px">
      <el-form-item prop="caseStatus" label="判决状态">
        <el-select v-model="form.caseStatus" placeholder="请选择判决状态" style="width: 220px">
          <el-option v-for="item in caseStatusOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="judgeLetter" label="判决文书">
        <el-select v-model="form.judgeLetter" placeholder="请选择判决文书" style="width: 220px">
          <el-option v-for="item in judgeLetterOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="judgeResult" label="判决结果">
        <el-select v-model="form.judgeResult" placeholder="请选择判决结果" style="width: 220px">
          <el-option v-for="item in judgeResultOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item> -->
      <el-form-item prop="judgeSum" label="判决金额">
        <el-input v-model="form.judgeSum" placeholder="请输入判决金额" style="width: 220px"></el-input>
      </el-form-item>
      <el-form-item prop="custSatisfy" label="客户满意度">
        <el-select v-model="form.custSatisfy" placeholder="请选择客户满意度" style="width: 220px">
          <el-option v-for="item in custSatisfyOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="courierNum" label="快递单号">
        <el-input v-model="form.courierNum" placeholder="请输入快递单号" style="width: 220px" />
      </el-form-item>
      <el-form-item prop="judgeTime" label="判决时间">
        <el-date-picker v-model="form.judgeTime" placeholder="请选择判决时间" value-format="YYYY-MM-DD" type="date"
          style="width: 220px" />
      </el-form-item>
      <el-form-item style="width: 100%" prop="judgeContent" label="判决描述">
        <el-input type="textarea" style="width: 100%" :maxlength="100" show-word-limit v-model="form.judgeContent"
          placeholder="请输入判决描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">保存</el-button>
        <el-button :loading="loading" @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getCaseStatusOption, getJudgeResultOption, getCustSatisfyOption, getJudgeLetterOption } from '@/api/common/common';
import { addJudge } from '@/api/mediation/judgmentResult';
const props = defineProps({
  getList: { type: Function }
})
const { proxy } = getCurrentInstance();

const data = reactive({
  form: {},
  rules: {
    caseStatus: [
      { required: true, message: '请选择判决状态', trigger: 'blur' }
    ],
    judgeLetter: [
      { required: true, message: '请选择判决文书', trigger: 'blur' }
    ],
    judgeResult: [
      { required: true, message: '请选择判决结果', trigger: 'blur' }
    ],
    judgeSum: [
      { required: true, message: '请输入判决金额', trigger: 'blur' },
      { pattern: /^\d+(.\d{1,4})?$/, message: '请输入数字，可保留四位小数', trigger: 'blur' }
    ],
    custSatisfy: [
      { required: true, message: '请选择客户满意度', trigger: 'blur' }
    ],
    courierNum: [
      { required: true, message: '请输入快递单号', trigger: 'blur' },
      { pattern: /^\w+$/, message: '请输入数字、字母', trigger: 'blur' }
    ],
    judgeTime: [
      { required: true, message: '请选择判决时间', trigger: 'blur' }
    ],
    judgeContent: [
      { required: true, message: '请输入判决描述', trigger: 'blur' }
    ],
  },
});
const caseStatusOption = ref([
  { code: 0, info: '审理判决' },
  { code: 1, info: '判决审理' },
  { code: 2, info: '诉讼撤案' },
  { code: 3, info: '停止诉讼' },
  { code: 4, info: '诉讼结案' },
  { code: 5, info: '申请执行立案' },
])
const judgeLetterOption = ref([
  { code: 0, info: '仲裁裁决' },
  { code: 1, info: '一审判决' },
  { code: 2, info: '二审判决' },
  { code: 3, info: '管辖异议' },
  { code: 4, info: '民事调解书' },
  { code: 5, info: '仲裁调解书' },
  { code: 6, info: '仲裁调解裁定书' },
  { code: 7, info: '一审调解裁定书' },
  { code: 8, info: '二审调解裁定书' },
  { code: 9, info: '仲裁裁定书' },
  { code: 10, info: '一审裁定书' },
  { code: 11, info: '二审裁定书' },
  { code: 12, info: '执行裁定书' },
  { code: 13, info: '终本裁定' },
])
const judgeResultOption = ref([
  { code: 0, info: '胜诉' },
  { code: 1, info: '败诉' },
  { code: 2, info: '中和判决' },
])
const custSatisfyOption = ref([
  { code: 0, info: '满意' },
  { code: 1, info: '不满意' },
  { code: 2, info: '未回访' },
  { code: 3, info: '客户无反馈' },
])
const { form, rules } = toRefs(data);

const open = ref(false);

const loading = ref(false);

function submit() {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      loading.value = true
      const reqForm = JSON.parse(JSON.stringify(form.value))
      addJudge(reqForm).then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess('操作成功')
          props.getList && props.getList()
          cancel()
        }
      }).finally(() => loading.value = false)
    }
  })
}

function openDialog(data) {
  open.value = true;
  form.value = { ...data.query, ...data }
};

function cancel() {
  open.value = false;
  form.value = {}
};
getCaseStatusOptionFun()
function getCaseStatusOptionFun() {
  getCaseStatusOption().then(res => {
    caseStatusOption.value = res.data
  })
}
getJudgeResultOptionFun()
function getJudgeResultOptionFun() {
  getJudgeResultOption().then(res => {
    judgeResultOption.value = res.data
  })
}
getJudgeLetterOptionFun()
function getJudgeLetterOptionFun() {
  getJudgeLetterOption().then(res => {
    judgeLetterOption.value = res.data
  })
}
getCustSatisfyOptionFun()
function getCustSatisfyOptionFun() {
  getCustSatisfyOption().then(res => {
    custSatisfyOption.value = res.data
  })
}
defineExpose({ openDialog });
</script>

<style lang="scss" scoped></style>
