import request from '@/utils/request'

//获取模板类型数据
export function getNoteLog(query) {
    return request({
        url: '/smsSending/selectSmsRecord',
        method: 'get',
        params: query
    })
}


//状态设置-选项
export function getStatusOption(query) {
    return request({
        url: '/smsSending/sendStatusDropdown',
        method: 'get',
        params: query
    })
}

//回复内容设置-选项
export function selectProce(query) {
    return request({
        url: '/smsSending/selectReplyRecords',
        method: 'get',
        params: query
    })
}

// 获取短信渠道列表
export function getSmsChannel(query) {
    return request({
        url: '/letter/item/getSmsChannel',
        method: 'get',
        params: query
    })
}

// 手动重新发送短信接口
export function resloadSendSms(data) {
    return request({
        url: '/letter/item/resloadSendSms',
        method: 'post',
        data: data
    })
}