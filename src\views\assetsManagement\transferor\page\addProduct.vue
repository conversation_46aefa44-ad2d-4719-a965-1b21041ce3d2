<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="设置产品"></el-step>
        <el-step title="设置导入模板"></el-step>
        <el-step title="完成，生成模板并下载"></el-step>
      </el-steps>
    </div>
    <div class="step-item step1" v-show="stepActive === 0">
      <el-form :inline="true" :model="prdform" :rules="prdrules" ref="setprdRef" label-width="92px">
        <div class="form-content">
          <div>
            <el-form-item label="委托方名称" prop="ownerId">
              <el-select v-model="prdform.ownerId" clearable filterable :reserve-keyword="false" placeholder="请选择委托方"
                style="width: 240px" @change="getcurrentOwner">
                <el-option v-for="asstes in asstesList" :key="asstes.id" :label="asstes.name" :value="asstes.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Plus" v-hasPermi="['assets:side:add']" @click="add">新 建</el-button>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="产品名称" prop="name">
              <!-- <el-select v-model="prdform.name" filterable :reserve-keyword="false" placeholder="请输入产品方名称"  @focus="searchName()">
                <el-option
                  v-for="item in options"
                  :key="item.code"
                  :label="item.info"
                  :value="item.code"
                />
              </el-select> -->
              <el-input v-model="prdform.name" placeholder="请输入产品名称" show-word-limit />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="产品代号" prop="productCode">
              <el-input v-model="prdform.productCode" placeholder="请输入产品代号" show-word-limit />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="handle-btn">
        <el-button type="primary" @click="toBack()">返回列表</el-button>
        <el-button type="primary" @click="nextStep(1)">下一步，设置导入模板</el-button>
      </div>
      <div class="hint">
        <p class="title">说明：</p>
        <p class="c-tit">设置转让方</p>
        <p class="text">
          若无转让方名称可点击添加转让方按钮自行添加，然后下拉选择资方名称、填写产品名称，进入下一步设置导入案件信息模板，生成模板，完成转让方管理管理工作
        </p>
        <p class="c-tit">案件信息确认</p>
        <p class="text">
          根据转让方的不同，对不同的产品定制不同方案的案件信息模板，以便案件的批量导入管理
        </p>
      </div>
    </div>
    <!-- 第一步 End -->

    <div class="step-item step2" v-show="stepActive === 1">
      <div class="step-item-head">
        <span class="title">为转让方 <span class="tit">{{ currentOwner }}</span>设置导入案件信息模板：</span>
        <el-radio-group v-model="tplType" @change="choseTpl">
          <el-radio :label="-1">自定义模板</el-radio>
          <el-radio v-for="item in tplList" :key="item.id" :label="item.id">{{
            item.name
          }}</el-radio>
        </el-radio-group>
      </div>
      <tpl-field></tpl-field>
      <div class="handle-btn">
        <el-button type="primary" @click="prevStep(0)">返回上一步</el-button>
        <el-button type="primary" :loading="loading" @click="nextStep(2)">保存模板，下一步</el-button>
      </div>
    </div>
    <!-- 第二步 End -->

    <div class="step-item step3" v-show="stepActive === 2">
      <div class="step-item-last">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
        <p>
          产品案件信息模板定制成功（对不同的产品定制不同方案的案件信息模板，以便案件的批量导入管理）
        </p>
      </div>
      <div class="handle-btn">
        <el-button type="primary" @click="downandback">下载模板，并返回</el-button>
      </div>
    </div>
    <!-- 第三部 End -->

    <!-- 添加修改转让方 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="asstesRef" label-position="right" label-width="150px">
        <el-form-item label="转让方名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入转让方名称" style="width:280px;" show-word-limit maxlength="50" />
        </el-form-item>
        <el-form-item label="代号" prop="shortName">
          <el-input v-model="form.shortName" placeholder="请输入转让方代号（纯字母）" style="width:280px;" show-word-limit
            maxlength="10" />
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="unifiedCode">
          <el-input v-model="form.unifiedCode" placeholder="请输入社会统一信用代码" maxlength="100" style="width:280px;"
            show-word-limit />
        </el-form-item>
        <el-form-item>
          <p class="text-danger">注：请选择转让方准入公司全称（工商登记营业执照全称）</p>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AddProduct">
import TplField from "@/components/TplField";
import { debounce } from "@/utils/index";

import {
  addAssetOwner,
  assetOwnerList,
  checkProName,
  getTplFields,
  addProduct,
  getDictProductType,
  getDictTransferor,
  addName,
} from "@/api/assets/assetside";
import { getOftenTplOptions } from "@/api/assets/oftentpl";
import message from "@/api/common/webSocket";

const timer = ref(null);
//检测产品类型
const namechange = (data, callback) => {
  //节流防止请求次数过多
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = null;
  }
  timer.value = setTimeout(() => {
    checkProName(data).then((res) => {
      if (res.data) {
        callback();
      } else {
        callback(new Error("产品类型已存在，请重新输入！"));
      }
    });
  }, 1000);
};

//检查产品类型
const checkname = (rule, value, callback) => {
  if (!value || value == "") {
    callback(new Error("请填写产品类型"));
  } else {
    // namechange({ name: value }, callback);
    //debounce(namechange, 2)
  }
};

const router = useRouter();
const route = useRoute();

const { proxy } = getCurrentInstance();

const stepActive = ref(0);
const data = reactive({
  // 转让方创建
  form: {
    name: undefined,
    shortName: undefined,
    unifiedCode: undefined
  },
  rules: {
    name: [{ required: true, message: "请输入转让方名称", trigger: "blur" }],
    shortName: [{ required: true, pattern: /^[a-zA-z]+$/, message: "请输入转让方代号（纯字母）", trigger: "blur" }],
  },
  //设置产品
  prdform: {
    ownerId: undefined,
    name: undefined,
  },
  prdrules: {
    ownerId: [{ required: true, message: "请选择转让方", trigger: "change" }],
    name: [{ required: true, message: "请输入产品类型", trigger: 'blur' }],
    productCode: [
      { required: true, message: "请选择产品代号", trigger: 'blur' },
    ]
  },
});
const asstesList = ref([]);
const currentOwner = ref(""); //当前转让方
const tplFields = ref(); //字段信息
const checkedres = ref({}); //字段选择数据
provide("tplFields", tplFields);
provide("checkedres", checkedres);
const loading = ref(false);

const title = ref("");
const open = ref(false);
const subloading = ref(false);

const downfileId = ref(undefined); //模板文件id

const { form, rules, prdform, prdrules } = toRefs(data);
const options = ref([]);
const optionTras = ref([]);
onMounted(() => {
  prdform.value.ownerId =
    route.params.ownerId[0] && route.params.ownerId[0] != "*"
      ? parseInt(route.params.ownerId[0])
      : undefined;
});

//设置导入模板
const tplType = ref(-1);
const tplList = ref([
  { id: 1, name: "通用模板" },
  // { id: 2, name: "资产通用模板" },
]);

//新增转让方
function add() {
  reset();
  title.value = "创建转让方";
  open.value = true;
}

//获取转让方列表
function getAssetOwnerList() {
  assetOwnerList().then((res) => {
    asstesList.value = res.data;
  });
}
getAssetOwnerList();

//选择的转让方
function getcurrentOwner(val) {
  asstesList.value.map((item) => {
    if (item.id == val) {
      currentOwner.value = item.name;
    }
  });
}

// 添加转让方提交按钮
function submitForm() {
  proxy.$refs["asstesRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      optionTras.value.forEach((item, index) => {
        if (item.info == form.value.name) {
          form.value.shortName = item.code
        }
      })
      addAssetOwner(form.value)
        .then((res) => {
          subloading.value = false;
          proxy.$modal.msgSuccess("创建成功！");
          getAssetOwnerList();
          cancel();
        })
        .catch(() => {
          subloading.value = false;
        });
    }
  });
}

//重置表单
function reset() {
  proxy.resetForm("asstesRef");
  form.value = {
    name: undefined,
    unifiedCode: undefined
  };
}

//取消按钮
function cancel() {
  open.value = false;
  reset();
}

//返回转让方管理
const toBack = () => {
  const obj = { path: "/assetsManagement/transferor" };
  proxy.$tab.closeOpenPage(obj);
};

//选择通用模板
function choseTpl(val) {
  tplFields.value = {};
  provide("checkedres", checkedres);
  getTplFields(val == -1 ? undefined : val)
    .then((res) => {
      tplFields.value = res.data;
    })
    .catch(() => {
      tplFields.value = {};
    });
}

//上一步
function prevStep(step) {
  stepActive.value = step;
}

//下一步
const nextStep = (step) => {
  if (step === 1) {
    proxy.$refs["setprdRef"].validate((valid) => {
      if (valid) {
        stepActive.value = step;
        getTplFields().then((res) => {
          tplFields.value = res.data;
        });
        getOftenTplOptions().then((res) => {
          tplList.value = res.data;
        });
      } else {
        return;
      }
    });
  }

  if (step === 2) {
    Object.keys(tplFields.value).map((key) => {
      if (key != "additionalInfo") {
        tplFields.value[key].columns.map((item) => {
          if (checkedres.value[key].indexOf(item.label) > -1) {
            item.choose = true;
          } else {
            item.choose = false;
          }
        });
      }
    });
    prdform.value.template = tplFields.value;
    loading.value = true;
    addProduct(prdform.value)
      .then((res) => {
        loading.value = false;
        proxy.$modal.msgSuccess("模板保存成功！");
        downfileId.value = res.data;
        stepActive.value = step;
      })
      .catch(() => {
        loading.value = false;
      });
  }
};

//下载模板并返回
function downandback() {
  // proxy.download(
  //   "caseManage/asset/product/downloadTemplate",
  //   { id: downfileId.value },
  //   `tpl_${prdform.value.name}.xlsx`
  // );
  proxy.download(
    "teamProduct/downloadTemplate",
    { id: downfileId.value },
    `tpl_${prdform.value.name}.xlsx`,
    { gateway: 'cis' }
  );
  toBack();
}

function searchName() {
  getDictProductType().then((res) => {
    options.value = res.data;
  })
}

function searchTraName() {
  // getDictTransferor().then((res)=>{
  //   optionTras.value = res.data;
  // })
}
searchTraName()
</script>

<style lang="scss" scoped>
.step-item {
  width: 80%;
  margin: 0 auto;

  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;

    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;

      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

.form-content {
  width: 480px;
  margin: 50px auto;
}

.handle-btn {
  text-align: center;
}

.hint {
  width: 86%;
  margin: 0 auto;
  margin-top: 48px;
  padding: 20px 0;
  border-top: 1px solid #e8e8e8;
  line-height: 20px;
  font-size: 12px;

  p {
    margin: 0;
  }

  .title {
    font-weight: 400;
    line-height: 17px;
    color: #3f3f3f;
  }

  .c-tit {
    margin-top: 10px;
  }

  .text {
    color: #888888;
  }
}

.step-item-last {
  text-align: center;
  margin: 32px auto;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 400;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    color: #888888;
  }
}
</style>
