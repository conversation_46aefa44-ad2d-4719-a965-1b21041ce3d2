<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="side-edge">
        <div class="head-container mb10 pl20">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          团队调解排名
        </div>
        <div class="dept-list">
          <div class="dept-item" v-for="dept in allDept" :key="dept.id">
            <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
              {{ `${dept.name}(${dept.caseNum || 0})` }}
            </div>
            <!-- <div :class="`employ-item ${activeDept == employ.id ? 'active' : ''}`" v-for="employ in dept.children"
              :key="employ.id">
              <div @click="handleChangeRanke(employ.id, 1)">{{ `${employ.name}(${employ.caseNum || 0})` }}</div>
            </div> -->
          </div>
        </div>
      </el-col>

      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" inline label-width="120px"
          :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
          <el-form-item prop="packageName" label="资产包名称">
            <el-input v-model="queryParams.packageName" style="width: 280px" placeholder="请输入资产包名称" />
          </el-form-item>
          <el-form-item prop="caseId" label="案件ID">
            <el-input v-model="queryParams.caseId" style="width: 280px" placeholder="请输入案件ID" />
          </el-form-item>
          <el-form-item prop="clientName" label="被告">
            <el-input v-model="queryParams.clientName" style="width: 280px" placeholder="请输入被告" />
          </el-form-item>
          <el-form-item prop="mediationNum" label="调解号">
            <el-input v-model="queryParams.mediationNum" style="width: 280px" placeholder="请输入调解号" />
          </el-form-item>
          <el-form-item label="标的额">
            <div class="range-scope" style="width: 280px">
              <el-input v-model="queryParams.amount1" />
              <span>-</span>
              <el-input v-model="queryParams.amount2" />
            </div>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <!-- <div class="operation-revealing-area">
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div> -->
        <el-row class="mb10 mt10">
          <el-button plain :disabled="single" @click="opendialog({ type: 1, row: selectedArr })"
            v-hasPermi="['saasc:teamCase:insertRetreat']">申请退案</el-button>
          <el-button plain :disabled="single" type="primary" @click="opendialog({ type: 2, row: selectedArr})"
            v-hasPermi="['saasc:teamCase:insertKeep']">申请留案</el-button>
          <el-button plain :disabled="single" type="success" @click="opendialog({ type: 3, row: selectedArr })"
            v-hasPermi="['saasc:teamCase:insertStop']">申请停案</el-button>
          <el-button plain :disabled="single" type="info" @click="openLabelCase"
            v-hasPermi="['saasc:teamCase:selectMarkCase']">标记案件</el-button>
          <el-button plain :disabled="single" type="warning" @click="toPoint()"
            v-hasPermi="['saasc:teamCase:updateCase']">指定分案</el-button>
          <el-button plain :disabled="single" type="danger" @click="toRules()"
            v-hasPermi="['saasc:teamCase:writeRuleDivision']">规则分案</el-button>
          <el-button plain type="info" @click="openTpldivision"
            v-hasPermi="['saasc:case:importDataTeam']">模板分案</el-button>
          <el-button plain :disabled="single" type="primary" @click="handleExport()"
            v-hasPermi="['saasc:teamCase:reminder']">导出沟通记录</el-button>
          <el-button plain :disabled="single" type="warning" @click="downloadCase"
            v-hasPermi="['saasc:teamCase:case']">导出案件</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
          <el-tab-pane label="待上传调解材料" name="待上传调解材料" />
          <el-tab-pane label="调解号" name="生成调解号" />
          <el-tab-pane label="司法确认材料" name="司法确认材料" />
          <el-tab-pane label="司法确认送达" name="司法确认送达" />
          <el-tab-pane label="司法确认书" name="下发司法确认书" />
          <el-tab-pane label="全部" name="全部" />
        </el-tabs>
        <selectedAll ref="selectedAllRef" v-model:allQuery="allQuery" :selectedArr="selectedArr" :dataList="dataList"
          :cusTableRef="proxy.$refs.multipleTableRef">
          <template #content>
            <span class="case-data-list">
              客户数量：<i class="danger">{{ statistics.size || 0 }}</i>
            </span>
            <span class="case-data-list">
              初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
            </span>
            <span class="case-data-list">
              初始债权本金：<i class="danger">{{ numFilter(statistics.principal) }}</i>
            </span>
          </template>
        </selectedAll>

        <el-table v-loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" :data="dataList">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column v-if="columns[0].visible" label="案件ID" align="center" prop="caseId" width="80">
            <template #default="{ row, $index }">
              <div class="df-center">
                <el-tooltip v-if="row.labelContent" placement="top">
                  <template #content>{{ row.labelContent }}</template>
                  <case-label class="ml5" v-if="row.label && row.label != 7" :code="row.label" />
                </el-tooltip>
                <el-button type="text" @click="toDetails(row, $index)">{{ row.caseId }}</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="资产包名称" align="center" prop="packageName" width="120px" />
          <el-table-column v-if="columns[2].visible" label="转让方" align="center" prop="entrustingPartyName" width="120px"
            show-overflow-tooltip />
          <el-table-column v-if="columns[3].visible" label="被告" align="center" prop="clientName" width="120px" />
          <el-table-column v-if="columns[4].visible" label="手机号码" align="center" prop="clientPhone" width="110px" />
          <el-table-column v-if="columns[5].visible" label="身份证号码" align="center" prop="clientIdNum" width="180px" />
          <el-table-column v-if="columns[6].visible" label="户籍地" align="center" prop="clientCensusRegister"
            width="160px" show-overflow-tooltip />
          <el-table-column v-if="columns[7].visible" label="标的额" align="center" prop="remainingDue" width="110px" >
            <template #default="{ row }">
              <span>{{ numFilter(row.remainingDue) }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[8].visible" label="调解号" align="center" prop="mediationNum" width="110px">
            <template #default="{ row }">
              <Tooltip :content="row.mediationNum" :length="10" />
            </template>
          </el-table-column>
          <el-table-column v-if="columns[9].visible" label="司法送达链接" align="center" prop="publicityLink" width="120px">
            <template #default="{ row }">
              <Tooltip :content="row.publicityLink" :length="10" />
            </template>
          </el-table-column>
          <el-table-column v-if="columns[10].visible" label="跟进人员" align="center" prop="updateBy" width="110px" />
          <el-table-column v-if="columns[11].visible" label="最近一次跟进时间" align="center" prop="updateTime" width="160px" />
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <!-- 模板分案 -->
    <tplDivision ref="tplDivisionRef" />
    <!-- 案件操作 -->
    <handleDialog :checkedType="allQuery ? '本页选中' : '搜索结果全选'" :query="addFieldsRange(queryParams, rangfiles)"
      ref="handleDialogRef" @getList="getList" :destroy-on-close="true" :stay="false" />

    <!-- 标记案件 -->
    <leabelCaseVue @getList="getList" ref="leabelCaseRef" />

    <!-- 导出沟通记录 -->
    <exportDialog :query="addFieldsRange(queryParams, rangfiles)" :checkedType="allQuery ? '搜索结果全选' : '本页选中'"
      ref="exportdialogRef" />
  </div>
</template>

<script setup name="AppealCase">
import { exportCase } from "@/api/mediation/appealCase"
import exportDialog from "@/views/mediation/mediationTeam/dialogIndex/exportDialog.vue"
import leabelCaseVue from "@/views/mediation/mediationTeam/dialogIndex/leabelCase.vue";
import handleDialog from "@/views/mediation/mediationTeam/dialogIndex/handleDialog.vue";
import tplDivision from "@/views/case/dialogIndex/tplDivision";
import { getTeamTree, phoneMediationList, selectMediationWithMoney } from "@/api/team/appealCase";
import { formatParams } from "@/utils/common";
import { nextTick } from "vue";

//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const store = useStore()
const showSearch = ref(false)
//树结构
const allDept = ref([]);
const activeDept = ref(undefined)

//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const rangfiles = [];
const selectedArr = ref([]);
const ids = ref([]);
const single = ref(true);
const allQuery = ref(false);
const statistics = ref({
  size: 0,
  money: 0,
  principal: 0
})
const activeTab = ref("全部");
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '资产包名称', visible: true },
  { key: 2, label: '转让方', visible: true },
  { key: 3, label: '被告', visible: true },
  { key: 4, label: '手机号码', visible: true },
  { key: 5, label: '身份证号码', visible: true },
  { key: 6, label: '户籍地', visible: true },
  { key: 7, label: '标的额', visible: true },
  { key: 8, label: '调解号', visible: true },
  { key: 9, label: '司法送达链接', visible: true },
  { key: 10, label: '跟进人员', visible: true },
  { key: 11, label: '最近一次跟进时间', visible: true },
])
//获取列表
function getList() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ["待上传调解材料", "生成调解号", "司法确认材料", "司法确认送达", "下发司法确认书",]
  reqForm.smallStageList = String(reqForm.smallStageList)
  loading.value = true;
  phoneMediationList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}
getList();

//获取团队排名
function getTeamTreeData() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles)
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ["待上传调解材料", "生成调解号", "司法确认材料", "司法确认送达", "下发司法确认书",]
  reqForm.smallStageList = String(reqForm.smallStageList)
  delete reqForm.pageNum
  delete reqForm.pageSize
  getTeamTree(reqForm).then((res) => {
    allDept.value = res.data
  });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
  queryParams.value.stringDeptId = val
  activeDept.value = val
  queryParams.value.pageNum = 1
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}
//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//重置
function resetQuery() {
  activeDept.value = undefined
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// tab筛选
function tabChange() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//表格选择
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

// 表格是否可以选择
function checkSelectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId;
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ["待上传调解材料", "生成调解号", "司法确认材料", "司法确认送达", "下发司法确认书",]
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = { query: queryChange };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
}

//打开回收案件等案件操作
function opendialog(data) {
  let condition = allQuery.value;
  let optCaseList = data.row?.filter((item, index) => {
    return item.caseState == 0 || item.caseState == 1 || item.caseState == 3;
  });
  if (optCaseList.length == 0) return proxy.$modal.msgWarning("没有可以操作的案件");
  if (data.row?.length == 1) condition = false;
  proxy.$refs["handleDialogRef"].openDialog(data.type, optCaseList, condition);
}
// 标记案件
function openLabelCase() {
  let data = {};
  if (!allQuery.value) {
    data = {
      caseIds: selectedArr.value.map(item => item.caseId),
      condition: false,
    };
  } else {
    data = proxy.addFieldsRange(queryParams.value, rangfiles);
    data.pageNum = undefined;
    data.pageSize = undefined;
    data = {
      // caseIds: selectedArr.value.map(item => item.caseId),
      condition: true,
    };
  }
  proxy.$refs["leabelCaseRef"].opendialog(data);
}

//指定分案
function toPoint(row) {
  let obj = proxy.addFieldsRange(queryParams.value, rangfiles);
  if (row) {
    obj.ids = [row.caseId];
    obj.condition = false;
  } else {
    obj.ids = ids.value;
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: allQuery.value ? '搜索结果全选' : "本页选中",
    query: obj,
  };
  obj.condition = allQuery.value;
  obj.allQuery = allQuery.value;
  localStorage.setItem(`point/${time}`, JSON.stringify(objStorage));
  store.dispatch("tagsView/delCachedView", { name: "Point" });
  router.push({ path: `/mediationTeam/caseManage-outcase/point/434`, query: { time, path: route.path, distributeType: 2 } });
}

//规则分案
function toRules() {
  let obj = {};
  if (!allQuery.value) {
    obj = selectedArr.value;
  } else {
    obj = [proxy.addFieldsRange(queryParams.value, rangfiles)];
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: allQuery.value ? '搜索结果全选' : "本页选中",
    query: obj,
    queryParams: proxy.addFieldsRange(queryParams.value, rangfiles),
  };
  objStorage.queryParams.condition = allQuery.value;
  obj.condition = allQuery.value;
  obj.allQuery = allQuery.value;
  objStorage.queryParams.ids = ids.value;
  localStorage.setItem(`rules/${time}`, JSON.stringify(objStorage));
  store.dispatch("tagsView/delCachedView", { name: "Rules" });
  router.push({ path: `/mediationTeam/caseManage-outcase/rules/4243`, query: { time, path: route.path, distributeType: 2 } });
}

// 模板分案
function openTpldivision() {
  proxy.$refs["tplDivisionRef"].opendialog();
}

//导出沟通记录
function handleExport(row) {
  console.log(row);
  const caseIds = row ? [row.caseId] : selectedArr.value.map(item => item.caseId)
  const condition = row ? false : allQuery.value
  proxy.$refs["exportdialogRef"].opendialog(0, caseIds, condition);
}
//导出数据
function downloadCase() {
  proxy.$modal.loading("正在导出数据，请稍候...");
  const reqForm = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)));
  if (!allQuery.value) {
    reqForm.caseIds = ids.value;
    reqForm.allQuery = false;
  } else {
    reqForm.condition = true;
    reqForm.allQuery = true;
  }
  exportCase(reqForm).then((res) => {
    proxy.$modal.closeLoading();
    proxy.$modal.exportTip(res.data)
  }).catch((error) => {
    proxy.$modal.closeLoading();
  })
}

//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0, };
      reslove()
      return false
    }
    nextTick(() => {
      selectMediationWithMoney(getReqParams()).then((res) => {
        statistics.value = res.data;
      }).finally(() => reslove());
    })
  })
}
function getReqParams() {
  const reqParams = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)))
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.condition = allQuery.value
  reqForm.allQuery = allQuery.value;
  if (reqForm.condition) { 
    reqForm.ids && delete reqForm.ids;
  }
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ["待上传调解材料", "生成调解号", "司法确认材料", "司法确认送达", "下发司法确认书",]
  return reqForm
}
watch(() => [selectedArr.value, allQuery.value], () => {
  console.log(allQuery.value);
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })
</script>

<style scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>

<style lang="scss" scoped>
.side-edge {
  height: calc(100vh - 125px);
  padding: 0 !important;
  border-right: 2px solid #eee;
}

.head-container {
  color: #333;
  font-weight: bold;
}

.dept-list {
  color: #5a5a5a;
  cursor: pointer;
  height: 80vh;
  overflow: auto;

  .active {
    border-right: 2px solid #60b2ff;
    background-color: #e6f7ff;
  }

  .dept-item {
    width: 100%;
    line-height: 44px;
    padding-left: 20px;
  }

  .employ-item {
    width: 100%;
    line-height: 44px;
    height: 44px;
    padding-left: 20px;
  }
}
</style>