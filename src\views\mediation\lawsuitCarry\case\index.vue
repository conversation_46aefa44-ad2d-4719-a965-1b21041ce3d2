<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      :label-width="110"
      class="h-50"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="被告" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入被告"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="结案方式" prop="closedMode">
        <el-select
          v-model="queryParams.closedMode"
          placeholder="请选择结案方式"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in closeWayEnum"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="案件涉及">
        <el-input
          v-model="queryParams.involvedWith"
          placeholder="请输入案件涉及"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="queryParams.executiveTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          style="width: 240px"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="执行法院" prop="executiveCourt">
        <el-select
          v-model="queryParams.executiveCourt"
          placeholder="请选择执行法院"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额" prop="syYhPrincipal">
        <div class="range-scope" style="width: 240px">
          <el-input
            type="text"
            v-model="queryParams.amount1"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
          <span>-</span>
          <el-input
            type="text"
            v-model="queryParams.amount2"
            @blur="validatePrincipalRange"
            @input="(value) => value.replace(/[^\d]/g, '')"
            clearable
          />
        </div>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <div class="mb10 operation-list mt10" style="min-height: 26px">
      <el-button
        v-if="checkPermi([setPermiss('download')])"
        :disabled="single"
        type="primary"
        @click="downloadCase"
        >批量导出</el-button
      >
      <!-- <el-button
        v-if="checkPermi([setPermiss('keep')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('applyKeepRef')"
        >申请保全</el-button
      > -->
      <el-button
        v-if="checkPermi([setPermiss('transfer')]) && route.meta.query != 5"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('transferCaseRef')"
        >案件流转</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('execute')]) && route.meta.query == 1"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('batchExecuteRef')"
        >批量执行</el-button
      >
      <el-button
        v-if="
          checkPermi([setPermiss('createWirt')]) && ['1', '2'].includes(route.meta.query)
        "
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('batchImportWritRef')"
        >批量生成文书</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('sendNote')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('sendMessageRef')"
        >发送短信</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </div>

    <selectedAll
      v-model:allQuery="allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      class="mt10 mb10"
    >
      <template #content>
        <div class="selected-all-content ml10">
          <span>案件数量：</span>
          <i class="danger">{{ statistics.caseNum || 0 }}</i>
          <span class="ml10">标的额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.totalMoney || 0) }}
          </i>
          <span>执行金额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.principal || 0) }}
          </i>
        </div>
      </template>
    </selectedAll>
    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="100"
        v-if="columns[0].visible"
      >
        <template #default="{ row, $index }">
          <el-button
            :disable="!checkPermi([setPermiss('detail')])"
            type="text"
            @click="toDetails(row, $index)"
          >
            {{ row.caseId }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="被告"
        align="center"
        key="clientName"
        prop="clientName"
        :min-width="100"
        v-if="columns[1].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="手机号码"
        align="center"
        key="phone"
        prop="phone"
        :width="110"
        v-if="columns[2].visible"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.phone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
            <workPhoneVue :phoneNumber="row.phone" :caseId="row.caseId" :borrower="row.clientName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="标的额"
        align="center"
        key="remainingDue"
        prop="remainingDue"
        :width="120"
        v-if="columns[3].visible"
      >
        <template #default="{ row }">
          <span>{{
            numFilter(row.remainingDue)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="身份证号码"
        align="center"
        key="clientIdcard"
        prop="clientIdcard"
        :width="180"
        v-if="columns[4].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="户籍地"
        v-if="columns[5].visible"
        align="center"
        key="clientCensusRegister"
        show-overflow-tooltip
        prop="clientCensusRegister"
        :min-width="160"
      />
      <el-table-column
        label="执行状态"
        align="center"
        key="disposeStage"
        prop="disposeStage"
        :width="120"
        v-if="columns[6].visible"
      />
      <el-table-column
        label="结案标的额"
        align="center"
        key="concludeCaseAmount"
        prop="concludeCaseAmount"
        :width="120"
        v-if="columns[7].visible"
      >
        <template #default="{ row }">
          <span>{{
            numFilter(row.concludeCaseAmount)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="案件涉及"
        align="center"
        key="involvedWith"
        prop="involvedWith"
        :min-width="120"
        show-overflow-tooltip
        v-if="columns[8].visible"
      />
      <el-table-column
        label="被执行下落不明"
        align="center"
        key="isMissing"
        prop="isMissing"
        :min-width="120"
        :formatter="(row) => isNoEnum[row.isMissing]"
        v-if="columns[9].visible"
      />
      <el-table-column
        label="执行法院"
        align="center"
        key="executiveCourt"
        prop="executiveCourt"
        min-width="180px"
        v-if="columns[10].visible"
      />
      <el-table-column
        label="结案案由"
        align="center"
        key="closedReason"
        prop="closedReason"
        :min-width="120"
        v-if="columns[11].visible"
      />
      <el-table-column
        label="结案方式"
        align="center"
        key="closedMode"
        prop="closedMode"
        :width="110"
        v-if="columns[12].visible"
      />
      <el-table-column
        label="有无结案文书"
        align="center"
        key="isFile"
        prop="isFile"
        :width="120"
        :formatter="(row) => ({ 0: '是', 1: '否' }[row.isFile])"
        v-if="columns[13].visible"
      />
      <el-table-column
        label="跟进人员"
        align="center"
        key="follower"
        prop="follower"
        :width="100"
        v-if="columns[14].visible"
      />
      <el-table-column
        label="最近一次跟进时间"
        align="center"
        key="followUpTime"
        prop="followUpTime"
        sortable="followUpAst"
        :width="160"
        v-if="columns[15].visible"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <batchClose :getList="getList" ref="batchCloseRef" />
    <batchExecute :getList="getList" ref="batchExecuteRef" />
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <transferCase :getList="getList" ref="transferCaseRef" />
    <batchImportWrit :getList="getList" ref="batchImportWritRef" />
    <sendMessage :getList="getList" ref="sendMessageRef" />
  </div>
</template>

<script setup name="LawsuitCarry">
import { checkPermi } from "@/utils/permission";
import batchImportWrit from "@/views/mediation/dialog/batchImportWrit";
import batchClose from "@/views/mediation/dialog/batchClose";
import batchExecute from "./dialog/batchExecute";
import applyKeep from "@/views/mediation/dialog/applyKeep";
import transferCase from "@/views/mediation/dialog/transferCase";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import { ElMessage } from "element-plus";
import { formatParams } from "@/utils/common";
import { closeWayEnum, pageTypeEnum, isNoEnum } from "@/utils/enum";
import { getExecuteList, totalMoneyExecute } from "@/api/mediation/lawsuitCarry";
import { getCourtOptions } from "@/api/common/common";

//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const store = useStore();
const pageType = "lawsuitCarry";
const courtOptions = ref([]);
//表格配置数据
const loading = ref(false);
const allQuery = ref(false);
const total = ref(0);
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
//需要拆分的字段
const rangFields = ["executiveTime"];
const stageObj = {
  1: "执行立案",
  2: "判决执行",
  3: "执行完成",
  4: "执行撤案",
  5: "终本案件",
};
const { queryParams } = toRefs(data);
//表单配置信息
const showSearch = ref(false);
// 列显隐信息
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "被告", visible: true },
  { key: 2, label: "手机号码", visible: true },
  { key: 3, label: "标的额", visible: true },
  { key: 4, label: "身份证号码", visible: true },
  { key: 5, label: "户籍地", visible: true },
  { key: 6, label: "执行状态", visible: true },
  { key: 7, label: "结案标的额", visible: true },
  { key: 8, label: "案件涉及", visible: true },
  { key: 9, label: "被执行下落不明", visible: true },
  { key: 10, label: "执行法院", visible: true },
  { key: 11, label: "结案案由", visible: true },
  { key: 12, label: "结案方式", visible: true },
  { key: 13, label: "有无结案文书", visible: true },
  { key: 14, label: "跟进人员", visible: true },
  { key: 15, label: "最后跟进时间", visible: true },
]);

//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = [];
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.type = 1;
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.condition = allQuery.value;
  getExecuteList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
provide("getList", Function, true);

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { amount1, amount2 } = queryParams.value;
  // 检测输入是否是数字
  if (amount1 && !Number.isFinite(Number(amount1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount1 = undefined;
  }
  if (amount2 && !Number.isFinite(Number(amount2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
  if (!amount1 || !amount2) return;

  const principal1 = parseFloat(amount1);
  const principal2 = parseFloat(amount2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.amount2 = undefined;
  }
};

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const newAllQuery = row ? false : allQuery.value;
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds };
  const data = {
    query,
    ...row,
    caseIds,
    allQuery: newAllQuery,
    pageType,
    isBatchSend: 1,
    isLitigationExecute: 1,
  };
  data.condition = row ? false : allQuery.value;
  data.oneStatus = "诉讼执行";
  proxy.$refs[refName].openDialog(data);
}
//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      disposeStage: stageObj[route.meta.query],
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      type: 1,
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: stageObj[route.meta.query] };
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  getList();
}

//选择列表
function handleSelectionChange(selection) {
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}
// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery);
  reqForm.condition = allQuery.value;
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.type = 1;
  return reqForm;
}
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      totalMoneyExecute(getReqParams())
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.concludeCaseAmount,
          };
        })
        .finally(() => reslove());
    });
  });
}
//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

//导出数据
function downloadCase() {
  proxy.downloadforjson(
    "/execute-case/exportWithExecuteCase",
    getReqParams(),
    `诉讼执行_${+new Date()}.xlsx`
  );
}

const htrxCall = computed(() => store.getters.htrxCall);
// 设置权限符
function setPermiss(val) {
  const permissBtn = {
    1: "lawsuitCarry:executeCase:",
    2: "lawsuitCarry:adJudication:",
    3: "lawsuitCarry:complete:",
    4: "lawsuitCarry:withdrawCase:",
    5: "lawsuitCarry:finalCase:",
  };
  return `${permissBtn[route.meta.query]}${val}`;
}
getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}
watch(
  () => route,
  () => {
    resetQuery();
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: #9fccdb;
  display: block;
}

.case-tips {
  font-size: 14px;
  background-color: #cce7fa;
  padding: 20px;
  color: #409eff;

  P {
    margin: 0;
    display: block;
  }

  p:nth-child(2) {
    display: inline;
  }
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
