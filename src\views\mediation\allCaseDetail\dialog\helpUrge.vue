<template>
  <el-dialog
    title="申请协助调解"
    v-model="open"
    width="960px"
    :before-close="cancel"
    append-to-body
  >
    <selectUser
      v-model:selected="expeditingRecord"
      :multiple="false"
      ref="selectUserRef"
    />
    <el-form
      class="mt10"
      :model="form"
      :rules="rules"
      ref="formRef"
      label-position="top"
    >
      <el-form-item label="申请原因" prop="reason">
        <el-input
          type="textarea"
          v-model="form.reason"
          maxlength="300"
          show-word-limit
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import selectUser from "@/components/selectUser/index";
import { applyHandle } from "@/api/mediation/allCaseDetail";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
const caseId = inject("caseId");
const open = ref(false);
const loading = ref(false);
const expeditingRecord = ref([]);
const data = reactive({
  form: {},
  rules: {
    reason: [{ required: true, message: "请填写申请原因", trigger: "blur" }],
  },
});

const { form, rules } = toRefs(data);

//打开
function opendialog(data) {
  reset();
  Object.assign(form.value, data);
  open.value = true;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
    caseId: caseId,
    applyTarget: undefined,
  };
}

//取消
function cancel() {
  reset();
  loading.value = false;
  proxy.$refs["selectUserRef"].clearSelected();
  open.value = false;
}

//提交
function submit() {
  console.log(expeditingRecord.value);
  if (expeditingRecord.value.length === 0) {
    proxy.$modal.msgWarning("请选择业务员！");
    return;
  }
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      // expeditingRecord.value
      let reqForm = {
        approveCode: "teamwork",
        approveData: {}
      }
      form.value.applyTarget = expeditingRecord.value[0].id;
      Object.assign(reqForm.approveData, form.value);
      applyHandle(reqForm)
        .then(() => {
          proxy.$modal.msgSuccess("申请提交成功");
          cancel();
          emit("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped></style>
