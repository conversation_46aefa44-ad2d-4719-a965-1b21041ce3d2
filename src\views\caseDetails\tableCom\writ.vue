<template>
    <div>
        <div class="mb10 mt10" style="text-align:right;margin-right:50px">
            <el-button type="primary" :disabled="urls.length == 0"
                @click="handleBatchDownload(`文书_${+new Date()}.zip`)">批量下载</el-button>
        </div>
        <el-table :data="dataList" max-height="350px" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="序号" prop="id" width="80" key="id" align="center">
                <template #default="{ $index }">
                    <div> {{ $index + 1 }} </div>
                </template>
            </el-table-column>
            <el-table-column label="文书类型" prop="classifyLabel" key="classifyLabel" align="center" />
            <el-table-column label="文书" prop="templateName" key="templateName" align="center" />
            <el-table-column label="上传人员" prop="createBy" key="createBy" align="center" width="120px" />
            <el-table-column label="操作">
                <template #default="{ row }">
                    <el-button type="text" link v-if="isPackage(row.signPreviewUrl)"
                        @click="previewSigntrue(row.signPreviewUrl)">预览</el-button>
                    <el-button type="text" link v-if="row.signPreviewUrl"
                        @click="handleDownload(row.signPreviewUrl, `${row.templateName}.pdf`)">下载</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script setup>
import { selectWrit } from "@/api/caseDetail/detail";
const route = useRoute()
const props = defineProps({
    urls: { type: Array, default: [] },
    previewSigntrue: { type: Function },
    handleDownload: { type: Function },
    handleBatchDownload: { type: Function },
    handleSelectionChange: { type: Function },
    isPackage: { type: Function },
})
const dataList = ref([])
const total = ref(0)
//获取文书列表
getWritChange()
function getWritChange() {
    let req = { caseId: route.params.caseId };
    selectWrit(req).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}

</script>