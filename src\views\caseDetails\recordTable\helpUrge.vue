<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="协催时间" prop="applyDate" key="applyDate" align="center" />
    <el-table-column label="申请人" prop="applicant" key="applicant" align="center" />
    <el-table-column label="申请原因" prop="reason" key="reason" align="center">
      <template #default="{ row }">
        <el-tooltip placement="top">
          <template #content>
            <p style="max-width: 300px">{{ row.reason }}</p>
          </template>
          <div>
            <span>{{ row.reason?.length > 15 ? `${row.reason?.substring(0, 15)}...` : row.reason }}</span>
          </div>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column label="协催人" prop="helper" key="helper" align="center" />
    <el-table-column label="协催状态" prop="state" key="state" align="center" :formatter="stateChange" />
    <el-table-column label="协催内容" prop="assistContent" key="assistContent" align="center" />
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import { selectAssistRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  params: { type: Object, default: {} },
  handleDownload: { type: Function },
});
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: route.params.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm, ...props.params }
  loading.value = true;
  selectAssistRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();


//协催状态 0-待协催，1-持续协催，2-完成协催，3-终止协催
function stateChange(row) {
  const arr = ["待协催", "持续协催", "完成协催", "终止协催"];
  return arr[row.state];
}

defineExpose({ getList })
</script>

<style scoped></style>
