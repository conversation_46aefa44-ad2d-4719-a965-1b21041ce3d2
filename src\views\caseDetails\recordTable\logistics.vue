<template>
    <div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column align="center" prop="createTime" key="createTime" label="导入时间" />
            <el-table-column align="center" prop="state" key="state" label="物流状态"
                :formatter="row => logisticsStatusEnum[row.state]" />
            <el-table-column align="center" prop="expressNumber" key="expressNumber" label="物流单号" />
            <el-table-column align="center" prop="companyCode" key="companyCode" label="物流编码" />
            <el-table-column align="center" prop="address" key="address" label="收件地址" />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>
<script setup>
import { selectExpressRecord } from "@/api/caseDetail/records";
import { logisticsStatusEnum } from "@/utils/enum";
const route = useRoute()
const props = defineProps({
    params: { type: Object, default: {} },
    handleDownload: { type: Function },
});
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        registerType: 2,
        caseId: route.params.caseId,
    },
});

const loading = ref(false);
const dataList = ref([])
const total = ref(0)
const { queryParams } = toRefs(data);
getList()
function getList() {
    loading.value = true;
    let reqForm = JSON.parse(JSON.stringify(queryParams.value))
    reqForm = { ...reqForm, ...props.params }
    reqForm.webSide = 2;
    selectExpressRecord(reqForm).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    }).finally(() => (loading.value = false));
}
defineExpose({ getList })
</script>