<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="102px"
    >
      <el-form-item label="检测号码" prop="mobile">
        <el-input
          style="width: 300px"
          v-model="queryParams.mobile"
          @input="(value) => (queryParams.mobile = value.replace(/[^\d]/g, ''))"
          placeholder="请输入检测号码"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="检测结果" prop="status">
        <el-select
          v-model="queryParams.status"
          style="width: 300px"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          filterable
        >
          <!-- 1 正常 2 空号 3 通话中 4 不在网(空号) 5 关机 7 在网但不可用 13 停机 10 未知 9 服务器异常 12 不存在的号码 -->
          <el-option label="正常" :value="1" />
          <el-option label="空号" :value="2" />
          <el-option label="通话中" :value="3" />
          <el-option label="不在网(空号)" :value="4" />
          <el-option label="关机" :value="5" />
          <el-option label="在网但不可用" :value="7" />
          <el-option label="停机" :value="13" />
          <el-option label="未知" :value="10" />
          <el-option label="服务器异常" :value="9" />
          <el-option label="不存在的号码" :value="12" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="detectionStatus">
        <el-select
          v-model="queryParams.detectionStatus"
          style="width: 300px"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          filterable
        >
          <el-option label="检测中" :value="0" />
          <el-option label="检测完成" :value="1" />
          <el-option label="检测失败" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="antiShake(handleQuery)"
          >查询</el-button
        >
        <el-button @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计数据 -->
    <div class="custom-tables">
      <div class="custom-table">
        <h3
          style="
            font-size: 14px;
            padding-left: 20px;
            border-bottom: 1px solid #e8e8e8;
            line-height: 34px;
          "
        >
          实时检测结果
        </h3>
        <div class="item" v-for="item in statisticsData" :key="item.status">
          <div class="title">
            {{ item.label }}
          </div>
          <div class="info">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <el-table :data="dataList" v-loading="loading">
      <el-table-column
        label="检测批次"
        prop="batchName"
        key="batchName"
        align="center"
      />
      <el-table-column
        label="检测号码"
        prop="mobile"
        key="mobile"
        align="center"
      />
      <el-table-column
        label="状态"
        prop="detectionStatus"
        key="detectionStatus"
        :formatter="
          (row) => ['检测中', '检测完成', '检测失败'][row.detectionStatus]
        "
        align="center"
      />
      <el-table-column
        label="检测结果"
        prop="status"
        key="status"
        align="center"
        :formatter="changeStatus"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import {
  cleanBatchResultList,
  cleanBatchResultStatistics,
} from "@/api/appreciation/phoneClean";

const { proxy } = getCurrentInstance();

const route = useRoute();

const dataList = ref([]);
const total = ref(0);
const loading = ref(false);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  batchId: route.params.id,
  mobile: undefined,
  status: undefined,
  detectionStatus: undefined,
});
const reqForm = ref({});

// 获取统计数据
// 1 正常 2 空号 3 通话中 4 不在网(空号) 5 关机 7 在网但不可用 13 停机 10 未知 9 服务器异常 12 不存在的号码
const statisticsData = ref([
  { label: "正常", status: 1, count: 0 },
  { label: "空号", status: 2, count: 0 },
  { label: "通话中", status: 3, count: 0 },
  { label: "不在网(空号)", status: 4, count: 0 },
  { label: "关机", status: 5, count: 0 },
  { label: "在网但不可用", status: 7, count: 0 },
  { label: "停机", status: 13, count: 0 },
  { label: "未知", status: 10, count: 0 },
  { label: "服务器异常", status: 9, count: 0 },
  { label: "不存在的号码", status: 12, count: 0 },
]);

// 获取统计
function getStatistics() {
  statisticsData.value = [
    { label: "正常", status: 1, count: 0 },
    { label: "空号", status: 2, count: 0 },
    { label: "通话中", status: 3, count: 0 },
    { label: "不在网(空号)", status: 4, count: 0 },
    { label: "关机", status: 5, count: 0 },
    { label: "在网但不可用", status: 7, count: 0 },
    { label: "停机", status: 13, count: 0 },
    { label: "未知", status: 10, count: 0 },
    { label: "服务器异常", status: 9, count: 0 },
    { label: "不存在的号码", status: 12, count: 0 },
  ];
  cleanBatchResultStatistics(reqForm.value).then((res) => {
    if (res.data.length > 0) {
      for (let i = 0; i < res.data.length; i++) {
        for (let j = 0; j < statisticsData.value.length; j++) {
          if (res.data[i].status === statisticsData.value[j].status) {
            statisticsData.value[j].count = res.data[i].count;
            break;
          }
        }
      }
    }
  });
}

function getList() {
  let form = JSON.parse(JSON.stringify(queryParams.value));
  if (form.status && form.status.length > 0) {
    form.status = form.status.join(",");
  }
  if (form.detectionStatus && form.detectionStatus.length > 0) {
    form.detectionStatus = form.detectionStatus.join(",");
  }
  reqForm.value = form;
  loading.value = true;
  getStatistics();
  cleanBatchResultList(form)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    batchId: route.params.id,
    mobile: undefined,
    status: undefined,
    detectionStatus: undefined,
  };
  getList();
}

function changeStatus(row) {
  for (let j = 0; j < statisticsData.value.length; j++) {
    if (row.status === statisticsData.value[j].status) {
      return statisticsData.value[j].label;
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tables {
  padding-bottom: 30px;
  overflow: hidden;
  line-height: 34px;
}

.custom-table {
  float: left;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.custom-table .item {
  float: left;
  width: auto;
  padding: 0 10px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
}

.custom-table .item:last-child {
  border-right: 0;
}

.custom-table .item .title {
  color: #888888;
  border-bottom: 1px solid #e8e8e8;
}

.custom-table .item .info {
  color: #3f3f3f;
  font-size: 18px;
  font-weight: bold;
}

.custom-table-status {
  float: right;
}
</style>
