<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="记录时间" prop="createTime" key="createTime" align="center" />
    <el-table-column label="收费类型" prop="costType" key="costType" align="center" />
    <el-table-column label="收费金额" prop="costAmt" key="costAmt" align="center">
      <template #default="{ row }">
        <span>{{ numFilter(row.costAmt) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="收费日期" prop="costDate" key="costDate" align="center" />
    <el-table-column label="付款方式" prop="payMethod" key="payMethod" align="center" />
    <el-table-column label="收费对象" prop="feeRecipient" key="feeRecipient" align="center" />
    <el-table-column label="入账单位" prop="accountUnit" key="accountUnit" align="center" />
    <el-table-column label="收费回执" prop="receiptUrl" align="center" width="170px">
      <template #default="{ row }">
        <div>
          <el-button type="text" v-if="row.receiptFileName">
            <Tooltip :content="row.receiptFileName" :length="5" />
          </el-button>
          <el-button type="text" v-if="row.receiptFileName" @click="handlePreviewImg(row)">预览</el-button>
          <el-button type="text" v-if="row.receiptFileName"
            @click="handleDownload(row.receiptUrl, row.receiptFileName)">下载</el-button>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="备注" prop="remark" key="remark" align="center">
      <template #default="{ row }">
        <Tooltip :content="row.remark" :length="8" />
      </template>
    </el-table-column>
    <el-table-column fixed="right" width="160" label="操作">
      <template #default="{ row }" v-if="route.query?.type == 'myCase' || route.query?.type == 'record'">
        <el-button type="text" @click="editCharge(row)"> 修改 </el-button>
        <el-button type="text" @click="delCharge(row)"> 删除 </el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="preivew-box">
    <el-image v-show="previewSrc" ref="previewImgRef" fit="cover" hide-on-click-modal initial-index="1" teleported
      :preview-teleported="true" :src="previewSrc" @close="cancelPreivewImg" :preview-src-list="previewSrcList" />
    <addChagre ref="addChagreRef" />
  </div>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import addChagre from '@/views/caseDetails/dialog/addChagre.vue';
import { delCost } from "@/api/caseDetail/detail";
import { selectCostRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const route = useRoute()
const props = defineProps({
  params: { type: Object, default: {} },
  handleDownload: { type: Function },
});
const previewSrcList = ref([])
const previewSrc = ref(undefined)
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: route.params.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm, ...props.params }
  loading.value = true;
  selectCostRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();
//删除
function delCharge(row) {
  let req = { id: row.id }
  proxy.$modal.confirm(`批量删除选中的信息？是否继续？`).then(() => {
    delCost(req).then((res) => {
      proxy.$modal.msgSuccess('操作成功！')
      emits('getList');
    }).catch(() => {
      emits('getList');
    })

  }).catch(() => {
    emits('getList');
  })
}
// 编辑
function editCharge(row) {
  proxy.$refs['addChagreRef'].opendialog(JSON.parse(JSON.stringify(row)))
}

// 关闭预览图片
function cancelPreivewImg() {
  previewSrcList.value = []
  previewSrc.value = undefined
}

// 预览图片
function handlePreviewImg(row) {
  previewSrcList.value = [row.receiptUrl]
  previewSrc.value = row.receiptUrl
  nextTick(() => {
    proxy.$refs['previewImgRef']?.clickHandler()
  })
}

defineExpose({ getList })
</script>

<style lang="scss" scoped>
.perivew-img {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.preivew-box {
  width: 1px;
  height: 1px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
