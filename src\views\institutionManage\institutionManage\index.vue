<template>
    <div class="app-container">
        <div class="app-content">
            <div class="app-query">
                <el-form :model="queryParams" inline ref="queryRef">
                    <el-form-item prop="courtName" label="法院名称">
                        <el-input style="width:280px" v-model="queryParams.courtName" placeholder="请输入法院名称" />
                    </el-form-item>
                    <el-form-item>
                        <el-button :loading="loading" icon="Search" @click="antiShake(handleQUery)"
                            type="primary">搜索</el-button>
                        <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQUery)">重置</el-button>
                    </el-form-item>
                </el-form>
                <div class="operation-btn mb20">
                    <el-button plain type="primary" icon="Plus" :loading="loading"
                        v-if="checkPermi(['institutionManage:index:add'])" @click="handleCourtInfo()">添加法院</el-button>
                </div>
            </div>
            <el-table :data="dataList" :loading="loading">
                <el-table-column align="center" label="法院名称" prop="courtName" />
                <el-table-column align="center" label="法院地址" prop="address">
                    <template #default="{ row }">
                        <el-tooltip effect="dark" placement="top">
                            <template #content>
                                <p style="max-width: 300px">{{ row.address }}</p>
                            </template>
                            <span v-if="row.address">
                                {{ row.address.length < 12 ? row.address : `${row.address.substring(0, 12)}...` }} </span>
                                    <span v-else></span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="联系电话" prop="phone" />
                <el-table-column align="center" label="关联文书" prop="number">
                    <template #default="{ row }">
                        <el-button @click="handleWrit(row)" type="text">{{ row.number }}</el-button>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="操作">
                    <template #default="{ row }">
                        <div>
                            <el-button :loading="loading" type="text" v-if="checkPermi(['institutionManage:index:delete'])"
                                @click="handleCourtDel(row)">删除</el-button>
                            <el-button :loading="loading" type="text" v-if="checkPermi(['institutionManage:index:update'])"
                                @click="handleCourtInfo(row)">修改</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination-area">
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>
        <institutionInfo :getList="getList" ref="courtInfoRef" />
        <writList ref="writListRef" :openPreivewPdf="openPreivewPdf" />
        <preivewPdf ref="preivewPdfRef" />
    </div>
</template>
<script setup name="InstitutionManage">
import preivewPdf from './dialog/preivewPdf';
import writList from './dialog/writList';
import { checkPermi } from "@/utils/permission";
import institutionInfo from './dialog/institutionInfo';
import { getCourtList, delCourt } from "@/api/institutionManage/institutionManage";
const { proxy } = getCurrentInstance()
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        courtName: undefined,
        // jurisdiction: undefined,
        // jurisdictionEconomize: undefined,
        // jurisdictionMarket: undefined,
    }
})
const dataList = ref([])
const loading = ref(false)
const total = ref(0)
const { queryParams } = toRefs(data)

// 打开关联文书
function handleWrit(row) {
    proxy.$refs['writListRef'].openDialog({ courtId: row.id })
}

// 打开预览
function openPreivewPdf(row) {
    proxy.$refs['preivewPdfRef'].openDialog(row)
}

// 机构信息
function handleCourtInfo(row) {
    let title = row ? '编辑法院' : '添加法院'
    proxy.$refs['courtInfoRef'].openDialog({ title, row })
}
// 删除
function handleCourtDel(row) {
    proxy.$modal.confirm(`是否删除${row.courtName}？`).then(() => {
        loading.value = true
        nextTick(() => {
            if (loading.value) {
                delCourt({ id: row.id }).then(res => {
                    if (res.code == 200) {
                        getList()
                        proxy.$modal.msgSuccess('操作成功！')
                        return false
                    }
                    proxy.$modal.msgWarning(res.msg)
                }).finally(() => { loading.value = false })
            }
        })
    })
}
// 重置
function resetQUery() {
    proxy.resetForm('queryRef')
    queryParams.value = {
        ...queryParams.value,
        jurisdictionEconomize: undefined,
        jurisdictionMarket: undefined,
    }
    getList()
}
// 搜索
function handleQUery() {
    queryParams.value.pageNum = 1
    getList()
}
getList()
// 获取数据
function getList() {
    loading.value = true
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    getCourtList(reqForm).then(res => {
        dataList.value = res.rows
        total.value = res.total
    }).finally(() => {
        loading.value = false
    })
}
// 选择省市
function jurisdictionChange(val) {
    if (val) {
        queryParams.value.jurisdictionEconomize = val[0]
        queryParams.value.jurisdictionMarket = val[1]
    }
}

// 格式地区
function formArea(province, city) {
    return province == city ? province : `${province}${city ? city : ''}`
}
</script>