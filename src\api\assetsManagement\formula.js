import request from '@/utils/request'

//计算公式列表
export function fmlList(query) {
  return request({
    url: '/project/fml/list',
    method: 'get',
    params: query
  })
}

// 计算公式下拉
export function fmlSelect() {
  return request({
    url: '/project/fml/option',
    method: 'get'
  })
}

// 新增计算公式
export function addFml(data) {
  return request({
    url: '/project/fml/add',
    method: 'post',
    data
  })
}

// 修改计算公式
export function updateFml(data) {
  return request({
    url: '/project/fml/edit',
    method: 'put',
    data
  })
}

// 删除计算公式
export function deleteFml(data) {
  return request({
    url: '/project/fml/remove',
    method: 'delete',
    data
  })
}

// 获取计算结果
export function getFmlResult(data) {
  return request({
    url: '/project/fml/calc',
    method: 'post',
    data
  })
}