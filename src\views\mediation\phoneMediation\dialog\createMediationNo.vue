<template>
    <el-dialog v-model="open" append-to-body @before-close="cancel" width="400px" :title="title">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="90px">
            <el-form-item v-if="route.meta.query == 2" prop="mediationNum" label="调解号">
                <el-input v-model="form.mediationNum" :maxlength="100" show-word-limit placeholder="请输入调解号" />
            </el-form-item>
            <el-form-item v-if="route.meta.query == 4" prop="publicityLink" label="填写链接">
                <el-input v-model="form.publicityLink" :maxlength="200" show-word-limit placeholder="请输入链接" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-center">
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="submit" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { addPhoneLink, addPhoneNum } from '@/api/mediation/phoneMediation'
const props = defineProps({
    getList: { type: Function }
})

const { proxy } = getCurrentInstance()
const route = useRoute()
const open = ref(false)
const loading = ref(false)
const data = reactive({
    form: {},
    rules: {
        mediationNum: [{ required: true, message: '请输入', trigger: 'blur' }],
        publicityLink: [{ required: true, message: '请输入', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)

// 提交
function submit() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            const reqApiObj = { 2: addPhoneNum, 4: addPhoneLink }
            if (reqApiObj[route.meta.query]) {
                loading.value = true
                const reqForm = JSON.parse(JSON.stringify(form.value))
                reqApiObj[route.meta.query](reqForm).then(res => {
                    if (res.code == 200) {
                        props.getList && props.getList()
                        proxy.$modal.msgSuccess('操作成功！')
                        cancel()
                    }
                }).finally(() => loading.value = false)
            }
        }
    })
}

function openDialog(data) {
    form.value = {
        ...data.query,
        caseIds: data.caseIds,
        mediationNum: data.mediationNum,
        publicityLink: data.publicityLink,
    }
    open.value = true
}

function cancel() {
    proxy.resetForm('formRef')
    form.value = {}
    open.value = false
}
const title = computed(() => ({ 2: '生成调解号', 4: '司法确认送达' }[route.meta.query]))
defineExpose({ openDialog })
</script>