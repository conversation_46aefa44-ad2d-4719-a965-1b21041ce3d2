<template>
  <div class="app-container">
    <div class="app-container-item">
      <div class="top">
        <div class="title">{{ dataStatistics.taskName }}</div>
        <div>
          <el-button
            v-if="dataStatistics.status == 4 || dataStatistics.status == 1"
            @click="executeTaskHandle"
            >启动</el-button
          >
          <el-button
            v-if="dataStatistics.status == 2"
            @click="suspendTaskHandle"
            >暂停</el-button
          >
        </div>
      </div>
      <div class="contain mt20">
        <div class="left">
          <el-progress type="circle" :percentage="dataStatistics.reachability" />
          <div class="task-state">{{ statusFor(dataStatistics.status) }}</div>
          <div class="rate">
            接听率
            <el-tooltip
              effect="customized"
              content="接听量÷呼叫总数x100%"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </div>
        <div class="right">
          <div class="progress">
            <div>任务进度：</div>
            <el-progress :percentage="taskProgress" :stroke-width="15" />
          </div>
          <el-table
            class="mt20 mb20"
            v-loading="loading"
            :data="dataTaskProgress"
            border
            :header-cell-style="{
              background: '#EEEFF4',
              color: '#888888',
              width: '100%',
            }"
          >
            <el-table-column
              label="外呼数量"
              prop="total"
              key="total"
              align="center"
            />
            <el-table-column
              label="已呼叫"
              prop="calledAlready"
              key="calledAlready"
              align="center"
            />
            <el-table-column
              label="已接通"
              prop="connected"
              key="connected"
              align="center"
            />
            <el-table-column
              label="未接通"
              prop="notConnected"
              key="notConnected"
              align="center"
            />
            <el-table-column
              label="通话总时长"
              prop="totalCallTime"
              key="totalCallTime"
              align="center"
            />
            <el-table-column
              label="平均时长"
              prop="averageCallTime"
              key="averageCallTime"
              align="center"
            />
          </el-table>
        </div>
      </div>
    </div>

    <div class="app-container-item">
      <taskData :data="dataStatistics" />
    </div>

    <!-- <div class="app-container-item">
      <div class="top">
        <div class="title">
          外呼效果
          <div class="tip">
            <span
              >接通量:接听+漏接＋未接听（呼叫到达被叫侧）。
              接通率：接通量÷呼叫总数x100%。
            </span>
            <span>未接通:黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。</span>
            <span
              >接听量：客户和坐席都接听。 接听率：接听量÷呼叫总数x100%。</span
            >
            <span
              >未接听：呼叫到达被叫侧，客户未接。（挂断+通话中+无人接听+停机+忙线+关机）</span
            >
            <span>漏接：客户已接听，坐席未接听。</span>
          </div>
        </div>
      </div>
      <div class="contain mt20">
        <div
          class="callout-effect"
          v-for="(item, index) in callData"
          :key="index"
        >
          <div>{{ item.label }}</div>
          <div class="callout-effect-value">
            {{
              item.type == "rate"
                ? (dataStatistics[item.value] ?? "--") + "%"
                : dataStatistics[item.value] ?? "--"
            }}
          </div>
        </div>
      </div>
    </div> -->

    <div class="app-container-item">
      <div class="top">
        <div class="title">数据列表</div>
        <div>
          <el-button @click="exportTaskCdrHandle">批量导出</el-button>
        </div>
      </div>
      <el-tabs class="mt10 mb10" v-model="activeTab" @tab-click="tabChange">
        <el-tab-pane label="已接听" name="1" :disabled="tabLoading">
        </el-tab-pane>
        <el-tab-pane label="未接通" name="2" :disabled="tabLoading">
        </el-tab-pane>
        <el-tab-pane label="全部" name="all" :disabled="tabLoading">
        </el-tab-pane>
      </el-tabs>
      <el-table
        v-loading="tabLoading"
        :data="dataList"
        ref="multipleTableRef"
        @selection-change="idhandleSelectionChange"
        :header-cell-style="{
          background: '#EEEFF4',
          color: '#888888',
          width: '100%',
        }"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="被叫号码"
          align="center"
          prop="callTo"
          :width="125"
        />
        <el-table-column label="通话时间" align="center" prop="callTime" />
        <el-table-column label="主叫号码" align="center" prop="callFrom" />
        <el-table-column label="通话状态" align="center" prop="connectFlag">
          <template #default="{ row }">
            {{ callStatusFor(row.connectFlag) }}
          </template>
        </el-table-column>
        <el-table-column label="通话时长" align="center" prop="agentDuration">
          <template #default="{ row }"> {{ row.agentDuration }} 秒 </template>
        </el-table-column>
        <el-table-column label="短信发送状态" align="center" prop="smsStatus">
          <template #default="{ row, $index }">
            <el-popover
              v-if="row.smsStatus == 3"
              placement="bottom"
              :width="550"
              :ref="`popover-${$index}`"
              trigger="click"
            >
              <template #reference>
                <el-button type="text">{{ changeSMS(row) }}</el-button>
              </template>
              <div>失败原因：{{ row.result }}</div>
            </el-popover>
            <span v-else>
              {{ changeSMS(row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="回复内容" align="center" prop="smsContent">
          <template #default="{ row }">
            <span>{{ row.smsContent || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column width="400" label="操作">
          <template #default="scope">
            <!-- <el-button type="text" :disabled="!scope.row.recording">播放</el-button> -->
            <div class="play-button">
              <musicPlayer
                v-if="scope.row.recording"
                :ref="`audioRef${scope.$index}`"
                @stopCheck="stopCheck(scope.$index)"
                :audioSrc="scope.row.recording"
                :durationOrigin="scope.row.agentDuration"
              />
            </div>
            <div class="download-button" v-if="scope.row.recording">
              <el-button type="text" @click="downVideo(scope.row.recording)"
                >下载</el-button
              >
            </div>
            <el-button type="text" @click="removeTask(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="position: relative">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>
  
<script setup name="voiceTaskResultsReport">
import taskData from "../table/taskData";
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { exportTaskCdr } from "@/api/appreciation/callOut";
import {
  aiCallTaskStatistics,
  aiCallTaskDataList,
  aiExecuteTask,
  aiSuspendTask,
  deleteAiTask,
} from "@/api/appreciation/voiceTask";

//全局数据
const { proxy } = getCurrentInstance();

const route = useRoute();
const router = useRouter();
const activeTab = ref("1");
const dataList = ref([]);
const taskId = ref(undefined);
const dataStatistics = ref([]);
const dataTaskProgress = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    connectFlag: undefined,
    id: undefined,
  },
});

const { queryParams } = toRefs(data);

const callData = [
  { label: "接通量", value: "throughput" },
  { label: "接通率", value: "reachability", type: "rate" },
  { label: "接听量", value: "answeringVolume" },
  { label: "接听率", value: "answerRate", type: "rate" },
  { label: "漏接", value: "fumble" },
  { label: "未接听", value: "noAnswer" },
  { label: "未接通", value: "notConnected" },
  { label: "停机", value: "shutDownCount" },
  { label: "关机", value: "powerOffCount" },
  { label: "正忙", value: "beBusyCount" },
  { label: "通话中", value: "onTheLineCount" },
  { label: "无人接听", value: "noAnswerCount" },
  { label: "空号", value: "vacantNumberCount" },
  { label: "无法接通", value: "unableToConnectCount" },
  { label: "其他", value: "otherQuantity" },
];

const tabLoading = ref(false);

const getList = () => {
  // console.log(activeTab.value);
  queryParams.value.connectFlag = ["all", "100"].includes(activeTab.value)
    ? undefined
    : activeTab.value;
  queryParams.value.connectFlag2 = ["100"].includes(activeTab.value)
    ? "0"
    : undefined;
  queryParams.value.id = taskId.value;
  tabLoading.value = true;
  aiCallTaskDataList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      dataList.value.forEach((item) => {
        if (item.recording) {
          item.recording = item.serviceHost + item.recording;
        }
      });
      total.value = res.total;
    })
    .finally(() => {
      tabLoading.value = false;
    });
};

const tabChange = (tab) => {
  getList();
};

const total = ref(0);

const statusFor = (status) => {
  const stutasEnum = {
    1: "未启动",
    2: "进行中",
    3: "已完成",
    4: "已暂停",
    5: "已撤销",
  };
  return stutasEnum[status];
};

const callStatusFor = (status) => {
  const callStutasEnum = {
    0: "待拨打",
    1: "已接听",
    2: "未接通",
    3: "漏接",
    100: "未接听",
  };
  return callStutasEnum[status];
};

const changeSMS = (row) => {
  if (!row.smsStatus) {
    return "未发送";
  }
  let change = {
    0: "未发送",
    2: "已发送",
    3: "失败",
  };
  return change[row.smsStatus];
};

const taskProgress = ref(0);

watch(
  () => dataStatistics.value,
  (newValue) => {
    const calledAlready = Number(dataStatistics.value.calledAlready);
    const total = Number(dataStatistics.value.total);
    const progress = (calledAlready / total) * 100; // 计算结果
    taskProgress.value = Number.isInteger(progress)
      ? progress
      : parseFloat(progress.toFixed(2));
  }
);

const executeTaskHandle = () => {
  // 执行任务
  aiExecuteTask({ ids: [taskId.value] }).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
    }
    getStatistics();
  });
};

const suspendTaskHandle = () => {
  // 暂停任务
  aiSuspendTask({ ids: [taskId.value] }).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
    }
    getStatistics();
  });
};

const exportTaskCdrHandle = () => {
  const query = {
    id: taskId.value,
    connectFlag: ["all", "100"].includes(activeTab.value)
      ? undefined
      : activeTab.value,
    connectFlag2: ["100"].includes(activeTab.value) ? "0" : undefined,
  };
  proxy.downloadforjson(
    "/aiVoiceTask/exportAiVoiceTask",
    query,
    `${new Date().getTime()}.xlsx`,
    { gateway: "cis" }
  );
};

//当有录音播放的时候停止其他录音
function stopCheck(count) {
  dataList.value.forEach((item, index) => {
    if (index !== count) {
      proxy.$refs[`audioRef${index}`]?.stopAudio();
    }
  });
}

//下载录音
function downVideo(url) {
  window.open(url);
}

// 删除
function removeTask(row) {
  proxy.$modal.confirm("是否确认删除").then(() => {
    deleteAiTask({ id: row.id }).then(() => {
      proxy.$modal.msgSuccess("操作成功！");
      getStatistics();
      getList();
    });
  });
}

const getStatistics = () => {
  aiCallTaskStatistics({ id: taskId.value }).then((res) => {
    dataStatistics.value = res.data;
    dataTaskProgress.value = [dataStatistics.value];
  });
};

const toCaseDetails = (caseId) => {
  let queryChange = {
    pageNum: 1,
    pageSize: 1,
  };
  let searchInfo = {
    query: queryChange,
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  nextTick(() => {
    router.push({ path: `/case/caseIndex-detail/manageDetails/${caseId}` });
  });
};

onMounted(() => {
  taskId.value = route.query.id;
  getStatistics();
  getList();
});
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  box-sizing: border-box;
  background-color: #f5f6fa;
  overflow: auto;
}
.app-container-item {
  padding: 1.25rem;
  background-color: #ffffff;
  //border-radius: .625rem;
  margin-bottom: 1.25rem;
}
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  //padding: .9375rem 0;

  .title {
    font-size: 1.125rem;
    color: #333333;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1.875rem;

    .tip {
      display: flex;
      flex-direction: column;
      font-size: 0.8rem;
      color: #409eff;
      font-weight: 500;
    }
  }
}

.contain {
  display: flex;
  align-items: center;
  width: 100%;
  .left {
    //flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    width: 20%;
    font-size: 1rem;

    :deep(.el-icon) {
      font-size: 3.75rem;
      color: #409eff;
    }

    .task-state {
      font-weight: 600;
      font-size: 1.125rem;
      color: #409eff;
    }

    .rate {
      display: flex;
      align-items: center;
      justify-content: center;
      :deep(.el-icon) {
        margin-left: 5px;
        font-size: 16px;
        color: #888888;
      }
    }
  }
  .right {
    //flex: 4;
    width: 80%;
    flex: 1;
    .progress {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 20px;
      white-space: nowrap;
      :deep(.el-progress) {
        width: 95%;
      }
    }

    .task-table {
      //width: 95%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: 3.125rem;
      margin-bottom: 1.25rem;
      .task-table-item {
        width: min(18%, 12.5rem);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        font-size: 1.125rem;

        .task-table-th {
          width: 3.375rem;
          color: #7f7f7f;
        }
      }
    }
  }

  .callout-effect {
    width: min(18%, 15.625rem);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    font-size: 1.125rem;
    margin: 0.9375rem 0;

    .callout-effect-value {
      font-weight: 600;
      font-size: 1.125rem;
      color: #409eff;
    }
  }
}

.header-tooltip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  :deep(.el-icon) {
    font-size: 16px;
  }
}

.play-button {
  display: inline-block;
  width: 80%;
  vertical-align: middle;
}

.download-button {
  display: inline-block;
  width: 20%;
  vertical-align: middle;
  margin-top: 5px;
}
</style>
  