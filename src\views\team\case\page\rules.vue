<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="设置分案规则"></el-step>
        <el-step title="选择处置人员"></el-step>
        <el-step title="预览分案结果"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div v-show="stepActive === 0" class="step-item pt20">
      <setCaseDivisionRules :toBack="toBack" :nextstep="nextstep" v-model:form="form" :rules="rules"
        v-model:queryRes="queryRes" :checkStatus="checkStatus" :scopeStatus="scopeStatus" :loading="loading"
        :modeList="modeList" :mannerList="mannerList" :stepActive="stepActive" />
    </div>

    <div v-show="stepActive === 1" class="step-item pt20">
      <subcaseObjective :prevStep="prevStep" :previewStep="previewStep" v-model:form="form" :rules="rules"
        v-model:queryRes="queryRes" :checkStatus="checkStatus" :scopeStatus="scopeStatus" :loading="loading"
        :modeList="modeList" :mannerList="mannerList" :stepActive="stepActive">
        <template #optTeam>
          <div>
            <optTeam ref="optteamRef" :type="formTableType" :queryRes="queryRes" :operation="true" :point="true"
              :unAssigned="form.divisionScope - 1" />
          </div>
        </template>
      </subcaseObjective>
    </div>

    <div v-show="stepActive === 2" class="step-item pt20">
      <div :style="`display:${isShowDivisionalLoad}`" class="preview-loading">
        <divisionalLoad ref="divisionalLoadRef" :scheduleName="`team-case-rules-schedule-${route.query.time}`"
          :getData="getDivisionalLoadData" />
      </div>
      <div v-if="schedule == 100">
        <table class="my-table">
          <thead>
            <tr>
              <td colspan="4">分案目标（搜索结果）</td>
            </tr>
          </thead>
          <tbody class="text-center">
            <tr>
              <td>可分配案件量</td>
              <td>总金额</td>
              <td>已分配</td>
              <td>未分配</td>
            </tr>
            <tr>
              <td>{{ queryRes.caseNum }}</td>
              <td>{{ numFilter(queryRes.caseMoney) }}</td>
              <td>{{ queryRes.unAssignedNum }}</td>
              <td>{{ queryRes.assignedNum }}</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td colspan="4">分案规则设置</td>
            </tr>
          </thead>
          <tbody class="text-left">
            <tr>
              <td colspan="2">分案原则：{{ divisionRule() }}</td>
              <td colspan="2">分配模式：{{ modeList[form.divisionalMode - 1].label }}</td>
            </tr>
            <tr>
              <td colspan="2">分案范围：{{ scopeStatus[form.divisionScope - 1].label }}</td>
              <td colspan="2">分配数值：{{ mannerList[form.divisionMethod - 1].label }}</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td colspan="4">预览分配结果</td>
            </tr>
          </thead>
          <tbody class="text-center">
            <tr>
              <td>工号</td>
              <td>处置人员</td>
              <td>案件量</td>
              <td>金额</td>
            </tr>
            <tr v-if="previewData.data?.length === 0">
              <td colspan="5">暂无可分配数据</td>
            </tr>
            <tr v-else v-for="item in previewData.data" :key="item.id">
              <td>{{ item.jobNumber }}</td>
              <td>{{ item.odvName }}</td>
              <td>
                {{ item?.number }}
                <!-- {{ item?.numericalValue ?? item?.number ?? item?.quantity ?? item?.money }} -->
              </td>
              <td>{{ numFilter(item.money) }}</td>
            </tr>
          </tbody>
        </table>
        <div class="text-center mt20">
          <el-button @click="prevStep">上一步，重新选择处置人员</el-button>
          <el-button type="primary" :loading="loading" plain @click="submit">提交分案</el-button>
        </div>
      </div>

    </div>

    <div v-show="stepActive === 3" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
      </div>
      <div class="text-center mt30">
        <el-button type="primary" @click="toBack">案件分配完成，返回</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="TeamRules">
import setCaseDivisionRules from './steps/setCaseDivisionRules';
import subcaseObjective from './steps/subcaseObjective';
import optTeam from "@/components/CaseOptTeam";
import { ruleSplitPreview, writeRuleDivision, selectCases } from "@/api/team/team";
//全局配置
const { proxy } = getCurrentInstance();
const route = useRoute();
const stepActive = ref(0);
const schedule = ref(0);
const loading = ref(false);
const isShowDivisionalLoad = ref('none');
const checkStatus = ref([
  { label: "隔月分案", is_settle: 1, indeterminate: false },
  { label: "共债分案", is_settle: 0, indeterminate: false },
]);
//分案范围
const scopeStatus = ref([
  { label: "全部", value: 1 },
  { label: "未分配", value: 2 },
]);
//分配模式
const modeList = [
  { label: "按案件数量分配", value: 1 },
  { label: "按案件金额分配", value: 2 },
  { label: "综合(案件数量分配优先)", value: 3 },
];
//分案方式
const mannerList = ref([
  { label: "数值", value: 1 },
  { label: "百分比", value: 2 },
])
//选择处置人员表格类型
const formTableType = ref(1);
//案件数量请求数据
const reqQuery = ref({});
const queryRes = ref({
  caseIds: [],
  caseNum: 0,
  caseMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
});
//提交参数
const data = reactive({
  form: {
    divisionalPrinciple: undefined,
    divisionalMode: 1,
    divisionScope: 1,
    divisionMethod: 1,
  },
  rules: {
    divisionalPrinciple: [
      { required: true, message: "请选择分案规则！", trigger: "change" },
    ],
    divisionScope: [{ required: true, message: "请选择分案范围！", trigger: "change" }],
    divisionMethod: [{ required: true, message: "请选择分配方式！", trigger: "change" }],
    divisionalMode: [{ required: true, message: "请选择", trigger: "change" }],
  },
});
//规则分案提交数据
const submitData = ref({});

//列表匹配字段
const caseOptTeamReq = [
  "id",
  "jobNumber",
  "name",
  "caseNum",
  "caseNumPercentage",
  "caseMoney",
  "caseMoneyPercentage",
];
//定义提交字段
const previewReq = [
  "odvId",
  "jobNumber",
  "odvName",
  "numericalValue",
  "valuePercentage",
  "numericalValue",
  "valuePercentage",
];
const personDate = ref({});
const personnelInformation = ref([]);
//预览数据
const previewData = ref({
  data: [],
});

const { form, rules } = toRefs(data);

onMounted(() => {
  try {
    const { checkedType, query, queryParams } = JSON.parse(localStorage.getItem(`rules/${route.query.time}`));
    reqQuery.value = queryParams;
    let obj = JSON.parse(JSON.stringify(reqQuery.value));
    if (checkedType === "本页选中") {
      form.value.caseIds = obj.ids = query.map((item) => item.caseId);
    }
    obj.pageSize = undefined;
    obj.pageNum = undefined;
    obj.caseIds = obj.ids;
    delete obj.ids;
    selectCases(obj).then((res) => {
      queryRes.value.caseIds = res.data.arrayList;
      reqQuery.value.caseIds = res.data.arrayList;
      queryRes.value.caseNum = res.data.zongshu;
      queryRes.value.caseMoney = res.data.zongjine;
      queryRes.value.unAssignedNum = res.data.yifenpei;
      queryRes.value.assignedNum = res.data.weifenpei;
    });
  } catch (error) {
    toBack()
  }
});

//选择处置人员
function nextstep() {
  if (form.value.divisionalMode == 1) {
    formTableType.value = form.value.divisionMethod == 1 ? 1 : 2;
  } else if (form.value.divisionalMode == 2) {
    formTableType.value = form.value.divisionMethod == 1 ? 3 : 4;
  } else {
    formTableType.value = form.value.divisionMethod == 1 ? 1 : 2;
  }
  stepActive.value++;
}

//预览分案
function previewStep() {
  loading.value = true;
  proxy.$refs["optteamRef"].exposeData().then((res) => {
    if (res && res.length > 0) {
      let reqData = {
        divisionMethod: form.value.divisionMethod - 1,
        divisionalPrinciple: form.value.divisionalPrinciple || [],
        divisionalMode: form.value.divisionalMode - 1,
        divisionScope: form.value.divisionScope - 1,
        teamCaseUtils: reqQuery.value,
      };
      res?.map((items, indexs) => {
        previewReq.forEach((item, index) => {
          let teamKey = caseOptTeamReq[index];
          if (items[teamKey]) {
            personDate.value[item] = items[teamKey];
          }
        });
        personnelInformation.value.push(personDate.value);
        personDate.value = {};
      });
      reqData.distributions = personnelInformation.value;
      isShowDivisionalLoad.value = 'block'
      sessionStorage.setItem(`team-case-rules-loading-${route.query.time}`, 'block')
      ruleSplitPreview(JSON.stringify(reqData))
        .then((res) => {
          //进入轮询
          submitData.value = JSON.parse(JSON.stringify(reqData));
          loading.value = false;
          stepActive.value++
          sessionStorage.setItem(`team-case-rules-no-${route.query.time}`, res.data.scheduleNo)
          if (proxy.$refs['divisionalLoadRef']) {
            proxy.$refs['divisionalLoadRef']?.pollTime(res.data.scheduleNo, '0')
          }
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

// 获取数据
function getDivisionalLoadData(data) {
  if (data.normal) {
    schedule.value = data.schedule;
    if (data.schedule == 100) {
      previewData.value.data = data.originalData
      isShowDivisionalLoad.value = 'none'
      sessionStorage.setItem(`team-case-rules-loading-${route.query.time}`, 'none')
    }
  } else {
    proxy.$modal.msgWarning(data.remarks || '')
    stepActive.value = 1
    sessionStorage.setItem(`team-case-rules-loading-${route.query.time}`, 'none')
  }
}

watch(isShowDivisionalLoad.value, () => {
  const isBlock = sessionStorage.getItem(`team-case-rules-loading-${route.query.time}`)
  isShowDivisionalLoad.value = isBlock
  if (isBlock == 'block') {
    stepActive.value = 2
    nextTick(() => {
      proxy.$refs['divisionalLoadRef']?.pollTime(sessionStorage.getItem(`team-case-rules-no-${route.query.time}`))
    })

  }
}, { deep: true, immediate: true })
//上一步
function prevStep() {
  stepActive.value--;
  personnelInformation.value = [];
  if (stepActive.value == 1 || stepActive.value == 0) {
    removeLocalData()
  }
}

function removeLocalData() {
  sessionStorage.removeItem(`team-case-rules-no-${route.query.time}`)
  sessionStorage.removeItem(`team-case-rules-loading-${route.query.time}`)
  sessionStorage.removeItem(`team-case-rules-schedule-${route.query.time}`)
}

//提交分案
function submit() {
  let data = JSON.parse(JSON.stringify(submitData.value))
  loading.value = true;
  writeRuleDivision(data).then(res => {
    stepActive.value++
    removeLocalData()
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  })
}

// 分案规则
function divisionRule() {
  const labels = checkStatus.value.filter(item => form.value.divisionalPrinciple?.includes(item.is_settle))
  if (labels.length > 0) {
    return labels.map(item => item.label).toString()
  } else {
    return '无'
  }
}

//返回
const toBack = () => {
  const obj = { path: "/team/teamsCases" };
  removeLocalData()
  proxy.$tab.closeOpenPage(obj);
};
</script>

<style lang="scss" scoped>
.rules-form .el-form-item--default {
  margin-bottom: 0px !important;
}

.blue {
  color: #409eff;
}

.hint {
  color: #888888;
  text-align: left;
  font-size: 14px;
  color: #409eff;
  margin: 0;
  padding: 4px;

  .text-primary:hover {
    color: #409eff;
  }

  p {
    margin: 2px;
    margin-left: 10px;
  }
}

.step-item {
  width: 90%;
  margin: 0 auto;
}

.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}

.my-table {
  margin: 20px auto;
  color: #666;
  text-align: center;
  border: 1px solid #ededed;
  outline: none;
  min-width: 80%;
  border-spacing: 0px !important;

  thead {
    background-color: #f2f2f2;
    font-size: 14px;
    border: 1px solid #ededed;

    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }

  tbody {
    tr {
      height: 40px;

      td {
        border: 1px solid #ededed;
      }
    }
  }
}
</style>
