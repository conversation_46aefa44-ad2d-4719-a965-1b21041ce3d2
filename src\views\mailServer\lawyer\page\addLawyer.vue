<template>
  <div class="app-container">
    <div class="app-content pb10">
      <div class="wcc-el-steps">
        <el-steps :active="stepActive" align-center>
          <el-step :title="`${isCompress ? '导入PDF函件' : '新建发函'}`" description="导入相关数据"></el-step>
          <el-step title="校验文件" description="校验导入文件"></el-step>
        </el-steps>
      </div>
      <!-- 新建发函 -->
      <div v-show="stepActive === 0" class="step-item pt20">
        <el-form :model="form" :rules="rules" ref="formRef" label-position="right" label-width="120px">
          <el-form-item label="发函批次号：" prop="batchNum">
            <el-input v-model="form.batchNum" placeholder="请输入批次号名称，不能超过25个字符；" style="width: 400px" maxlength="25"
              show-word-limit />
          </el-form-item>
          <el-form-item label="选择模板" prop="templateId">
            <el-select v-model="form.templateId" placeholder="请选择模板" clearable filterable :reserve-keyword="false"
              style="width: 400px" @focus="getTemplate">
              <el-option v-for="item in templateList" :key="item.code" :label="item.info" :value="item.code" />
            </el-select>
            <el-button type="text" class="ml10" :disabled="!form.templateId"
              @click="templateDetails()">预览</el-button>
          </el-form-item>
          <div v-if="!isCompress">
            <el-form-item label="模板" prop="signatureMec">
              <el-button type="primary" :disabled="!form.templateId" plain @click="downSigntrue()">下载模板</el-button>
            </el-form-item>
            <el-form-item label="寄送方式" prop="deliveryWay">
              <el-select v-model="form.deliveryWay" placeholder="请选择寄送方式" clearable filterable :reserve-keyword="false"
                style="width: 400px" @focus="getDeliveryWayList()">
                <el-option v-for="item in deliveryWayList" :key="item.dictValue" :label="item.dictValue"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
            <el-form-item label="案件上传" prop="sourceFileUrl">
              <el-upload ref="uploadRef" drag :limit="1" accept=".xls, .xlsx" :headers="upload.headers"
                :action="upload.url" :before-upload="handleFileUploadBefore" :on-change="handleEditChange"
                :before-remove="handleRemove" :on-success="handleContractFileSuccess" :file-list="fileCoverList"
                :auto-upload="false">
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  <div>将文件拖到此处，或<em>点击上传</em></div>
                  <div style="color: #999">
                    注意：1.文件需小于5M，导入需少于10000条
                  </div>
                  <div style="color: #999">
                    2. 文件仅支持xls、xlsx格式，请优先使用
                  </div>
                </div>
                <template #tip>
                  <div>
                    <el-button type="success" @click="submitFile">上传到服务器</el-button>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
          <div v-else>
            <el-form-item label="寄送方式" prop="deliveryWay">
              <el-select v-model="form.deliveryWay" placeholder="请选择寄送方式" clearable filterable :reserve-keyword="false"
                style="width: 400px" @focus="getDeliveryWayList()">
                <el-option v-for="item in deliveryWayList" :key="item.dictValue" :label="item.dictValue"
                  :value="item.dictValue" />
              </el-select>
            </el-form-item>
            <el-form-item label="PDF函件上传" prop="sourceFileUrl">
              <ImportTemplatePdf v-model:sourceFileUrl="form.sourceFileUrl" fileType=".zip"
                uploadUrl="/letter/message/upload" />
            </el-form-item>
          </div>
        </el-form>

        <div class="text-center mt20">
          <el-button @click="toBack">取消</el-button>
          <el-button type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
        </div>
      </div>

      <div v-show="stepActive === 1" class="step-data pt20">
        <div style="width: 70%; margin: 0 auto; padding: 20px; min-height: 500px">
          <div class="msg-data">
            <div class="msg-icon">
              <el-icon :size="48" color="#fff">
                <Document />
              </el-icon>
            </div>
            <div class="msg-count">
              <div class="mt20">
                <span class="text700">正常数量条数：</span>
                <span class="text-green">{{ results.successNum }}</span>
              </div>
            </div>
            <div class="mt10 text700" v-if="results?.errorDataList?.length > 0">
              <span class="text700">异常数量条数：</span>
              <span class="text-red">{{ results.errorDataList?.length }}</span>
            </div>
            <div class="mt10 text700" v-if="results?.errorDataList?.length > 0">
              <p class="ml10">异常提示：</p>
              <p v-for="item in results?.errorDataList" :key="item">{{ item }}</p>
            </div>
          </div>
        </div>
        <div class="text-center">
          <el-button plain @click="backimport">上一步，修改数据</el-button>
          <el-button plain @click="toBack">取消</el-button>
          <el-button type="primary" plain :disabled="!results.successNum || results.successNum == 0"
            @click="submitData()">过滤错误数据，提交</el-button>
        </div>
      </div>
    </div>

    <!-- 详情 -->
    <templateDetailsBox ref="templateDetailsBoxRef" />
  </div>
</template>

<script setup name="AddLawyer">
import { getToken } from "@/utils/auth";
import { listData } from "@/api/system/dict/data.js";
import {
  getTemplateOptions,
  addMessage,
  verifyFileMessage,
  verifyZipFile,
  addZip,
} from "@/api/lawyer/lawyer";
import templateDetailsBox from "../../template/page/templateDetails";
import ImportTemplatePdf from "../../template/page/importTemplatePdf";
//全局样式
const { proxy } = getCurrentInstance();
const route = useRoute();
//下载
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/case/upload",
});
const fileCoverList = ref([]); //上传成功文件列表
//模板列表
const templateList = ref([]);
//当前步骤
const stepActive = ref(0);
//加载
const loading = ref(false);
//表单提交
const data = reactive({
  form: {
    batchNum: undefined,
    templateId: undefined,
    deliveryWay: undefined,
    sourceFileUrl: undefined,
  },
  rules: {
    batchNum: [
      { required: true, message: "请输入发函批次号", trigger: "blur" },
    ],
    templateId: [
      { required: true, message: "请选择模板信息", trigger: "blur" },
    ],
    sourceFileUrl: [{ required: true, message: "请选择文件", trigger: "blur" }],
    deliveryWay: [
      { required: true, message: "请选择寄送方式", trigger: "blur" },
    ],
  },
});
const { form, rules } = toRefs(data);
const results = ref({
  successNum: 0,
  errorDataList: [],
});

// 是否压缩
const isCompress = ref(false);

// 寄送方式列表
const deliveryWayList = ref([]);

//下载模板
function downSigntrue() {
  proxy.downloadforjson(
    "/letter/template/downloadTemplateVariable",
    { id: form.value.templateId },
    `函件模板_${new Date().getTime()}.xlsx`
  );
}

// 获取寄送列表
function getDeliveryWayList() {
  listData({ dictType: "delivery_method" }).then((res) => {
    deliveryWayList.value = res.rows.filter((item) => item.status != "1");
  });
}

//获取模板数据
function getTemplate() {
  const type = isCompress.value ? 1 : 0;
  getTemplateOptions({ type }).then((res) => {
    templateList.value = res.data;
  });
}

//预览
function templateDetails() {
  proxy.$refs["templateDetailsBoxRef"].opendialog(form.value.templateId);
}

//下一步
function nextstep() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req = {
        templateId: form.value.templateId,
        sourceFileUrl: form.value.sourceFileUrl[0],
        deliveryWay: form.value.deliveryWay,
        sourceFileType: isCompress.value ? 1 : 0,
      };
      const request = isCompress.value ? verifyZipFile : verifyFileMessage;
      request(req).then((res) => {
        if (res.data) {
          if (res.data.successNum == 0) {
            proxy.$modal.msgWarning("检测出所有数据都出现异常！请重新上传数据");
            return false;
          }
          results.value = res.data;
          stepActive.value++;
        }
      });
    }
  });
}

//上一步
function backimport() {
  stepActive.value--;
}

//提交
function submitData() {
  let req = {
    batchNum: form.value.batchNum,
    templateId: form.value.templateId,
    sourceFileUrl: form.value.sourceFileUrl[0],
    verifyId: results.value.verifyId,
    deliveryWay: form.value.deliveryWay,
    sourceFileType: isCompress.value ? 1 : 0,
  };
  const request = isCompress.value ? addZip : addMessage;
  request(req).then((res) => {
    toBack();
    proxy.$modal.msgSuccess(`操作成功！`);
  });
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 5 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过5MB!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 5 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过5MB!");
    fileList.pop();
    return false;
  }
  if (fileList.length > 0) {
    fileCoverList.value = fileList;
  }
}

//上传到服务器
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/* 上传文件上传成功处理 */
const handleContractFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg ? msg : `文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    form.value.sourceFileUrl = data.fileUrl
  }
};

//删除
function handleRemove() {
  form.value.sourceFileUrl = "";
  fileCoverList.value = [];
}

//返回
function toBack() {
  const obj = { path: "/mailServer/lawyer" };
  proxy.$tab.closeOpenPage(obj);
}

onMounted(() => {
  if (Boolean(route.query.isCompress)) {
    isCompress.value = Boolean(route.query.isCompress);
  }
});
</script>

<style lang="scss" scoped>
.step-item {
  width: 700px;
  min-height: 400px;
  margin: 40px auto;

  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;

    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;

      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

:deep(.el-upload-dragger) {
  width: 500px;
  height: 250px;
}

.step-data {
  min-height: 600px;

  .msg-data {
    .msg-icon {
      background-color: #e6e6e6;
      text-align: center;
      vertical-align: top;
      width: 100px;
      height: 100px;
      display: inline-block;

      i {
        margin-top: 25px;
      }
    }

    .msg-count {
      display: inline-block;
      height: 100px;
      padding-left: 20px;
    }
  }

  .err-msg {
    height: 240px;
    overflow: auto;
  }
}

.text-green {
  color: #94c746;
}

.text-red {
  color: red;
}
</style>
