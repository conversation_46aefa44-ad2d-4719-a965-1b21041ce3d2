<template>
    <el-dialog title="案件流转" v-model="open" width="600px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" v-if="open" ref="formRef" label-width="120px">
            <el-form-item v-if="typeObj[pageType].code != 2" prop="twoStatus" label="阶段选择">
                <el-radio-group v-model="form.twoStatus">
                    <el-radio v-for="item in statusOption" :key="item.code" :label="item.info" :value="item.info">
                        {{ item.info }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <div v-if="typeObj[pageType].code == 2">
                <el-form-item prop="oneStatus" label="一级阶段选择">
                    <el-radio-group v-model="form.oneStatus" @change="handleChangeOneStatus">
                        <el-radio v-for="item in oneState" :key="item" :label="item" :value="item">
                            {{ item }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item prop="twoStatus" label="二级阶段选择">
                    <el-radio-group v-model="form.twoStatus">
                        <el-radio v-for="item in twoState" :key="item.code" :label="item.info" :value="item.info">
                            {{ item.info }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </div>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="subimt" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { stateOnlineFilling, transferCaseApi, selectWithLawsuitStage } from '@/api/mediation/onlineFiling'
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const pageType = ref('')
const open = ref(false)
const loading = ref(false)
const statusOption = ref([])
const data = reactive({
    form: {
        twoStatus: 0,
        sign: ''
    },
    rules: {
        twoStatus: [{ required: true, message: '请选择二级阶段', trigger: 'blur' }],
        oneStatus: [{ required: true, message: '请选择一级阶段', trigger: 'blur' }]
    },
})
const isLitigationExecute = ref(undefined)
const typeObj = ref({
    'exeOfPaybacks': { code: 2, info: 'disposeStage' },
    'filingCourt': { code: 2, info: 'disposeStage' },
    'iegalPreservation': { code: 3, info: 'saveStage' },
    'judgmentResult': { code: 2, info: 'disposeStage' },
    'lawsuitCarry': { code: 2, info: 'disposeStage' },
    'onlineFiling': { code: 2, info: 'disposeStage' },
    'phoneMediation': { code: 1, info: 'mediatedStage' },
    'mediateCarry': { code: 1, info: 'mediatedStage' },
})
const { form, rules } = toRefs(data)
function subimt() {
    proxy.$refs['formRef'].validate((vaild) => {
        if (vaild) {
            const reqForm = { ...form.value.query, allQuery: form.value.allQuery }
            reqForm[typeObj.value[pageType.value].info] = form.value.twoStatus
            reqForm.originalStage = form.value.originalStage
            loading.value = true
            transferCaseApi(reqForm).then(res => {
                if (res.code == 200) {
                    props.getList && props.getList()
                    proxy.$modal.msgSuccess('操作成功！')
                    cancel()
                }
            }).finally(() => loading.value = false)
        }
    })
}
function openDialog(data) {
    open.value = true
    pageType.value = data.pageType
    form.value.query = data.query
    form.value.allQuery = data.allQuery
    isLitigationExecute.value = data.isLitigationExecute
    form.value.twoStatus = data.query.mediatedStage || data.query.disposeStage || data.query.saveStage
    form.value.originalStage = data.query.mediatedStage || data.query.disposeStage || data.query.saveStage
    form.value.oneStatus = data.oneStatus
    nextTick(() => {
        getStatusOptionFun(typeObj.value[data.pageType].code, data.sign)
    })
}
function cancel() {
    open.value = false
    form.value = { twoStatus: '' }
}

// 选择一级阶段
function handleChangeOneStatus() {
    form.value.twoStatus = ''
}

function getStatusOptionFun(type) {
    const reqApi = isLitigationExecute.value ? selectWithLawsuitStage : stateOnlineFilling
    reqApi({ type }).then(res => {
        statusOption.value = res.data
    })
}

const oneState = computed(() => [...new Set(statusOption.value.map(item => item.code))])
const twoState = computed(() => statusOption.value.filter(item => item.code == form.value.oneStatus))

defineExpose({ openDialog })
</script>