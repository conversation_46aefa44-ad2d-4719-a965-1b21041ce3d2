<template>
  <div class="handle-urage caseinfo-wrap pd20">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="160px">
      <el-form-item label="阶段选择" prop="urgeState">
        <el-radio-group v-model="form.urgeState">
          <el-radio v-for="item in contactChannel" :key="item.code" :label="item.info">
            {{ item.info }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 表单配置 -->
    <formComponents ref="formComponentsRef" :key="formOption" :contactMedium="form.urgeState"
      v-model:formOption="formOption" v-model:worksheetForm="worksheetForm" />
    <div class="mt20" style="margin-left:160px">
      <el-button type="primary" plain :loading="loading" @click="saveLawsuit">保存</el-button>
    </div>
  </div>
</template>

<script setup name="lawsuitUrge">
import formComponents from "@/components/formComponents/index";
import { selectContactChannel, insertComplaint } from "@/api/caseDetail/urge";
import { selectStageFrom } from "@/api/caseDetail/detail";
import { stateOnlineFilling } from "@/api/mediation/onlineFiling";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(["setCurrentRow", "getcaseDetailInfo", "getUrgeList"]);
//表单设置
const props = defineProps({
  caseId: {
    type: [Number, String]
  }
});

const loading = ref(false);
const data = reactive({
  form: {
    registerType: 2,
    urgeState: route.query.twoStage,
  },
  rules: {
    urgeState: [{ required: true, message: "请选择调诉阶段", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//调诉阶段
const contactChannel = ref([]);
const formOption = ref([]);
//自定义表单
const worksheetForm = ref([]);

//获取调诉阶段
function getContactChannel() {
  let req = { type: 1 , caseId: route.params.caseId}
  stateOnlineFilling(req).then((res) => {
    contactChannel.value = res.data;
  });
}
getContactChannel();

//查询阶段表单
function getStageFrom() {
  let req = { stageTwoName: form.value.urgeState, disposeWay: 1, };
  selectStageFrom(req).then((res) => {
    formOption.value = res.data;
  });
  worksheetForm.value = []
}

//保存催记
function saveLawsuit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      // loading.value = true;
      let req = {
        caseId: props.caseId,
        stageId: form.value.urgeState,
        registerType: form.value.registerType,
        registerType: 1,
        disposeWay: 1,
        urgeState: form.value.urgeState
      }
      proxy.$refs['formComponentsRef'].submitForm()
      Object.assign(req, worksheetForm.value)
      addFile(req).then(() => {
        req.connectContent = req.objContent
        insertComplaint(req).then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          emit('getUrgeList')
          reset()
        }).finally((erroe) => {
          loading.value = false;
        })
      })
    }
  })
}

// 添加字段
function addFile(form) {
  return new Promise((resolve) => {
    form.objContent.forEach(item => {
      const obj = formOption.value.find(item2 => item2?.sysDictType?.dictName == item.fieldName)
      if (obj && obj?.sysDictType?.sysDictDataList) {
        obj && obj?.sysDictType?.sysDictDataList?.forEach(item2 => {
          if (item[item.receiveParam].includes(item2.dictValue)) {
            if (item2.dictLabel) {
              // item.labelName += `${item2.dictLabel}、`
              // item.labelName = item.labelName.substr(0, item.labelName.length - 1)
              item.labelName = item.labelName.replace('undefined', '')
            }
          }
        })
      } else {
        item.labelName = item[item.receiveParam]
      }
    })
    resolve()
  })
}

//清空表单方法
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    registerType: 2,
    urgeState: route.query.twoStage,
  };
  if (proxy.$refs["formComponentsRef"]) {
    proxy.$refs["formComponentsRef"]?.resetNewForm()
  }
}
watch(() => form.value, () => {
  nextTick(() => {
    getStageFrom()
  })
}, { immediate: true, deep: true })
</script>

<style></style>
