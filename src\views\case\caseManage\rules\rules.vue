<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="选择处置人员"></el-step>
        <el-step title="预览分案结果"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div v-show="stepActive === 0" class="step-item pt20">
      <el-form :model="form" :rules="rules" ref="formRef">
        <div class="step-item-title"> 搜索结果 </div>
        <div class="step-item-content">
          <div class="content-item">
            <div class="content-item-label">可分配案件量</div>
            <div class="content-item-value">{{ queryRes.caseNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">总金额</div>
            <div class="content-item-value">{{ numFilter(queryRes.caseMoney) }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">已分配</div>
            <div class="content-item-value">{{ queryRes.unAssignedNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">未分配</div>
            <div class="content-item-value">{{ queryRes.assignedNum }}</div>
          </div>
        </div>
        <div class="step-item-title"> 分案规则 </div>
        <div class="step-item-info pt10">
          <el-form-item label="分案规则">
            <el-checkbox-group v-model="form.divisionalPrinciple">
              <el-checkbox v-for="item in checkStatus" :key="item.is_settle" :label="item.is_settle"
                :indeterminate="item.indeterminate">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="hint ml10">
            <p>共债分案：将所有未分配的共债案件单独列出来，统一进行分配;</p>
            <p>
              隔月分案：上个月至今分过的案件，本月不重复分给同一催员，且本月分过案件不可再分;
            </p>
            <p>
              同时勾选：共债优先，为共债的案件系统会优先分配给同一催员，仅支持所选择的案件里的所有共债进行分配；隔月分案不重复分配至同一催员;
            </p>
          </div>
        </div>
        <div class="step-item-title">分案范围</div>
        <div class="step-item-info pt10">
          <el-form-item label="分案范围" prop="divisionScope">
            <el-radio-group v-model="form.divisionScope" size="default">
              <el-radio v-for="item in scopeStatus" :key="item.label" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="hint ml10">
            <p>全部:分配当前搜索结果的全部可分案件</p>
            <p>未分配：只分配当前搜索结果案件状态为未分配的可分案</p>
          </div>
        </div>
        <div class="step-item-title">分配模式</div>
        <div class="step-item-info pt10 pb10">
          <el-form-item label="分配模式" prop="divisionalMode">
            <el-radio-group v-model="form.divisionalMode" size="default">
              <el-radio v-for="item in modeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="step-item-title">选择处置人员</div>
        <optTeam ref="optteamRef" class="opt-team" :type="form.divisionalMode" :queryRes="queryRes" :operation="true"
          :point="true" :unAssigned="form.divisionScope - 1" :returnCaseDateRequired="true"/>
      </el-form>

      <div class="text-center mt20">
        <el-button @click="toBack">返回</el-button>
        <el-button type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
      </div>
    </div>

    <div v-show="stepActive === 1" class="step-item pt20">
      <div :style="`display:${isShowDivisionalLoad}`" class="preview-loading">
        <divisionalLoad ref="divisionalLoadRef" :scheduleName="`case-caseManage-rules-schedule-${route.query.time}`"
          :getData="getList" />
      </div>
      <div v-if="schedule == 100" class="perview-data">
        <div class="step-item-title"> 分案目标（搜索结果） </div>
        <div class="step-item-content">
          <div class="content-item">
            <div class="content-item-label">可分配案件量</div>
            <div class="content-item-value">{{ queryRes.caseNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">总金额</div>
            <div class="content-item-value">{{ numFilter(queryRes.caseMoney) }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">已分配</div>
            <div class="content-item-value">{{ queryRes.unAssignedNum }}</div>
          </div>
          <div class="content-item">
            <div class="content-item-label">未分配</div>
            <div class="content-item-value">{{ queryRes.assignedNum }}</div>
          </div>
        </div>
        <div class="step-item-title"> 分案规则设置 </div>
        <div class="step-item-both-sides">
          <div class="step-item-content-value">分案方式：规则分案</div>
          <div class="step-item-content-value">分配模式：{{ modeList[form.divisionalMode - 1].label }}</div>
          <div class="step-item-content-value">分案范围：{{ scopeStatus[form.divisionScope - 1].label }}</div>
        </div>
        <div class="step-item-title"> 预览分配结果 </div>
        <div class="table-list">
          <div class="table-thead">
            <div class="table-thead-item">工号</div>
            <div class="table-thead-item">处置人员</div>
            <div class="table-thead-item">案件量</div>
            <div class="table-thead-item">金额</div>
            <div class="table-thead-item">退案日期</div>
          </div>
          <div class="table-body">
            <div class="table-body-item" v-for="item in previewData.data" :key="item.odvId">
              <div>{{ item.number }}</div>
              <div>{{ item.name }}</div>
              <div>{{ item?.quantity ?? item?.money }}</div>
              <div>{{ numFilter(item.money) }}</div>
              <div>{{ item.returnCaseDate }}</div>
            </div>
          </div>
        </div>
        <div class="text-center mt20">
          <el-button @click="prevStep">上一步，选择处置人员</el-button>
          <el-button type="primary" :loading="loading" plain @click="submit">提交分案</el-button>
        </div>
      </div>
    </div>
    <div v-show="stepActive === 2" class="step-item pt20">
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>操作成功</h2>
      </div>
      <div class="text-center mt30">
        <el-button type="primary" @click="toBack">案件分配完成，返回</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="Rules">
import optTeam from "@/components/CaseOptTeam";
import { strategyAllocationQuery, ruleSplitPreview, writeRuleDivision } from "@/api/case/index/index";

const { proxy } = getCurrentInstance();
const route = useRoute();
const stepActive = ref(0);
const loading = ref(false);
const isShowDivisionalLoad = ref("none");
const checkStatus = ref([
  { label: "共债分案", is_settle: 0, indeterminate: false },
  { label: "隔月分案", is_settle: 1, indeterminate: false },
]);
const scopeStatus = ref([
  { label: "全部", value: 1 },
  { label: "未分配", value: 2 },
]);
const modeList = [
  { label: "按案件数量分配", value: 1 },
  { label: "按案件数量百分比分配", value: 2 },
  { label: "按案件金额分配", value: 3 },
  { label: "按案件金额百分比分配", value: 4 },
];
const reqQuery = ref({});
const queryRes = ref({
  caseIds: [],
  caseNum: 0,
  caseMoney: 0,
  assignedNum: 0,
  unAssignedNum: 0,
  distributeType: route.query.distributeType
});
const data = reactive({
  form: {
    divisionalPrinciple: [],
    divisionalMode: 1,
    bringDiary: true,
    aiDringDiary: true,
    divisionScope: 1,
  },
  rules: {
    divisionalPrinciple: [
      { required: true, message: "请选择分案规则！", trigger: "change" },
    ],
    divisionScope: [{ required: true, message: "请选择分案范围！", trigger: "change" }],
    divisionalMode: [{ required: true, message: "请选择", trigger: "change" }],
  },
});
const submitData = ref({});

//列表匹配字段
const caseOptTeamReq = [
  "id",
  "jobNumber",
  "name",
  "caseNum",
  "caseNumPercentage",
  "caseMoney",
  "caseMoneyPercentage",
  "returnCaseDate"
];
//定义提交字段
const previewReq = [
  "id",
  "number",
  "name",
  "numericalValue",
  "valuePercentage",
  "numericalValue",
  "valuePercentage",
  "returnCaseDate"
];
const personDate = ref({});
const personnelInformation = ref([]);

const previewData = ref({
  //预览数据
  data: [],
});
const schedule = ref(0);


const { form, rules } = toRefs(data);

onMounted(() => {
  try {
    const { query, queryParams } = JSON.parse(localStorage.getItem(`rules/${route.query.time}`));
    reqQuery.value = queryParams;
    let arr = query.map((item) => item.caseId);
    form.value.caseIds = arr;
    let obj = JSON.parse(JSON.stringify(reqQuery.value));
    obj.pageSize = undefined;
    obj.pageNum = undefined;
    strategyAllocationQuery(obj).then((res) => {
      queryRes.value.caseIds = res.data.arrayList;
      queryRes.value.caseNum = res.data.zongshu;
      queryRes.value.caseMoney = res.data.zongjine;
      queryRes.value.unAssignedNum = res.data.yifenpei;
      queryRes.value.assignedNum = res.data.weifenpei;
    });
  } catch (error) {
    toBack()
  }
});

// 获取数据
function getList(data) {
  if (data.normal) {
    schedule.value = data.schedule;
    if (data.schedule == 100) {
      previewData.value.data = data.originalData;
      isShowDivisionalLoad.value = 'none'
      sessionStorage.setItem(`case-caseManage-rules-loading-${route.query.time}`, 'none')
    }
  } else {
    proxy.$modal.msgWarning(data.remarks || '')
    stepActive.value = 0
    sessionStorage.setItem(`case-caseManage-rules-loading-${route.query.time}`, 'none')
  }
}

//下一步
function nextstep() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      proxy.$refs["optteamRef"].exposeData().then((res) => {
        if (res && res.length > 0) {
          let reqData = {
            divisionalPrinciple: form.value.divisionalPrinciple,
            divisionalMode: form.value.divisionalMode - 1,
            divisionScope: form.value.divisionScope - 1,
            exportReminder: reqQuery.value,
            distributeType: route.query.distributeType
          };
          res?.map((items, indexs) => {
            previewReq.forEach((item, index) => {
              let teamKey = caseOptTeamReq[index];
              if (!personDate.value[item]) {
                personDate.value[item] = items[teamKey];
              }
            });
            personnelInformation.value.push(personDate.value);
            personDate.value = {};
          });
          reqData.personnelInformation = personnelInformation.value;
          isShowDivisionalLoad.value = 'block'
          sessionStorage.setItem(`case-caseManage-rules-loading-${route.query.time}`, 'block')
          submitData.value = reqData
          ruleSplitPreview(JSON.stringify(reqData))
            .then((res) => {
              sessionStorage.setItem(`case-caseManage-rules-no-${route.query.time}`, res.data.scheduleNo)
              proxy.$refs['divisionalLoadRef'].pollTime(res.data.scheduleNo, '0')
              loading.value = false;
              stepActive.value++;
            })
            .catch(() => {
              loading.value = false;
            });
        } else {
          loading.value = false;
        }
      });
    }
  });
}

watch(isShowDivisionalLoad.value, () => {
  const isBlock = sessionStorage.getItem(`case-caseManage-rules-loading-${route.query.time}`)
  isShowDivisionalLoad.value = isBlock
  sessionStorage.setItem(`case-caseManage-rules-loading-${route.query.time}`, isBlock)
  if (isBlock == 'block') {
    stepActive.value = 1
    nextTick(() => {
      proxy.$refs['divisionalLoadRef']?.pollTime(sessionStorage.getItem(`case-caseManage-rules-no-${route.query.time}`))
    })
  }
}, { deep: true, immediate: true })

//上一步
function prevStep() {
  stepActive.value--;
  personnelInformation.value = [];
  if (stepActive.value == 0) {
    remove()
  }
}

function remove() {
  sessionStorage.removeItem(`case-caseManage-rules-loading-${route.query.time}`)
  sessionStorage.removeItem(`case-caseManage-rules-no-${route.query.time}`)
  sessionStorage.removeItem(`case-caseManage-rules-schedule-${route.query.time}`)
}

//提交分案
function submit() {
  let reqForm = JSON.parse(JSON.stringify(submitData.value));
  reqForm.distributeType = route.query.distributeType
  loading.value = true;
  writeRuleDivision(reqForm)
    .then((res) => {
      stepActive.value++;
      remove()
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

//返回
const toBack = () => {
  remove()
  const obj = { path: route.query.path };
  proxy.$tab.closeOpenPage(obj);
};

</script>

<style lang="scss" scoped>
.blue {
  color: #409eff;
}

.hint {
  color: #888888;
  text-align: left;
  font-size: 14px;
  color: #409eff;
  margin: 0;
  padding: 4px;

  .text-primary:hover {
    color: #409eff;
  }

  p {
    margin: 2px;
    margin-left: 10px;
  }
}

.step-item {
  width: 90%;
  margin: 0 auto;
}

.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}

.step-item-title {
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  color: #666666;
  background-color: #f2f2f2;
}

.step-item-content {
  &>div {
    color: #666666;

    .content-item-label {
      border-bottom: 1px solid #ededed;
    }
  }
}

.step-item-info {
  padding-left: 20px;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;
}

.step-item-content,
.table-thead,
.table-body-item {
  display: flex;
  flex-wrap: wrap;
  border-right: 1px solid #ededed;

  &>div {
    flex: 1;
    line-height: 44px;
    text-align: center;
    border-left: 1px solid #ededed;
  }
}

.step-item-both-sides {
  display: flex;
  flex-wrap: wrap;
  line-height: 44px;
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;

  .step-item-content-value:nth-of-type(odd) {
    border-right: 1px solid #ededed;
  }

  .step-item-content-value:nth-of-type(-n + 2) {
    border-bottom: 1px solid #ededed;
  }

  &>div {
    width: 50%;
    padding-left: 20px;
  }
}

.step-item-content-value {
  width: 50% !important;
}

.table-list {
  .table-thead {
    font-size: 14px;
    border-bottom: 1px solid #ededed;
  }

  .table-body {
    max-height: 400px;
    overflow: auto;

    .table-body-item {
      border-bottom: 1px solid #ededed;
    }

  }
}

.opt-team {
  padding: 0 20px;
  border: 1px solid #ededed;
}
</style>
