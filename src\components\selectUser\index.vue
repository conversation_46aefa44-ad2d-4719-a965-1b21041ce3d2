<template>
  <div class="warp">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="pd-20-border">
          <el-input class="mb8" v-model="filterText" placeholder="请输入关键字" />
          <el-tree
            ref="treeRef"
            class="filter-tree"
            show-checkbox
            :data="data"
            :props="defaultProps"
            :expand-on-click-node="false"
            check-on-click-node
            check-strictly
            :render-after-expand="false"
            node-key="id"
            @check-change="getChecked"
            :filter-node-method="filterNode"
          />
        </div>
      </el-col>
      <el-col :span="12">
        <div class="pd-20-border">
          <div class="chosed">已选人员（{{ selectedArr.length }}<span v-if="!props.multiple">/1</span>）</div>
          <div class="chosed-list filter-tree">
            <div class="item" v-for="item in selectedArr" :key="item.id">
              {{ item.name }}
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { getTeamTreeById } from "@/api/team/team";

const { proxy } = getCurrentInstance();
const props = defineProps({
  selected: {
    //已选中
    type: Array,
    default: [],
  },
  multiple: {
    //是否多选
    type: Boolean,
    default: true,
  }
});
const emit = defineEmits();

const filterText = ref("");
const defaultProps = {
  children: "children",
  label: "name",
  disabled: "disabled",
};
const data = ref([]); //树结构
const selectedArr = ref([]); //已选中

getTeamTreeById().then((res) => {
  data.value = handleData(res.data);
});

//树形数据处理
function handleData(data) {
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.id.indexOf("Dept") > -1) {
      //部门不能选择
      item.disabled = true;
    } else {
      item.disabled = false;
    }
    if (item.children && item.children.length > 0) {
      handleData(item.children);
    }
  }
  return data;
}

//获取选中数据
function getChecked(node, ischecked) {
  if (ischecked) {
    if (!props.multiple) {
      // 当multiple为false时，先清空已选中的节点
      selectedArr.value = [];
      // 然后添加当前选中的节点
      selectedArr.value.push(node);
      // 取消其他节点的选中状态
      proxy.$refs["treeRef"].setCheckedKeys([node.id], false);
    } else {
      // 当multiple为true时，正常添加或移除节点
      selectedArr.value.push(node);
    }
  } else {
    // 移除被取消选中的节点
    selectedArr.value = selectedArr.value.filter(item => item.id !== node.id);
    // selectedArr.value.map((item, index) => {
    //   if (item.id === node.id) {
    //     selectedArr.value.splice(index, 1);
    //   }
    // });
  }
  emit("update:selected", selectedArr.value);
}

//数据过滤筛选
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

//清空选择
function clearSelected() {
  proxy.$refs["treeRef"].setCheckedKeys([], false);
}

watch(filterText, (val) => {
  proxy.$refs["treeRef"].filter(val);
});

defineExpose({
  clearSelected,
});
</script>

<style lang="scss" scoped>
.pd-20-border {
  width: 100%;
  height: 100%;
  border-radius: 2px;
  border: 1px solid #e8e8e8;
  padding: 1rem;
  .chosed {
    height: 32px;
    line-height: 32px;
    border-bottom: 1px solid #e8e8e8;
  }
  .chosed-list {
    .item {
      height: 26px;
      line-height: 26px;
      padding-left: 10px;
      margin-top: 8px;
      background-color: #e3f0ff;
      border-radius: 2px;
    }
  }
}
.filter-tree {
  height: 200px;
  overflow: auto;
}
</style>
