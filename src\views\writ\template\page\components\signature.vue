<template>
  <div v-show="stepActive === 1" class="step-item pt20">
    <el-form
      :model="form"
      :rules="rules"
      ref="signFormRef"
      class="sign-form"
      label-position="right"
      :label-width="120"
    >
      <div v-for="(item, index) in form.lawyer" :key="index" class="template-sign">
        <el-form-item
          label="签章名称"
          :prop="`lawyer.${index}.lawId`"
          :rules="rules.lawId"
        >
          <el-select
            v-model="form.lawyer[index].lawId"
            placeholder="请选择签章名称"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 400px"
            @focus="getLawList"
            @change="getAttorneyList(index, $event)"
          >
            <el-option
              v-for="item in lawList"
              :key="item.code"
              :label="item.info"
              :value="item.code"
            />
          </el-select>
          <el-space class="ml20" v-if="index === 0">
            <el-button @click="addSign()">添加</el-button>
            <el-button :disabled="form.lawyer.length === 1" @click="delSign()"
              >删除</el-button
            >
          </el-space>
        </el-form-item>
        <el-form-item label="代理律师" :prop="`lawyer.${index}.signId`">
          <el-select
            v-model="form.lawyer[index].signId"
            placeholder="请选择代理律师"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 400px"
            @change="getSignSrc(index, $event)"
          >
            <el-option
              v-for="item in attorneyList"
              :key="item.code"
              :label="item.info"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <div class="template-preview-chapter">
      <div class="template-preview">
        <slot name="previewContent" />
        <div
          ref="previewRef"
          class="template-preview-sign-area"
          @dragover="handleDragover"
          @drop="handleDrop"
        />
        <template v-if="route.params.id">
          <div
            class="template-chapter-item"
            draggable="true"
            v-for="(item, index) in positionListLeft()"
            ref="dragElRef"
            data-old="true"
            @dragstart="handleDragstart(item.index, $event)"
            @mousedown="getMouse"
            :key="index"
            :style="`position: absolute;left:${item.left}px; top:${item.top}px; z-index:${
              index + 3
            };`"
            :data-chapter-info="JSON.stringify(item)"
          >
            <img class="chapter-img" :src="item.signPic || item.info" alt="" />
          </div>
        </template>
      </div>
      <div ref="chapterListRef" class="template-chapter-list ml10">
        <template v-for="(item, index) in positionListRight()" :key="index">
          <div
            class="template-chapter-item"
            draggable="true"
            v-if="item.info"
            ref="dragElRef"
            @dragstart="handleDragstart(item.index, $event)"
            @mousedown="getMouse"
            :data-chapter-info="item.index"
          >
            <img class="chapter-img" :src="item.info" alt="" />
          </div>
        </template>
      </div>
    </div>

    <div class="text-center mt20">
      <el-button @click="backstep">上一步</el-button>
      <el-button class="ml10" @click="toBack">取消</el-button>
      <el-button class="ml10" type="primary" :loading="loading" plain @click="nextstep"
        >下一步</el-button
      >
    </div>
  </div>
</template>
<script setup>
import {
  getLawOption,
  getAttorneyOption,
  getCompanySignature,
  createTemplatePreview,
} from "@/api/writ/template";
// 全局变量
const { proxy } = getCurrentInstance();
const route = useRoute();

// 父级传参
const props = defineProps({
  form: { type: Object, default: {} },
  rules: { type: Object, default: {} },
  signData: { type: Object },
  pdfSrcNotSign: { type: String },
  totalNotSign: { type: Number },
  stepActive: { type: Number },
  toBack: { type: Function },
  nextstep: { type: Function },
});
const loading = ref(false);
const lawList = ref([]); // 获取签章列表
const attorneyList = ref([]); // 获取代理律师签章列表

// 下一步
function nextstep() {
  proxy.$refs["signFormRef"].validate((valid) => {
    if (valid) {
      let req = JSON.parse(JSON.stringify(props.form));
      if (req.positionList.length == 0) {
        proxy.$modal.msgWarning("请选择签章图片！");
        return false;
      }
      req.positionList = req.positionList.filter((sign, index) => {
        if (sign) {
          sign.signPic = sign.info || sign.signPic;
          sign.lawyerInfo = req.lawyer[index];
        }
        return sign;
      });

      if (req.tailContent) {
        req.tailContent = "";
      }
      req.pageHeader = req.pageHeader || "";
      req.pageFooter = req.pageFooter || "";
      req.scaleHeader = req.scaleHeader || "";
      req.scaleFooter = req.scaleFooter || "";
      req.originalUrl = props.detailSrc;
      req.previewUrl = props.detailSrc;
      req.sourceFileType = 0;
      createTemplatePreview(req).then((res) => {
        if (props.stepActive == 1) {
          if (res.data) {
            props.nextstep(res);
          }
        }
      });
    }
  });
}

let mouseX = 0;
let mouseY = 0;
// 获取鼠标在图片的位置
function getMouse(e) {
  mouseX = e.offsetX;
  mouseY = e.offsetY;
}
// 获取拖动元素
let signImg = null;

// 拖动第几个元素
let index = 0;

// 设置 z-index 层级变量
let zIndex = 3;

// 存放编辑时，拖动的图片src径路
let signImgSrc = undefined;

// 开始拖动签章
function handleDragstart(i, e) {
  signImg = e.target.parentNode;
  if (route.params.id) {
    const positionList = props.form.positionList[i];
    signImgSrc = positionList.signPic || positionList.info;
    e.target.src = positionList.signPic || positionList.info;
  }

  index = i;
  if (signImg.style["z-index"] !== "") {
    signImg.style["zIndex"] = 1000;
    ++zIndex;
    proxy.$refs["previewRef"].style["zIndex"] = 999;
  }
}

// 目标元素事件
// 拖动元素在目标元素区域中拖动
function handleDragover(e) {
  e.preventDefault();
}

let pageLength = 1123;

// 鼠标放下的事件
function handleDrop(e) {
  let top = e.offsetY - mouseY;
  let left = e.offsetX - mouseX;
  signImg.style = `position: absolute;top: ${top}px;left: ${left}px;z-index:${
    zIndex < 3 ? 3 : zIndex
  };`;
  props.form.positionList[index].top = top;
  props.form.positionList[index].left = left;
  const location = props.form.positionList[index].info ? "right" : "left";
  props.form.positionList[index].location = location;
  let pageNum = Math.ceil(top / pageLength < 1 ? 1 : top / pageLength);

  let bottom = 0;
  if (pageNum > 1) {
    bottom = top % 1123;
  } else {
    bottom = top;
  }
  props.form.positionList[index].pageNum = pageNum;
  props.form.positionList[index].bottom = 1123 - bottom;

  const { code, info, signName, signPic } = props.form.positionList[index];
  props.form.positionList[index].signName = code || signName;
  props.form.positionList[index].signPic = info || signPic;
  props.form.positionList[index].index = index;
  proxy.$refs["previewRef"].style["zIndex"] = 2;
  const isOld = signImg.dataset["old"];
  if (route.params.id && signImg.firstChild.tagName == "IMG" && isOld && signImgSrc) {
    signImg.innerHTML = `<img class="chapter-img" src=${signImgSrc}>`;
  }
}

//获取签章机构
function getLawList() {
  getLawOption().then((res) => {
    lawList.value = res.data;
    nextTick(() => {
      props.form.lawyer.forEach((item, index) => {
        if (item.lawId) {
          getAttorneyList(index, item.lawId);
        }
      });
    });
  });
}
//选项-获取代理律师（签章名称）
function getAttorneyList(index, lawId) {
  props.form.lawyer[index] = {};
  props.form.lawyer[index].lawId = lawId;
  props.form.lawyer[index].pid = index;
  if (lawId) {
    let req = { lawId };
    const i = lawList.value.findIndex((law) => law.code == lawId);
    props.form.lawyer[index].lawPerson = lawList.value[i].info;
    getAttorneyOption(req).then((res) => {
      attorneyList.value = res.data;
      props.form.signId = res.data?.[0]?.code;
      props.form.lawyer[index].signId = res.data?.[0]?.code;
      props.form.lawyer[index].lawFactory = res.data?.[0]?.info;
      if (props.signData) {
        props.signData.lawPerson = res.data?.[0]?.info;
      }

      getCompanySignature({
        lawId,
        signId: res.data?.[0]?.code,
      }).then((res) => {
        res.data.forEach((sign) => {
          sign.pid = index;
          sign.index = index;
          sign.signType = 1;
          sign.signId = sign.code;
          sign.lawId = lawId;
          sign.location = "right";
        });
        props.form.positionList[index] = res.data[0];
      });
    });
  } else {
    props.form.positionList[index] = {
      index,
      location: "right",
    };
    props.form.lawyer[index].lawFactory = "";
    props.form.lawyer[index].signId = "";
    props.form.lawyer[index].lawPerson = "";
  }
}
//获取代理律师签章（代理律师）
function getSignSrc(index, signId) {
  props.form.lawyer[index].signId = signId;

  const i = attorneyList.value.findIndex((law) => law.code == signId);
  if (signId) {
    props.form.lawyer[index].lawFactory = attorneyList.value?.[i]?.info;
    props.signData.lawPerson = undefined;
    attorneyList.value.forEach((item, index) => {
      if (item.code == props.form.signId) {
        props.signData.lawPerson = item.info;
      }
    });
    signSrc.value = gongzhang;
    getCompanySignature({ ...props.form.lawyer[index], signId }).then((res) => {
      res.data.forEach((sign) => {
        sign.pid = index;
        sign.location = "right";
        sign.signType = 1;
        sign["signId"] = sign.code;
        sign.index = index;
        sign.signId = props.form.lawyer[index]?.signId;
        sign.lawId = props.form.lawyer[index]?.lawId;
      });

      props.form.positionList[index] = res.data[0];
      nextTick(() => {
        const drags = proxy.$refs["dragElRef"];
        const dragIndex = drags.findIndex((drag) => {
          return +drag.dataset["chapterInfo"] == index;
        });
        proxy.$refs["dragElRef"][dragIndex].style["position"] = "";
      });
    });
  } else {
    props.form.lawyer[index].lawFactory = "";

    getCompanySignature({ lawId: props.form.lawyer[index].lawId })
      .then((res) => {
        res.data.forEach((sign) => {
          sign.pid = index;
          sign.signType = 0;
          sign.location = "right";
          sign.index = index;
          sign.lawId = props.form.lawyer[index].lawId;
        });
        props.form.positionList[index] = res.data?.[0];
        props.form.positionList[index].signId = null;
        nextTick(() => {
          const drags = proxy.$refs["dragElRef"];
          const dragIndex = drags.findIndex((drag) => {
            return +drag.dataset["chapterInfo"] == index;
          });
          proxy.$refs["dragElRef"][dragIndex].style["position"] = "";
        });
      })

      .catch((err) => {});
  }
}
// 添加签章
function addSign() {
  props.form.lawyer.push({ lawId: undefined, signId: undefined });
}
// 删除签章
function delSign() {
  const index = props.form.lawyer && props.form.lawyer.length - 1;
  props.form.positionList[index] && props.form.positionList.splice(index, 1);
  props.form.lawyer.pop();
}
// 计算-盖章的个数
function positionListLeft() {
  return (
    props.form.positionList &&
    props.form.positionList?.filter((sign) => sign.location == "left")
  );
}
// 计算-没盖章的个数
function positionListRight() {
  return (
    props.form.positionList &&
    props.form.positionList?.filter((sign) => sign.location == "right")
  );
}
defineExpose({ getLawList });
</script>
<style>
img.chapter-img {
  display: block !important;
  width: 157px !important;
  height: 158px !important;
}
</style>
<style lang="scss" scoped>
.step-item {
  width: 95%;
  min-height: 400px;
  margin: 40px auto;

  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;

    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;

      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

.template-preview-chapter {
  position: relative;
  display: flex;

  .template-preview {
    position: relative;
    width: 794px;
    min-height: 1123px;

    .template-preview-sign-area {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 1px solid #999;
    }

    .template-preview-content {
      width: 794px;
      min-height: 1123px;
    }

    .template-preview-sign-area {
      z-index: 2;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }

  .template-chapter-list {
    & > div {
      margin-bottom: 10px;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }
}
</style>
