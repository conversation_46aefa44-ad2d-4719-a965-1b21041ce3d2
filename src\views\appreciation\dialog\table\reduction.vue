<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="申请时间" prop="applyDate" key="applyDate" align="center" />
    <el-table-column label="申请人" prop="applicant" key="applicant" align="center" />
    <el-table-column label="申请原因" prop="reason" key="reason" align="center">
      <template #default="{ row }">
        <Tooltip :content="row.reason" />
      </template>
    </el-table-column>
    <el-table-column label="减免后应还金额" prop="amountAfterDeduction" key="amountAfterDeduction" align="center">
      <template #default="{ row }">
        <span>{{ numFilter(row.amountAfterDeduction) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="减免后还款日" prop="afterReductionDate" key="afterReductionDate" align="center" />
    <el-table-column label="申请状态" prop="state" key="state" align="center" />
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import { selectReductionRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  caseId: { type: [String, Number], default: undefined }
});
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: props.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm }
  loading.value = true;
  selectReductionRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();

defineExpose({ getList })
</script>

<style scoped></style>
