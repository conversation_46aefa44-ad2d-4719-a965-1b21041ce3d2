import request from '@/utils/request'

// 诉讼保全 - 列表
export function getFreezeList(query) {
    return request({
        url: '/freeze/selectList',
        method: 'get',
        params: query
    })
}

// 诉讼保全 - 保全流程-案件流转
export function transferIegalFreeze(data) {
    return request({
        url: '/freeze/batchTransfer',
        method: 'post',
        data: data
    })
}

// 诉讼保全 - 统计剩余应还债权金额 计算
export function totalMoneyFreeze(data) {
    return request({
        url: '/freeze/selectWithMoney',
        method: 'post',
        data: data
    })
}
// 诉讼保全 - 统计剩余应还债权金额 计算
export function applyKeepFreeze(data) {
    return request({
        url: '/freeze/batchFreeze',
        method: 'post',
        data: data
    })
}
