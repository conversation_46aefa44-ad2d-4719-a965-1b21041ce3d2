<template>
  <div class="sidebar-logo-container" :class="{ 'collapse': collapse }" :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 :class="['sidebar-title', isStaging ? 'text-watermark': '']" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
        <!-- <img :src="testWatermark" class="sidebar-test-watermark" v-if="isStaging" /> -->
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss'
import logo from '@/assets/logo/logo.png'
import testWatermark from '@/assets/images/test.png'

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

const title = computed(() => import.meta.env.VITE_APP_TITLE);
const store = useStore();
const sideTheme = computed(() => store.state.settings.sideTheme);

// 是否是测试环境
const isStaging = computed(() => {
    return import.meta.env.MODE === 'staging' || 
          import.meta.env.VITE_APP_ENV === 'staging'
});
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: rgba(255, 255, 255, .9) !important;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
      position: relative;
    }

    & .sidebar-test-watermark {
      opacity: .38;
      position: absolute;
      top: 10%;
      right: 5%;
      width: 35px;
      height: 35px;
      transform: rotate(-45deg);
      animation: all .8s ease-out;
    }

    & .text-watermark::after {
      position: absolute;
      content: '测试环境';
      top: 65%;
      right: 0;
      color: #fff;
      background-color: #1fb1f2;
      font-size: 11px;
      padding: 0 2px;
      height: 1.6em;
      line-height: 1.6em;
      border-radius: 4px;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>