<template>
    <div>
        <el-form-item prop="documentTemplateIds" label="选择立案文书">
            <el-checkbox-group v-model="form.documentTemplateIds" class="list">
                <div class="item" v-for="item in writList" :key="item.id">
                    <el-checkbox :label="item.documentTemplateId" :value="item.documentTemplateId">
                        {{ item.documentTemplateName }}
                    </el-checkbox>
                </div>
            </el-checkbox-group>
        </el-form-item>
    </div>
</template>
<script setup>
import { getWritTemplateBylawAgencyId } from '@/api/courtManage/courtManage';

const props = defineProps({
    form: { type: Object, default: {} }
})
const writList = ref([])
onMounted(() => {
    getWritTemplate(props.form.courtId)
})
// 根据机构查询文书模板
function getWritTemplate(agencyId) {
    getWritTemplateBylawAgencyId({ agencyId }).then(res => {
        writList.value = res.data
    })
}           
</script>
<style lang="scss" scoped>
.list {
    width: 600px;
    display: flex;
    flex-wrap: wrap;

    .item {
        width: 50%;
    }
}
</style>