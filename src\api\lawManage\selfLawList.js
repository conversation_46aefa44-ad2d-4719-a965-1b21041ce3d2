import request from '@/utils/request'

// 诉讼列表
export function getLawListApi(params) {
  return request({
    url: '/lawsuitStatus/lawsuit/list',
    method: 'get',
    params
  })
}

// 诉讼申请通过
export function passApplyApi(data) {
  return request({
    url: '/zws_jg_approve/jgApprovePass',
    method: 'post',
    data
  })
}

// 诉讼申请不通过
export function rejectApplyApi(data) {
  return request({
    url: '/zws_jg_approve/jgApproveNotPass',
    method: 'post',
    data
  })
}

// 诉讼撤销
export function cancelSuitApi(data) {
  return request({
    url: '/lawsuitStatus/lawsuit/revoke',
    method: 'post',
    data
  })
}

// 查询诉讼进度列表
export function getLawProgressListApi(params) {
  return request({
    url: '/lawsuitStatus/process/list',
    method: 'get',
    params
  })
}

// 更新诉讼进度
export function updateLawProgressApi(data) {
  return request({
    url: '/lawsuitStatus/process',
    method: 'post',
    data
  })
}
