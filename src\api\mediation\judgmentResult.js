import request from '@/utils/request'

// 判决与审判 - 查询
export function getJudgeList(query) {
    return request({
        url: '/judge/list',
        method: 'get',
        params: query
    })
}

// 判决与审判 - 新增判决
export function addJudge(data) {
    return request({
        url: '/judge/addJudge',
        method: 'post',
        data
    })
}

// 判决与审判 - 判决与结果批量发送生成短信模板短信
export function previewTemplateJudge(data) {
    return request({
        url: '/judge/previewJudgeTemplate',
        method: 'post',
        data
    })
}

// 判决与审判 - 判决与结果批量发送短信
export function sendMessagesJudge(data) {
    return request({
        url: '/judge/sendJudgeMessages',
        method: 'post',
        data
    })
}

// 判决与审判 - 申请执行立案
export function addAplayCaseJudge(data) {
    return request({
        url: '/judge/addAplayCase',
        method: 'post',
        data
    })
}
// 判决与审判 - 批量导入
export function importDataJudge(data) {
    return request({
        url: '/judge/importData',
        method: 'post',
        data
    })
}
// 判决与审判 - 批量结案
export function batchConcludeCaseJudge(data) {
    return request({
        url: '/judge/batchConcludeCase',
        method: 'post',
        data
    })
}
