import request from "@/utils/request";

//获取工单详情
export function getFollowUp(query) {
  return request({
    url: "/collection/selectWorkOrderFollowUp",
    method: "get",
    params: query,
  });
}

//获取团队工单详情
export function getFollowUpTeam(query) {
  return request({
    url: "/collection/selectTeamWorkOrderFollowUp",
    method: "get",
    params: query,
  });
}

//获取列表
export function getWorkOrder(query) {
  return request({
    url: "/collection/selectWorkOrder",
    method: "get",
    params: query,
  });
}

//根据主键id集合批量处理工单
export function updateWorkOrder(data) {
    return request({
      url: "/collection/updateWorkOrder",
      method: "PUT",
      data: data,
    });
  }
  

//获取工单数量
export function getWorkCount(query) {
  return request({
    url: "/collection/selectWorkOrderNumber",
    method: "get",
    params: query,
  });
}

//继续跟进
export function addFollowUp(data) {
  return request({
    url: "/collection/insertWorkOrderFollowUp",
    method: "post",
    data: data,
  });
}

