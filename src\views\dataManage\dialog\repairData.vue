<template>
  <!-- 退案留案弹窗 -->
  <el-dialog :title="title" v-model="open" width="600px" :before-close="cancel" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-table :data="form.fileData" row-key="index" max-height="290px"
        :tree-props="{ children: 'fileInformationPojo', hasChildren: 'hasChildren' }">
        <el-table-column :width="30" prop="index">
          <template>
            <span></span>
          </template>
        </el-table-column>
        <el-table-column label="资料路径">
          <template #default="{ row, $index }">
            <el-form-item :prop="`fileData.${$index}.path`" :rules="rules.path">
              <el-popover placement="bottom" :width="240" :ref="`popover-${$index}`" trigger="hover" v-if="row.path">
                <template #reference>
                  <el-input v-model="row.path" clearable :disabled="true" style="width: 240px" />
                </template>
                <span>{{ row.path }}</span>
              </el-popover>
              <el-input v-else v-model="row.originalFilename" clearable :disabled="true" style="width: 240px" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="文件名称">
          <template #default="{ row, $index }">
            <el-form-item :prop="`fileData.${$index}.originalFilename`" :rules="rules.originalFilename">
              <el-popover placement="bottom" :width="240" :ref="`popover-${$index}`" trigger="hover"
                v-if="row.originalFilename">
                <template #reference>
                  <el-input v-model="row.originalFilename" clearable :disabled="true" style="width: 240px" />
                </template>
                <span>{{ row.originalFilename }}</span>
              </el-popover>
              <el-input v-else v-model="row.originalFilename" clearable :disabled="true" style="width: 240px" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="150">
          <template #default="{ row, $index }">
            <el-button type="text" v-if="row?.outermostLayer == 1" link @click="delFileUrl(row)">删除</el-button>
            <el-button type="text" link v-if="type == 1 && ![0, -1].includes(row.id) && row.outermostLayer == 1"
              @click="downLoadData(row, $index)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item class="mt10">
        <el-upload ref="uploadRef" accept=".zip, .rar, .doc, .docx, .xls, .xlsx, .pdf, .png, .jpg, .jpeg "
          style="width: 500px" :headers="upload.headers" :action="upload.url" :before-upload="handleFileUploadBefore"
          :on-change="handleEditChange" :limit="99" :on-success="handleContractFileSuccess" :file-list="fileList"
          :auto-upload="false" :show-file-list="false" :disabled="uploadding">
          <template #default>
            <el-button type="primary" class="mt10" :loading="uploadding">新增上传</el-button>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item class="mt10">
        <div>注：请填写您想修复的文件在服务器上的路径和文件名称，可放多个路径</div>
        <div>示例</div>
        <div class="example-img">
          <img src="../../../assets/images/example.png" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          :disabled="type == 0 && !form.fileData[0]?.originalFilename">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { getToken } from "@/utils/auth";
import {
  getInformation,
  getRetrievalCaseId,
  repairUploadedData,
  checkDownloadFile,
} from "@/api/assets/dataManage";
import exampleImg from "@/assets/images/example.png";
//上传参数
const fileList = ref([]);
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/management/retrieval/uploadFile",
});
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const uploadding = ref(false);
const open = ref(false);
const title = ref("");
const emit = defineEmits(["getList"]);
const type = ref(0);
const caseId = ref(undefined);
const ids = ref([]);
//提交数据
const data = reactive({
  form: {
    fileData: [
      {
        id: -1,
        path: undefined,
        originalFilename: undefined,
        fileInformationPojo: [],
        outermostLayer: 1,
        index: +new Date(),
      },
    ],
  },
  rules: {
    path: [{ required: true, message: "请输入文件路径", trigger: "blur" }],
    originalFilename: [{ required: true, message: "请输入文件名", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
async function opendialog(row, code) {
  open.value = true;
  if (code == 1) {
    getCaseFile(row.caseId);
  }
  caseId.value = row.caseId;
  type.value = code;
  title.value = type.value == 0 ? `上传资料` : `修复资料`;
}

//获取案件资料
function getCaseFile(caseId) {
  let req = {
    caseId,
  };
  getInformation(req).then((res) => {
    form.value.fileData = [];
    res.data.forEach((item, index) => {
      let obj = {
        id: item.id,
        path: item.fileRoute,
        originalFilename: item.firstName,
        name: item.modifyName,
        url: item.fileUrl,
        outermostLayer: item.outermostLayer,
        index: +new Date() + index,
        minioUrl: item.minioUrl
      };
      let separateListData = [];
      (item.separateList?.length != 0) && item.separateList?.forEach((v, h) => {
        let fileObj = {
          id: v.id,
          path: v.fileRoute,
          originalFilename: v.firstName,
          name: v.modifyName,
          url: v.fileUrl,
          outermostLayer: 0,
          minioUrl: item.minioUrl
        }
        separateListData.push(fileObj)
      })
      obj.fileInformationPojo = separateListData
      obj.hasChildren = separateListData.length > 0;
      form.value.fileData.push(obj);
    });
  });
}

//删除
function delFileUrl(row) {
  let index = form.value.fileData?.findIndex(item => item.id == row.id)
  form.value.fileData.splice(index, 1);
  fileList.value.splice(index, 1);
  if (![0, -1].includes(row.id)) {
    ids.value.push(row.id);
  }
}

//下载文件
function downLoadData(row, index) {
  let req = {
    fileId: row.id,
  };
  let fileType = row.originalFilename
    .substring(row.originalFilename.lastIndexOf(".") + 1, row.originalFilename.length)
    .toLowerCase();
  checkDownloadFile(req).then((res) => {
    if (res.data.state == 1) {
      if (["jpg", "jpeg", "png"].includes(fileType)) {
        let imgData = {
          url:
            import.meta.env.VITE_APP_BASE_API +
            `/management/retrieval/downloadFileId?fileId=${row.id
            }&Authorization=${getToken()}`,
          name: row.originalFilename,
        };
        downloadImg(imgData);
      } else {
        proxy.$modal.loading("正在下载文件，请稍候...");
        window.location.href =
          import.meta.env.VITE_APP_BASE_API +
          `/management/retrieval/downloadFileId?fileId=${row.id
          }&Authorization=${getToken()}`;
        proxy.$modal.closeLoading();
      }
    } else {
      proxy.$modal.msgWarning(msg);
    }
  });
}

//下载图片
function downloadImg(data) {
  proxy.$modal.loading("正在下载文件，请稍候...");
  pathToBase64(data.url)
    .then((res) => {
      const link = document.createElement("a");
      link.href = res;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      proxy.$modal.closeLoading();
    })
    .catch((err) => {
      console.log(err);
    });
}

//获取Base64
function pathToBase64(url) {
  return new Promise((resolve, reject) => {
    let image = new Image();
    image.onload = function () {
      let canvas = document.createElement("canvas");
      canvas.width = this.naturalWidth;
      canvas.height = this.naturalHeight;
      canvas.getContext("2d").drawImage(image, 0, 0);
      let result = canvas.toDataURL("image/png");
      resolve(result);
    };
    image.setAttribute("crossOrigin", "Anonymous");
    image.src = url;
    image.onerror = () => {
      reject(new Error("urlToBase64 error"));
    };
  });
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    let formatFileData = [];
    form.value.fileData.forEach((item, index) => {
      let formatItem = {
        id: item.id,
        firstName: item.originalFilename,
        modifyName: item.name,
        fileUrl: item.url,
        fileRoute: item.path,
        minioUrl: item.minioUrl
      };
      let fileInformationPojo = []
      item.fileInformationPojo?.forEach((v, h) => {
        let separateObj = {
          id: v.id,
          firstName: v.originalFilename,
          modifyName: v.name,
          fileUrl: v.url,
          fileRoute: v.path,
          minioUrl: item.minioUrl
        }
        fileInformationPojo.push(separateObj)
      })
      formatItem.separateList = fileInformationPojo
      formatFileData.push(formatItem);
    });
    let req = {
      caseId: caseId.value,
      ids: ids.value,
      retrievalFiles: formatFileData,
    };
    repairUploadedData(req)
      .then((res) => {
        proxy.$modal.msgSuccess(`操作成功！`);
        cancel();
        emit("getList");
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    fileData: [
      {
        id: -1,
        path: undefined,
        originalFilename: undefined,
        fileInformationPojo: [],
        outermostLayer: 1,
        index: +new Date(),
      },
    ],
  };
  ids.value = [];
  uploadding.value = false;
}

//取消
function cancel() {
  reset();
  open.value = false;
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 1024 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过1GB!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 1024 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过1GB!");
    fileList.pop();
    return false;
  }
  console.log(file);
  console.log(fileList);
  if (fileList.length > 0) {
    let req = {
      caseId: caseId.value,
    };
    let fileFlag = false;
    form.value.fileData.forEach((item, index) => {
      if (item.originalFilename == file.name) {
        fileFlag = true;
      }
    });
    if (!fileFlag && file.response == undefined) {
      getRetrievalCaseId(req)
        .then((res) => {
          proxy.$refs[`uploadRef`].submit();
          uploadding.value = true;
        })
        .catch((err) => {
          uploadding.value = false;
        });
    }
  }
}

/* 上传文件上传成功处理 */
const handleContractFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    uploadding.value = false;
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    uploadding.value = false;
    //文件路径填充
    let req = {
      id: 0,
      path: data.path,
      originalFilename: data.originalFilename,
      name: data.name,
      url: data.url,
      minioUrl: data.minioUrl,
      fileInformationPojo: data.fileInformationPojo,
      outermostLayer: 1,
      index: +new Date(),
    };
    let flag = false;
    form.value.fileData.forEach((item, index) => {
      if (item.id == -1) {
        flag = true;
        form.value.fileData.splice(index, 1);
        form.value.fileData.push(req);
        return false;
      }
    });
    if (!flag) {
      form.value.fileData.push(req);
    }
  }
};

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
.example-img {
  width: 400px;
  height: 180px;

  img {
    width: 100%;
    height: 100%;
  }
}

:deep(.cell) {
  .el-form-item {
    margin-bottom: 0px;
  }
}
</style>
