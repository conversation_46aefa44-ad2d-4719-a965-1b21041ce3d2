<template>
  <div class="app-container">
    <el-form
      v-show="!showTagSearch"
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-position="right"
      :label-width="130"
      class="form-content h-50"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="案件ID">
        <el-input
          type="textarea"
          :rows="1"
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input
          type="textarea"
          :rows="1"
          v-model="queryParams.clientName"
          placeholder="请输入姓名"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input
          type="textarea"
          :rows="1"
          v-model="queryParams.clientPhone"
          placeholder="请输入手机号码"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input
          type="textarea"
          :rows="1"
          v-model="queryParams.clientIdcard"
          placeholder="请输入证件号码"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="UID" prop="uid">
        <el-input
          v-model="queryParams.uid"
          placeholder="请输入uid"
          clearable
          style="width: 328px"
          onkeyup="value=value.replace(/[^A-z0-9/g, '')"
          oninput="value=value.replace(/[^A-z0-9]/g, '')"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="分配时间">
        <el-date-picker
          v-model="queryParams.allocatedTime"
          style="width: 328px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="转让方" prop="entrustingPartyId">
        <el-select
          v-model="entrustingPartyIdList"
          placeholder="请输入或选择转让方"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @visible-change="OwnerList"
          style="width: 328px"
        >
          <el-option
            v-for="item in entrusts"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="批次号" prop="batchNum">
        <el-select
          v-model="batchNumSelected"
          placeholder="请输入或选择批次号"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @visible-change="getbatchNumList"
          style="width: 328px"
        >
          <el-option
            v-for="item in batchNumArray"
            :key="item.label"
            :label="item.value"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="跟进状态" prop="followUpState">
        <el-select
          v-model="followUpStateList"
          placeholder="请输入或选择跟进状态"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @focus="FollowUpState"
          style="width: 328px"
        >
          <el-option
            v-for="item in follows"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="户籍地" prop="clientCensusRegister">
        <el-cascader
          :options="areas"
          ref="clientCensusRegisterRef"
          v-model="clientCensusRegisterList"
          @change="changeOption"
          filterable
          placeholder="请输入或选择地区"
          collapse-tags
          collapse-tags-tooltip
          clearable
          @focus="CensusRegisters"
          style="width: 328px"
          :props="{ multiple: true, value: 'label' }"
        />
      </el-form-item>
      <el-form-item label="逾期日期（末次）">
        <el-date-picker
          v-model="queryParams.clientOverdueStart"
          style="width: 328px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="调解员" prop="odvName">
        <el-select
          v-model="odvIdList"
          placeholder="选择调解员"
          clearable
          filterable
          :reserve-keyword="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @focus="getOdvName"
          style="width: 328px"
        >
          <el-option
            v-for="item in member"
            :key="item.id"
            :label="item.employeeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退案日期">
        <el-date-picker
          v-model="queryParams.returnCaseDate"
          style="width: 328px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="未跟进天数">
        <el-row style="width: 328px">
          <el-col :span="11">
            <el-input v-model="queryParams.notFollowed1" clearable />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input v-model="queryParams.notFollowed2" clearable />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item prop="mediateStage" label="调解阶段">
        <el-cascader
          v-model="mediateStageSelected"
          style="width: 328px"
          clearable
          filterable
          placeholder="请选择案件阶段"
          :options="mediateStageOptions"
          @focus="getMediateStageTreeFun"
          :props="{ label: 'stageName', value: 'stageName' }"
        />
      </el-form-item>
      <el-form-item label="委案金额">
        <el-row style="width: 328px">
          <el-col :span="11">
            <el-input
              type="text"
              v-model="queryParams.clientMoney1"
              @blur="validatePrincipalRange"
              @input="(value) => value.replace(/[^\d]/g, '')"
              clearable
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input
              type="text"
              v-model="queryParams.clientMoney2"
              @blur="validatePrincipalRange"
              @input="(value) => value.replace(/[^\d]/g, '')"
              clearable
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="地区" prop="area">
        <el-input
          v-model="queryParams.area"
          placeholder="请输入地区"
          clearable
          style="width: 328px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="客户标签" prop="label">
        <el-button
          class="custom-btn"
          color="#626aef"
          @click="showTagSearch = !showTagSearch"
          >切换客户标签搜索板块</el-button
        >
        <!-- <el-select v-model="labelList" placeholder="请输入或选择标签" multiple collapse-tags collapse-tags-tooltip clearable
          filterable :reserve-keyword="false" style="width: 328px" @change="labelChange">
          <el-option v-for="item in tags" :key="item.code" :label="item.labelContent" :value="item.code">
            <div>
              <el-icon :color="lableColor[item.code]" class="icon-flag">
                <flag />
              </el-icon>
              {{ item.labelContent }}
            </div>
          </el-option>
        </el-select> -->
      </el-form-item>
    </el-form>
    <div v-show="showTagSearch" class="tag-search">
      <div class="mb20">
        <el-button
          class="custom-btn"
          color="#626aef"
          @click="showTagSearch = !showTagSearch"
          >切换其他搜索板块</el-button
        >
      </div>

      <div class="tag-search-content">
        <div class="tag-search-item">
          <el-radio-group v-model="logical" size="small">
            <el-radio-button :label="0">或</el-radio-button>
            <el-radio-button :label="1">且</el-radio-button>
          </el-radio-group>
        </div>
        <div class="tag-search-list">
          <div
            v-for="(item, index) in queryTags"
            :key="index"
            class="tag-search-item"
          >
            <el-select
              v-model="item.field"
              placeholder="请选择搜索字段"
              clearable
              filterable
              style="width: 180px"
            >
              <el-option label="客户标签" :value="0" />
            </el-select>
            <el-select
              v-model="item.general"
              placeholder="请选择判断语句"
              clearable
              filterable
              style="width: 180px"
            >
              <el-option label="只包含" :value="0" />
              <el-option label="包含" :value="1" />
              <el-option label="不包含" :value="2" />
            </el-select>
            <el-select
              v-model="item.content"
              placeholder="请选择客户标签"
              clearable
              filterable
              style="width: 180px"
            >
              <el-option
                v-for="item in tags"
                :key="item.code"
                :label="item.labelContent"
                :value="item.code"
              >
                <div>
                  <el-icon :color="lableColor[item.code]" class="icon-flag">
                    <flag />
                  </el-icon>
                  {{ item.labelContent }}
                </div>
              </el-option>
            </el-select>

            <el-button
              type="danger"
              icon="Delete"
              circle
              @click="delTagSearch(index)"
            />
          </div>
        </div>
        <div class="tag-search-item">
          <el-button type="text" @click="addTagSearch">新增条件</el-button>
        </div>
      </div>
    </div>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb10 mt10" style="min-height: 26px">
      <el-button
        plain
        :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)"
        @click="opendialog({ type: 0, row: selectedArr })"
        v-hasPermi="['saasc:case:updateRecovery']"
        >回收案件</el-button
      >
      <el-button
        plain
        :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)"
        type="success"
        @click="opendialog({ type: 2, row: selectedArr })"
        v-hasPermi="['saasc:case:insertKeep']"
        >留案</el-button
      >
      <el-button
        plain
        :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)"
        type="info"
        @click="opendialog({ type: 3, row: selectedArr })"
        v-hasPermi="['saasc:case:insertStop']"
        >停案</el-button
      >
      <!-- <el-button plain :disabled="single" type="danger" @click="openLabelCase"
        v-hasPermi="['saasc:case:selectMarkCase']">标记案件</el-button> -->
      <el-button
        plain
        :disabled="single"
        type="primary"
        @click="downloadCase"
        v-hasPermi="['saasc:case:exportSettingStatus']"
        >导出案件</el-button
      >
      <el-button
        plain
        :disabled="single || !['2'].includes(activeTab)"
        type="primary"
        @click="opendialog({ type: 4, row: selectedArr })"
        v-hasPermi="['saasc:case:recover']"
        >恢复案件</el-button
      >
      <!-- <el-button plain :disabled="single" type="primary" @click="openCreateCaseTaskDialog"
        v-if="settleState != '1' && checkPermi(['saasc:case:createOutboundTasks'])">创建智能外呼任务</el-button> -->
      <!-- <el-button plain :disabled="single" type="danger" @click="removeCase()"
        v-hasPermi="['saasc:case:removeCase']">删除案件</el-button> -->
      <el-button
        v-hasPermi="['saasc:case:recycleBin']"
        plain
        type="warning"
        :disabled="single"
        @click="recycleBinCase()"
        >放入回收站</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-row class="mb10">
      <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
        <el-checkbox
          v-for="item in checkStatus"
          :key="item.label"
          :label="item.label"
          :indeterminate="item.indeterminate"
          :disabled="caseList.length === 0"
        />
      </el-checkbox-group>
      <el-col :span="1.5" class="ml10 mt5">
        <el-tooltip class="hint" effect="light" placement="bottom-start">
          <div>
            <svg-icon class="hint-item" icon-class="question" />
          </div>
          <template #content>
            <div class="info-tip case-tips">
              <el-icon class="info-tip-icon" :size="16">
                <warning-filled />
              </el-icon>
              <p>
                本页选中：数据范围是当前结果页选中的案件；搜索结果全选：数据范围是当前列表的所有数据或者是当前搜索结果的全部数据合集；进行模板分案操作前请先下载案件信息模版，
                点击下载
                <span
                  class="text-danger"
                  @click="downTpl"
                  style="cursor: pointer"
                  >案件分配模板</span
                >
              </p>
              <p>
                规则分案：根据当前页面结果或搜索结果，选择分案规则进行分案操作；指定分案：将当前页面结果或搜索结果指定分配给某个处置人员；
              </p>
              <!-- <p>
                留案需操作退案后可再次分配、停催案件需恢复后再次分配；如需进行共债分案，点击进入共债案件后开始分案；
              </p> -->
            </div>
          </template>
        </el-tooltip>
      </el-col>
      <div class="text-flex ml20">
        <span>剩余债权总额：</span>
        <span class="text-danger mr10">
          {{ numFilter(statistics.totalMoney) }}
        </span>
        <span>剩余债权本金：</span>
        <span class="text-danger mr10">
          {{ numFilter(statistics.principal) }}
        </span>
        <span>案件数量：</span>
        <span class="text-danger">{{ statistics.caseNum }}</span>
      </div>
      <div class="top-right-btn">
        <el-radio-group v-model="settleState" @change="settleStateChange">
          <el-radio label="1" value="1">已结清</el-radio>
          <el-radio label="0" value="0">未结清</el-radio>
          <el-radio label="all" value="all">全部</el-radio>
        </el-radio-group>
      </div>
    </el-row>

    <el-row class="mb10">
      <el-button
        plain
        :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)"
        type="primary"
        @click="toPoint(2)"
        v-if="checkPermi(['saasc:case:writeAssignedDivision'])"
        >调解指定分案</el-button
      >
      <el-button
        plain
        :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)"
        type="success"
        @click="toRules(2)"
        v-if="checkPermi(['saasc:case:writeRuleDivision'])"
        >调解规则分案</el-button
      >
      <!-- <el-button plain :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)" type="primary"
        @click="toPoint(1)" v-if="checkPermi(['saasc:case:lawsuitAssignedDivision'])">诉讼指定分案</el-button> -->
      <!-- <el-button plain :disabled="single || !['0', '1', '3', 'all'].includes(activeTab)" type="success"
        @click="toRules(1)" v-if="checkPermi(['saasc:case:lawsuitRuleDivision'])">诉讼规则分案</el-button> -->
      <el-button
        plain
        type="info"
        @click="openTpldivision"
        v-hasPermi="['saasc:case:importData']"
        >调解模板分案</el-button
      >
      <el-button
        plain
        type="primary"
        @click="openCreateCaseTaskDialog"
        :disabled="single"
        v-hasPermi="['saasc:collection:createOutboundTasks']"
        >创建智能外呼任务</el-button
      >
      <div class="top-right-btn">
        <el-button
          plain
          type="warning"
          @click="caseBatchHandle"
          v-hasPermi="['saasc:case:batchOperation']"
          >案件批量操作</el-button
        >
        <el-button
          plain
          type="danger"
          @click="handleExport(0)"
          v-hasPermi="['saasc:case:selectUrgeRecord']"
          >导出沟通记录</el-button
        >
      </div>
    </el-row>

    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane label="未分配" name="0" />
      <el-tab-pane label="已分配" name="1" />
      <el-tab-pane label="停案" name="2" />
      <el-tab-pane label="留案" name="3" />
      <el-tab-pane label="全部" name="all" />
    </el-tabs>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="caseList"
      @selection-change="handleSelectionChange"
      @sort-change="sortChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="100"
        v-if="columns[0].visible"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-button type="text" @click="toDetails(row, $index)">{{
              row.caseId
            }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="UID"
        align="center"
        key="uid"
        prop="uid"
        :width="100"
        v-if="columns[1].visible"
      />
      <el-table-column
        label="客户标签"
        align="center"
        key="labelContent"
        prop="labelContent"
        :width="100"
        v-if="columns[2].visible"
      >
        <template #default="{ row }">
          <el-tooltip v-if="row.labelContent" placement="top">
            <template #content>{{ row.labelContent }}</template>
            <case-label
              class="ml5"
              v-if="row.label && row.label != 7"
              :code="row.label"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="调解状态"
        align="center"
        key="caseMediateState"
        prop="caseMediateState"
        :width="90"
        :formatter="(row) => caseStateFor(row.caseMediateState)"
        v-if="columns[3].visible"
      />
      <el-table-column
        label="调解阶段"
        align="center"
        key="mediatedStage"
        prop="mediatedStage"
        :width="90"
        v-if="columns[4].visible"
      />
      <el-table-column
        label="调解员"
        align="center"
        key="mediatorName"
        prop="mediatorName"
        :width="100"
        v-if="columns[5].visible"
      />
      <el-table-column
        label="诉讼阶段"
        align="center"
        key="disposeStage"
        prop="disposeStage"
        :width="90"
        v-if="columns[6].visible"
      />
      <el-table-column
        label="诉讼状态"
        align="center"
        key="caseState"
        prop="caseState"
        :width="90"
        :formatter="(row) => caseStateFor(row.caseState)"
        v-if="columns[7].visible"
      />
      <el-table-column
        label="诉讼员"
        align="center"
        key="odvName"
        prop="odvName"
        :width="100"
        v-if="columns[8].visible"
      />
      <el-table-column
        label="产品类型"
        align="center"
        key="productName"
        prop="productName"
        :width="100"
        v-if="columns[9].visible"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="姓名"
        align="center"
        key="clientName"
        prop="clientName"
        :width="90"
        v-if="columns[10].visible"
      />
      <el-table-column
        label="手机号码"
        align="center"
        key="clientPhone"
        prop="clientPhone"
        :width="110"
        v-if="columns[11].visible"
      />
      <el-table-column
        label="证件类型"
        align="center"
        key="clientIdType"
        prop="clientIdType"
        :width="90"
        v-if="columns[12].visible"
      />
      <el-table-column
        label="证件号码【户籍地】"
        align="center"
        key="clientIdcard"
        prop="clientIdcard"
        :width="180"
        v-if="columns[13].visible"
      >
        <template #default="{ row }">
          <span>
            {{ `${row.clientIdcard}` }}<br />
            {{ `【${row.clientCensusRegister || "--"}】` }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="出生日期"
        align="center"
        key="clientBirthday"
        prop="clientBirthday"
        v-if="columns[14].visible"
      />
      <el-table-column
        label="债权总金额"
        align="center"
        key="clientMoney"
        sortable="clientMoney"
        prop="clientMoney"
        :width="120"
        v-if="columns[15].visible"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.clientMoney) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="剩余应还债权金额"
        align="center"
        key="remainingDue"
        prop="remainingDue"
        :width="130"
        v-if="columns[16].visible"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="剩余应还本金"
        align="center"
        key="syYhPrincipal"
        prop="syYhPrincipal"
        :width="110"
        v-if="columns[17].visible"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.syYhPrincipal) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="逾期日期（末次）"
        align="center"
        key="clientOverdueStart"
        sortable="clientOverdueStart"
        prop="clientOverdueStart"
        :width="160"
        v-if="columns[18].visible"
      />
      <el-table-column
        label="逾期天数（末次）"
        align="center"
        key="overdueDays"
        sortable="overdueDays"
        prop="overdueDays"
        :width="160"
        v-if="columns[19].visible"
      />
      <el-table-column
        label="地区"
        align="center"
        key="area"
        prop="area"
        v-if="columns[20].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="跟进状态"
        align="center"
        key="followUpState"
        prop="followUpState"
        sortable="followUpState"
        :width="100"
        v-if="columns[21].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="沟通状态"
        align="center"
        key="urgeState"
        prop="urgeState"
        :width="90"
        v-if="columns[22].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="退案日期"
        align="center"
        key="returnCaseDate"
        sortable="returnCaseDate"
        prop="returnCaseDate"
        :width="110"
        v-if="columns[23].visible"
        :formatter="formatDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="分配时间"
        align="center"
        key="allocatedTime"
        sortable="allocatedTime"
        prop="allocatedTime"
        :width="160"
        v-if="columns[24].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="未跟进天数"
        align="center"
        key="notFollowed"
        sortable="notFollowed"
        prop="notFollowed"
        :width="120"
        v-if="columns[25].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="最后跟进时间"
        align="center"
        key="followUpAst"
        prop="followUpAst"
        sortable="followUpAst"
        :width="160"
        v-if="columns[26].visible"
        show-overflow-tooltip
      />
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="{ row }">
          <el-button
            v-if="
              row.caseState != 2 &&
              row.caseState != 4 &&
              checkPermi(['saasc:case:insertKeep'])
            "
            type="text"
            @click="opendialog({ type: 2, row: [row] })"
            >留案</el-button
          >
          <el-button
            v-if="
              row.caseState != 2 &&
              row.caseState != 4 &&
              checkPermi(['saasc:case:insertStop'])
            "
            type="text"
            @click="opendialog({ type: 3, row: [row] })"
            >停案</el-button
          >
          <el-button
            v-if="
              row.caseState != 2 &&
              row.caseState != 4 &&
              checkPermi(['saasc:case:selectUrgeRecord'])
            "
            type="text"
            @click="handleExport(1, row)"
            >导出沟通记录</el-button
          >
          <el-button
            v-if="checkPermi(['saasc:case:removeCase'])"
            type="text"
            @click="removeCase(row)"
            >删除案件</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 案件操作 -->
    <handleDialog
      :checkedType="checkedType[0]"
      :query="addFieldsRange(queryParams, rangfiles)"
      ref="handleDialogRef"
      @getList="getList"
      :destroy-on-close="true"
    />

    <!-- 导出沟通记录 -->
    <exportDialog
      :query="addFieldsRange(queryParams, rangfiles)"
      :checkedType="checkedType[0]"
      ref="exportdialogRef"
    />

    <!-- 模板分案 -->
    <tplDivision ref="tpldivisionRef" @getList="getList" />

    <!-- 标记案件 -->
    <leabelCaseVue @getList="getList" ref="leabelCaseRef" />

    <!-- 成功弹窗 -->
    <exportTip ref="exportTipRef" :exportType="`案件导出`" />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />
  </div>
</template>

<script setup name="CaseIndex">
import { checkPermi } from "@/utils/permission";
import { ElMessage } from "element-plus";
import { checkUserSip } from "@/api/system/user";
import tplDivision from "../../dialogIndex/tplDivision";
import handleDialog from "../../dialogIndex/handleDialog";
import exportDialog from "../../dialogIndex/exportDialog";
import leabelCaseVue from "../../dialogIndex/leabelCase";
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
import {
  getBatchNums,
  selectLabel,
  getFollowUpState,
  getUrgeState,
  getProvinces,
  selectAssetOwner,
  selectEmployees,
  selectListWithBatchNum,
  getMediateStageTree,
} from "@/api/common/common";
import {
  caseManageList,
  selectCaseManagesMoney,
  exportCase,
  deleteCase,
  insertRecycleBin,
} from "@/api/case/index/index";

//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const store = useStore();
//表格配置数据
const loading = ref(false);
const total = ref(0);
const multipleTableRef = ref();
const checkedType = ref(["本页选中"]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const caseList = ref([]);
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
//列表切换字段
const settleState = ref("0");
const activeTab = ref("all");
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    divisionTime: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    followUpState: undefined,
    urgeState: undefined,
    clientCensusRegister: undefined,
    clientOverdueStart: [],
    odvName: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    allocatedTime: [],
    notFollowed1: undefined,
    notFollowed2: undefined,
    syYhPrincipal1: undefined,
    syYhPrincipal2: undefined,
    label: undefined,
    area: undefined,
    condition: false, //是否搜索结果全选
    ids: [],
    orderBy: undefined, // 排序字段
    sortOrder: undefined, // 排序（1-正序；2-倒序）,
    batchNum: undefined,
    mediateStage: undefined,
  },
});
//需要拆分的字段
const rangfiles = [
  "clientOverdueStart",
  "entrustingCaseDate",
  "returnCaseDate",
  "allocatedTime",
  "divisionTime",
];
const { queryParams } = toRefs(data);
const reqForm = ref({});

// 案件标签
const logical = ref(0);
const queryTags = ref([]);
const showTagSearch = ref(false);

//表单配置信息
const showSearch = ref(false);
//表单数据集合
const batchs = ref([]);
const entrusts = ref([]);
const batchNumArray = ref([]);
const follows = ref([]);
const backs = ref([]);
const areas = ref([]);
const tags = ref([]);
const member = ref([]);
//多选信息
const labelList = ref([]);
const odvIdList = ref([]);
const clientCensusRegisterList = ref([]);
const followUpStateList = ref([]);
const urgeStateList = ref([]);
const batchNumList = ref([]);

import { exportSwtich } from "@/api/common/common";
import { ref } from "vue";
// 控制导出数据按钮
const exportStatus = ref(0);
const entrustingPartyIdList = ref([]);
const batchNumSelected = ref([]);
//多选字段
const checkMoreList = ref([
  "label",
  "odvId",
  "followUpState",
  "urgeState",
  "entrustingCaseBatchNum",
  "entrustingPartyId",
  "batchNum",
]);
const checkMoreName = ref([
  labelList,
  odvIdList,
  followUpStateList,
  urgeStateList,
  batchNumList,
  entrustingPartyIdList,
  batchNumSelected,
]);
const lableColor = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];

const mediateStageOptions = ref([]);
const mediateStageSelected = ref("");

const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "UID", visible: true },
  { key: 2, label: "labelContent", visible: true },
  { key: 3, label: "调解状态", visible: true },
  { key: 4, label: "调解阶段", visible: true },
  { key: 5, label: "调解员", visible: true },
  { key: 6, label: "诉讼阶段", visible: true },
  { key: 7, label: "诉讼状态", visible: true },
  { key: 8, label: "诉讼员", visible: true },
  { key: 9, label: "产品类型", visible: true },
  { key: 10, label: "姓名", visible: true },
  { key: 11, label: "手机号码", visible: true },
  { key: 12, label: "证件类型", visible: true },
  { key: 13, label: "证件号码【户籍地】", visible: true },
  { key: 14, label: "出生日期", visible: true },
  { key: 15, label: "债权总金额", visible: true },
  { key: 16, label: "剩余应还债权金额", visible: true },
  { key: 17, label: "剩余应还本金", visible: true },
  { key: 18, label: "逾期日期（末次）", visible: true },
  { key: 19, label: "逾期天数（末次）", visible: true },
  { key: 20, label: "地区", visible: true },
  { key: 21, label: "跟进状态", visible: true },
  { key: 22, label: "沟通状态", visible: true },
  { key: 23, label: "退案日期", visible: true },
  { key: 24, label: "分配时间", visible: true },
  { key: 25, label: "未跟进天数", visible: true },
  { key: 26, label: "最后跟进时间", visible: true },
]);

// 新增tagSerach
function addTagSearch() {
  queryTags.value.push({
    field: undefined,
    general: undefined,
    content: undefined,
  });
}

// 删除tagSearch
function delTagSearch(index) {
  queryTags.value.splice(index, 1);
}

//获取列表数据
function getList() {
  queryParams.value.caseState =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.caseMediateState =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.settlementStatus =
    settleState.value == "all" ? undefined : settleState.value;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });
  queryParams.value.ids = [];

  // 案件标签搜索板块
  if (queryTags.value.length > 0) {
    let tagArr = [];
    queryTags.value.forEach((item) => {
      if (
        (item.field || item.field === 0) &&
        (item.general || item.general === 0) &&
        (item.content || item.content === 0)
      ) {
        tagArr.push(`${item.field}-${item.general}-${item.content}`);
      }
    });
    if (tagArr.length > 0) {
      Object.assign(queryParams.value, {
        logical: logical.value,
        selectCondition: tagArr.join("/"),
      });
    }
  } else {
    Object.assign(queryParams.value, {
      logical: undefined,
      selectCondition: undefined,
    });
  }

  reqForm.value = proxy.addFieldsRange(queryParams.value, rangfiles);
  // 检测caseId clientName clientPhone clientIdcard的值是否有空格或者回车，有的话替换成英文逗号、多个空格或者回车替换成一个英文逗号
  Object.keys(reqForm.value).forEach((key) => {
    if (
      key.includes("caseId") ||
      key.includes("clientName") ||
      key.includes("clientPhone") ||
      key.includes("clientIdcard")
    ) {
      if (reqForm.value[key]) {
        reqForm.value[key] = reqForm.value[key].replace(/[\s\r\n]+/g, ",");
      }
    }
  });
  loading.value = true;
  caseManageList(reqForm.value)
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
provide("getList", Function, true);
getList();

// 设置导出按钮显示与隐藏
function getExportSwtich() {
  exportSwtich().then((res) => {
    exportStatus.value = res.data.exportStatus;
  });
}
getExportSwtich();

//户籍地
function CensusRegisters() {
  getProvinces().then((res) => {
    areas.value = res.data;
  });
}
CensusRegisters();

//标签下拉
function getTagList() {
  selectLabel().then((res) => {
    tags.value = res.data;
  });
}
getTagList();

// 获取案件阶段
function getMediateStageTreeFun() {
  getMediateStageTree().then((res) => {
    console.log(res.data);
    mediateStageOptions.value = res.data;
  });
}

// 应还本金区间校验
const validatePrincipalRange = () => {
  const { clientMoney1, clientMoney2 } = queryParams.value;
  // 检测输入是否是数字
  if (clientMoney1 && !Number.isFinite(Number(clientMoney1))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney1 = undefined;
  }
  if (clientMoney2 && !Number.isFinite(Number(clientMoney2))) {
    ElMessage({
      message: "请输入正确的金额！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney2 = undefined;
  }
  if (!clientMoney1 || !clientMoney2) return;

  const principal1 = parseFloat(clientMoney1);
  const principal2 = parseFloat(clientMoney2);
  // 检查区间逻辑
  if (principal1 >= principal2) {
    ElMessage({
      message: "后面区间的值必须大于前面区间的值！",
      type: "warning",
      grouping: true,
      repeatNum: 1,
      duration: 1000,
    });
    queryParams.value.clientMoney2 = undefined;
  }
};

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//获取跟进状态
function FollowUpState() {
  getFollowUpState().then((res) => {
    follows.value = res.data.genjin;
  });
}

//沟通状态
function UrgeState() {
  getUrgeState().then((res) => {
    backs.value = res.data.cuishou;
  });
}

//获取转让方
function OwnerList() {
  selectAssetOwner().then((res) => {
    entrusts.value = res.data;
  });
}

function getbatchNumList() {
  selectListWithBatchNum().then((res) => {
    let data = [];
    res.data.forEach((item) => {
      data.push({
        label: item,
        value: item,
      });
    });
    batchNumArray.value = data;
  });
}

function labelChange() {
  console.log(labelList.value);
}

//处置人员
function getOdvName() {
  selectEmployees().then((res) => {
    member.value = res.data;
  });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//结算类型切换
function settleStateChange() {
  getList();
}

//下载案批量操作模板
function downTpl() {
  proxy.download("case/export", {}, `tpl_案件分配模板.xlsx`);
}

function openLabelCase() {
  let data = {};
  if (checkedType.value[0] == "本页选中") {
    data = {
      ids: caseIds.value,
      condition: false,
    };
  } else {
    data = reqForm.value; //proxy.addFieldsRange(queryParams.value, rangfiles);
    data.pageNum = undefined;
    data.pageSize = undefined;
  }
  proxy.$refs["leabelCaseRef"].opendialog(data);
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId;
  let queryChange = reqForm.value; //proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange,
    type: "caseManage",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({
    path: `/case/caseIndex-detail/manageDetails/${caseId}`,
    query: { type: "caseManage" },
  });
}

// 排序
function sortChange({ prop, order }) {
  const orderByObj = {
    clientMoney: 2,
    clientOverdueStart: 3,
    overdueDays: 4,
    followUpState: 5,
    entrustingCaseDate: 6,
    returnCaseDate: 7,
    allocatedTime: 8,
    notFollowed: 1,
    followUpAst: 9,
  };
  const orderBy = orderByObj[prop];
  queryParams.value.sortOrder = proxy.orderEnum[order];
  queryParams.value.orderBy = orderBy;
  getList();
}

//tab选择
function tabChange() {
  queryParams.value.ids = [];
  queryParams.value.pageNum = 1;
  caseList.value = [];
  getList();
  let select = selectedArr.value;
  let type = checkedType.value[0];
  if (caseList.value.length > 0) {
    if (type === "本页选中") {
      getStaticForQuery();
    } else if (type === "搜索结果全选") {
      getStaticForQuery();
    } else {
      statistics.value = {
        caseNum: 0,
        totalMoney: 0,
        principal: 0,
      };
    }
  } else {
    statistics.value = {
      caseNum: 0,
      totalMoney: 0,
      principal: 0,
    };
  }
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    uid: undefined,
    caseId: undefined,
    clientName: undefined,
    divisionTime: undefined,
    clientPhone: undefined,
    clientIdcard: undefined,
    entrustingCaseBatchNum: undefined,
    entrustingPartyId: undefined,
    followUpState: undefined,
    urgeState: undefined,
    clientCensusRegister: undefined,
    clientOverdueStart: [],
    odvId: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    entrustingCaseDate: [],
    returnCaseDate: [],
    allocatedTime: [],
    notFollowed1: undefined,
    notFollowed2: undefined,
    syYhPrincipal1: undefined,
    syYhPrincipal2: undefined,
    label: undefined,
    area: undefined,
    condition: false, //是否搜索结果全选
    ids: [],
    orderBy: undefined, // 排序字段
    sortOrder: undefined, // 排序（1-正序；2-倒序）,
    batchNum: undefined,
    mediateStage: undefined,
  };
  labelList.value = [];
  odvIdList.value = [];
  clientCensusRegisterList.value = [];
  followUpStateList.value = [];
  urgeStateList.value = [];
  batchNumList.value = [];
  entrustingPartyIdList.value = [];
  batchNumSelected.value = [];
  mediateStageSelected.value = [];
  queryTags.value = [];
  getList();
}

//拼接户籍地
function changeOption() {
  if (clientCensusRegisterList.value?.length > 0) {
    let checkedMap = proxy.$refs["clientCensusRegisterRef"].getCheckedNodes();
    let checkedMapLabel = checkedMap.map((item) => item.label);
    let provinceList = clientCensusRegisterList.value.map((item, index) => {
      let prolabel = "";
      if (checkedMapLabel.includes(item?.[0])) {
        prolabel = item?.[0];
      }
      return prolabel;
    });
    let clientCensusRegisterFormat = Array.from(
      new Set(provinceList.filter((item) => item?.length > 0))
    ).toString();
    clientCensusRegisterList.value.forEach((item, index) => {
      if (provinceList.includes(item?.[0])) {
        return true;
      } else {
        let text = "";
        item.forEach((v, h) => {
          text = text + v;
        });
        clientCensusRegisterFormat =
          clientCensusRegisterFormat +
          `${
            index == 0 && clientCensusRegisterFormat.length == 0
              ? ""
              : text.length > 0
              ? ","
              : ""
          }${text}`;
      }
    });
    queryParams.value.clientCensusRegister = clientCensusRegisterFormat;
  } else {
    queryParams.value.clientCensusRegister = undefined;
  }
}

//查询案件金额
function getStaticForQuery() {
  let form = JSON.parse(JSON.stringify(reqForm.value));
  delete form.pageNum;
  delete form.pageSize;
  selectCaseManagesMoney(form).then((res) => {
    statistics.value = {
      caseNum: res.data.size,
      totalMoney: res.data.money,
      principal: res.data.principal,
    };
  });
}

//监听选择发生改变
watch(caseList, (newval, preval) => {
  multipleTableRef.value.toggleAllSelection();
});

watch([selectedArr, checkedType], (newval) => {
  nextTick(() => {
    let select = newval[0];
    let type = newval[1][0];
    if (type === "本页选中") {
      getStaticForQuery();
    } else if (type === "搜索结果全选") {
      getStaticForQuery();
    } else {
      statistics.value = {
        caseNum: 0,
        totalMoney: 0,
        principal: 0,
      };
    }
  });
});

//全选类型
function checkedTypeChange(val) {
  checkedType.value.length > 1 && checkedType.value.shift(); //单选
  if (checkedType.value.length === 0) {
    //全不选
    multipleTableRef.value.clearSelection();
    checkStatus.value[0].indeterminate = false;
  } else if (checkedType.value?.[0] == "搜索结果全选") {
    nextTick(() => {
      caseList.value.length > 0 &&
        caseList.value.map((item) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
    });
  } else {
    caseList.value.length > 0 &&
      caseList.value.map((item) => {
        multipleTableRef.value.toggleRowSelection(item, true);
      });
  }

  // nextTick(() => {
  if (checkedType.value[0] == "搜索结果全选") {
    checkStatus.value[0].indeterminate = false;
    queryParams.value.condition = true;
  } else {
    queryParams.value.condition = false;
  }
  // })
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
  queryParams.value.ids = caseIds.value;

  if (selectedArr.value.length) {
    //选择了案件时
    checkedType.value = queryParams.value.condition
      ? ["搜索结果全选"]
      : ["本页选中"];
    checkStatus.value.map((item) => {
      //全选/半选样式变化
      if (item.label === checkedType.value[0]) {
        item.indeterminate =
          selectedArr.value.length > 0 &&
          selectedArr.value.length < caseList.value.length;
      }
    });
  } else {
    checkedType.value = [];
  }
}

//表格行能否选择
function checkSelectable() {
  if (checkedType.value[0] === "本页选中" || checkedType.value.length === 0) {
    return true;
  } else {
    return false;
  }
}

//指定分案
function toPoint(distributeType, type, row) {
  let obj = JSON.parse(JSON.stringify(reqForm.value)); //proxy.addFieldsRange(queryParams.value, rangfiles);
  if (0 == type) {
    obj.ids = [...[], row.caseId];
    obj.condition = false;
  }
  if (checkedType.value[0] === "本页选中") {
    obj.ids = [...[], ...caseIds.value];
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: row ? "本页选中" : checkedType.value[0],
    query: obj,
  };
  localStorage.setItem(`point/${time}`, JSON.stringify(objStorage));
  store.dispatch("tagsView/delCachedView", { name: "Point" });
  router.push({
    path: `/case/caseManage-outcase/point/434`,
    query: { time, distributeType, path: route.path },
  });
}

//规则分案
function toRules(distributeType) {
  let obj = {};
  let query = JSON.parse(JSON.stringify(reqForm.value));
  if (checkedType.value[0] === "本页选中") {
    obj = selectedArr.value;
    query.ids = caseIds.value;
  } else {
    obj = [reqForm.value];
  }
  let time = new Date().getTime();
  let objStorage = {
    checkedType: checkedType.value[0],
    query: obj,
    queryParams: query, //proxy.addFieldsRange(queryParams.value, rangfiles),
  };
  localStorage.setItem(`rules/${time}`, JSON.stringify(objStorage));
  store.dispatch("tagsView/delCachedView", { name: "Rules" });
  router.push({
    path: `/case/caseManage-outcase/rules/4243`,
    query: { time, distributeType, path: route.path },
  });
}

//导出沟通记录
function handleExport(type, row) {
  let condition = undefined;
  if (row) condition = false;
  proxy.$refs["exportdialogRef"].opendialog(
    0,
    type == 1 ? [...[], row.caseId] : caseIds.value,
    condition
  );
}

//打开模板分案
function openTpldivision() {
  proxy.$refs["tpldivisionRef"].opendialog();
}

//案件批量操作
function caseBatchHandle() {
  router.push("/case/caseManage-handle/batchHandle");
}

//导出数据
function downloadCase() {
  proxy.$modal.loading("正在导出数据，请稍候...");
  const reqForm = JSON.parse(JSON.stringify(reqForm.value));
  exportCase(reqForm)
    .then((res) => {
      proxy.$modal.closeLoading();
      proxy.$modal.exportTip(res.data);
    })
    .catch((error) => {
      proxy.$modal.closeLoading();
    });
}

//打开回收案件等案件操作
function opendialog(data) {
  let condition = undefined;
  let optCaseList = data.row?.filter((item, index) => {
    return item.caseState == 0 || item.caseState == 1 || item.caseState == 3;
  });
  if (data.type == 4) optCaseList = data.row;
  console.log(optCaseList);
  if (optCaseList.length == 0)
    return proxy.$modal.msgWarning("没有可以操作的案件");
  if (data.row?.length == 1) condition = false;
  proxy.$refs["handleDialogRef"].openDialog(data.type, optCaseList, condition);
}

//删除案件
function removeCase(row) {
  // 是否为批量操作
  const isBatch = !row;
  const reqForm = isBatch
    ? { ...reqForm.value, caseIds: caseIds.value }
    : { caseId: row.caseId };

  const confirmText = isBatch
    ? `是否确认删除所选案件 ，删除后不可恢复，是否确认`
    : `是否确认删除该案件，删除后不可恢复，是否确认`;

  proxy.$modal.confirm(confirmText).then(() => {
    deleteCase(reqForm)
      .then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(res.msg);
        }
        getList();
      })
      .catch(() => {});
  });
}

// 放入回收站
function recycleBinCase() {
  let reqForm = {};
  if (caseIds.value.length === 1) {
    Object.assign(reqForm, { caseId: caseIds.value[0] });
  } else {
    Object.assign(reqForm, { ids: caseIds.value });
  }
  proxy.$modal.confirm("该操作将选中案件移入回收站，是否确认！？").then(() => {
    insertRecycleBin(reqForm).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      }
      getList();
    });
  });
}

//案件状态 0-未分配 1-已分配 2-停催 3-留案 4-退案 5-回收案件 6-案件结清
function caseStateFor(val) {
  if (val == 0 || val == null) return "未分配";
  return {
    0: "未分配",
    1: "已分配",
    2: "停案",
    3: "留案",
    4: "退案",
    5: "回收案件",
    6: "案件结清",
  }[val];
}

//时间格式转换为日期格式
function formatDate(row) {
  return proxy.parseTime(row.returnCaseDate, `{y}-{m}-{d}`);
}

// 默认排序
function orderFun() {
  const orderObj = { 1: "ascending", 2: "descending" };
  return orderObj[queryParams.value.sortOrder];
}

watch(
  () => mediateStageSelected.value,
  (newValue, oldValue) => {
    if (!newValue) {
      queryParams.value.mediateStage = undefined;
      return;
    }
    queryParams.value.mediateStage = newValue[newValue.length - 1];
  }
);

function openCreateCaseTaskDialog() {
  // checkUserSip().then((res) => {
  //   if (res.data?.isPreTestSip) {
      const query = JSON.parse(
        JSON.stringify(proxy.newAddFieldsRange(reqForm.value, rangfiles))
      );
      query.ids = caseIds.value;
      // 退案日期不显示时分秒，特殊处理
      query.returnCaseDate1 &&
        (query.returnCaseDate1 = query.returnCaseDate1.split(" ")[0]);
      query.returnCaseDate2 &&
        (query.returnCaseDate2 = query.returnCaseDate2.split(" ")[0]);
      proxy.$refs["createCaseTaskRef"].openDialog(query, 0);
    // }
    // else {
    //   proxy.$modal.msgWarning("未分配预测式外呼坐席");
    // }
  // });
}
</script>
<style lang="scss" scoped>
::v-deep(.custom-btn > span) {
  color: #ffffff !important;
}
.tag-search-content {
  padding-left: 12px;
  .tag-search-item {
    position: relative;
    width: 600px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    padding-bottom: 12px;
    box-sizing: border-box;
    &::before {
      content: "";
      position: absolute;
      top: calc(50% - 5px);
      left: -12px;
      width: 10px;
      height: 1px;
      background-color: #d9d9d9;
    }
    &:first-child {
      &::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 50%;
        top: calc(50% - 5px);
        left: -12px;
        background-color: #d9d9d9;
      }
    }
    &:last-child {
      &::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 50%;
        top: -4px;
        left: -12px;
        background-color: #d9d9d9;
      }
    }
  }
}
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: #9fccdb;
  display: block;
}

.case-tips {
  font-size: 14px;
  background-color: #cce7fa;
  padding: 20px;
  color: #409eff;

  P {
    margin: 0;
    display: block;
  }

  p:nth-child(2) {
    display: inline;
  }
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}
</style>
