<template>
  <div class="task-data">
    <el-row>
      <el-col :span="4">
        <div class="label-content">执行时间</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ timeFor(data.executionTime) }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">任务时间段</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">
          {{ `${data.taskStartTime} ${data.taskEndTime}` }}
        </div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">外呼时段</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.executionCallTime }}</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="4">
        <div class="label-content">重呼次数</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.recallCount }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">重呼间隔</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.recallMinute || 0 }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">任务创建时间</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.createTime }}</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="4">
        <div class="label-content">话术模板</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.templateName }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">机器人数量</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ data.robotCount }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">备注</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">
          <Tooltip :content="data.remark" :length="8" />
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="4">
        <div class="label-content">发送挂机短信对象</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">{{ changeSenderTarget(data) }}</div>
      </el-col>
      <el-col :span="4">
        <div class="label-content">短信模板</div>
      </el-col>
      <el-col :span="4">
        <div class="value-content">
          <Tooltip :content="data.messageContent" :length="8" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
});

const timeFor = (str) => {
  const weekDays = [
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
    "星期日",
  ];
  if (str) {
    // 将字符串转换为数组，映射到对应的星期名称
    const convertedDays = str.split(",").map((day) => weekDays[day - 1]);
    // 对数组进行排序
    convertedDays.sort((a, b) => {
      return weekDays.indexOf(a) - weekDays.indexOf(b);
    });
    const result = convertedDays.join("，");
    return result;
  }
  return "";
};

const popupFor = (value) => {
  const screenMap = {
    1: "是",
    2: "否",
  };
  return screenMap[value] || "--";
};

const changeSenderTarget = (row) => {
  let newData = "";
  let data = {
    1: "接通",
    2: "未接通",
    3: "空号",
    4: "停机",
    5: "关机",
    6: "忙线",
    7: "呼叫转移",
    8: "未知",
  };
  if (row.senderTarget) {
    let newDataArr = [];
    const senderArr = row.senderTarget.split(",");
    if (senderArr.length > 0) {
      senderArr.forEach((item) => {
        newDataArr.push(data[item]);
      });
      newData = newDataArr.join(",");
    }
  }
  return newData;
};
</script>

<style lang="scss" scoped>
.task-data {
  border: 1px solid #dcdfe6;
  border-bottom: none;
  border-radius: 4px;
  .label-content,
  .value-content {
    height: 2.5rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #dcdfe6;
    padding-left: 10px;

    span {
      white-space: nowrap; /* 不换行 */
    }
  }
  .label-content {
    color: #909399;
    background-color: #ebeef5;
  }
  .value-content {
    white-space: nowrap; /* 不换行 */
    overflow-x: auto; /* 隐藏溢出部分 */
  }
}
</style>