<template>
  <el-dialog title="录入执行案号" v-model="open" width="450px" :before-close="cancel" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item prop="executiveCourt" label="执行法院">
        <el-select v-model="form.executiveCourt" filterable placeholder="请选择执行法院" clearable style="width: 240px">
          <el-option v-for="item in courtOptions" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="executeNo" label="执行案号">
        <el-input v-model="form.executeNo" placeholder="请输入执行案号" style="width: 240px" />
      </el-form-item>
      <el-form-item prop="filingTime" label="立案时间">
        <el-date-picker v-model="form.filingTime" placeholder="请选择立案" value-format="YYYY-MM-DD hh:mm:ss" type="datetime"
          style="width: 240px" />
      </el-form-item>
      <el-form-item prop="contractor" label="承办人">
        <el-input v-model="form.contractor" placeholder="请输入承办人" style="width: 240px" :maxlength="20" />
      </el-form-item>
      <el-form-item prop="clerk" label="书记员">
        <el-input v-model="form.clerk" placeholder="请输入书记员" style="width: 240px" :maxlength="20" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getCourtOptions } from '@/api/common/common';
import { addAplayCaseJudge } from '@/api/mediation/judgmentResult';
const props = defineProps({
  getList: { type: Function }
})
const { proxy } = getCurrentInstance();

const data = reactive({
  form: {},
  rules: {
    executiveCourt: [{ required: true, message: '请选择执行法院', trigger: 'blur' }],
    executeNo: [{ required: true, message: '请输入执行案号', trigger: 'blur' }],
    filingTime: [{ required: true, message: '请选择立案时间', trigger: 'blur' }],
  },
});

const { form, rules } = toRefs(data);
const courtOptions = ref([])
const open = ref(false);

const loading = ref(false);

function submit() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      loading.value = true
      const reqForm = JSON.parse(JSON.stringify(form.value))
      addAplayCaseJudge(reqForm).then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess('操作成功')
          props.getList && props.getList()
          cancel()
        }
        loading.value = false
      }).finally(() => loading.value = false)
    }
  })
}
function openDialog(data) {
  open.value = true;
  form.value = { ...data.query, ...data }
};

function cancel() {
  open.value = false;
  form.value = {}
};


getCourtOptionsFun()
function getCourtOptionsFun() {
  getCourtOptions().then(res => {
    courtOptions.value = res.data
  })
}
defineExpose({ openDialog });
</script>

<style lang="scss" scoped></style>
