<template>
  <div class="step-item pt20">
    <el-form :model="form" :rules="rules" ref="formActiveRef" label-width="180px">
      <el-form-item label="模板类型" prop="classifyIds">
        <el-cascader v-model="form.classifyIds" :options="templateTypeList" />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="form.templateName" placeholder="输入模板名称" style="width: 400px" @blur="checkTemplateName"
          maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="正文内容" prop="bodyContent">
        <div class="template-item">
          <myEditor v-model:modelValue="form.bodyContent" ref="myEditorRef" :myEditorFocus="() => (isBody = true)"
            :imgUploadUrl="upload.url" />
        </div>
        <div class="template-botton">
          <el-button v-for="(item, index) in paramsList" :key="index" type="primary" plain
            @click="setContent(item.info)">
            {{ item.info && item.info.length > 8 ? `${item.info.substring(0, 8)}...]` : `${item.info}` }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="增加页眉" prop="pageHeader">
        <el-upload ref="uploadHeadRef" accept=".png, .jpg, .jpeg" :headers="upload.headers" :action="upload.url"
          :before-upload="handleCoverUploadBefore" :on-change="handleBackChange" :limit="1"
          :on-success="handleBackFileSuccess" :file-list="props.fileHeaderList" :auto-upload="false"
          :on-remove="handleBackRemove" list-type="picture-card" :class="{
            concealcrad:
              (form.pageHeader && form.pageHeader != '' ? [form.pageHeader] : [])
                .length == 1,
          }">
          <el-icon>
            <Plus />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              选择上传，上传内容为png、jpg、jpeg、内容不能超过20M
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="增加页脚" prop="pageFooter">
        <el-upload ref="uploadFootRef" accept=".png, .jpg, .jpeg" :headers="upload.headers" :action="upload.url"
          :before-upload="handleCoverUploadBefore" :on-change="handleFootChange" :limit="1"
          :on-success="handleFootFileSuccess" :file-list="props.fileFooterList" :auto-upload="false"
          :on-remove="handleFootRemove" list-type="picture-card" :class="{
            concealcrad:
              (form.pageFooter && form.pageFooter != '' ? [form.pageFooter] : [])
                .length == 1,
          }">
          <el-icon>
            <Plus />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              选择上传，上传内容为png、jpg、jpeg、内容不能超过20M
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="text-center mt20">
      <el-button @click="toBack">返回</el-button>
      <el-button class="ml10" type="primary" :loading="loading" plain @click="nextstep">下一步</el-button>
    </div>
  </div>
</template>
<script setup>
import { getToken } from "@/utils/auth";
import { getOpenOptions } from "@/api/writ/parametric";
import { getClassifyList, checkUniqueName, createTemplatePdf } from "@/api/writ/template";
// 全局变量
const { proxy } = getCurrentInstance();
const route = useRoute();

const paramsList = ref([]); //模板参数
const templateTypeList = ref([]); //模板类型
const isBody = ref(false); // 是否正文内容
const loading = ref(false);
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/document/template/upload",
});

const props = defineProps({
  rules: { type: Object, default: {} },
  form: { type: Object, default: {} },
  fileHeaderList: { type: Array, default: [] },
  fileFooterList: { type: Array, default: [] },
  toBack: { type: Function },
  nextstep: { type: Function },
  stepActive: { type: Number },
});

// 下一步
function nextstep() {
  proxy.$refs["formActiveRef"].validate((valid) => {
    if (valid) {
      const req = JSON.parse(JSON.stringify(props.form));
      if (req.tailContent) {
        req.tailContent = "";
      }
      req.pageHeader = req.pageHeader || "";
      req.pageFooter = req.pageFooter || "";
      req.scaleHeader = req.scaleHeader || "";
      req.scaleFooter = req.scaleFooter || "";
      createTemplatePdf(req).then((res) => {
        if (res.code == 200) {
          if (props.stepActive == 0) {
            props.nextstep(res);
          }
        }
      });
    }
  });
}
//设置模板
function setContent(text) {
  if (isBody.value) {
    proxy.$refs["myEditorRef"]?.getBookMark(`${text}`);
  } else {
    proxy.$refs["myEditorFootRef"]?.getBookMark(`${text}`);
  }
}

//获取分类数据
function getClassify() {
  getClassifyList().then((res) => {
    templateTypeList.value = res.data;
  });
}
getClassify();
//选项-获取模板变量
function getParamsList() {
  getOpenOptions().then((res) => {
    paramsList.value = res.data;
  });
}
getParamsList();
//检测模板名称
function checkTemplateName() {
  if (props.form.templateName && props.form.templateName.length > 0) {
    let req = {
      id: route.params.id,
      templateName: props.form.templateName,
    };
    checkUniqueName(req).then((res) => {
      if (res.data == 1) {
        props.form.templateName = "";
        proxy.$modal.msgError(res.data.msg);
      }
    });
  }
}

/** 文件上传前的处理*/
const handleCoverUploadBefore = (file, fileList) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    return false;
  }
};

//文件列表变化
function handleBackChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    fileList.pop();
    return false;
  }
  if (!checkFileType(file)) {
    proxy.$modal.msgWarning("请上传正确的文件格式!");
    fileList.pop();
    props.fileHeaderList.pop();
    return false;
  }
  if (fileList.length > 0) {
    props.fileHeaderList = fileList;
  }
  if (fileList.length > 0) {
    proxy.$refs[`uploadHeadRef`].submit();
  }
}
//检查文件格式
function checkFileType(file) {
  //文件名
  let fileName = file.name;
  //获取最后一个.的位置
  var index = fileName.lastIndexOf(".");
  //获取后缀
  let fileType = fileName.substring(index + 1);
  //判断是否合法后缀
  return isAssetTypeAnImage(fileType);
}
//判断是否合法的格式
function isAssetTypeAnImage(ext) {
  return ["png", "jpg", "jpeg"].indexOf(ext.toLowerCase()) !== -1;
}
/* 上传文件上传成功处理 */
const handleBackFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    props.form.pageHeader = data.fileUrl[0];
    props.form.scaleHeader = data.scaleFileUrl[0];
  }
};

//删除
function handleBackRemove(file) {
  props.form.pageHeader = undefined;
  props.form.scaleHeader = undefined;
  props.fileHeaderList = [];
}

//文件列表变化
function handleFootChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    fileList.pop();
    return false;
  }
  if (!checkFileType(file)) {
    proxy.$modal.msgWarning("请上传正确的文件格式!");
    props.fileFooterList.pop();
    fileList.pop();
    return false;
  }
  if (fileList.length > 0) {
    props.fileFooterList = fileList;
  }
  if (fileList.length > 0) {
    proxy.$refs[`uploadFootRef`].submit();
  }
}

/* 上传文件上传成功处理 */
const handleFootFileSuccess = (response, file, fileList) => {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    props.form.pageFooter = data.fileUrl[0];
    props.form.scaleFooter = data.scaleFileUrl[0];
  }
};

//删除
function handleFootRemove(file) {
  props.form.pageFooter = undefined;
  props.form.scaleFooter = undefined;
  props.fileFooterList = [];
}
</script>
<style>
.concealcrad .el-upload--picture-card {
  display: none;
}

img.chapter-img {
  display: block !important;
  width: 157px !important;
  height: 158px !important;
}
</style>
<style lang="scss" scoped>
.add-template {
  padding-bottom: 5px;
}

.case-pdf {
  width: 794px;
  height: 500px;
  margin: 30px auto 60px auto;
  overflow: auto;
  overflow-x: hidden;
}

.sign-name {
  position: absolute;
  font-family: 宋体;
  display: flex;
  width: 138px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-left: 18px;
  z-index: -1;
  margin-top: 22px;
}

.step-item {
  width: 95%;
  min-height: 400px;
  margin: 40px auto;

  .step-item-head {
    width: 90%;
    margin: 0 auto;
    padding-top: 20px;
    font-size: 14px;

    .title {
      display: inline-block;
      color: #3f3f3f;
      font-weight: bold;
      vertical-align: text-bottom;

      .tit {
        color: var(--el-color-primary);
        padding-right: 20px;
      }
    }
  }
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.params-text {
  display: inline-block;
  line-height: 32px;
  vertical-align: text-bottom;
}

.hint-div {
  align-self: start;
  display: flex;
  margin-top: 8px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.template-item {
  width: 730px;
  height: 400px;
  display: inline-block;
  margin-right: 10px;
}

.template-botton {
  width: 200px;
  height: 400px;
  overflow: auto;
  overflow-x: hidden;
  display: inline-block;
  text-align: left;

  button {
    display: block;
    margin-bottom: 10px;
    width: 150px;
  }
}

.sign-box {
  .law-label {
    position: absolute;
    min-width: 150px;
    text-align: center;
    top: 75px;
    left: 75px;
    font-size: 15px;
  }

  .law-person {
    position: absolute;
    top: 100px;
    min-width: 150px;
    text-align: center;
    left: 80px;
  }

  .law-time {
    position: absolute;
    top: 180px;
    left: 100px;
  }

  .sign-img {
    width: 300px;
    height: 300px;
  }
}

.template-preview-chapter {
  position: relative;
  display: flex;

  .template-preview {
    position: relative;
    width: 794px;
    min-height: 1123px;

    .template-preview-sign-area {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 1px solid #999;
    }

    .template-preview-content {
      width: 794px;
      min-height: 1123px;
    }

    .template-preview-sign-area {
      z-index: 2;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }

  .template-chapter-list {
    &>div {
      margin-bottom: 10px;
    }

    .template-chapter-item {
      width: 157px;
      height: 158px;
    }
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0px;
}

:deep(.el-upload-list__item-name) {
  width: 180px;
}

:deep(.el-upload-list__item-preview) {
  .el-icon {
    display: none;
  }
}
</style>
