<template>
  <div class="app-container">
    <div class="app-content">
        <div class="form-content">
          <el-form :model="queryParams" ref="queryRef" :inline="true">
            <el-form-item>
              <el-select v-model="classifyIdsList" clearable filterable @focus="getClassify" multiple collapse-tags
                         collapse-tags-tooltip style="width: 240px" placeholder="请选择类型名称">
                <el-option v-for="(item, index) in classifyList" :key="index" :label="item.info" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryParams.status" placeholder="请选择状态" style="width: 240px">
                <el-option label="启用" :value="0" />
                <el-option label="禁用" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.createBy" placeholder="请输入创建人" style="width: 240px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
              <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="mb10">
            <el-button type="primary" v-hasPermi="['lawyer:typeSet:add']" plain @click="addType()">创建类型</el-button>
          </div>
        </div>
        <el-table v-loading="loading" :data="dataList">
          <el-table-column label="类型名称" align="center" prop="classifyName" />
          <el-table-column label="二级类型名称" align="center" prop="classifyLabel">
            <template #default="scope">
              <el-tooltip placement="top">
                <template #content>
                  <p style="max-width: 300px">{{ scope.row.classifyLabel ? scope.row.classifyLabel : '--' }}</p>
                </template>
                <div>
              <span>
                {{ scope.row.classifyLabel && scope.row.classifyLabel?.length > 20 ?
                  `${scope.row.classifyLabel?.substring(0, 20)}...` : (scope.row.classifyLabel ? scope.row.classifyLabel :
                      '--')
                }}
              </span>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <span v-if="scope.row.status == 0" class="blue">启用</span>
              <span v-else class="red">禁用</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column label="创建时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="text"  v-hasPermi="['lawyer:typeSet:edit']" @click="addType(scope.row)">编辑</el-button>
              <el-button v-if="scope.row.status == 0" type="text"  @click="setStatus(scope.row)"
                         v-hasPermi="['lawyer:typeSet:close']">禁用</el-button>
              <el-button v-else type="text"  @click="setStatus(scope.row)"
                         v-hasPermi="['lawyer:typeSet:open']">启用</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-area">
          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <!-- 新增类型 -->
        <addTypeBox ref="addTypeBoxRef" @getList="getList" />
    </div>
  </div>

</template>

<script setup name="TypeSet">
import addTypeBox from "../parametric/dialog/addType.vue";
import { getClassifyList, getClassifyOption, editStatus } from "@/api/lawyer/parametric";
//props传值
const props = defineProps({
  activeTab: {
    type: String,
    required: true,
  },
});
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ids: undefined,
    createBy: undefined,
    status: undefined,
  },
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
//分类多选
const classifyList = ref([]);
const classifyIdsList = ref([]);
//多选
const checkMoreList = ref([
  "ids",
]);
const checkMoreName = ref([
  classifyIdsList
]);
//获取列表
function getList() {
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString();
  });
  loading.value = true;
  getClassifyList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

/** 多选框选中数据 */
function addType(row) {
  if (row) {
    let req = JSON.parse(JSON.stringify(row));
    proxy.$refs["addTypeBoxRef"].opendialog(req);
  } else {
    proxy.$refs["addTypeBoxRef"].opendialog();
  }
}

//获取分类数据
function getClassify() {
  getClassifyOption().then((res) => {
    classifyList.value = res.data;
  });
}
getClassify();


//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    classifyName: undefined,
    createBy: undefined,
    status: undefined,
  };
  classifyIdsList.value = []
  getList();
}

//停用
function setStatus(row) {
  let req = {
    status: row.status == 0 ? 1 : 0,
    id: row.id
  };
  proxy.$modal
    .confirm(`是否确认${row.status == 0 ? '停用' : '启用'}？此操作将${row.status == 0 ? '停用' : '启用'}该类型，是否确认？`)
    .then(() => {
      editStatus(req)
        .then((res) => {
          getList();
        })
    })
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.form-content {
  padding: 10px 20px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
