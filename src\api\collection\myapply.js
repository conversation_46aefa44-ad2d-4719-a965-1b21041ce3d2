import request from '@/utils/request'

//审核状态tab
export function examineStates() {
    return request({
        url: '/collection/enumeration',
        method: 'get',
    })
}

//转让方
export function entrustingPartyList(){
    return request({
        url: '/dropDown/selectAssetOwner',
        method: 'get',
    })
}

//还款类型 选项
export function repaymentModeList(){
    return request({
        url: '/collection/selectRepaymentSetup',
        method: 'get',
    })
}




//============================================================
//回款-列表
export function repaymentList(query){
    return request({
        url: '/collection/selectRepaymentRecordId',
        method: 'get',
        params: query
    })
}
//回款-撤销
export function updateRepaymentRecord(data){
   return request({
        url: '/collection/updateRepaymentRecord',
        method: 'PUT',
        data: data
    })
}
//回款-审批进度
export  function selectApproveProceOne(query){
    return request({
        url: '/case/selectApproveProceOne',
        method: 'get',
        params: query
    })
}

//回款-获取凭证
export  function selectRepaymentRecordById(query){
    return request({
        url: `collection/selectRepaymentRecordById/${query}`,
        method: 'get',
    })
}
//============================================================

//减免-列表
export function reductionList(query){
    return request({
        url: '/collection/selectReductionRecordId',
        method: 'get',
        params: query
    })
}
//减免-撤销
export function reductionRevoke(data){
    return request({
        url: '/collection/updateReductionRecord',
        method: 'PUT',
        data: data
    })
}
//减免-审批进度
export  function reductionProce(query){
    return request({
        url: '/case/selectApproveProceTwo',
        method: 'get',
        params: query
    })
}
//减免-获取凭证
export  function selectReductionFile(query){
    return request({
        url: `collection/selectReductionFile/${query}`,
        method: 'get',
    })
}
//============================================================

//分期-列表
export function stagingList(query){
    return request({
        url: '/collection/selectStagingRecordId',
        method: 'get',
        params: query
    })
}
//分期-撤销
export function stagingRevoke(data){
    return request({
        url: '/collection/updateStagingRecord',
        method: 'PUT',
        data: data
    })
}
//分期-审批进度
export  function stagingProce(query){
    return request({
        url: '/case/selectApproveProceThree',
        method: 'get',
        params: query
    })
}

//==========================================================

//留案-列表
export function staycaseList(query){
    return request({
        url: '/collection/selectKeepCaseId',
        method: 'get',
        params: query
    })
}
//留案-撤销
export function staycaseRevoke(data){
    return request({
        url: '/collection/updateKeepCase',
        method: 'PUT',
        data: data
    })
}
//留案-审批进度
export  function staycaseProce(query){
    return request({
        url: '/case/selectApproveProceFour',
        method: 'get',
        params: query
    })
}

//==========================================================
//停催-列表
export function stopurgingList(query){
    return request({
        url: '/collection/selectSuspensionId',
        method: 'get',
        params: query
    })
}
//停催-撤销
export function stopurgingRevoke(data){
    return request({
        url: '/collection/updateStopUrging',
        method: 'PUT',
        data: data
    })
}
//停催-审批进度
export  function stopurgingProce(query){
    return request({
        url: '/case/selectApproveProceFive',
        method: 'get',
        params: query
    })
}

//==========================================================

//退案-列表
export function sendbackList(query){
    return request({
        url: '/collection/selectWithdrawalId',
        method: 'get',
        params: query
    })
}
//退案-撤销
export function sendbackRevoke(data){
    return request({
        url: '/collection/updateWithdrawal',
        method: 'PUT',
        data: data
    })
}
//退案-审批进度
export  function sendbackProce(query){
    return request({
        url: '/case/selectApproveProceSix',
        method: 'get',
        params: query
    })
}

//==========================================================

//外访-列表
export function outsideList(query){
    return request({
        url: '/collection/selectOutsideRecordId',
        method: 'get',
        params: query
    })
}

//外访-状态
export function outsideState(){
    return request({
        url: '/collection/foreignCountry',
        method: 'get'
    })
}

//外访-撤销
export function outsideRevoke(data){
    return request({
        url: '/collection/updateOutsideRecord',
        method: 'PUT',
        data: data
    })
}
//外访-审批进度
export  function outsideProce(query){
    return request({
        url: '/case/selectApproveProceSeven',
        method: 'get',
        params: query
    })
}
//外访-下载外访单
//TODO 外访-下载外访单
export  function outsideDownloadForm(query){
    return request({
        url: '/case/outsideDownloadForm',
        method: 'get',
        params: query
    })
}
//外访-完成外访
export  function outsideFulfil(data){
    return request({
        url: '/collection/updateVisitStatus',
        method: 'post',
        data: data
    })
}

// 外访登记
export  function outsideRegistrationAPI(data){
    return request({
        url: '/collection/signIn',
        method: 'post',
        data: data
    })
}



//==========================================================
//协催-列表
export function assistList(query){
    return request({
        url: '/collection/selectAssistRecordId',
        method: 'get',
        params: query
    })
}

//协催-状态
export function assistState(){
    return request({
        url: '/collection/assistInUrging',
        method: 'get'
    })
}

//撤销协催
export function updateAssistRecord(data){
    return request({
        url: '/collection/updateAssistRecord',
        method: 'PUT',
        data: data
    })
}

//协催-协催记录
export  function assistRecord(id){
    return request({
        url: '/collection/selectAssistDetails/'+id,
        method: 'get',

    })
}

//==========================================================
//资料调取-列表
export function datumList(query){
    return request({
        url: '/collection/selectRetrievalRecordId',
        method: 'get',
        params: query
    })
}

//撤销资料申请
export function updateRetrievalRecord(data){
    return request({
        url: '/collection/updateRetrievalRecord',
        method: 'PUT',
        data: data
    })
}

//获取短信签名
export function selectAllSmsSignature(query){
    return request({
        url: '/collection/selectAllSmsSignature',
        method: 'get',
        params: query
    })
}


//获取短信模板标签
export function selectAllSmsTemplate(query){
    return request({
        url: '/collection/selectAllSmsTemplate',
        method: 'get',
        params: query
    })
}

//获取线路规划
export function getOutsideAddress(query){
    return request({
        url: '/collection/selectOutsideAddress',
        method: 'get',
        params: query
    })
}



