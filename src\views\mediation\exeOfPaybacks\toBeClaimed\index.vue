<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      :loading="loading"
      ref="queryRef"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
      inline
      label-width="110px"
    >
      <el-form-item prop="caseId" label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          style="width: 240px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input
          v-model="queryParams.clientName"
          style="width: 240px"
          placeholder="请输入被告"
        />
      </el-form-item>
      <el-form-item prop="involvedWith" label="案件涉及">
        <el-input
          v-model="queryParams.involvedWith"
          style="width: 240px"
          placeholder="请输入案件涉及"
        />
      </el-form-item>
      <el-form-item prop="closedMode" label="结案方式">
        <el-select
          placeholder="请选择结案方式"
          style="width: 240px"
          v-model="queryParams.closedMode"
        >
          <el-option
            v-for="item in closeWayEnum"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="executiveCourt" label="执行法院">
        <el-select
          placeholder="请选择执行法院"
          style="width: 240px"
          filterable
          clearable
          v-model="queryParams.executiveCourt"
        >
          <el-option
            v-for="item in courtOption"
            :key="item.code"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 240px">
          <el-input v-model="queryParams.amount1" />
          <span>-</span>
          <el-input v-model="queryParams.amount1" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button
        :loading="loading"
        icon="Search"
        type="primary"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)"
        >重置</el-button
      >
    </div>
    <div class="operation-revealing-area">
      <el-button
        v-if="checkPermi(['exeOfPaybacks:toBeClaimed:download'])"
        :disabled="selectedArr.length == 0"
        type="primary"
        @click="downloadCase"
        >批量导出</el-button
      >
      <!-- <el-button
        v-if="checkPermi(['exeOfPaybacks:toBeClaimed:keep'])"
        :disabled="selectedArr.length == 0"
        type="primary"
        @click="handleOpenDialog('applyKeepRef')"
        >申请保全</el-button
      > -->
      <el-button
        v-if="checkPermi(['exeOfPaybacks:toBeClaimed:transfer'])"
        :disabled="selectedArr.length == 0"
        type="primary"
        @click="handleOpenDialog('transferCaseRef')"
        >案件流转</el-button
      >
      <el-button
        v-if="checkPermi(['exeOfPaybacks:toBeClaimed:sendNote'])"
        :disabled="selectedArr.length == 0"
        type="primary"
        @click="handleOpenDialog('sendMessageRef')"
        >发送短信</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <div class="mb10 hint">
      <selectedAll
        :dataList="dataList"
        v-model:all-query="allQuery"
        :selected-arr="selectedArr"
      >
        <template #content>
          <div class="text-flex ml20">
            <span>剩余债权总额：</span>
            <span class="text-danger mr10">
              {{ numFilter(statistics.totalMoney) }}
            </span>
            <span>剩余债权本金：</span>
            <span class="text-danger mr10">
              {{ numFilter(statistics.principal) }}
            </span>
            <span>案件数量：</span>
            <span class="text-danger">{{ statistics.caseNum }}</span>
          </div>
        </template>
      </selectedAll>
    </div>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="selectionChange"
    >
      <el-table-column type="selection" :selectable="selectable" align="right" />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="left"
        key="caseId"
        prop="caseId"
      >
        <template #default="{ row, $index }">
          <el-button
            :disable="!checkPermi(['exeOfPaybacks:toBeClaimed:detail'])"
            type="text"
            @click="toDetails(row, $index)"
          >
            {{ row.caseId }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="被告"
        align="center"
        key="clientName"
        prop="clientName"
        :width="90"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="手机号码"
        align="center"
        key="phone"
        prop="phone"
        :width="110"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.phone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[3].visible"
        label="标的额"
        align="center"
        key="remainingDue"
        prop="remainingDue"
        :width="120"
      >
        <template #default="{ row }">
          <span>{{
            numFilter(row.remainingDue)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[4].visible"
        label="身份证号码"
        align="center"
        key="clientIdcard"
        prop="clientIdcard"
        :width="180"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="户籍地"
        align="center"
        key="clientCensusRegister"
        prop="clientCensusRegister"
        show-overflow-tooltip
        :width="160"
      />
      <el-table-column
        v-if="columns[6].visible"
        label="执行状态"
        align="center"
        key="disposeStage"
        prop="disposeStage"
        :width="110"
      />
      <el-table-column
        v-if="columns[7].visible"
        label="结案标的额"
        align="center"
        key="concludeCaseAmount"
        prop="concludeCaseAmount"
        width="120px"
      >
        <template #default="{ row }">
          <span>{{
            numFilter(row.concludeCaseAmount)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[8].visible"
        label="案件涉及"
        align="center"
        key="involvedWith"
        prop="involvedWith"
        width="130px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[9].visible"
        label="被执行人下落不明"
        align="center"
        key="isMissing"
        prop="isMissing"
        :formatter="(row) => isNoEnum[row.isMissing]"
        width="180px"
      />
      <el-table-column
        v-if="columns[10].visible"
        label="执行法院"
        align="center"
        key="executiveCourt"
        prop="executiveCourt"
        min-width="130px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[11].visible"
        label="结案案由"
        align="center"
        key="closedReason"
        prop="closedReason"
        :min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[12].visible"
        label="结案方式"
        align="center"
        key="closedMode"
        prop="closedMode"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[13].visible"
        label="有无结案文书"
        align="center"
        key="isFile"
        prop="isFile"
        :width="120"
        :formatter="(row) => ({ 0: '是', 1: '否' }[row.isFile])"
      />
      <el-table-column
        v-if="columns[14].visible"
        label="跟进人员"
        align="center"
        key="follower"
        prop="follower"
      />
      <el-table-column
        v-if="columns[15].visible"
        label="最近一次跟进时间"
        align="center"
        key="followUpTime"
        prop="followUpTime"
        width="160px"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <transferCase :getList="getList" ref="transferCaseRef" />
    <sendMessage :getList="getList" ref="sendMessageRef" />
  </div>
</template>

<script setup name="toBeClaimed">
import { checkPermi } from "@/utils/permission";
import applyKeep from "@/views/mediation/dialog/applyKeep";
import transferCase from "@/views/mediation/dialog/transferCase";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import { formatParams } from "@/utils/common";
import { totalMoneyReturn } from "@/api/mediation/exeOfPaybacks";
import { closeWayEnum, pageTypeEnum, isNoEnum } from "@/utils/enum";
import { getCourtOptions } from "@/api/common/common";
import { getExecuteList } from "@/api/mediation/lawsuitCarry";
const router = useRouter();
const route = useRoute();
const pageType = "exeOfPaybacks";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const statistics = ref({ totalMoney: 0, principal: 0, caseNum: 0 });
const showSearch = ref(false);
const { queryParams } = toRefs(data);
const dataList = ref([]);

const allQuery = ref(false);
const selectedArr = ref([]);

const rangFields = ["judgeTime"];
const courtOption = ref([]);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "被告", visible: true },
  { key: 2, label: "手机号码", visible: true },
  { key: 3, label: "标的额", visible: true },
  { key: 4, label: "身份证号码", visible: true },
  { key: 5, label: "户籍地", visible: true },
  { key: 6, label: "执行状态", visible: true },
  { key: 7, label: "结案标的额", visible: true },
  { key: 8, label: "案件涉及", visible: true },
  { key: 9, label: "被执行人下落不明", visible: true },
  { key: 10, label: "执行法院", visible: true },
  { key: 11, label: "结案案由", visible: true },
  { key: 12, label: "结案方式", visible: true },
  { key: 13, label: "有无结案文书", visible: true },
  { key: 14, label: "跟进人员", visible: true },
  { key: 15, label: "最近一次跟进时间", visible: true },
]);
//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = [];
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.disposeStage = "待领款";
  reqForm.condition = allQuery.value;
  reqForm.type = 1;
  getExecuteList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 获取机构列表
getCourts();
function getCourts() {
  getCourtOptions().then((res) => {
    courtOption.value = res.data;
  });
}
//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  handleQuery();
}

// 选择
function selectionChange(selection) {
  selectedArr.value = selection; // 选中的列表
}

// 是否可选
function selectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      type: 1,
      disposeStage: "待领款",
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: "待领款" };
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}

function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const newAllQuery = row ? false : allQuery.value;
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds };
  const data = {
    query,
    ...row,
    caseIds,
    allQuery: newAllQuery,
    pageType,
    isBatchSend: 1,
    isLitigationExecute: 1,
  };
  data.condition = row ? false : allQuery.value;
  data.type = 1;
  data.oneStatus = "回款登记";
  proxy.$refs[refName].openDialog(data);
}

//导出数据
function downloadCase() {
  proxy.downloadforjson(
    "/execute-case/exportWithRefundRecord",
    getReqParams(),
    `待领款_${+new Date()}.xlsx`
  );
}

// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery);
  reqForm.condition = allQuery.value;
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.disposeStage = "待领款";
  reqForm.type = 1;
  return reqForm;
}
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      totalMoneyReturn(reqForm)
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.principal,
          };
        })
        .finally(() => reslove());
    });
  });
}

const htrxCall = computed(() => store.getters.htrxCall);
provide("getList", Function, true);
getList();
</script>

<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

:deep(.hint .el-tooltip__trigger) {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.hint-item {
  font-size: 18px;
  // color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.table-title-question {
  display: inline-block;
  margin-left: 5px;
  color: var(--theme);
}
</style>
