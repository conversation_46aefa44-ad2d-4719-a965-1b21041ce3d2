<template>
  <div id="assest-charts" :key="new Date().getTime()" class="assest-charts"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { numFilter } from "@/utils/common.js";

//生成图表
function getMap(assestMoney, mapData) {
  let MapID = echarts.init(document.getElementById(`assest-charts`));
  MapID.clear();
  //图表属性
  let options = {
    color: ["#8492a6", "#409eff"],
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}%",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "-50",
      containLabel: true,
    },
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        avoidLabelOverlap: false,
        label: {
          show: true,
          formatter: `{d|{c}%}`,
          rich: {
            d: {
              fontSize: 20,
              lineHeight: 26,
              height: 26,
            },
          },
        },
        labelLine: {
          show: true,
        },
        data: mapData,
      },
      {
        type: "pie",
        radius: ["50%", "65%"],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: "center",
          formatter: `{d|委案规模/元} \n {b|${numFilter(assestMoney)}}`,
          rich: {
            d: {
              color: "#333",
              fontSize: 16,
              lineHeight: 24,
              height: 24,
            },
            b: {
              color: "#409eff",
              fontSize: 24,
              lineHeight: 30,
              height: 30,
            },
          },
        },
        labelLine: {
          show: false,
        },
        data: mapData,
      },
    ],
  };
  MapID.setOption(options);
  window.onresize = function () {
    MapID.resize();
  };
}

defineExpose({
  getMap,
});

</script>

<style scoped lang="scss">
#assest-charts {
  height: 100%;
  width: 49%;
  margin-right: 2%;
  top: -28px;
  display: inline-block;
}
</style>
