<template>
  <!-- 案件信息 -->
  <div class="caseinfo-wrap">
    <div class="radio-btn">
      <el-radio-group v-model="infotype" @change="tabChange">
        <el-radio-button value="债权信息" label="债权信息" />
        <el-radio-button value="共债信息" label="共债信息">共债信息({{ count }})</el-radio-button>
        <el-radio-button value="还款计划" label="还款计划" />
        <el-radio-button value="附加信息" label="附加信息" />
        <el-radio-button value="账户信息" label="账户信息" />
        <!-- <el-radio-button value="证据材料" label="证据材料" v-if="false" /> -->
        <el-radio-button value="文书" label="文书" />
        <el-radio-button value="档案资料" label="档案资料" />
      </el-radio-group>
    </div>

    <!-- 债权信息 -->
    <formCom v-if="infotype == '债权信息'" :form="infoLoan" :form2="infoBase" :formList="infoLoanList" />

    <!-- 共债信息 -->
    <debtInfo v-if="infotype == '共债信息'" :count="count" />
    <!-- 还款计划 -->
    <refundPlan v-if="infotype == '还款计划'" />

    <div v-if="infotype == '账户信息'">
      <el-table :data="accounts" max-height="450">
        <el-table-column label="开户行名称" prop="bankName" key="bankName" :width="150" align="center" />
        <el-table-column label="账户名称" prop="accountName" key="accountName" :width="150" align="center" />
        <el-table-column label="开户账户" prop="accountNumber" key="accountNumber" align="center">
          <template #default="{ row }">
            {{ row.accountNumber }}
            <span class="ml10" @click="setLink(row.accountNumber)"><img src="@/assets/icons/svg/copy.svg" /></span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 附加信息 -->
    <div v-if="infotype == '附加信息'">
      <div class="other-info">
        <el-form inline label-position="left">
          <el-row :gutter="10">
            <el-col :span="12" v-for="item in infoExtra" :key="item.id">
              <el-form-item :label="`${item.extraName}:`" label-width="auto">
                {{ item.extraValue || "--" }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 证据材料 -->
    <evidenceMaterial v-if="infotype == '证据材料'" :previewSigntrue="previewSigntrue" :urls="urls"
      :handleSelectionChange="handleSelectionChange" :handleDownload="handleDownload" :isPackage="isPackage"
      :handleBatchDownload="handleBatchDownload" />
    <!-- 文书 -->
    <writ v-if="infotype == '文书'" :previewSigntrue="previewSigntrue" :urls="urls" :isPackage="isPackage"
      :handleDownload="handleDownload" :handleSelectionChange="handleSelectionChange"
      :handleBatchDownload="handleBatchDownload" />

    <!-- 档案资料 -->
    <fileData v-if="infotype == '档案资料'" />
  </div>
</template>

<script setup>
import evidenceMaterial from './tableCom/evidenceMaterial';
import writ from './tableCom/writ';
import formCom from './components/formCom';
import debtInfo from './tableCom/debtInfo';
import refundPlan from './tableCom/refundPlan';
import fileData from './tableCom/fileData';
const { proxy } = getCurrentInstance();
const router = useRouter();
const props = defineProps({
  caseId: {
    //案件ID
    type: String,
    default: "0",
  },
  infoLoan: {
    //债权信息
    type: Object,
    default: {},
  },
  infoBase: {
    //债权信息
    type: Object,
    default: {},
  },
  jointDebtData: {
    //共债信息
    type: Array,
    default: [],
  },
  infoPlan: {
    //还款计划
    type: Object,
    default: [],
  },
  infoExtra: {
    //附加信息
    type: Array,
    default: [],
  },
  accounts: {
    //账户信息
    type: Array,
    default: [],
  },
  count: {
    type: Number,
    default: 0,
  },
});
const infotype = ref("债权信息");
const infoLoanList = ref([
  { code: "entrustMoney", isNum: true, info: "债权总金额", class: "text-blue" },
  { code: "residualPrincipal", isNum: true, info: "债权本金", class: "text-blue" },
  { code: "interestMoney", isNum: true, info: "债权利息", class: "text-blue" },
  { code: "baseOverdueDays", info: "基准日逾期天数", class: "text-blue" },
  { code: "overdueStart", info: "逾期日期（末次）", class: "text-blue" },
  { code: "principalInterestTotal", isNum: true, info: "本息费合计" },
  { code: "interestFeesTotal", isNum: true, info: "息费合计" },
  { code: "serviceFee", isNum: true, info: "债权费用" },
  { code: "bankCardNumber", info: "银行卡号", isVal: true },
  { code: "amountFinalRepayment", isNum: true, info: "最后还款金额", class: "text-red" },
  { code: "amountAfterDeduction", isNum: true, info: "减免后应还金额", class: "text-red" },
  { code: "syYhPrincipal", isNum: true, info: "剩余应还本金", class: "text-red" },
  { code: "syYhInterest", isNum: true, info: "剩余应还利息", class: "text-red" },
  { code: "syYhFees", isNum: true, info: "剩余应还费用", class: "text-red" },
  { code: "remainingDue", isNum: true, info: "剩余应还债权金额", class: "text-red" },
  { code: "amountCalledBack", isNum: true, info: "累计已还", class: "text-red" },
  { code: "ycContractNo", info: "合同号" },
  { code: "cardIssuanceDate", info: "发卡日期" },
  { code: "repaymentDate", info: "每月还款日" },
  { code: "statementDate", info: "账单日" },
  { code: "firstOverdueDate", info: "首次逾期日期" },
  { code: "baseDate", info: "基准日" },
  { code: "creditLimit", info: "统计时点信用额度" },
  { code: "amountFinalDate", info: "末次还款日期" },
  { code: "ycOverdueDays", info: "实际逾期天数" },
  { code: "loanMoney", isNum: true, info: "贷款金额" },
  { code: "ycDisbursement", isNum: true, info: "垫付费用" },
  { code: "contractNo", info: "借据号", class: "text-blue" },
  { code: "bankName", info: "开户行", isVal: true, },
  { code: "ycCurrencies", info: "币种" },
  { code: "ycPurpose", info: "贷款用途" },
  { code: "ycLendingRate", info: "贷款利率", isPercent: true },
  { code: "ycRepaymentMethod", info: "还款渠道" },
  { code: "ycLitigationStatus", info: "诉讼状态" },
  { code: "ycIsDishonest", info: "是否失信被执行人" },
  { code: "ycIsLimitConsumption", info: "是否被限制高消费" },
  { code: "ycWriteDate", info: "核销日期" },
  { code: "ycDefaultRate", info: "罚息利率", isPercent: true },
  { code: "loanPrincipal", isNum: true, info: "贷款本金" },
  { code: "repaymentMonthly", isNum: true, info: "每月应还" },
  { code: "caseRegion", info: "案件地区" },
  { code: "loanPeriods", info: "贷款期数" },
  { code: "alreadyPeriods", info: "已还期数" },
  { code: "notPeriods", info: "未还期数" },
  { code: "loanInstitution", info: "债权机构" },
  { code: "accountPeriod", info: "账期（M1~Mn）", },
  { code: "ycLoanBank", info: "放款分（支）行" },
  { code: "ycBusinessType", info: "业务类型" },
  { code: "productType", info: "产品类型" },
  { code: "ycContractMoney", isNum: true, info: "合同金额" },
  { code: "ycLoanTerm", info: "贷款期限" },
  { code: "ycLoanIssuanceDate", info: "贷款发放日" },
  { code: "ycLoanMaturityDate", info: "贷款到期日" },
  { code: "ycAbdRepayment", isNum: true, info: "基准日后还款金额" },
  { code: "ycAbdPrincipal", isNum: true, info: "基准日后本金还款" },
  { code: "ycAbdInterest", isNum: true, info: "基准日后利息还款" },
  { code: "latestFollowUpTime", info: "最近跟进时间", class: "text-blue" },
  { code: "returnCaseDate", info: "退案日期", class: "text-blue" }
])

const urls = ref([]);
//点击复制
function setLink(text) {
  proxy.copyUrl(text);
  proxy.$modal.msgSuccess("复制成功！");
}
const tabChange = () => {
}
// 预览
function previewSigntrue(originUrl) {
  if (originUrl && originUrl.includes(".pdf")) {
    window.open(originUrl + '?response-content-type=application/pdf')
  } else if (originUrl) {
    window.open(originUrl)
  }  else {
    proxy.$modal.msgWarning("暂无链接")
  }
}

// 子表格选择数据
function handleSelectionChange(selection) {
  urls.value = selection.map(item => item.fileUrl ? item.fileUrl : item.signPreviewUrl).filter(item => item)
}

// 批量下载
function handleBatchDownload(fileName) {
  proxy.downloadforjson("/letter/item/downloadMediate", urls.value, fileName, { gateway: "sign" });
}
//下载
function handleDownload(fileUrl, fileName) {
  // fileUrl && window.open(fileUrl);

  // ie和浏览器兼容模式会有问题，可以用下面代码调试。
  try {
    exportFile(fileUrl, fileName) // 调用方式
  } catch (err) {
    // 兼容模式下，IE
    const exportBlob = new Blob([data]);
    if (navigator.userAgent.indexOf('Trident') > -1) {
      window.navigator.msSaveBlob(data, fileName);
    } else {
      exportFile(fileUrl, fileName) // 调用方式
    }
  };
}
function exportFile(data, fileName) {
  // 地址不存在时，禁止操作
  if (!data) return;
  // 下载文件并保存到本地
  const callback = (data) => {
    // 创建a标签，使用 html5 download 属性下载，
    const link = document.createElement('a');
    // 创建url对象
    const objectUrl = window.URL.createObjectURL(new Blob([data]));
    link.style.display = 'none';
    link.href = objectUrl;
    // 自定义文件名称， fileName
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    // 适当释放url
    window.URL.revokeObjectURL(objectUrl);
  };
  // 把接口返回的url地址转换为 blob
  const xhr = new XMLHttpRequest();
  xhr.open('get', data, true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    // 返回文件流，进行下载处理
    callback(xhr.response);
  };
  xhr.send(); // 不要忘记发送
};



// 判断是否是压缩包
function isPackage(fileUrl) {
  if (!fileUrl) return false
  const index = fileUrl.lastIndexOf('.')
  const fileType = fileUrl.slice(index, fileUrl.length)
  return !['.ZIP', '.RAR', '.7Z', '.TAR', '.GZ'].includes(fileType)
}

</script>

<style lang="scss" scoped>
.radio-btn {
  position: relative;
  padding: 20px;
  border-bottom: 1px solid rgba(171, 186, 223, 0.1);
}

.case-info {
  .other-info {
    min-height: 60px;
  }
}

.man-info.flex-wrap {
  padding: 20px;
  border-bottom: 1px solid rgba(171, 186, 223, 0.1);

  .flex-wrap-item {
    padding: 0 20px;
    border-right: 1px solid rgba(171, 186, 223, 0.1);
    font-size: 14px;

    .text {
      color: #888888;
    }
  }

  .flex-wrap-item:first-child {
    padding-left: 0;
  }

  .flex-wrap-item:last-child {
    padding-right: 0;
  }
}

.other-info {
  padding: 20px;
  min-height: 60px;
}

.other-info :deep(.el-form-item--default) {
  margin-bottom: 0px;
}

.form-item-text {
  width: 100%;
  word-wrap: break-word;
  line-height: 20px;
  padding-right: 5px;
}

:deep(.top-right-btn) {
  top: 20px;
  right: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
}
</style>
