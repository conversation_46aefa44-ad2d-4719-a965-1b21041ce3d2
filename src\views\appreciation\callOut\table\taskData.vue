<template>
    <div class="task-data">
        <el-row>
            <el-col :span="6">
                <div class="label-content">外呼执行时间</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ timeFor(data.executionTime) }}</div>
            </el-col>
            <el-col :span="6">
                <div class="label-content">外呼时段</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{data.executionCallTime }}</div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
                <div class="label-content">任务创建时间</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ data.createTime }}</div>
            </el-col>
            <el-col :span="6">
                <div class="label-content">呼叫坐席</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">
                    <span v-for="(item, index) in data.callTheSeatList">{{ item }}</span>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
                <div class="label-content">重呼次数</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ data.recallCount }} 次</div>
            </el-col>
            <el-col :span="6">
                <div class="label-content">重呼间隔</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ data.recallMinute ? data.recallMinute : 0 }} 分钟</div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
                <div class="label-content">是否弹屏</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ popupFor(data.isScreen) }}</div>
            </el-col>
            <el-col :span="6">
                <div class="label-content">单次批量外呼数量</div>
            </el-col>
            <el-col :span="6">
                <div class="value-content">{{ data.singleCallNumber }}</div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
                <div class="label-content">备注</div>
            </el-col>
            <el-col :span="18">
                <div class="value-content">{{ data.remark }}</div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})

const timeFor = (str) => {
    const weekDays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    if (str) {
        // 将字符串转换为数组，映射到对应的星期名称
        const convertedDays = str.split(',').map(day => weekDays[day - 1]);
        // 对数组进行排序
        convertedDays.sort((a, b) => {
            return weekDays.indexOf(a) - weekDays.indexOf(b);
        });
        const result = convertedDays.join('，');
        return result;
    }
    return '';
}

const popupFor = (value) => {
    const screenMap = {
        1: "是",
        2: "否"
    };
    return screenMap[value] || "--";
}
</script>

<style lang="scss" scoped>
.task-data {
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-radius: 4px;
    .label-content, .value-content {
        height: 2.5rem;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        border-bottom: 1px solid #dcdfe6;
        padding-left: 10px;

        span {
            white-space: nowrap;    /* 不换行 */
        }
    }
    .label-content{
        color: #909399;
        background-color: #ebeef5;
    }
    .value-content {
        white-space: nowrap;    /* 不换行 */
        overflow-x: auto;       /* 隐藏溢出部分 */
    }
}


</style>