<template>
  <el-dialog title="预览" v-model="open" :before-close="cancel">
    <div class="case-pdf" v-loading="loading">
      <previewPdf v-if="pdfSrc && pdfSrc.length > 0" :pdfSrc="pdfSrc" :total="total" />
    </div>
    <template #footer>
      <div class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="cancel">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="TemplateDetails">
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
//全局变量
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const pdfSrc = ref(undefined);
const total = ref(undefined);
//打开弹窗
function opendialog(value) {
  open.value = true;
  pdfSrc.value = value.url;
  total.value = value.total;
}


//关闭弹窗
function cancel() {
  open.value = false;
  pdfSrc.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped>
.case-pdf {
  width: 100%;
  height: 550px;
  overflow: auto;
  overflow-x: hidden;
}
</style>
