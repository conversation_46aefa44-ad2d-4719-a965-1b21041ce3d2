<template>
  <!-- 添加联系人 -->
  <el-dialog
    :title="title"
    v-model="open"
    width="600px"
    append-to-body
    :before-close="cancel"
  >
    <div class="case-info"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submit">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { insertInfoContact, updateInfoContact } from "@/api/caseDetail/detail";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getContactList"]);

const title = ref("");
const open = ref(false);
const loading = ref(false);
const data = reactive({
  form: {},
  rules: {
    contactName: [{ required: true, message: "请输入联系人姓名", trigger: "blur" }],
    contactPhone: [{ required: true, message: "请输入联系人电话号码", trigger: "blur" }],
    contactRelation: [{ required: true, message: "请输入联系人关系", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);

//打开弹窗
function opendialog(data) {
  reset();
  if (data.id) {
    title.value = "编辑备注";
  } else {
    title.value = "新增联系人";
  }
  Object.assign(form.value, data);
  !data.remarks &&
    (form.value.remarks = form.value.phoneState == 0 ? "有效；" : "无效；");
  open.value = true;
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    contactName: undefined,
    contactPhone: undefined,
    phoneState: 0,
    contactRelation: undefined,
    remarks: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//有效无效转换
function stateChange() {
  let strs = ["有效；", "无效；"];
  let remarks = form.value.remarks || "";
  if (remarks && remarks != "") {
    let reg = /^有效；|无效；/;
    remarks = remarks.replace(reg, "");
  }
  form.value.remarks = strs[form.value.phoneState] + remarks;
}

//提交
function submit() {
  loading.value = false;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.id) {
        updateInfoContact(form.value)
          .then(() => {
            proxy.$modal.msgSuccess("编辑成功！");
            emit("getContactList");
            cancel();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        insertInfoContact(form.value)
          .then(() => {
            proxy.$modal.msgSuccess("新增成功！");
            emit("getContactList");
            cancel();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
