<template>
  <el-dialog
    title="申请保全"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="150px">
      <el-form-item prop="courtId" label="保全法院">
        <el-select
          placeholder="请选择保全法院"
          style="width: 80%"
          v-model="form.courtId"
          clearable
        >
          <el-option
            v-for="item in courtOption"
            :label="item.info"
            :key="item.code"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="startFreeze" label="保全期限开始时间">
        <el-date-picker
          style="width: 80%"
          v-model="form.startFreeze"
          value-format="YYYY-MM-DD 00:00:00"
          type="date"
          placeholder="请选择保全期限开始时间"
        />
      </el-form-item>
      <el-form-item prop="endFreeze" label="保全期限结束时间">
        <el-date-picker
          style="width: 80%"
          v-model="form.endFreeze"
          value-format="YYYY-MM-DD 23:59:59"
          type="date"
          range-separator="-"
          placeholder="请选择保全期限结束时间"
        />
      </el-form-item>
      <el-form-item prop="freezeAmount" label="申请保全金额">
        <el-input
          style="width: 80%"
          v-model="form.freezeAmount"
          placeholder="请输入申请保全金额"
        />
      </el-form-item>
      <el-form-item prop="actualFreezeAmount" label="实际保全金额">
        <el-input
          style="width: 80%"
          v-model="form.actualFreezeAmount"
          placeholder="请输入实际保全金额"
        />
      </el-form-item>
      <el-form-item prop="freezeAssets" label="保全标的物">
        <el-input
          style="width: 80%"
          v-model="form.freezeAssets"
          placeholder="请输入保全标的物"
        />
      </el-form-item>
      <el-form-item prop="remark" label="备注">
        <el-input
          style="width: 80%"
          v-model="form.remark"
          placeholder="请输入备注"
          :maxlength="100"
          show-word-limit
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button :loading="loading" @click="subimt" type="primary">确认</el-button>
        <el-button :loading="loading" @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { getCourtOptions } from "@/api/common/common";
import { applyKeepFreeze } from "@/api/mediation/iegalPreservation";
const props = defineProps({
  getList: { type: Function },
});
const courtOption = ref([]);
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const data = reactive({
  form: {},
  rules: {
    court: [{ required: true, message: "请选择保全法院", trigger: "blur" }],
    startFreeze: [{ required: true, message: "请选择保全期限开始时间", trigger: "blur" }],
    endFreeze: [{ required: true, message: "请选择保全期限结束时间", trigger: "blur" }],
    freezeAmount: [
      { required: true, message: "请输入申请保全金额", trigger: "blur" },
      {
        pattern: /^\d+(.\d{2})?$/,
        message: "请输入数字，可保留两位小数",
        trigger: "blur",
      },
    ],
    actualFreezeAmount: [
      { required: true, message: "请输入实际保全金额", trigger: "blur" },
      {
        pattern: /^\d+(.\d{2})?$/,
        message: "请输入数字，可保留两位小数",
        trigger: "blur",
      },
    ],
    freezeAssets: [{ required: false, message: "请输入保全标的物", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
function subimt() {
  proxy.$refs["formRef"].validate((vaild) => {
    if (vaild) {
      const reqForm = { ...form.value.query, freezeRecord: form.value };
      const court = courtOption.value.find((item) => item.code == form.value.courtId);
      reqForm.freezeRecord.court = court?.info;
      reqForm.freezeRecord.courtId = court?.code;
      reqForm.saveStage = "材料提交";
      loading.value = true;
      applyKeepFreeze(reqForm)
        .then((res) => {
          if (res.code == 200) {
            props.getList && props.getList();
            proxy.$modal.msgSuccess("操作成功！");
            cancel();
          }
        })
        .finally(() => (loading.value = false));
    }
  });
}
function openDialog(data) {
  open.value = true;
  form.value.query = { ...data.query, caseIds: data.caseIds};
}
function cancel() {
  open.value = false;
  form.value = {};
}
// 获取机构列表
getCourts();
function getCourts() {
  getCourtOptions().then((res) => {
    courtOption.value = res.data;
  });
}
defineExpose({ openDialog });
</script>
