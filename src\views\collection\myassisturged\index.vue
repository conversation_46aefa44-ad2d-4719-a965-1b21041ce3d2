<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" :label-width="100" :rules="rules"
      class="form-content h-50" :class="{ 'h-auto': showSearch }">
      <el-form-item label="案件ID" prop="caseId">
        <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable type="number" style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>

      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="产品类型" prop="productId">
        <el-input v-model="queryParams.productId" placeholder="请输入产品类型" clearable style="width: 240px"
          @keyup.enter="antiShake(handleQuery)" />
      </el-form-item>
      <el-form-item label="申请日期" style="width: 336px">
        <el-date-picker v-model="queryParams.applyDate" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <el-row class="mb10 mt10 h32">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane :label="`${tab[0].label}(${tab[0].count})`" name="0"> </el-tab-pane>
      <el-tab-pane :label="`${tab[1].label}(${tab[1].count})`" name="1"> </el-tab-pane>
      <el-tab-pane :label="`${tab[2].label}(${tab[2].count})`" name="2"> </el-tab-pane>
      <el-tab-pane :label="`${tab[3].label}(${tab[3].count})`" name="3"> </el-tab-pane>
      <el-tab-pane :label="`${tab[4].label}(${tab[4].count})`" name="all"> </el-tab-pane>
    </el-tabs>

    <el-table v-loading="loading" ref="multipleTableRef" :data="caseList">
      <el-table-column label="案件ID" align="center" key="caseId" prop="caseId" v-if="columns[0].visible">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center">
            <case-label class="ml5" v-if="scope.row.label && scope.row.label != 7" :code="scope.row.label" />
            <span style="color:#409eff;cursor: pointer;" type="text" v-if="scope.row.button == 1"
              @click="toDetails(scope.row.caseId, scope.$index)">{{
                scope.row.caseId
              }}</span>
            <span v-if="scope.row.button == 0">{{ scope.row.caseId }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" key="productName" prop="productName" v-if="columns[1].visible" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[2].visible" />
      <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" v-if="columns[3].visible"
        :width="120" />
      <el-table-column label="证件类型" align="center" key="clientIdType" prop="clientIdType" :width="120"
        v-if="columns[4].visible" />
      <el-table-column label="证件号码" align="center" key="clientIdcard" prop="clientIdcard" v-if="columns[5].visible"
        :width="180">
      </el-table-column>
      <el-table-column label="债权总金额" width="120" align="center" key="clientMoney" prop="clientMoney"
        v-if="columns[6].visible">
        <template #default="scope">
          <span>{{ scope.row.clientMoney ? proxy.setNumberToFixed(scope.row.clientMoney) : `--` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请原因" align="center" v-if="columns[7].visible">
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.reason }}</p>
            </template>
            <div>
              <span>{{
                scope.row.reason?.length > 15
                  ? `${scope.row.reason?.substring(0, 15)}...`
                  : scope.row.reason
              }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" key="applicant" prop="applicant" v-if="columns[8].visible" />
      <el-table-column label="申请时间" align="center" key="applyDate" prop="applyDate" v-if="columns[9].visible"
        :width="160" />
      <el-table-column label="协催状态" align="center" key="state" prop="state" :formatter="stateFor"
        v-if="columns[10].visible" />
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.button == 1 && scope.row.state != 3"
            v-hasPermi="['collection:myassisturged:register']" type="text"
            @click="urgedRegister(scope.row)">协案登记</el-button>
          <el-button type="text" v-hasPermi="['collection:myassisturged:log']"
            @click="urgedRecord(scope.row)">协案记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 协催登记 -->
    <insertAssistDetails @getList="getList" ref="insertAssistDetailsRef" />
    <!-- 协催记录 -->
    <selectAssistDetails ref="selectAssistDetailsRef" />
  </div>
</template>
<script setup name="Myassisturged">
import insertAssistDetails from "./dialog/insertAssistDetails.vue";
import selectAssistDetails from "./dialog/selectAssistDetails.vue";
import {
  selectAssistRecordHelperId,
  getAssistCount
} from "@/api/collection/myassisturged";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表格配置数据
const loading = ref(false);
const total = ref(0);
const multipleTableRef = ref();
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    productId: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    clientPhone: undefined,
    applyDate: [],
    state: undefined,
  },
  rules: {
    clientPhone: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
    ],
    clientIdcard: [
      { required: false, message: "请输入证件号码", trigger: "blur" },
    ],
  },
});
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 50 },
  { key: 1, label: `产品类型`, visible: true, width: 200 },
  { key: 2, label: `姓名`, visible: true, width: 100 },
  { key: 3, label: `手机号码`, visible: true, width: 180 },
  { key: 4, label: `证件号码`, visible: true, width: 300 },
  { key: 5, label: `债权总金额`, visible: true, width: 100 },
  { key: 6, label: `申请原因`, visible: true, width: 150 },
  { key: 7, label: `申请人`, visible: true, width: 50 },
  { key: 8, label: `申请时间`, visible: true, width: 180 },
  { key: 9, label: `协催状态`, visible: true, width: 100 },
  { key: 10, label: `证件类型`, visible: true, width: 100 },
]);
const { queryParams, rules } = toRefs(data);
//表单配置信息
const showSearch = ref(false);
const tab = ref([
  { label: "待协催", count: 0 },
  { label: "持续协催", count: 0 },
  { label: "完成协催", count: 0 },
  { label: "终止协催", count: 0 },
  { label: "全部", count: 0 },
]);
const rangfiles = ["applyDate"];
const activeTab = ref("all");
const caseList = ref([]);

//获取列表数据
function getList() {
  queryParams.value.state = activeTab.value == "all" ? undefined : activeTab.value;
  loading.value = true;
  selectAssistRecordHelperId(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
      getCount()
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取工单数量
function getCount() {
  getAssistCount(proxy.addFieldsRange(queryParams.value, rangfiles)).then((res) => {
    tab.value.forEach((item) => item.count = 0);
    res?.data.forEach((item, index) => {
      if (item.state == "0") {
        tab.value[0].count = item.number;
      }
      if (item.state == "1") {
        tab.value[1].count = item.number;
      }
      if (item.state == "2") {
        tab.value[2].count = item.number;
      }
      if (item.state == "3") {
        tab.value[3].count = item.number;
      }
      tab.value[4].count += item.number;
    });
  })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getCount();

//协催登记
function urgedRegister(row) {
  let data = {
    caseId: row.caseId,
    assistId: row.id,
  };
  proxy.$refs["insertAssistDetailsRef"].opendialog(data);
}

//跳转案件详情
function toDetails(caseId, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNum = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange,
    type: "mycase",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}`, query: { type: "myCase" } });
}

//协催记录
function urgedRecord(row) {
  proxy.$refs["selectAssistDetailsRef"].opendialog(row.id);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    productId: undefined,
    clientMoney1: undefined,
    clientMoney2: undefined,
    clientPhone: undefined,
    applyDate: [],
    state: undefined,
  };
  getList();
}

//tab选择
function tabChange() {
  getList();
}

//协催状态 0-待协催，1-持续协催，2-完成协催，3-终止协催
function stateFor(row) {
  return ["待协催", "持续协催", "完成协催", "终止协催"][
    row.state
  ];
}

</script>
<style lang="scss" scoped>
.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h32 {
  height: 32px !important;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}
</style>
