import request from '@/utils/request'

// 号码清洗列表
export function phoneCleanList(query) {
  return request({
    url: '/saasNumberDetectionBatch/selectList',
    method: 'get',
    params: query
  })
}

// 申请号码清洗
export function applyPhoneClean(data) {
  return request({
    url: '/saasNumberDetectionBatch/numberDetection',
    method: 'post',
    data
  })
}

// 获取号码清洗信息
export function getPhoneCleanInfo(data) {
  return request({
    url: '/saasNumberDetectionBatch/getCleaningInformation',
    method: 'post',
    data
  })
}

// 清洗批次下拉
export function cleanBatchOptions() {
  return request({
    url: '/saasNumberDetectionBatch/selectDropDown',
    method: 'get',
  })
}

// 批次统计
export function cleanBatchStatistics() {
  return request({
    url: '/saasNumberDetectionBatch/selectCount',
    method: 'get',
  })
}

// 批次结果列表
export function cleanBatchResultList(query) {
  return request({
    url: '/numberDetectionDetails/selectList',
    method: 'get',
    params: query
  })
}

// 批次结果统计
export function cleanBatchResultStatistics(query) {
  return request({
    url: '/numberDetectionDetails/selectCount',
    method: 'get',
    params: query
  })
}