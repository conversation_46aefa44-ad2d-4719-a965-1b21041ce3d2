<template>
  <el-dialog title="重置密码" v-model="open" width="650px" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
      <el-form-item label="已选员工" :error="validSelected" required>
        <div class="selected-user" v-for="(item, index) in selectedUsers" :key="item.id">
          <div>
            <span class="name">{{ item.employeeName }}</span>
            <span class="role">{{ item.departments + " / " + item.theRole }}</span>
          </div>
          <el-icon class="close" @click="remove(index)"><close /></el-icon>
        </div>
      </el-form-item>
      <el-form-item label="用户密码" prop="password">
        <el-input
            v-model="form.password"
            placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
            type="password"
            maxlength="20"
            show-password
            oncut="return false"
            onpaste="return false"
            oncopy="return false"
            onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
            oninput="value=value.replace(/[^\x00-\xff]/g, '')"
          />
        </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updatePassword } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
const selectedUsers = ref([]);
const validSelected = ref(undefined);

const open = ref(false);
const loading = ref(false);

const data = reactive({
  form: {},
  rules: {
    password: [{
        required: true,
        pattern:  /^\S*$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },{
        required: true,
        pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },
    ]
  },
});
const { form, rules } = toRefs(data);

function opendialog(selecteds) {
  reset();
  selectedUsers.value = JSON.parse(JSON.stringify(selecteds));
  open.value = true;
}

//删除已选员工
function remove(index) {
  selectedUsers.value.splice(index, 1);
}

//提交
function submitForm() {
  loading.value = true;
  try {
    if (selectedUsers.value.length === 0) {
      validSelected.value = "请选择员工";
      throw new Error("请选择员工");
    } else {
      validSelected.value = undefined;
    }

    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        let req = [];
        selectedUsers.value.map((item) => {
          let obj = { id: item.id ,password:form.value.password};
          req.push(obj);
        });
        updatePassword(req)
          .then((res) => {
            proxy.$modal.msgSuccess("修改成功！");
            cancel();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  } catch (error) {
    loading.value = false;
    // throw error
  }
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
.selected-user {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  .name {
    font-weight: 450;
    margin-right: 10px;
  }
  .role {
    font-size: 12px;
    color: #888888;
  }
  .close {
    cursor: pointer;
  }
}
</style>
