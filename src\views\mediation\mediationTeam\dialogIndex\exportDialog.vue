<template>
  <el-dialog :title="title" v-model="open" width="750px" :before-close="cancel" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
      <el-form-item label="沟通日期" prop="time">
        <el-date-picker v-model="form.time" type="daterange" range-separator="-" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD hh:mm:ss" style="width: 240px" />
      </el-form-item>
      <el-form-item label="导出字段" prop="headers">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group v-model="form.headers" @change="handleHeadChange">
          <el-row>
            <el-col :span="6" v-for="item in headerArr" :key="item">
              <el-checkbox :label="item">{{ item }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="subloading" @click="submit">导出</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { selectTickField, selectUrgeRecord } from "@/api/mediation/appealCase";
const { proxy } = getCurrentInstance();

const props = defineProps({
  query: {
    type: Object,
  },
  checkedType: {
    type: String,
  },
});

const exType = ref("");

const title = ref("");
const open = ref(false);
const checkAll = ref(false);
const isIndeterminate = ref(false);
const headerArr = ref([]);
const subloading = ref(false);
const data = reactive({
  form: {},
  rules: {
    headers: [{ required: true, message: "请勾选导出字段", trigger: "blur" }],
    time: [{ required: true, message: "请选择沟通日期", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);

const batchs = ref([]);

//打开弹窗
async function opendialog(type, caseIds, condition) {
  reset();
  exType.value = type;
  const titles = ["导出沟通记录", ""];
  title.value = titles[type];

  if (0 === type) {
    if (caseIds.length < 1) {
      proxy.$modal.msgWarning("请选择案件");
      return false;
    }
    if (props.checkedType != "搜索结果全选") {
      form.value.caseIds = caseIds;
      form.value.condition = false;
      console.log(form.value.caseIds);
    } else {
      let querydata = JSON.parse(JSON.stringify(props.query));
      delete querydata.pageNum;
      delete querydata.pageSize;
      // form.value.queryParams = querydata;
      for (let key in querydata) {
        form.value[key] = querydata[key];
      }
      form.value.condition = true;
    }
    if (condition != undefined) form.value.condition = condition;
  }

  //复选框字段
  await selectTickField().then((res) => {
    headerArr.value = res.data;
  });

  open.value = true;
}

//全选
function handleCheckAllChange(val) {
  form.value.headers = val ? headerArr.value : [];
  isIndeterminate.value = false;
}

//复选框勾选
function handleHeadChange(value) {
  const checkedCount = value.length;
  checkAll.value = checkedCount === headerArr.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < headerArr.length;
}

//提交导出
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.time) {
        form.value.createTime1 = form.value.time[0];
        form.value.createTime2 = form.value.time[1];
      }
      proxy.$modal.loading("正在导出数据，请稍候...");
      const reqForm = JSON.parse(JSON.stringify(form.value))
      reqForm.allQuery = reqForm.condition
      selectUrgeRecord(reqForm).then(res => {
        cancel()
        proxy.$modal.closeLoading();
        proxy.$modal.exportTip(res.data)
      });
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    caseIds: undefined,
    headers: undefined,
    time: undefined,
    createTime1: undefined,
    createTime2: undefined,
    queryParam: undefined,
    ids: undefined,
  };
  checkAll.value = false;
  isIndeterminate.value = false;
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});

</script>

<style scoped></style>
