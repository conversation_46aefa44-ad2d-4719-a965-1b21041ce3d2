import request from '@/utils/request'
//获取机构树结构
export function getDeptTreeWithDisposeStage(params) {
  return request({
    url: '/team/DeptTreeWithDisposeStage',
    method: 'get',
    params
  })
}

// 根据条件查询列表信息
export function getSessionList(query) {
  return request({
    url: '/team/getSessionList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectSessionWithMoney(data) {
  return request({
    url: '/team/selectSessionWithMoney',
    method: 'post',
    data: data,
  })
}
