<template>
  <div class="app-container">
    <el-row class="mt10 mb20">
      <el-button plain icon="Back" @click="toBack">返回部门列表</el-button>
    </el-row>
    <div class="user-card">
      <div style="display:inline-block">
        <el-avatar class="user-icon" :size="72" :icon="UserFilled" />
      </div>
      <div style="display:inline-block;height:72px;vertical-align: top;">
        <div class="user-name">{{ userInfo.employeeName }}</div>
        <div class="user-info">{{ `${userInfo.departments}/${userInfo.theRole}` }}</div>
      </div>
    </div>
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane label="登录日志" value="0"> </el-tab-pane>
      <el-tab-pane label="操作记录" value="1"> </el-tab-pane>
    </el-tabs>
    <!-- 登录日志 -->
    <el-row v-if="activeTab == `0`">
      <el-table v-loading="loading" :data="userList">
        <el-table-column
            label="登录时间"
            align="center"
            key="accessTime"
            prop="accessTime"
        />
        <el-table-column
            label="终端类型"
            align="center"
            key="terminalType"
            prop="terminalType"
        />
        <el-table-column
            label="浏览器/终端版本"
            align="center"
            key="terminalVersion"
            prop="terminalVersion"
        />
        <el-table-column label="操作系统" align="center" key="os" prop="os"/>
        <el-table-column
            label="设备名称"
            align="center"
            key="devicename"
            prop="devicename"
        />
        <el-table-column label="登录IP" align="center" key="ipaddr" prop="ipaddr"/>
        <el-table-column
            label="所在地区"
            align="center"
            key="loginArea"
            prop="loginArea"
        />
      </el-table>
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-row>
    <el-row v-if="activeTab == `1`">
      <el-table v-loading="loading" :data="optList">
        <el-table-column
            label="操作时间"
            align="center"
            key="operTime"
            prop="operTime"
        />
        <el-table-column
            label="操作者"
            align="center"
            key="operName"
            prop="operName"
        />
        <el-table-column
            label="操作类型"
            align="center"
            key="businessType"
            prop="businessType"
            :formatter="businessTypeFor"
        />
        <el-table-column
            label="操作详情"
            align="center"
            :show-overflow-tooltip="true"
        >
          <template #default="scope">
        <span>{{
            `${scope.row.title}`
          }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getPage"
      />
    </el-row>
    <el-row style="display:block;width:500px" v-if="activeTab == `2`">
      <el-form :model="form" ref="formRef" label-position="right" :label-width="100">
        <el-form-item label="菜单权限">
          <el-tree
              ref="treeRef"
              class="tree-border"
              :data="powerList"
              show-checkbox
              :props="defaultProps"
              check-on-click-node
              :render-after-expand="false"
              node-key="id"
          />
        </el-form-item>
      </el-form>
      <div class="text-center mt20">
        <el-button @click="toBack">返回</el-button>
        <el-button type="primary" :loading="loading" plain @click="submit"
        >确定
        </el-button
        >
      </div>
    </el-row>
  </div>
</template>
<script setup name="UserDetails">
</script>

<style>
.user-icon .el-icon {
  font-size: 32px !important;
}
</style>
<style lang="scss" scoped>
.user-card {
  padding: 40px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;

  .user-name {
    color: #333;
    margin-left: 20px;
    height: 20px;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 20px;
  }

  .user-info {
    color: #999;
    height: 20px;
    font-size: 16px;
    margin-left: 20px;
  }
}
</style>
