<template>
  <div>
    <el-radio-group class="mb20" v-model="activeType">
      <el-radio-button label="呼叫中心2" value="呼叫中心2" />
      <el-radio-button label="手机通话记录" value="手机通话记录" />
    </el-radio-group>

    <callcenter2 v-if="activeType === '呼叫中心2'" />
    <phoneRecords v-if="activeType === '手机通话记录'" />

  </div>
</template>

<script setup>
import callcenter2 from './mycall_tab/callcenter2.vue';
import phoneRecords from './mycall_tab/phoneRecords.vue';
const activeType = ref('呼叫中心2');
</script>

<style scoped></style>
