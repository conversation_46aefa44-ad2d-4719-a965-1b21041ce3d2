<template>
    <el-drawer v-model="open" @before-close="cancel" append-to-body lock-scroll title="诉讼时效" size="55vw">
        <div class="form-content">
            <el-form :model="queryParams" ref="queryRef" inline label-width="90px">
                <el-form-item label="日期" prop="createTime">
                    <el-date-picker style="width:260px;" v-model="queryParams.createTime" placeholder="请选择日期"
                        value-format="YYYY-MM-DD" type="date" />
                </el-form-item>
                <el-form-item label="跟进类型" prop="contactInformation">
                    <el-select v-model="queryParams.contactInformation" collapse-tags-tooltip collapse-tags multiple
                        clearable filterable style="width:260px;" placeholder="请选择跟进类型">
                        <el-option v-for="item in followOption" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
                    <el-button @click="antiShake(resetQuery)">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="timeline">
            <el-timeline style="max-width: 600px">
                <el-timeline-item v-for="(item, index) in dataList" :type="`${index == 0 ? 'primary' : ''}`"
                    size="large" :key="index">
                    <div class="timeline-content">
                        <div class="title">
                            <span>{{ item.time }}</span>
                            <el-button type="text" class="arrow-btn"
                                :icon="item.isShow ? 'ArrowUpBold' : 'ArrowDownBold'"
                                @click="handleShow(item)"></el-button>
                        </div>
                        <div class="content-list" v-if="item.isShow">
                            <div class="content-item" v-for="item2 in item.children" :key="item2.id">
                                <div class="top">
                                    <el-tag effect="dark">{{ item2.contactInformation }}</el-tag>
                                    <span class="ml10 mr10">{{ item2.createTime }}</span>|&nbsp;&nbsp;
                                    <span class="mr20" v-if="isLiaison(item2.contactInformation)">联系人：
                                        <i> {{ item2.liaison }}</i>
                                    </span>
                                    <span class="mr20" v-if="isProposer(item2.contactInformation)">申请人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isInitiator(item2.contactInformation)">发起人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isOperator(item2.contactInformation)">操作人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isRegistrant(item2.contactInformation)">登记人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isCreator(item2.contactInformation)">创建人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isFollow(item2.contactInformation)">跟进人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                    <span class="mr20" v-if="isBelong(item2.contactInformation)">坐席所属人：
                                        <i>{{ item2.createBy }}</i>
                                    </span>
                                </div>
                                <div class="bottom mt20">
                                    {{ isApply(item2.contactInformation) }}：<i>{{ item2.contactStateContent }}</i>
                                </div>
                            </div>

                        </div>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </div>
        <template #footer>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </template>
    </el-drawer>
</template>
<script setup>
import { agingDetailById, followOptionApi } from '@/api/caseDetail/detail'
const { proxy } = getCurrentInstance()
const open = ref(false)
const route = useRoute()
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        caseId: route.params.caseId,
        createTime: undefined,
        contactInformation: undefined
    }
})
const total = ref(0)
const dataList = ref([])
const followOption = ref([])
const { queryParams } = toRefs(data)
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    reqForm.contactInformation = reqForm.contactInformation ? String(reqForm.contactInformation) : ''
    agingDetailById(reqForm).then(res => {
        if (res.code == 200) {
            dataList.value = handleData(res.rows)
            total.value = res.total
        }
    })
}

// 处理显示与隐藏
function handleShow(row) {
    dataList.value = dataList.value.map(item => {
        if (row.key == item.key) {
            item.isShow = !item.isShow
        }
        return item
    })
}

// 搜索
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}
// 重置
function resetQuery() {
    proxy.resetForm('queryRef')
    getList()
}
function openDialog() {
    open.value = true
    resetQuery()
    getList()
    getOption()
}

function getOption() {
    followOptionApi().then(res => {
        followOption.value = res.data
    })
}

function handleData(data) {
    const obj = {}
    const set = new Set()
    data.forEach(item => {
        const timeArr = item.createTime.split('-')
        const time = `${timeArr[0]}年${timeArr[1]}月`
        if (set.has(time)) {
            obj[time].push(item)
        } else {
            obj[time] = [item]
            set.add(time)
        }
    });
    const newArr = []
    for (const key in obj) {
        if (Object.hasOwnProperty.call(obj, key)) {
            newArr.push({ isShow: true, time: key, children: obj[key], key: +new Date() + Math.random() })
        }
    }
    return newArr
}

// 关闭
function cancel() {
    open.value = false
}

// 是否申请
function isApply(val) {
    return val.includes('申请') ? '申请内容' : '内容'
}

// 是否联系人
function isLiaison(val) {
    return ['回款登记', '做催记', '外呼', '发短信', '申请回款', '申请减免', '申请停催', '停催', '申请外访', '外访记录', '申请分期', '申请资料', '诉讼', '工单', '更新工单'].includes(val)
}
// 是否申请人
function isProposer(val) {
    return ['申请减免', '申请留案', '申请退案', '申请协催', '申请外访', '申请分期', '申请资料',].includes(val)
}

// 是否发起人
function isInitiator(val) {
    return ['工单',].includes(val)
}
// 是否操作人
function isOperator(val) {
    return ['退案', '申请停催', '停催', '诉讼', '回收案件', '留案', '申请留案'].includes(val)
}
// 是否登记人
function isRegistrant(val) {
    return ['回款登记', '申请回款', '做催记'].includes(val)
}
// 是否创建人
function isCreator(val) {
    return ['发短信',].includes(val)
}
// 是否跟进人
function isFollow(val) {
    return ['更新工单',].includes(val)
}
// 是否坐席所属人
function isBelong(val) {
    return ['外呼'].includes(val)
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped>
.el-timeline {
    max-width: none !important;
    width: 100% !important;
}

.timeline {
    padding: 0 10px;
    background-color: #f5f6fa;
    max-height: 68vh;
    overflow: auto;

    .timeline-content {
        padding: 16px 0;

        .title {
            display: flex;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            line-height: 32px;
            justify-content: space-between;
        }

        .content-item {
            padding: 16px;
            margin-top: 16px;
            color: #999999;
            background-color: #fff;

            i {
                color: #333333;
                font-style: normal;
            }
        }
    }
}

:deep(.el-timeline .el-timeline-item:last-child .el-timeline-item__tail) {
    display: inline-block;
}

:deep(.el-timeline .el-timeline-item .el-timeline-item__tail) {
    display: inline-block;
    color: #DEE1E3 !important;
}
</style>
<style>
.el-timeline-item__node {
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.el-timeline-item {
    padding-bottom: 0;
}

.arrow-btn.el-button--text {
    color: #333 !important;
}
</style>