<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="900px"
    :before-close="cancel"
    append-to-body
  >
    <el-table v-loading="loading" :data="contentList">
      <el-table-column
        label="名称"
        align="center"
        key="resourceName"
        prop="resourceName"
        v-if="type == 1 || type == 2"
        :show-overflow-tooltip="true"
      />
      <el-table-column v-if="type == 0" label="内容" align="center">
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.content }}</p>
            </template>
            <div>
              <span>{{
                scope.row.content?.length > 15
                  ? `${scope.row.content?.substring(0, 15)}...`
                  : scope.row.content
              }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column v-if="type == 1" label="内容" align="center">
        <template #default="scope">
          <el-icon
            :size="24"
            v-if="scope.row.content && scope.row.content != ''"
            @click="openCheckalter(scope.row)"
            ><PictureFilled
          /></el-icon>
          <span v-if="!scope.row.content">--</span>
        </template>
      </el-table-column>
         <el-table-column v-if="type == 2" label="内容" align="center" :width="300">
        <template #default="scope">
           <musicPlayer :ref="`audioRef${scope.$index}`" @stopCheck="stopCheck(scope.$index)" :audioSrc="scope.row.content"  />
          <span v-if="!scope.row.content">--</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" key="createTime" prop="createTime">
        <template #default="scope">
          <span>{{ scope.row.createTime || `--` }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script setup>
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { selectOutsideResourceById } from "@/api/caseDetail/detail";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);

const open = ref(false);
const loading = ref(false);
const title = ref("");
const type = ref(undefined);
const contentList = ref([]);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  outsideId: undefined,
  resourceType: undefined,
});
//打开弹窗
function opendialog(id, resourceType) {
  type.value = resourceType;
  title.value =
    resourceType == 0
      ? `外访记录信息`
      : resourceType == 1
      ? `外访照片信息`
      : `外访录音信息`;
  open.value = true;
  queryParams.value.outsideId = id;
  queryParams.value.resourceType = resourceType;
  loading.value = true;
  selectOutsideResourceById(queryParams.value)
    .then((res) => {
      contentList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

//取消
function cancel() {
  open.value = false;
}

//查看凭证
function openCheckalter(row) {
  window.open(row.content, "_blank");
}

//当有录音播放的时候停止其他录音
function stopCheck(count){
  contentList.value.forEach((item, index) => {
    if (index !== count) {
      proxy.$refs[`audioRef${index}`].stopAudio();
    }
  });
}

//资源
function resourceTypeFor(row) {
  return ["外访记录", "照片", "录音"][row.resourceType];
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
