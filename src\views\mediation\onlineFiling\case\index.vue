<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      :label-width="110"
      class="h-50"
      :class="{ 'h-auto': showSearch }"
    >
      <el-form-item label="资产包名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入资产包名称"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="案件ID" prop="caseId">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="被告" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入被告"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="立案号" prop="filingNumber">
        <el-input
          v-model="queryParams.filingNumber"
          placeholder="请输入立案号"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item prop="court" label="法院">
        <el-select
          v-model="queryParams.court"
          style="width: 240px"
          placeholder="请选择法院"
        >
          <el-option
            v-for="item in courtOptions"
            :key="item.code"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 240px">
          <el-input v-model="queryParams.amount1" clearable />
          <span>-</span>
          <el-input v-model="queryParams.amount2" clearable />
        </div>
      </el-form-item>
      <el-form-item prop="isFreeze" label="是否诉保">
        <el-select
          v-model="queryParams.isFreeze"
          style="width: 240px"
          placeholder="请选择是否诉保"
        >
          <el-option
            v-for="(item, index) in isNoEnum"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <div class="mb10 operation-list mt10" style="min-height: 26px">
      <el-button
        v-if="checkPermi([setPermiss('back')])"
        plain
        :disabled="single"
        type="primary"
        @click="opendialog('backStayRef', 0)"
        >退案</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('stay')])"
        plain
        :disabled="single"
        type="success"
        @click="opendialog('backStayRef', 1)"
        >留案</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('stop')])"
        plain
        :disabled="single"
        type="info"
        @click="opendialog('stopUrgeRef', 2)"
        >停案</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('sign')])"
        plain
        :disabled="single"
        type="danger"
        @click="openLabelCase"
        >标记案件</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('createWirt')]) && route.meta.query != 2"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('batchImportWritRef')"
        >批量生成文书</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('aduit')]) && route.meta.query == 2"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('statusRegRef')"
        >审核状态登记</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('fillCase')]) && route.meta.query != 5"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('bulkFilingRef')"
        >并案号</el-button
      >
      <el-button
        v-if="checkPermi([setPermiss('transfer')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('transferCaseRef')"
        >案件流转</el-button
      >
      <!-- <el-button
        v-if="checkPermi([setPermiss('keep')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('applyKeepRef')"
        >申请保全</el-button
      > -->
      <el-button
        v-if="checkPermi([setPermiss('register')]) && route.meta.query == 1"
        type="primary"
        @click="handleOpenDialog('importRegisterRef')"
        >批量登记</el-button
      >
      <!-- <el-button
        v-if="checkPermi([setPermiss('import')]) && route.meta.query == 1"
        type="primary"
        @click="handleOpenDialog('importLogisticsRef')"
        >批量导入物流单号</el-button
      > -->
      <el-button
        v-if="checkPermi([setPermiss('sendNote')])"
        :disabled="single"
        type="primary"
        @click="handleOpenDialog('sendMessageRef')"
        >发送短信</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </div>

    <selectedAll
      v-model:allQuery="allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      class="mb10"
    >
      <template #content>
        <div>
          <span class="ml10">剩余债权总额：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.totalMoney) }}
          </i>
          <span>剩余债权本金：</span>
          <i class="danger mr10">
            {{ numFilter(statistics.principal) }}
          </i>
          <span>案件数量：</span>
          <i class="danger">{{ statistics.caseNum }}</i>
        </div>
      </template>
    </selectedAll>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        :width="110"
        v-if="columns[0].visible"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label
                class="ml5"
                v-if="row.label && row.label != 7"
                :code="row.label"
              />
            </el-tooltip>
            <GreenCircle v-if="row.isFreeze == 1" :data="row" />
            <el-button
              :disable="!checkPermi([setPermiss('detail')])"
              type="text"
              @click="toDetails(row, $index)"
            >
              {{ row.caseId }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="资产包名称"
        align="center"
        key="packageName"
        prop="packageName"
        :width="100"
        v-if="columns[1].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="转让方"
        align="center"
        key="entrustingPartyName"
        prop="entrustingPartyName"
        :width="100"
        v-if="columns[2].visible"
        show-overflow-tooltip
      />
      <el-table-column
        label="立案状态"
        align="center"
        key="disposeStage"
        prop="disposeStage"
        :width="110"
        v-if="columns[3].visible"
      />
      <el-table-column
        label="被告"
        align="center"
        key="clientName"
        prop="clientName"
        :min-width="110"
        v-if="columns[4].visible"
      />
      <el-table-column
        label="手机号码"
        align="center"
        key="clientPhone"
        prop="clientPhone"
        :width="110"
        v-if="columns[5].visible"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.clientPhone }}</span>
            <callBarVue class="ml5" :caseId="row.caseId" :key="htrxCall" />
            <workPhoneVue :phoneNumber="row.clientPhone" :caseId="row.caseId" :borrower="row.clientName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="身份证号码"
        align="center"
        key="clientIdcard"
        prop="clientIdcard"
        :width="180"
        v-if="columns[6].visible"
      />
      <el-table-column
        label="户籍地"
        align="center"
        key="clientCensusRegister"
        prop="clientCensusRegister"
        :min-width="160"
        v-if="columns[7].visible"
      />
      <el-table-column
        label="法院"
        align="center"
        key="court"
        prop="court"
        :min-width="120"
        v-if="columns[8].visible"
      />
      <el-table-column
        label="立案号"
        align="center"
        key="filingNumber"
        prop="filingNumber"
        :width="180"
        v-if="columns[9].visible"
      />
      <el-table-column
        label="立案时间"
        align="center"
        key="filingTime"
        prop="filingTime"
        :width="110"
        v-if="columns[10].visible"
      />
      <el-table-column
        label="标的额"
        align="center"
        key="remainingDue"
        prop="remainingDue"
        :width="120"
        v-if="columns[11].visible"
      >
        <template #default="{ row }">
          <span>{{
            numFilter(row.remainingDue)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="承办员"
        align="center"
        key="contractor"
        prop="contractor"
        :width="100"
        v-if="columns[12].visible"
      />
      <el-table-column
        label="书记员"
        align="center"
        key="clerk"
        prop="clerk"
        :width="100"
        v-if="columns[13].visible"
      />
      <el-table-column
        label="是否诉保"
        align="center"
        key="isFreeze"
        prop="isFreeze"
        :width="100"
        v-if="columns[14].visible"
        show-overflow-tooltip
        :formatter="(row) => isNoEnum[row.isFreeze]"
      />
      <el-table-column
        label="跟进人员"
        align="center"
        key="follower"
        prop="follower"
        :width="110"
        v-if="columns[15].visible"
      />
      <el-table-column
        label="最近一次跟进时间"
        align="center"
        key="followUpTime"
        prop="followUpTime"
        :width="160"
        v-if="columns[16].visible"
        show-overflow-tooltip
      />
      <el-table-column label="操作" :width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="checkPermi([setPermiss('keep')])"
            type="text"
            @click="handleOpenDialog('applyKeepRef', row)"
            >申请保全</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 案件操作 -->
    <stopUrge ref="stopUrgeRef" @getList="getList" />
    <backStay ref="backStayRef" @getList="getList" />

    <!-- 标记案件 -->
    <leabelCaseVue @getList="getList" ref="leabelCaseRef" />

    <!-- 成功弹窗 -->
    <exportTip :getList="getList" ref="exportTipRef" :exportType="`案件导出`" />

    <batchImportWritVue :getList="getList" ref="batchImportWritRef" />

    <!-- 案件流转 -->
    <transferCase :getList="getList" ref="transferCaseRef" />
    <!-- 导入物流单号 -->
    <importLogistics :getList="getList" ref="importLogisticsRef" />
    <!-- 批量登记 -->
    <importRegister :getList="getList" ref="importRegisterRef" />
    <!-- 申请保全 -->
    <applyKeep :getList="getList" ref="applyKeepRef" />
    <!-- 批量立案 -->
    <bulkFiling :getList="getList" ref="bulkFilingRef" />
    <!-- 发送短信 -->
    <sendMessage :getList="getList" ref="sendMessageRef" />
    <!-- 审核状态登记 -->
    <statusReg :getList="getList" ref="statusRegRef" />
  </div>
</template>

<script setup name="OnlineFiling">
import { checkPermi } from "@/utils/permission";
import { ElMessage } from "element-plus";
import statusReg from "./dialog/statusReg";
import stopUrge from "@/views/mediation/dialog/stopUrge";
import backStay from "@/views/mediation/dialog/backStay";
import sendMessage from "@/views/collection/mycase/dialog/sendMessage";
import applyKeep from "@/views/mediation/dialog/applyKeep";
import transferCase from "@/views/mediation/dialog/transferCase";
import importLogistics from "@/views/mediation/dialog/importLogistics";
import importRegister from "@/views/mediation/dialog/importRegister";
import batchImportWritVue from "@/views/mediation/dialog/batchImportWrit";
import exportDialog from "@/views/mediation/dialog/exportDialog";
import leabelCaseVue from "@/views/mediation/dialog/leabelCase";
import bulkFiling from "@/views/mediation/dialog/bulkFiling";
import { isNoEnum, pageTypeEnum } from "@/utils/enum";
import { formatParams } from "@/utils/common";
import { getCourtOptions } from "@/api/common/common";
import { getOnlineFilingList, totalMoneyApi } from "@/api/mediation/onlineFiling";
import { selectFillingWithMoney } from "@/api/team/filingCaseInNet";

//全局数据
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const store = useStore();
const pageType = "onlineFiling";
// 下拉数据
const courtOptions = ref([]);

const stageObj = {
  1: "未提交立案",
  2: "审核中案件",
  3: "立案不通过",
  4: "接受调解",
  5: "已网上立案",
};
//表格配置数据
const loading = ref(false);
const total = ref(0);
const caseIds = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合
//表格数据
const dataList = ref([]);
const statistics = ref({
  caseNum: 0,
  totalMoney: 0,
  principal: 0,
});
//需要拆分的字段
const rangFields = [];
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);
//表单配置信息
const showSearch = ref(false);
const allQuery = ref(false);
// 列显隐信息
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "资产包名称", visible: true },
  { key: 2, label: "转让方", visible: true },
  { key: 3, label: "立案状态", visible: true },
  { key: 4, label: "被告", visible: true },
  { key: 5, label: "手机号码", visible: true },
  { key: 6, label: "身份证号码", visible: true },
  { key: 7, label: "户籍地", visible: true },
  { key: 8, label: "法院", visible: true },
  { key: 9, label: "立案号", visible: true },
  { key: 10, label: "立案时间", visible: true },
  { key: 11, label: "标的额", visible: true },
  { key: 12, label: "承办员", visible: true },
  { key: 13, label: "书记员", visible: true },
  { key: 14, label: "是否诉保", visible: true },
  { key: 15, label: "跟进人员", visible: true },
  { key: 16, label: "最近一次跟进时间", visible: true },
]);

//获取列表数据
function getList() {
  loading.value = true;
  selectedArr.value = [];
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFields);
  reqForm.sign = pageTypeEnum[pageType];
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.condition = allQuery.value;
  getOnlineFilingList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
provide("getList", Function, true);

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleOpenDialog(refName, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const newAllQuery = row ? false : allQuery.value;
  const query = { ...getReqParams(), allQuery: newAllQuery, caseIds };
  const data = {
    query,
    ...row,
    caseIds,
    allQuery: newAllQuery,
    pageType,
    isBatchSend: 1,
    caseId: row?.caseId,
  };
  data.condition = row ? false : allQuery.value;
  data.oneStatus = "网上立案";
  proxy.$refs[refName].openDialog(data);
}

function openLabelCase() {
  const reqForm = getReqParams();
  proxy.$refs["leabelCaseRef"].opendialog(reqForm);
}
//批量下载文书
function downloadWorkZip(downloadWork) {
  // 0 pdf格式  1 word格式
  const func = { 0: downloadPdfFormat, 1: downloadWordFormat };
  func[downloadWork](reqForm);
}

// 下载pdf格式文件
function downloadPdfFormat(reqForm) {
  proxy.downloadforjson(
    "/zip/downloadMediate",
    reqForm,
    `批量PDF文件_${new Date().getTime()}.zip`
  );
}
// 下载word格式文件
function downloadWordFormat(reqForm) {
  proxy.downloadforjson(
    "/zip/downloadWordDocumentsMediate",
    reqForm,
    `批量WORD文件_${new Date().getTime()}.zip`
  );
}
//跳转案件详情
function toDetails(row, index) {
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFields);
  let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  delete queryChange.pageNum;
  delete queryChange.pageSize;
  let searchInfo = {
    query: {
      disposeStage: stageObj[route.meta.query],
      manageQueryParam: queryChange, //查询参数
      pageNumber: pageNumber, //当前第几页(变动)
      pageSize: 1, //一页一条
      pageIndex: 0, //当前第一条
      caseIdCurrent: row.caseId, //当前案件id
    },
    type: "mycase",
    total: total.value, //查询到的案件总数
  };
  localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  const query = { type: "record", pageType, twoStage: stageObj[route.meta.query] };
  router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query });
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  getList();
}

//查询案件金额
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && caseIds.value.length == 0) {
      statistics.value = { caseNum: 0, totalMoney: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      selectFillingWithMoney(getReqParams())
        .then((res) => {
          statistics.value = {
            caseNum: res.data.size,
            totalMoney: res.data.money,
            principal: res.data.principal,
          };
        })
        .finally(() => reslove());
    });
  });
}

//选择列表
function handleSelectionChange(selection) {
  caseIds.value = selection.map((item) => item.caseId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//表格行能否选择
function checkSelectable() {
  return !allQuery.value;
}

//导出数据
function downloadCase() {
  const reqForm = getReqParams();
  proxy.downloadforjson(
    "/filing-case/selectWithMoney",
    reqForm,
    `网上立案_${+new Date()}.xlsx`
  );
}
getCourtOptionsFun();
function getCourtOptionsFun() {
  getCourtOptions().then((res) => {
    courtOptions.value = res.data;
  });
}

//打开回收案件等案件操作
function opendialog(refName, type, row) {
  const caseIds = row ? [row.caseId] : selectedArr.value.map((item) => item.caseId);
  const sign = pageTypeEnum[pageType];
  const newAllQuery = row ? false : allQuery.value;
  const totalSum = newAllQuery ? total.value : caseIds.length;
  const req = {
    ...getReqParams(),
    caseIds,
    condition: newAllQuery,
    allQuery: newAllQuery,
    ids: caseIds,
  };
  const openDialogData = { sign, req, total: totalSum, type };
  proxy.$refs[refName].openDialog(openDialogData);
}

// 获取参数
function getReqParams() {
  const reqParams = proxy.addFieldsRange(queryParams.value, rangFields);
  const reqForm = formatParams(reqParams, selectedArr, allQuery);
  reqForm.condition = allQuery.value;
  const stageObj = {
    1: "未提交立案",
    2: "审核中案件",
    3: "立案不通过",
    4: "接受调解",
    5: "已网上立案",
  };
  reqForm.disposeStage = stageObj[route.meta.query];
  reqForm.sign = pageTypeEnum[pageType];
  return reqForm;
}
// 设置权限符
function setPermiss(val) {
  const permissBtn = {
    1: "onlineFiling:failurFile:",
    2: "onlineFiling:caseUnderReview:",
    3: "onlineFiling:rejectCase:",
    4: "onlineFiling:acceptConciliation:",
    5: "onlineFiling:registeredOnline:",
  };
  return `${permissBtn[route.meta.query]}${val}`;
}
const htrxCall = computed(() => store.getters.htrxCall);
watch(
  () => selectedArr.value,
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

watch(
  () => route,
  () => {
    resetQuery();
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
body {
  color: #666 !important;
}

.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.block {
  display: block;
  margin: 10px auto;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: #9fccdb;
  display: block;
}

.selected-all-content {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 10px;
  justify-content: space-between;
}

.case-tips {
  font-size: 14px;
  background-color: #cce7fa;
  padding: 20px;
  color: #409eff;

  P {
    margin: 0;
    display: block;
  }

  p:nth-child(2) {
    display: inline;
  }
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

:deep(.el-cascader .el-cascader__search-input) {
  margin: 2px 0 2px 13px !important;
}

.operation-list {
  button {
    margin-bottom: 10px;
  }
}
</style>
