<template>
  <div class="app-container">
    <el-row class="mb10 mt10">
      <el-button
        plain
        v-hasPermi="['message:system:publish']"
        @click="postMessage({ type: 0, row: selectedArr })"
        >发布消息</el-button
      >
    </el-row>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column
        label="消息类型"
        align="center"
        key="messageType"
        prop="messageType"
        :formatter="messageTypeFor"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="标题"
        align="center"
        key="messageTitle"
        prop="messageTitle"
        width="300"
      >
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.messageTitle }}</p>
            </template>
            <span>{{
              scope.row.messageTitle?.length > 30
                ? `${scope.row.messageTitle?.substring(0, 15)}...`
                : scope.row.messageTitle
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="推送时间" align="center" key="pushTime" prop="pushTime" />
      <el-table-column
        label="推送人群"
        align="center"
        key="pushCrowdName"
        prop="pushCrowdName"
        :formatter="pushCrowdNameFor"
      />
      <el-table-column
        label="推送方式"
        align="center"
        key="pushMode"
        prop="pushMode"
        :formatter="pushModeFor"
      />
      <el-table-column label="状态" align="center" key="states" prop="states" />
      <el-table-column width="300" label="发布人/发布时间" align="center">
        <template #default="scope">
          <span>{{ `${scope.row.founder}/${scope.row.creationtime}` }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最后修改人/修改时间"
        align="center"
        key="caseState"
        prop="caseState"
      >
        <template #default="scope">
          <span>{{
            scope.row.modifier ? `${scope.row.modifier}/${scope.row.modifyTime}` : `--`
          }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="250" label="操作">
        <template #default="scope">
          <el-button
            v-if="scope.row?.deleteLogo != 1 && checkPermi(['message:system:detail'])"
            type="text"
            @click="detailsMessage(scope.row)"
          >
            详情
          </el-button>
          <el-button
            v-if="
              scope.row.states !== `已推送` &&
              scope.row.pushMode != 1 &&
              checkPermi(['message:system:edit'])
            "
            type="text"
            @click="editMessage(scope.row)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            v-hasPermi="['message:system:delete']"
            @click="remove(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script setup name="SystemMessage">
import { selectMessageCenter, deleteMessageCenter } from "@/api/message/system";
import { checkPermi } from "@/utils/permission";
//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表格配置数据
const loading = ref(false);
const total = ref(0);
//表格数据
const dataList = ref([]);
//表格查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);
//获取列表数据
function getList() {
  loading.value = true;
  selectMessageCenter(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//发布消息
function postMessage() {
  router.push("/message/system/postMessage");
}

//修改信息
function editMessage(row) {
  router.push(`/message/system/editMessage/${row.id}`);
}

//消息详情
function detailsMessage(row) {
  router.push(`/message/system/detailsMessage/${row.id}`);
}

//消息状态 0-默认 1-系统通知 2-公告/公示 3-知识库
function messageTypeFor(row) {
  return ["默认", "系统通知", "公告/公示", "知识库"][row.messageType];
}

//删除消息
function remove(row) {
  let req = {
    id: row.id,
  };
  proxy.$modal
    .confirm("此操作将删除消息内容, 是否继续?")
    .then(function () {
      loading.value = true;
      return deleteMessageCenter(req);
    })
    .then(() => {
      getList(queryParams.value);
      proxy.$modal.msgSuccess("操作成功！");
    })
    .catch((err) => {loading.value = false;})
}

//推送人群
function pushCrowdNameFor(row) {
  if (row.pushCrowdName == `*`) {
    return `所有人`;
  } else {
    return row.pushCrowdName;
  }
}
//推送方式
function pushModeFor(row) {
  return ["", "实时推送", "定时推送"][row.pushMode];
}

</script>
<style lang="scss" scoped></style>
