# 国慧调解

# 页面标题/ico/logo
VITE_APP_SIGN = zcax
VITE_APP_TITLE = 调解管理系统
VITE_APP_FAVICON = favicon_zcax.ico

# 开发环境配置
VITE_APP_ENV = 'development'
VITE_APP_BASE_API = '/dev-api/appeal'
VITE_APP_WS_BASE_URL = 'wss://ts-assets.amcmj.com/message-api/pushMessage'
# webrtc 配置
VITE_APP_RECORD_URL = 'https://call-uat.amcmj.com/prod-record/'
VITE_APP_WEBRTC_API_URL = 'https://call-uat.amcmj.com/prod-api/'
VITE_APP_WEBRTC_WSS_URI = 'wss://call-uat.amcmj.com:7443'
VITE_APP_WEBRTC_SIP_DOMAIN = 'call-uat.amcmj.com:7088'
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =gzip