<template>
  <el-dialog title="结案" v-model="open" width="750px" :before-close="cancel" append-to-body>
    <el-form :model="form" :rules="rules" inline ref="formRef" label-width="100px">
      <el-form-item prop="lastStatus" label="状态">
        <el-select v-model="form.lastStatus" placeholder="请选择状态" style="width: 220px">
          <el-option v-for="item in statusOptions" :key="item.code" :value="item.code" :label="item.info" />
        </el-select>
      </el-form-item>
      <el-form-item prop="undertakingLawyer" label="承办律师">
        <el-select v-model="form.undertakingLawyer" placeholder="请选择承办律师" style="width: 220px">
          <el-option v-for="item in emplyOption" :key="item.code" :value="item.info" :label="item.info" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">保存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getEmplyOption } from '@/api/common/common';
import { batchConcludeCaseJudge } from '@/api/mediation/judgmentResult';

const { proxy } = getCurrentInstance();
const props = defineProps({
  getList: { type: Function }
})
const data = reactive({
  form: {},
  rules: {
    lastStatus: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    undertakingLawyer: [{ required: true, message: '请选择承办律师', trigger: 'blur' }],
  },
});

const { form, rules } = toRefs(data);
const emplyOption = ref([])
const open = ref(false);

const statusOptions = ref([
  { code: '结案', info: '结案' },
  { code: '终本结案', info: '终本结案' },
]);
const loading = ref(false);

function openDialog(data) {
  open.value = true;
  form.value = { ...data.query, ...data }
};

function cancel() {
  open.value = false;
  form.value = {}
};

function submit() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      const reqForm = JSON.parse(JSON.stringify(form.value))
      loading.value = true
      batchConcludeCaseJudge(reqForm).then(res => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess('操作成功')
          props.getList && props.getList()
          cancel()
        }
      }).finally(() => loading.value = false)
    }
  })

}
getEmplyOptionFun()
function getEmplyOptionFun() {
  getEmplyOption().then(res => {
    emplyOption.value = res.data
  })
}
defineExpose({ openDialog });
</script>

<style lang="scss" scoped></style>
