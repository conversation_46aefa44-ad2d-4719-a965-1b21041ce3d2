<template>
    <div>
        <el-form :model="queryParams" :class="{'form-h50':!showSearch}" ref="queryRef" inline label-width="auto">
            <el-form-item prop="caseId" label="案件ID">
                <el-input v-model="queryParams.caseId" onkeyup="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')"
                    oninput="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')" placeholder="请输入案件ID" clearable
                    style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item prop="clientName" label="客户姓名">
                <el-input v-model="queryParams.clientName" placeholder="请输入客户姓名" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item prop="clientIdcard" label="身份证">
                <el-input v-model="queryParams.clientIdcard" placeholder="请输入身份证" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item prop="registrar" label="申请人">
                <el-input v-model="queryParams.registrar" placeholder="请输入申请人" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item prop="approveTime" label="处理时间">
                <el-date-picker v-model="queryParams.approveTime" style="width: 240px" value-format="YYYY-MM-DD"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item prop="createTime" label="申请时间">
                <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="YYYY-MM-DD"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item prop="returnCaseDate" label="退案日期">
                <el-date-picker v-model="queryParams.returnCaseDate" style="width: 240px" value-format="YYYY-MM-DD"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item prop="examineState" label="审核状态">
                <el-select v-model="queryParams.examineState" multiple collapse-tags collapse-tags-tooltip
                    placeholder="请输入或选择审核状态" clearable filterable :loading="selectLoading" style="width: 240px">
                    <el-option v-for="item in ExamineOptions" :key="item.info" :label="item.info" :value="item.info" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
            <el-button v-if="checkPermi(['assets:caseApprove:allocatPass']) && activeTab == '2'" type="success" plain
                :loading="loading" :disabled="selectedArr.length == 0" @click="handlePass()">通过</el-button>
            <el-button v-if="checkPermi(['assets:caseApprove:allocatNotPass']) && activeTab == '2'" type="warning" plain
                :loading="loading" :disabled="selectedArr.length == 0" @click="handleUnpass()">不通过</el-button>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <el-tabs class="mb8" v-model="activeTab" @tab-click="handleQuery">
            <el-tab-pane label="待处理" name="2" />
            <el-tab-pane label="已同意" name="0" />
            <el-tab-pane label="未同意" name="1" />
            <el-tab-pane label="全部" name="" />
        </el-tabs>
        <div class="table-area">
            <el-table v-loading="loading" ref="multipleTableRef" :data="dataList"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="44px" />
                <el-table-column v-if="columns[0].visible" align="center" prop="caseId" label="案件ID">
                    <template #default="{ row }">
                        <el-button type="text" @click="toDetails(row)">{{ row.caseId }}</el-button>
                    </template>
                </el-table-column>
                <el-table-column v-if="columns[1].visible" align="center" prop="caseState" label="案件状态" />
                <el-table-column v-if="columns[3].visible" align="center" prop="productName" label="产品名称" />
                <el-table-column v-if="columns[4].visible" align="center" prop="clientName" label="客户姓名" />
                <el-table-column v-if="columns[5].visible" align="center" prop="" label="身份证【户籍地】" width="180">
                    <template #default="{ row }">
                        <div> {{ row.clientIdcard }} </div>
                        <div> {{ `【${row.clientCensusRegister || '--'}】` }} </div>
                    </template>
                </el-table-column>
                <el-table-column v-if="columns[6].visible" align="center" prop="registrar" label="申请人" />
                <el-table-column v-if="columns[7].visible" align="center" prop="createTime" label="申请时间" />
                <el-table-column v-if="columns[9].visible" align="center" prop="returnCaseDate" label="退案日期" />
                <el-table-column v-if="columns[10].visible" align="center" prop="reason" label="申请原因" />
                <el-table-column v-if="columns[11].visible" align="center" prop="state" label="处理状态" />
                <el-table-column v-if="columns[12].visible" align="center" prop="approveTime" label="处理时间" />
                <el-table-column v-if="columns[13].visible" align="center" prop="examineState" label="申核状态">
                    <template #default="{ row, $index }">
                        <el-popover placement="bottom" :width="550" :ref="`popover-${$index}`" trigger="click">
                            <template #reference>
                                <el-button @click="showPopover(row)" type="text">
                                    {{ row.state }}
                                </el-button>
                            </template>
                            <el-table :data="gridData">
                                <el-table-column width="200" property="approveTime" label="处理时间" />
                                <el-table-column property="reviewer" label="处理人" show-overflow-tooltip />
                                <el-table-column property="approveStartInfo" label="处理状态" />
                                <el-table-column width="150" property="refuseReason" label="原因"
                                    v-if="row.state == '未通过'" />
                            </el-table>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template #default="{ row }">
                        <div>
                            <el-button :loading="loading" type="primary" @click="handlePass(row)" link
                                :disabled="loading || row.authority == 0"
                                v-if="row.approveStart == null && checkPermi(['assets:caseApprove:allocatPass']) && row.allocationUpshot !== 1">通过</el-button>
                            <el-button :loading="loading" type="primary" @click="handleUnpass(row)" link
                                :disabled="loading || row.authority == 0"
                                v-if="row.approveStart == null && checkPermi(['assets:caseApprove:allocatNotPass']) && row.allocationUpshot !== 1">不通过</el-button>
                            <el-button :loading="loading" :disabled="loading" @click="handleCheckVoucher(row)"
                                type="text" link>查看凭证</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script setup>
import { checkPermi } from "@/utils/permission";
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10
    }
})
const activeTab = ref('')
const dataList = ref([])
const loading = ref(false)
const showSearch = ref(false)
const columns = ref([
    { "key": 0, "label": "案件ID", "visible": true },
    { "key": 1, "label": "案件状态", "visible": true },
    { "key": 2, "label": "产品名称", "visible": true },
    { "key": 3, "label": "客户姓名", "visible": true },
    { "key": 4, "label": "身份证【户籍地】", "visible": true },
    { "key": 5, "label": "申请人", "visible": true },
    { "key": 6, "label": "申请时间", "visible": true },
    { "key": 7, "label": "退案日期", "visible": true },
    { "key": 8, "label": "申请原因", "visible": true },
    { "key": 9, "label": "处理状态", "visible": true },
    { "key": 10, "label": "处理时间", "visible": true },
    { "key": 11, "label": "申核状态", "visible": true }
])
const gridData = ref([])
const selectedArr = ref([])
const { queryParams } = toRefs(data)
function getList() {

}

// 通过
function handlePass(row) {

}
// 不通过
function handleUnpass(row) {

}
// 查看凭证
function handleCheckVoucher(row) {

}
// 跳转详情
function toDetails(row) {

}

//搜索
function handleQuery() {
    selectedArr.value = []
    queryParams.value.pageNum = 1;
    getList();
}

//重置
function resetQuery() {
    proxy.resetForm("queryRef");
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
    };
    handleQuery();
}
// 选择表格数据
function handleSelectionChange(selection) {

}
</script>