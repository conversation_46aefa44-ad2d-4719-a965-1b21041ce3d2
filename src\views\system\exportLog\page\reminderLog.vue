<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" inline label-width="82px">
      <el-form-item label="导出时间">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          style="width: 240px" start-placeholder="开始日期" end-placeholder="结束日期" :disabled-date="disabledDate" />
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" class="mt20">
      <el-table-column label="导出时间" align="center" key="createTime" prop="createTime" show-overflow-tooltip />
      <el-table-column label="文件名称" align="center" key="fileName" prop="fileName" />
      <el-table-column label="操作人" align="center" key="createBy" prop="createBy" :width="150" />
      <el-table-column label="处理状态" align="center" key="exportStart" prop="exportStart" :formatter="exportStartFor"
        show-overflow-tooltip />
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.exportStart == 1 && checkPermi([`system:reminder:downLoad`])" type="text"
            @click="downloadExport(row)">下载</el-button>
          <el-popover placement="top" v-else-if="row.exportStart == 2 && checkPermi([`system:reminder:fail`])"
            :width="550" trigger="click">
            <template #reference>
              <el-button type="text">失败原因</el-button>
            </template>
            <div>
              <span>失败原因 : {{ row.failMsg || `--` }}</span>
            </div>
          </el-popover>
          <span v-else>--</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import { getNoteList } from "@/api/system/exportLog";
//全局配置
const { proxy } = getCurrentInstance();
//查询参数
const statusList = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createTime: [],
    fileName: undefined,
    exportType: undefined,
    exportClass: undefined,
  },
});
const { queryParams } = toRefs(data);
const rangfiles = ["createTime"];
//表格参数
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const props = defineProps({
  activeTab: {
    type: String,
    default: ''
  }
})

//获取列表
function getList() {
  loading.value = true;
  queryParams.value.exportClass = props.activeTab;
  getNoteList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      loading.value = false;
      dataList.value = res.rows;
      total.value = res.total;
    })
}
provide("getList", Function, true);

//下载导出
function downloadExport(row) {
  proxy.$modal.loading("正在下载，请稍候...");
  fetch(row.fileUrl, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], {
          type: "application/vnd.ms-excel",
        })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", `${row.fileName}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      setTimeout(() => {
        proxy.$modal.closeLoading();
      }, 1000)
    })
    .catch((error) => {
      window.open(row.fileUrl);
    });
}

//查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

onMounted(() => {
  getList()
})

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    createTime: [],
    fileName: undefined,
    exportType: undefined,
    exportClass: undefined,
  };
  getList();
}

//发送状态
function exportStartFor(row) {
  return ["处理中", "成功", "失败"][row.exportStart];
}

// 禁用当前日期之后的日期
function disabledDate(time) {
  return time.getTime() >= Date.now();
}

</script>

<style></style>
