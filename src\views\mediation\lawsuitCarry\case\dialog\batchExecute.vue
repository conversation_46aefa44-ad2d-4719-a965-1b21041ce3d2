<template>
  <el-dialog title="批量执行" v-model="open" width="850px" :before-close="cancel" append-to-body>
    <el-form :model="form" ref="formRef" :rules="rules" inline label-width="auto">
      <el-form-item prop="courtId" label="执行法院">
        <el-select v-model="form.courtId" filterable clearable placeholder="请选择执行法院" style="width: 220px">
          <el-option v-for="item in courtOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="concludeCaseAmount" label="结案标的(金额)">
        <el-input v-model="form.concludeCaseAmount" style="width: 220px" placeholder="请输入结案标的(金额)" />
      </el-form-item>
      <el-form-item prop="executiveAmount" label="执行到位标的(金额)">
        <el-input v-model="form.executiveAmount" style="width: 220px" placeholder="请输入执行到位标的(金额)" />
      </el-form-item>
      <el-form-item prop="involvedWith" label="案件涉及">
        <el-input v-model="form.involvedWith" style="width: 220px" :maxlength="50" placeholder="请输入案件涉及" />
      </el-form-item>
      <el-form-item prop="involvedRegion" label="涉及地域">
        <el-cascader v-model="form.involvedRegion" style="width:220px" :props="{ value: 'label' }"
          :options="areaOptions" placeholder="请输入涉及地域" />
      </el-form-item>
      <el-form-item prop="isMissing" label="被执行人下落不明">
        <el-radio-group v-model="form.isMissing" style="width: 220px">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="closedReason" label="结案案由">
        <el-input v-model="form.closedReason" style="width: 220px" :maxlength="50" placeholder="请输入结案案由" />
      </el-form-item>
      <el-form-item prop="caseReason" label="案由">
        <el-input v-model="form.caseReason" style="width: 220px" :maxlength="200" placeholder="请输入案由" />
      </el-form-item>
      <el-form-item prop="isFile" label="有无结案文书">
        <el-radio-group v-model="form.isFile" style="width: 220px">
          <el-radio :label="0">有文书</el-radio>
          <el-radio :label="1">无文书</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="closedMode" label="结案方式">
        <el-select v-model="form.closedMode" placeholder="请选择结案方式" style="width: 220px">
          <el-option v-for="item in closeWayEnum" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item prop="fileUrl" label="结案通知书">
        <FileUpload v-model:fileList="fileList" uploadFileUrl="/upload" gateway="file" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer" style="text-align: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { municipalGovernment, getCourtOptions } from '@/api/common/common'
import { batchExecute } from '@/api/mediation/lawsuitCarry';
import FileUpload from '@/components/FileUpload';
import { closeWayEnum } from '@/utils/enum';
const { proxy } = getCurrentInstance();
const props = defineProps({
  getList: { type: Function }
})
const data = reactive({
  form: {},
  rules: {
    courtId: [{ required: true, message: '请选择执行法院', trigger: 'blur' }],
    concludeCaseAmount: [{ required: true, message: '请输入结案标的(金额)', trigger: 'blur' }],
    executiveAmount: [{ required: true, message: '请输入执行到位标的(金额)', trigger: 'blur' }],
    involvedWith: [{ required: true, message: '请输入案件涉及', trigger: 'blur' }],
    caseReason: [{ required: true, message: '请输入案由', trigger: 'blur' }],
  },
});
const { form, rules } = toRefs(data);
const query = ref(undefined)
const areaOptions = ref([])
const courtOption = ref([])
const fileList = ref([])
const open = ref(false);
const loading = ref(false);
function openDialog(data) {
  open.value = true;
  query.value = data.query
};

function cancel() {
  open.value = false;
  query.value = undefined
  fileList.value = []
  form.value = {}
};

function submit() {
  form.value.fileUrl = fileList.value.length > 0 ? fileList.value[0].response.data.url : undefined
  nextTick(() => {
    proxy.$refs['formRef'].validate((valid) => {
      if (valid) {
        const reqForm = { executeCase: JSON.parse(JSON.stringify(form.value)), ...query.value }
        reqForm.executeCase.involvedRegion = reqForm.executeCase.involvedRegion ? reqForm.executeCase.involvedRegion.join('') : ''
        reqForm.executeCase.type = reqForm.type
        const court = courtOption.value.find(item => item.code == form.value.courtId)
        reqForm.executeCase.court = court?.info
        reqForm.executeCase.executiveCourt = court?.info
        reqForm.executeCase.courtId = court?.code
        batchExecute(reqForm).then(res => {
          if (res.code == 200) {
            props.getList && props.getList()
            proxy.$modal.msgSuccess('操作成功')
            cancel()
          }
        })
      }
    })
  })
}

// 获取机构列表
getCourts()
function getCourts() {
  getCourtOptions().then((res) => {
    courtOption.value = res.data
  })
}
getMunicipalGovernment()
function getMunicipalGovernment() {
  municipalGovernment().then(res => {
    areaOptions.value = res.data
  })
}
defineExpose({ openDialog });
</script>

<style lang="scss" scoped></style>
