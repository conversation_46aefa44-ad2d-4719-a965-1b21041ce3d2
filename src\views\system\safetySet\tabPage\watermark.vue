<template>
    <div>
      <div class="title mb20">
        <span>开启后，在系统页面显示水印。</span>
        <el-switch
          class="myswitch ml20"
          v-model="state.settingStatus"
          active-color="#2ECC71"
          :active-value="1"
          :inactive-value="0"
          active-text="开"
          inactive-text="关"
          @change="change"
          v-if="checkPermi(['system:safetySet:watermark:switch'])"
        ></el-switch>
      </div>
      <div v-if="state.settingStatus">
        <div class="mb20" style="color: #888888">水印样式</div>
        <div style="width: 600px">
          <el-form :inline="true" :model="form" :rules="rules" ref="formRef">
            <el-form-item prop="watermarkOne">
              <el-select
                v-model="form.watermarkOne"
                :disabled="state.settingStatus === 0"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in selectList1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="watermarkTwo">
              <el-select
                v-model="form.watermarkTwo"
                :disabled="state.settingStatus === 0"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in selectList2"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="watermarkThree">
              <el-select
                v-model="form.watermarkThree"
                :disabled="state.settingStatus === 0"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in selectList3"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="watermarkFour">
              <el-select
                v-model="form.watermarkFour"
                :disabled="state.settingStatus === 0"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in selectList4"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="mb20" style="color: #888888; font-size: 14px">
            默认样式：{{ examples }}
          </div>
          <div>
            <el-button
              type="primary"
              :loading="loading"
              :disabled="state.settingStatus === 0"
              @click="save"
              v-if="checkPermi(['system:safetySet:watermark:save'])"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { changeSafeStatus, changeWatermark } from "@/api/safetySet/index";
  import { checkPermi } from "@/utils/permission";
  const { proxy } = getCurrentInstance();
  const state = inject("state");
  const watermark = inject("watermark");
  const getTeamSafe = inject("getTeamSafe", Function, true);
  
  const validatorFuc = (rule, value, callback) => {
    if (!value || value == "") {
      value = 0;
      callback();
    } else {
      let arr = [];
      let obj = JSON.parse(JSON.stringify(form.value));
      delete obj.createId;
      delete obj.id;
      delete obj[rule.field];
      let iserr = false;
      for (const key in obj) {
        if (obj[key] === value) {
          iserr = true;
          break;
        } else {
          iserr = false;
        }
      }
      if (iserr) {
        callback(new Error("请勿重复选择！"));
      } else {
        callback();
      }
    }
  };
  
  const loading = ref(false);
  const selectList1 = ref([
    { value: 0, ex: "无", label: "无" },
    { value: 1, ex: "账号名称", label: "账号名称" },
    { value: 2, ex: "账号姓名", label: "账号姓名" },
    { value: 3, ex: "日期", label: "日期" },
    { value: 4, ex: "案件ID", label: "案件ID" },
  ]);
  const exList = ["", "CY095@cy", "曾黎", "2021/5/11", "64646646"];
  
  const rules = ref({
    watermarkOne: [{ validator: validatorFuc, trigger: "change" }],
    watermarkTwo: [{ validator: validatorFuc, trigger: "change" }],
    watermarkThree: [{ validator: validatorFuc, trigger: "change" }],
    watermarkFour: [{ validator: validatorFuc, trigger: "change" }],
  });
  const form = computed(() => {
    let obj = watermark.value;
    selectList1.value.map((item) => {
      for (const key in obj) {
        if (item.label == obj[key]) {
          obj[key] = item.value;
          break;
        }
      }
    });
    return obj;
  });
  
  //开关修改
  function change() {
    changeSafeStatus(state.value)
      .then(() => {})
      .catch(() => {
        getTeamSafe();
      });
  }
  
  const selectList2 = computed(() => {
    var res = selectList1.value.filter((item) => {
      return item.value != form.value.watermarkOne || item.value == 0;
    });
    return res;
  });
  const selectList3 = computed(() => {
    var res = selectList1.value.filter((item) => {
      return (
        (item.value != form.value.watermarkOne && item.value != form.value.watermarkTwo) ||
        item.value == 0
      );
    });
    return res;
  });
  
  const selectList4 = computed(() => {
    var res = selectList1.value.filter((item) => {
      return (
        (item.value != form.value.watermarkOne &&
          item.value != form.value.watermarkTwo &&
          item.value != form.value.watermarkThree) ||
        item.value == 0
      );
    });
    return res;
  });
  
  const examples = computed(() => {
    let res1 = exList[form.value.watermarkOne];
    let res2 = exList[form.value.watermarkTwo];
    let res3 = exList[form.value.watermarkThree];
    let res4 = exList[form.value.watermarkFour];
    return `${res1 || ""}${!res2 || res2 == "" ? "" : "-"}${res2 || ""}${
      !res3 || res3 == "" ? "" : "-"
    }${res3 || ""}${!res4 || res4 == "" ? "" : "-"}${res4 || ""}`;
  });
  
  //保存
  function save() {
    proxy.$refs["formRef"].validate((valid) => {
      if (valid) {
        let obj = {
          id: form.value.id,
          createId: form.value.createId,
          watermarkOne: selectList1.value[form.value.watermarkOne].label,
          watermarkTwo: selectList1.value[form.value.watermarkTwo].label,
          watermarkThree: selectList1.value[form.value.watermarkThree].label,
          watermarkFour: selectList1.value[form.value.watermarkFour].label,
        };
        loading.value = true;
        changeWatermark(obj)
          .then((res) => {
            loading.value = false;
            let { code, msg } = res;
            if (code !== 200) {
              proxy.$modal.msgError(msg);
              getTeamSafe();
            } else {
              proxy.$modal.msgSuccess(msg);
            }
          })
          .catch(() => {
            loading.value = false;
            getTeamSafe();
          });
      }
    });
  }
  </script>
  
  <style scoped>
  .title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  </style>
  