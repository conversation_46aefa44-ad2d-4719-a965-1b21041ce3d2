<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="登记时间" prop="createTime" key="createTime" align="center" />
    <el-table-column label="登记人" prop="registrar" key="registrar" align="center" />
    <el-table-column label="交易时间" prop="repaymentDate" key="repaymentDate" align="center" />
    <el-table-column label="还款金额" prop="repaymentMoney" key="repaymentMoney" align="center">
      <template #default="{ row }">
        <span>{{ numFilter(row.repaymentMoney) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="还款渠道" prop="repaymentMode" key="repaymentMode" align="center" />
    <el-table-column label="交易流水号" prop="orderNo" key="orderNo" align="center" />
    <el-table-column label="还款类型" prop="repaymentType" key="repaymentType" align="center" />
    <el-table-column label="审核状态" prop="examineState" key="examineState" align="center" />
    <el-table-column label="还款凭证" prop="repaymentProof" key="repaymentProof" align="center" width="180px">
      <template #default="scope">
        <el-icon :size="24" v-if="scope.row.repaymentProof && scope.row.repaymentProof != ''"
          @click="openCheckalter(scope.row)">
          <PictureFilled />
        </el-icon>
        <span v-if="!scope.row.repaymentProof">--</span>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import { selectRepaymentRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  caseId: { type: [String, Number], default: undefined }
});
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: props.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm }
  loading.value = true;
  selectRepaymentRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();

//查看凭证
function openCheckalter(row) {
  window.open(row.repaymentProof, "_blank");
}

</script>

<style scoped></style>
