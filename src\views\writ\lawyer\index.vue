<template>
  <div class="app-container">
    <div class="app-content">
      <div class="app-query">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right" class="form-content">
          <el-form-item>
            <el-cascader v-model="classifyIdsList" style="width: 240px" placeholder="请选择模板类型" :options="classifyList" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" style="width: 240px" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.createBy" placeholder="请输入发件人" style="width: 240px" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.batchNum" placeholder="请输入文书批次号" style="width: 240px" />
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator=" "
              start-placeholder="请选择创建时间" end-placeholder="" value-format="YYYY-MM-DD" style="width: 240px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row class="mt10 mb20 height32 operation-area" :gutter="10">
          <el-button type="primary" v-hasPermi="['writ:lawyer:add']" plain @click="addLawyer()">新建文书</el-button>
          <el-button type="success" v-hasPermi="['writ:lawyer:export']" plain :disabled="ids.length == 0"
            @click="exportLawyer()">导出文书</el-button>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="批次号" align="center" prop="batchNum" v-if="columns[0].visible" />
        <el-table-column label="模板类型" align="center" prop="classifyLabel" v-if="columns[1].visible" />
        <el-table-column label="模板名称" align="center" prop="templateName" v-if="columns[2].visible" />
        <el-table-column label="文书量" align="center" prop="quantity" v-if="columns[5].visible">
          <template #default="{ row }">
            <el-button type="text" link @click="getLayerDetails(row)" :disabled="row.importStatus == 0">
              {{ row.quantity }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="发件人" align="center" prop="createBy" v-if="columns[6].visible" />
        <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[7].visible" />
      </el-table>
      <div class="pagination-area">
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <LogisticsDialog ref="logisticsDialogRef" :getList="getList" />
  </div>
</template>

<script setup name="LawyerIndex">
import { getMessageList, getProceOptions, exportLawyerZip } from "@/api/writ/lawyer";
import { getClassifyList } from "@/api/writ/template";
import { ElLoading } from "element-plus";
import LogisticsDialog from "./dialog/logisticsDialog.vue";

//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
const ids = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    classifyIds: undefined,
    templateName: undefined,
    createBy: undefined,
    proce: undefined,
    batchNum: undefined,
    createTime: [],
  },
});
const { queryParams } = toRefs(data);
// 列显隐信息
const columns = ref([
  { key: 0, label: `批次号`, visible: true },
  { key: 1, label: `模板类型`, visible: true },
  { key: 2, label: `模板名称`, visible: true },
  { key: 3, label: `导入状态`, visible: true },
  { key: 4, label: `审核状态`, visible: true },
  { key: 5, label: `文书量`, visible: true },
  { key: 6, label: `发件人`, visible: true },
  { key: 7, label: `创建时间`, visible: true },
]);
//数据参数
const dataList = ref([]);
const total = ref(0);
const classifyList = ref([]);
const classifyIdsList = ref([]);
const statusList = ref([]);
//多选字段
const checkMoreList = ref(["classifyIds"]);
const checkMoreName = ref([classifyIdsList]);
//拆分字段
const rangfiles = ["createTime"];
//获取列表
function getList() {
  loading.value = true;
  checkMoreList.value.forEach((item, index) => {
    queryParams.value[item] =
      checkMoreName.value[index].value.length === 0
        ? undefined
        : checkMoreName.value[index].value.toString().replace(",", "/");
  });
  getMessageList(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取分类数据
function getProce() {
  getProceOptions().then((res) => {
    statusList.value = res.data;
  });
}
getProce();

//获取分类数据
function getClassify() {
  getClassifyList().then((res) => {
    classifyList.value = res.data;
  });
}
getClassify();

/** 多选框选中数据 */
function addLawyer() {
  router.push("/writ/addLawyer");
}

//导出律涵
function exportLawyer() {
  let downloadLoadingInstance;
  exportLawyerZip({ ids: ids.value })
    .then((res) => {
      res.data?.forEach(async (file) => {
        downloadLoadingInstance = ElLoading.service({
          text: "正在下载数据，请稍候",
          background: "rgba(0, 0, 0, 0.7)",
        });
        const { fileUrl, fileName } = file;
        if (fileUrl && fileName) {
          await downloadFile({ url: fileUrl, name: fileName });
          downloadLoadingInstance.close();
        }
      });
      proxy.$modal.msgSuccess("操作成功！");
    })
    .catch((err) => {
      downloadLoadingInstance?.close();
    });
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    classifyIds: undefined,
    templateName: undefined,
    createBy: undefined,
    proce: undefined,
    batchNum: undefined,
    createTime: [],
  };
  classifyIdsList.value = [];
  getList();
}
//获取文书量
function getLayerDetails(row) {
  router.push(`/writ/lawyerDetails/${row.id}`);
}

function downloadFile(data) {
  fetch(data.url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const typeObj = {
        'pdf': "application/pdf",
        'word': "application/msword",
        'xlsx': "application/vnd.ms-excel",
        'zip': "application/zip",
      }
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], { type: typeObj[data.type] })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      window.open(url);
    });
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}

.height32 {
  height: 32px;
}

.form-content {
  overflow: hidden;
}

.h-50 {
  height: 50px;
}

.h-auto {
  height: auto !important;
}
</style>
