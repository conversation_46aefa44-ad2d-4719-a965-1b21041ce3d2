<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" class="left-side">
        <el-scrollbar style="padding-right: 10px">
          <div class="left-side-top">
            <el-radio-group
              v-model="leftSiderRadio"
              class="custome-radio-group"
            >
              <el-radio-button label="我的案件客户" />
              <el-radio-button label="客户详情" />
            </el-radio-group>
          </div>
          <myCaseClient v-if="leftSiderRadio === '我的案件客户'" />
          <clientDetail v-if="leftSiderRadio === '客户详情'" @authorizationStatus="getAuthorizationStatus" />
        </el-scrollbar>
      </el-col>
      <el-col :span="18" class="right-side">
        <div class="ml10" style="height: 40px; line-height: 40px">
          <span style="font-size: 15px">案件详情</span>
          （案件ID：{{ route.params.caseId }}）
          <span>调解员：{{ caseInfoData?.infoBase?.mediatorName }}</span>
          <div style="display: inline-block" class="ml10">
            <el-button type="primary" plain @click="openhelpUrge"
              >申请协助调解</el-button
            >
            <el-button
              type="success"
              v-if="authorizationStatus == 1"
              plain
              @click="openOutVisit"
              >申请外访</el-button
            >
            <el-button
              type="info"
              plain
              @click="openLawSuit"
              >申请诉讼</el-button
            >
          </div>
        </div>
        <!-- 案件信息 -->
        <caseInfo
          :infoBase="caseInfoData.infoBase"
          :infoLoan="caseInfoData.infoLoan"
          :imgFor="imgFor"
          :jointDebtData="caseInfoData.caseManage"
          :infoExtra="caseInfoData.infoExtra"
          :infoPlan="caseInfoData.infoPlan"
          @getcaseDetailInfo="getcaseDetailInfo"
        />
        <el-scrollbar style="flex: 1">
          <!-- 客户基本信息 -->
          <caseInfoForm
            :infoBase="caseInfoData.infoBase"
            :infoLoan="caseInfoData.infoLoan"
          />
          <!-- 贷款信息/案件信息下 -->
          <loanAndCaseInfo />
          <!-- 跟进操作 -->
          <followIng style="margin-bottom: 10px" />
          <!-- 记录 -->
          <record />
        </el-scrollbar>
      </el-col>
    </el-row>

    <!-- 申请协催 -->
    <helpUrgeVue ref="helpUrgeRef" />

    <!-- 申请外访 -->
    <outVisitVue ref="outVisitRef" />

    <!-- 申请诉讼 -->
    <lawsuitVue ref="lawsuitRef" />
  </div>
</template>

<script setup name="AllCaseDetail">
import myCaseClient from "./leftSideTop/myCaseClient.vue";
import clientDetail from "./leftSideTop/clientDetail.vue";
import helpUrgeVue from "./dialog/helpUrge";
import outVisitVue from "./dialog/outVisit";
import lawsuitVue from "./dialog/lawsuit.vue";
import caseInfo from "./caseInfo.vue";
import caseInfoForm from "./caseInfoForm.vue";
import loanAndCaseInfo from "./loanAndCaseInfo.vue";
import followIng from "./followIng.vue";
import record from "./record.vue";

import { getCaseInfo } from "@/api/caseDetail/detail";
import { getCallNetworkBySipNumber } from "@/api/mediation/allCaseDetail";
import { checkUserSip } from "@/api/system/user";

import discontinuation from "@/assets/images/discontinuation.png";
import settle from "@/assets/images/settle.png";

const { proxy } = getCurrentInstance();

const route = useRoute();
const store = useStore();
const caseId = route.params.caseId;
// 将caseId传给所有子组件
provide("caseId", caseId);
const leftSiderRadio = ref("客户详情");

const caseInfoData = ref({}); //案件所有信息

const authorizationStatus = ref(0);

//获取案件信息
function getcaseDetailInfo() {
  getCaseInfo(route.params.caseId).then((res) => {
    caseInfoData.value = res.data;
    store.commit("allCaseDetail/SET_CASE_INFO_DATA", res.data); // 存储案件信息
  });
}
getcaseDetailInfo();
provide("getcaseDetailInfo", Function, true);

function getAuthorizationStatus(status) {
  authorizationStatus.value = status;
}

//打开申请协催
function openhelpUrge() {
  proxy.$refs["helpUrgeRef"].opendialog();
}

//打开申请外访
function openOutVisit() {
  proxy.$refs["outVisitRef"].opendialog();
}

// 打开申请诉讼
function openLawSuit() {
  proxy.$refs["lawsuitRef"].opendialog();
}

// 改变图片
function imgFor() {
  const imgObj = { 2: discontinuation, 6: settle };
  if (caseInfoData.value?.infoLoan) {
    const { caseState, settlementStatus } = caseInfoData.value?.infoLoan;
    const type = settlementStatus == 1 ? 6 : caseState;
    return imgObj[type];
  }
}

// 获取sip账号
checkUserSip().then((res) => {
  if (res.data.isSip) {
    // 获取线路
    getCallNetworkBySipNumber({ sipNumber: res.data.account }).then((r) => {
      store.commit("allCaseDetail/SET_NETWORK_NAME_DATAS", r.data); // 获取线路
    });
  }
});

onBeforeUnmount(() => {
  store.dispatch("allCaseDetail/clearData", {}); // 清空案件信息
});
</script>

<style lang="scss" scoped>
.left-side,
.right-side {
  height: calc(100vh - 124px);
  display: flex;
  flex-direction: column;
}
.left-side-top {
  position: sticky;
  top: 0;
  z-index: 1000;
}
.custome-radio-group {
  width: 100%;
}
::v-deep(.custome-radio-group .el-radio-button) {
  width: 50%;
}
::v-deep(
    .custome-radio-group .el-radio-button__inner,
    .custome-radio-group.el-radio-group
  ) {
  width: 100%;
}
</style>
