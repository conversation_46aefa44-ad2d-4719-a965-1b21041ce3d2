<template>
  <div class="gouTong">
    <RichEditor
      v-model:modelValue="richEditorValue"
      ref="richEditorRef"
      :toolbarKeys="toolbarKeys"
      :height="300"
    />
    <div class="mt10" style="text-align: right">
      <el-button :loading="subloading" @click="onSubmit">保存</el-button>
    </div>

    <div class="record-list">
      <div class="record-item" v-for="item in resDataList" :key="item.id">
        <div class="mb10">沟通时间：{{ item.updateTime || item.createTime }}&nbsp;&nbsp;调解员：{{ item.mediatorName }}</div>
        <div class="tag mb5">
          <el-tag v-if="item.lineType">{{ item.lineType }}</el-tag>
          <el-tag v-if="item.contactLabel" class="ml10">{{ item.contactLabel }}</el-tag>
          <el-tag v-if="item.wechatLabel" class="ml10">{{ item.wechatLabel }}</el-tag>
          <el-tag v-if="item.intentionLabel" class="ml10">{{ item.intentionLabel }}({{ intentionLabelTextMap[item.intentionLabel] }})</el-tag>
          <el-tag v-if="item.contactInfoLabel" class="ml10">{{ item.contactInfoLabel }}</el-tag>
        </div>
        <div class="content">
          <p class="remark mb10" v-html="item.remarks"></p>
          <div>
            <p v-if="item.customerAttribute">客户属性：<span>{{ item.customerAttribute }}</span></p>
            <p v-if="item.caseAttribute">案件属性：<span>{{ caseAttributeChange(item.caseAttribute) }}</span></p>
            <p v-if="item.promiseRepaymentTime">预计还款时间：<span>{{ item.promiseRepaymentTime }}</span></p>
            <p v-if="item.promiseRepaymentMoney">还款金额：<span>{{ numFilter(item.promiseRepaymentMoney) }}&nbsp;元</span></p>
          </div>
        </div>
      </div>
      <div v-show="dataList.length === 0" style="text-align: center;">暂无沟通记录</div>
    </div>
    <pagination
      v-show="total > 0 && resDataList.length > 1"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getDataList"
      layout="prev, pager, next"
    />
    <div style="text-align: center">
      <el-button type="text" @click="isExpend = !isExpend"
        >{{ isExpend ? '收缩列表' : '展开查看历次沟通情况'}}</el-button
      >
    </div>
  </div>
</template>

<script setup>
import RichEditor from "@/components/RichEditor";
import { selectNegotiateRecordList } from "@/api/mediation/allCaseDetail";
import { numFilter } from "@/utils/common";
const { proxy } = getCurrentInstance();
const store = useStore();

const intentionLabelTextMap = {
  X: '已结清客户',
  S: '已分期客户',
  A: '高意向客户',
  B: '中意向客户',
  C: '低意向客户',
  D: '放弃客户',
}

const caseId = inject("caseId");
const richEditorValue = ref("");
const isUpdatingFromStore = ref(false);
const toolbarKeys = [
  // 编辑器工具栏
  "undo",
  "redo",
  "brush",
  "eraser",
  "|",
  "heading",
  "font-family",
  "font-size",
  "|",
  "bold",
  "italic",
  "underline",
  "strike",
  "link",
  "code",
  "subscript",
  "superscript",
  "hr",
  "emoji",
  "|",
  "highlight",
  "font-color",
  "|",
  "align",
  "line-height",
  "|",
  "bullet-list",
  "ordered-list",
  "indent-decrease",
  "indent-increase",
  "break",
  "|",
  "quote",
  "code-block",
  "table",
  "|",
  "printer",
  "fullscreen",
];

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  caseId: caseId,
});
const subloading = computed(() => store.state.allCaseDetail.loading);
watch(() => subloading.value, (newVal) => {
  if (!newVal) {
    getDataList();
  }
})

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const isExpend = ref(false)
const resDataList = computed(() => {
  if (isExpend.value) {
    return dataList.value
  } else {
    return dataList.value.length > 0 ? [dataList.value[0]] : []
  }
})
function getDataList() {
  loading.value = true;
  selectNegotiateRecordList(queryParams.value)
    .then((res) => {
      handleContactInfoLabel(res.rows).then(list => {
        dataList.value = list;
      })
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getDataList();

// 处理联系情况(包含了线路和其他类型的数据，需要区分开，mdsb)
function handleContactInfoLabel(list) {
  const lineTypes = ['普通线路联系', '律所联系路线', '调解联系路线', '白名单联系路线'];
  return new Promise((resolve) => {
    let resLine = [];
    list = list.map(item => {
      if (item.contactInfoLabel) {
        let labels = item.contactInfoLabel.split(",");
        // 获取labels中所有存在于lineTypes中的元素，并将其放入resLine数组中，并将其从labels中移除
        const lineType = lineTypes.filter(type => labels.includes(type));
        if (lineType.length > 0) {
          lineType.forEach(type => {
            labels.splice(labels.indexOf(type), 1);
          });
          resLine.push(lineType.join(","));
        }
        item.contactInfoLabel = labels.join(",");
        item.lineType = resLine.join(",");
      }
      return item;
    })
    resolve(list);
  })
}

function onSubmit() {
  if (
    richEditorValue.value &&
    !/^<p>(&nbsp;)*<\/p>$/.test(richEditorValue.value)
  ) {
    const zhongwen = ["客户属性", "预期还款金额", "预期还款日期"];
    const arr = richEditorValue.value.split("<p>");
    let remarks = [];

    arr.forEach((item, index) => {
      // 判断item中是否有'客户属性', '预期还款金额', '预期还款日期'字眼
      if (!zhongwen.some((word) => item.includes(word)) && item !== "") {
        // 去除</p>标签
        remarks.push(item.replace("</p>", ""));
      }
    });
    store.dispatch("allCaseDetail/submitForm", { caseId, remarks }).then((res) => {
      proxy.$refs.richEditorRef.clear();
    });
  } else {
    proxy.$modal.msgWarning("请填写沟通记录！")
  }
}

function caseAttributeChange(caseAttribute) {
  if (!caseAttribute) {
    return '--'
  }
  const Map = {
    1: '新案',
    2: '留案',
    3: '轮换'
  }
  if (caseAttribute.length === 1) {
    return Map[caseAttribute]
  }
  const arr = caseAttribute.split(",");
  let result = "";
  arr.forEach((item) => {
    result += Map[item] + "、";
  });
  return result.slice(0, -1);
}

// store中richEditValue的变化
watch(
  () => store.state.allCaseDetail.richEditValue,
  (newVal) => {
    if (!isUpdatingFromStore.value && newVal !== richEditorValue.value) {
      isUpdatingFromStore.value = true;
      proxy.$refs.richEditorRef.clear();
      proxy.$refs.richEditorRef.insertText(newVal);
      richEditorValue.value = newVal;
      nextTick(() => {
        isUpdatingFromStore.value = false;
      })
    }
  }
);
watch(() => richEditorValue.value, (newVal) => {
  if (!isUpdatingFromStore.value) {
    store.dispatch("allCaseDetail/setRichEditValue", {type: 'rich', value: newVal});
  }
  
})
</script>

<style lang="scss" scoped>
.record-list {
  border: 1px solid #e8e8e8;
  margin-top: 10px;
  padding: 10px;
  .record-item {
    background-color: #e8e8e8;
    padding: 15px;
    border-radius: 4px;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
    .content {
      background-color: #ffffff;
      padding: 10px 20px;
      border-radius: 4px;
      span {
        color: var(--el-color-primary)
      }
    }
  }
}

::v-deep(.pagination-container) {
  position: relative;
  margin-top: 0;
}



</style>
