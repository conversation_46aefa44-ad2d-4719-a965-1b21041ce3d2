import request from '@/utils/request'

// 网上立案 - 列表查询
export function getOnlineFilingList(query) {
    return request({
        url: '/filing-case/selectList',
        method: 'get',
        params: query
    })
}

// 网上立案 - 诉讼流程-案件流转
export function transferCauseOnlineFiling(data) {
    return request({
        url: '/filing-case',
        method: 'post',
        data
    })
}

// 网上立案 - 批量立案 生成立案信息记录 唯一的
export function filingCaseOnlineFiling(data) {
    return request({
        url: '/filing-case/batchFilingCase',
        method: 'post',
        data
    })
}

// 网上立案 - 批量立案登记-导入excel
export function registerCaseOnlineFiling(data) {
    return request({
        url: '/filing-case/batchRegister',
        method: 'post',
        data
    })
}

// 网上立案 - 审核状态登记 更改阶段 批量更改
export function checkCase(data) {
    return request({
        url: '/filing-case/updateWithCheck',
        method: 'post',
        data
    })
}

// 网上立案 - 统计 剩余应还债权金额 计算
export function totalMoneyApi(data) {
    return request({
        url: '/filing-case/selectWithMoney',
        method: 'post',
        data
    })
}
// 网上立案 - 网上立案批量发送生成短信模板
export function previewTemplateOnlineFilling(data) {
    return request({
        url: '/filing-case/previewFillingTemplate',
        method: 'post',
        data
    })
}
// 网上立案 - 网上立案批量发送短信
export function sendMessagesOnlineFilling(data) {
    return request({
        url: '/filing-case/sendFillingMessages',
        method: 'post',
        data
    })
}
// 网上立案 - 案件流转 获取阶段
export function stateOnlineFilling(params) {
    return request({
        url: '/filing-case/selectWithStage',
        method: 'get',
        params
    })
}
// 网上立案 - 案件流转 
export function transferCaseApi(data) {
    return request({
        url: '/filing-case/batchTransfer',
        method: 'post',
        data
    })
}

// 网上立案 - 案件流转 获取阶段
export function selectWithLawsuitStage(params) {
    return request({
        url: '/filing-case/selectWithLawsuitStage',
        method: 'get',
        params
    })
}
