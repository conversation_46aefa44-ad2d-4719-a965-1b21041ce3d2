<template>
  <div>
    <div class="mb20">
      <el-button type="primary" plain icon="Plus" @click="handleUpdateRepayInfo()"
        v-hasPermi="['system:repayset:add']">添加还款渠道</el-button>
    </div>

    <el-table :data="dataList" v-loading="loading">
      <el-table-column label="还款渠道" prop="repaymentMethod" align="center" />
      <el-table-column label="登记回款必填字段" prop="registerPayment" align="center" show-overflow-tooltip />
      <el-table-column label="自动对账字段" prop="reconciliation" align="center" show-overflow-tooltip />
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="备注" prop="remark" align="center">
        <template #default="{ row }">
          <Tooltip :content="row.remark" :length="10" width="500" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="state" align="center">
        <template #default="{ row }">
          <el-switch v-if="checkPermi(['system:repayset:status'])" class="myswitch" v-model="row.state"
            active-color="#2ECC71" :active-value="0" :inactive-value="1" active-text="开" inactive-text="关"
            @change="change(row)"></el-switch>
          <span v-else>{{ row.state == 0 ? '开启' : '关闭' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" v-hasPermi="['system:repayset:edit']" link
            @click="handleUpdateRepayInfo(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="pageNum" v-model:limit="pageSize"
      @pagination="getList" />
    <repayInfo ref="repayInfoRef" :getList="getList" />
  </div>
</template>

<script setup name="PayType">
import repayInfo from '../dialog/repayInfo';
import { repaysetList, editState } from "@/api/system/repaySet";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();

const loading = ref(false);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const dataList = ref([]);
//获取列表
function getList() {
  loading.value = true;
  let req = {
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }
  repaysetList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//新建、编辑操作
function handleUpdateRepayInfo(row) {
  const title = row ? '编辑还款渠道' : '添加还款渠道'
  proxy.$refs['repayInfoRef'].openDialog({ row, title })
}
//状态
function change(row) {
  let req = { id: row.id, state: row.state };
  editState(req).catch(() => {
    getList();
  });
}

</script>

<style scoped></style>
