<template>
    <el-dialog
      :title="exType == 0?'添加账户':'编辑账户'"
      v-model="open"
      width="600px"
      :before-close="cancel"
      append-to-body
    >
      <el-form
        :model="form"
        class="mt10 "
        label-width="120px"
        :rules="rules"
        ref="formRef"
      >
        <el-form-item class="mar0" label="开户行名称" prop="bankName">
          <el-input v-model="form.bankName" placeholder="请输入开户行名称" />
        </el-form-item>
        <el-form-item class="mar0" label="账户名称" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入账户名称" />
        </el-form-item>
        <el-form-item class="mar0" label="开户账户" prop="accountNumber">
          <el-input v-model="form.accountNumber" placeholder="请输入开户账户" />
        </el-form-item>
        <el-form-item class="mar0" label="账户状态" prop="accountStatus">
          <el-switch
            class="myswitch"
            v-model="form.accountStatus"
            active-color="#2ECC71"
            :active-value="0"
            :inactive-value="1"
            active-text="开"
            inactive-text="关"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  <script setup>
  import { addAccount, updateAccount } from "@/api/system/account";
  //全局参数
  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const emit = defineEmits(["getList"]);
  const loading = ref(false);
  //开启
  const open = ref(false);
  //接口参数
  const typeInfo = ref([addAccount, updateAccount]);
  //表单参数
  const data = reactive({
    form: {
      bankName: undefined,
      accountName: undefined,
      accountNumber: undefined,
      accountStatus: 0,
    },
    rules: {
      bankName: [{ required: true, message: "请输入开户行名称！", trigger: "blur" }],
      accountName: [{ required: true, message: "请输入账户名称！", trigger: "blur" }],
      accountNumber: [{ required: true, message: "请输入开户账户！", trigger: "blur" }],
      accountStatus: [{ required: true, message: "请输入账户状态！", trigger: "blur" }],
    },
  });
  const { form ,rules} = toRefs(data);
  //判断参数
  const reqId = ref(undefined)
  const exType = ref(0);
  
  //重置表单
  function reset() {
    proxy.resetForm("formRef");
    form.value = {
      bankName: undefined,
      accountName: undefined,
      accountNumber: undefined,
      accountStatus: 0,
    };
  }
  
  function opendialog(type,row){
      open.value = true;
      exType.value= type;
      if(type == 1){
          reqId.value = row.id;
          form.value = row;
      }
  }
  
  //提交
  function submit(){
      proxy.$refs["formRef"].validate((valid) => {
          if (valid) {
              loading.value = true;
              let req = {
                  id:exType.value == 1?reqId.value:undefined,
                  bankName: form.value.bankName,
                  accountName: form.value.accountName,
                  accountNumber: form.value.accountNumber,
                  accountStatus: form.value.accountStatus,
              }
              typeInfo.value[exType.value](req).then((res) =>{
                  loading.value = false;
                  proxy.$modal.msgSuccess("操作成功");
                  cancel();
                  emit("getList");
              }).catch(() => {
                loading.value = false;
              });
          } else {
              loading.value = false;
          }
      });
  }
  
  //取消
  function cancel() {
    reset();
    open.value = false;
  }
  
  defineExpose({
    opendialog,
  });
  </script>
  <style lang="scss" scoped></style>
  