<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" :class="{ 'form-h50': !showSearch }" label-width="100px" inline>
      <el-form-item prop="variableNames" label="参数名称">
        <el-select v-model="queryParams.variableNames" clearable filterable @focus="getVariableNames" multiple
          collapse-tags collapse-tags-tooltip placeholder="请选择参数名称" style="width: 280px">
          <el-option v-for="(item, index) in variableNamesOption" :key="index" :label="item.code" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="status" label="状态">
        <el-select v-model="queryParams.status" style="width: 280px" placeholder="请选择状态">
          <el-option label="启用" :value="0" />
          <el-option label="禁用" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item prop="createBy" label="创建人">
        <el-input v-model="queryParams.createBy" placeholder="请输入创建人" style="width: 280px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="operation-revealing-area mb10 mt10">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column v-if="columns[0].visible" label="参数名称" align="center" prop="variableName" />
      <el-table-column v-if="columns[1].visible" label="状态" align="center" prop="status">
        <template #default="{ row }">
          <el-tag :effect="row.status == 0 ? 'dark' : 'info'">
            {{ row.status == 0 ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[2].visible" label="创建人" align="center" prop="createBy" />
      <el-table-column v-if="columns[3].visible" label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.status" :active-value="0" :disabled="!checkPermi(['writ:paramSet:switch'])"
            @change="setStatus(row)" :inactive-value="1" style="--el-switch-on-color: #409eff" />
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-area">
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 新增类型 -->
    <addParamsBox ref="addParamsBoxRef" @getList="getList" />
  </div>
</template>

<script setup name="ParamSet">
import { checkPermi } from "@/utils/permission";
import addParamsBox from "../dialog/addParams";
import { getTemplateOptions } from "@/api/writ/template";
import { getVariableList, editVariableStatus } from "@/api/writ/parametric";
//props传值
const props = defineProps({
  activeTab: {
    type: String,
    required: true,
  },
});
//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const loading = ref(false);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    variableNames: undefined,
    status: undefined,
  },
});
const { queryParams } = toRefs(data);
//模板变量下拉
const variableNamesOption = ref([]);
//数据参数
const dataList = ref([]);
const total = ref(0);
const showSearch = ref(false)
const columns = ref([
  { key: 0, label: `参数名称`, visible: true },
  { key: 1, label: `状态`, visible: true },
  { key: 2, label: `创建人`, visible: true },
  { key: 3, label: `创建时间`, visible: true },
]);
//获取列表
function getList() {
  loading.value = true;
  getVariableList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

/** 多选框选中数据 */
function addParams(row) {
  if (row) {
    let req = JSON.parse(JSON.stringify(row));
    proxy.$refs["addParamsBoxRef"].opendialog(req);
  } else {
    proxy.$refs["addParamsBoxRef"].opendialog();
  }
}

// 获取参数名称下拉
function getVariableNames() {
  getTemplateOptions().then(res => {
    variableNamesOption.value = res.data;
  })
}
getVariableNames()

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    variableNames: undefined,
    status: undefined,
  };
  getList();
}

//停用
function setStatus(row) {
  let req = { status: row.status, id: row.id };
  const statusInfo = row.status == 1 ? "停用" : "启用"
  const content = `是否确认${statusInfo}是否确认${statusInfo}？<br/>此操作将${statusInfo}该参数，是否确认？`
  proxy.$modal.confirm(content, "系统提示", { dangerouslyUseHTMLString: true }).then(() => {
    editVariableStatus(req).then((res) => {
      proxy.$modal.msgSuccess('操作成功！')
      getList();
    });
  }).catch(() => {
    getList();
  });
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.form-content {
  padding: 10px 20px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
