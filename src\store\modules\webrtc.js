/**
 * 外呼拨号状态
 * **/ 
import { checkUserSip } from '@/api/system/user'

const state = {
  isRegistered: false, //通道注册状态
  iscalling: false, //是否通话中
  isincall: false, //是否呼入
  htrxCall:undefined
}

const mutations = {
  SET_IS_REGISTERED: (state, isRegistered) => {
    state.isRegistered = isRegistered
  },
  SET_IS_CALLING: (state, iscalling) => {
    state.iscalling = iscalling
  },
  SET_IS_IN_CALL: (state, isincall) => {
    state.isincall = isincall
  },
  SET_HtrxCall: (state, htrxCall) => {
    state.htrxCall = htrxCall
  },
}

const actions = {
  //判断是否有坐席
  checksip({ commit },obj) {
    commit('SET_HtrxCall',obj)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}