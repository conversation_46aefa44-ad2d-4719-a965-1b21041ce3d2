/**
 * 呼叫
 * **/
 import request from '@/utils/request'
 // 联系人方外呼
 export function callOutByContact(data) {
  return request({
    url: '/call/callOutByContact',
    method: 'post',
    data: data
  })
 }

 //案件外呼
 export function callOutByCase(data) {
  return request({
    url: '/call/callOutByCase',
    method: 'post',
    data: data
  })
 }

 //输入号码外呼
 export function callOutByPhoneNumber(data) {
  return request({
    url: '/call/callOutByPhoneNumber',
    method: 'post',
    data: data
  })
 }

 //工单号外呼
 export function callOutByWorkOrderPhoneNumber(data) {
  return request({
    url: '/call/callOutByWorkOrderPhoneNumber',
    method: 'post',
    data: data
  })
 }

 //挂断
 export function callOutHangup() {
  return request({
    url: '/call/callOutHangup',
    method: 'post'
  })
 }