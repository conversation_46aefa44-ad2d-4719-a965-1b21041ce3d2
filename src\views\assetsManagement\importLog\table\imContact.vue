<template>
  <el-table v-loading="loading" :data="dataList">
    <el-table-column
      label="ID"
      prop="id"
      key="id"
      align="center"
      v-if="columns[0].visible"
    />
    <el-table-column
      label="导入批次/文件名称"
      prop="importBatchNum"
      key="importBatchNum"
      align="center"
      v-if="columns[1].visible"
    >
      <template #default="{row}">
        {{ row.importBatchNum }}
      </template>
    </el-table-column>
    <el-table-column
      label="导入类型"
      prop="importTypeInfo"
      key="importTypeInfo"
      align="center"
      v-if="columns[2].visible"
    />
    <el-table-column
      label="成功数量"
      prop="successNumber"
      key="successNumber"
      align="center"
      v-if="columns[3].visible"
    >
      <template #default="{ row }">
        {{ formatAmountWithComma(row.successNumber || 0) }}
        <!-- {{ row.successNumber || 0 }} -->
      </template>
    </el-table-column>
    <el-table-column
      label="转让方"
      prop="ownerName"
      key="ownerName"
      align="center"
      v-if="columns[4].visible"
    />
    <el-table-column
      label="产品类型"
      prop="productType"
      key="productType"
      align="center"
      v-if="columns[5].visible"
    />
    <el-table-column
      label="操作人"
      prop="operatorName"
      key="operatorName"
      align="center"
      v-if="columns[6].visible"
    />
    <el-table-column
      label="导入时间"
      prop="importTime"
      key="importTime"
      align="center"
      v-if="columns[7].visible"
    />
    <el-table-column
      label="导入状态"
      prop="importStartInfo"
      key="importStartInfo"
      align="center"
      v-if="columns[8].visible"
    />
    <el-table-column label="描述及原因" width="180px">
      <template #default="{row}">
        <el-button type="text"
            link @click="downSourceFile(row.sourceFileUrl)"
            v-hasPermi="['assets:importLog:exportPerson:source']"
          >源文件下载</el-button
        >
        <el-button
          v-if="row.importStart == 2 || row.importStart == 3"
          type="text"
            link
          v-hasPermi="['assets:importLog:exportPerson:fail']"
          @click="downFailFile(row.failFileUrl)"
          >失败下载</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { formatAmountWithComma } from "@/utils/common";
const { proxy } = getCurrentInstance();
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  dataList: {
    type: Array,
    default: [],
  },
  columns: {
    type: Array,
    default: [],
  },
});
//下载源文件
function downSourceFile(url) {
  if (!url || url == "") {
    proxy.$modal.msgWarning("源文件不存在，请联系管理员～");
    return false;
  }
  window.open(url);
}

//下载失败文件
function downFailFile(url) {
  if (!url || url == "") {
    proxy.$modal.msgWarning("失败文件不存在，请联系管理员～");
    return false;
  }
  window.open(url);
}
</script>

<style scoped></style>
