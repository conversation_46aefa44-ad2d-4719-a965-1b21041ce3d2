<template>
  <el-dialog v-if="open" title="绑定工作手机" v-model="open" width="700px" append-to-body :before-close="cancel"  modal-class="bind-workPhone-dialog">
    <div class="search">
      <div>
        <el-button @click="handleDataSync">同步数据</el-button>
      </div>
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-position="right">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="userPhone">
          <el-input v-model="queryParams.userPhone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="dataList" class="multiple-table" ref="multipleTableRef" :loading="loading" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" ></el-table-column>
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="手机号" align="center" prop="userPhone" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { workPhoneDataSync, workPhoneList, bind } from "@/api/workPhone/index.js"

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);

const store = useStore();
const name = computed(() => store.getters.name);

const open = ref(false);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const {
  queryParams
} = toRefs(data);

const selectedRows = ref([]) // 存储选中的行

const userId = ref(undefined);
const teamId = ref(undefined);
const employeeName = ref(undefined);
const loginAccount = ref(undefined);

//打开弹窗
async function opendialog(message) {
  userId.value = message.id;
  teamId.value = message.teamId;
  employeeName.value = message.employeeName;
  loginAccount.value = message.loginAccount;
  open.value = true;
  getList();
}

//提交
function submit() {
  if (selectedRows.value.length === 0) {
    cancel();
    return;
  }
  loading.value = true;
  const req = { 
    userId: userId.value,
    phone: selectedRows.value[0].userPhone,
    userName: employeeName.value
  };
  bind(req).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
      loading.value = false;
      if (loginAccount.value == name.value) {
        store.dispatch('call/checkWorkPhone');
      }
      emit("getList");
      cancel();
    }
  }).catch(() => {
    loading.value = false;
  })
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  getList();
}

//取消
function cancel() {
  proxy.$refs["queryRef"].resetFields();
  open.value = false
}

function handleSelectionChange(selection) {
  // 限制为单选
  if (selection.length > 1) {
    const lastSelected = selection[selection.length - 1];
    proxy.$refs.multipleTableRef.clearSelection(); // 清除所有选中项
    proxy.$refs.multipleTableRef.toggleRowSelection(lastSelected, true); // 只保留最后一个
    selectedRows.value = [lastSelected];
  } else {
    selectedRows.value = selection;
  }
}

async function handleDataSync() {
  try {
    await workPhoneDataSync({ teamId: teamId.value });
    await getList();
  } catch (error) {}
}

async function getList() {
  try {
    loading.value = true;
    const reqForm = queryParams.value;
    reqForm.bindStatus = false;
    const res = await workPhoneList(reqForm);
    dataList.value = res.rows;
    total.value = res.total;
  } catch (error) {
  } finally {
    loading.value = false;
  }
}

defineExpose({
  opendialog
})
</script>

<style lang="scss" scoped>
.search {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
</style>
