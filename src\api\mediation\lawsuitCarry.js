import request from '@/utils/request'

// 诉讼执行 -  列表查询
export function getExecuteList(query) {
    return request({
        url: '/execute-case/selectList',
        method: 'get',
        params: query
    })
}

// 诉讼执行 -  列表统计
export function totalMoneyExecute(data) {
    return request({
        url: '/execute-case/selectWithMoney',
        method: 'post',
        data: data
    })
}
// 诉讼执行 -  诉讼执行-批量执行
export function batchExecute(data) {
    return request({
        url: '/execute-case/batchExecute',
        method: 'post',
        data: data
    })
}