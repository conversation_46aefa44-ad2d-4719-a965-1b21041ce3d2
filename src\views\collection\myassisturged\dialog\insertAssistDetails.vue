<template>
  <!-- 退案留案弹窗 -->
  <el-dialog
    title="协催登记"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="124px">
      <el-form-item label="协催状态" prop="state">
        <el-select
          v-model="form.state"
          placeholder="请输入或选择标签"
          clearable
          filterable
          :reserve-keyword="false"
          @focus="urgedTab"
          style="width: 240px"
        >
          <el-option
            v-for="(item,index) in urged"
            :key="index+1"
            :label="item"
            :value="index+1"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="协催内容" prop="assistContent">
        <el-input
          type="textarea"
          v-model="form.assistContent"
          placeholder="请输入"
           maxlength="300"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getUrged } from "@/api/common/common";
import { insertAssistDetails } from "@/api/collection/myassisturged";

const { proxy } = getCurrentInstance();
const loading = ref(false);
const open =  ref(false);
const emit = defineEmits(["getList"]);
//提交数据
const data = reactive({
  form: {
    state: undefined,
    assistContent: undefined,
  },
  rules: {
    state: [{ required: true, message: "请选择状态", trigger: "change" }],
    assistContent: [{ required: true, message: "请输入", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
//催收状态
const urged = ref([]);

//获取催收状态
function urgedTab() {
  getUrged().then((res) => {
    urged.value = res.data;
  });
}

//打开弹窗
function opendialog(data) {
  reset();
  Object.assign(form.value, data);
  open.value = true;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    state: undefined,
    assistContent: undefined,
  };
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      insertAssistDetails(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess("协催登记成功！");
          cancel();
          emit("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
}

 defineExpose({
        opendialog
    })
</script>

<style scoped></style>
