<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" @submit.native.prevent>
      <el-form-item prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="角色名称"
          clearable
          style="width: 240px"
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          v-hasPermi="['system:role:add']"
          @click="add"
          >新建</el-button
        >
      </el-col>
      <right-toolbar
        @queryTable="getList"
        :columns="columns"
        :types="[2, 3]"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column
        label="角色名称"
        align="center"
        key="roleName"
        prop="roleName"
        v-if="columns[0].visible"
      />
      <el-table-column
        label="描述"
        align="center"
        key="remark"
        prop="remark"
        v-if="columns[1].visible"
      >
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <p style="max-width: 300px">{{ scope.row.remark }}</p>
            </template>
            <div>
              <span>{{
                scope.row.remark?.length > 15
                  ? `${scope.row.remark?.substring(0, 15)}...`
                  : scope.row.remark
              }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        key="founder"
        prop="founder"
        v-if="columns[2].visible"
      />
      <el-table-column
        label="更新时间"
        align="center"
        key="modifyTime"
        prop="modifyTime"
        v-if="columns[3].visible"
      >
        <template #default="scope">
          {{ scope.row.modifyTime || scope.row.creationtime }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        key="status"
        prop="status"
        v-if="columns[4].visible"
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-color="#409eff"
            active-value="0"
            inactive-value="1"
            :disabled="!checkPermi(['system:role:status'])"
            @change="handleStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            type="text"
            v-hasPermi="['system:role:setpower']"
            @click="toSetPower(scope.row)"
            >设置权限</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['system:role:setedit']"
            @click="edit(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['system:role:delete']"
            @click="remove(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <setPower ref="setPowerRef" @getList="getList" />

    <!-- 新建/编辑角色 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入角色名称"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="角色描述" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入角色描述"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Role">
import { ElMessageBox } from "element-plus";
import { getRoleList, addrole, editrole, delRole } from "@/api/system/roles";
import setPower from "./setPower.vue";
import { h } from "vue-demi";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const router = useRouter();

const title = ref("");
const open = ref(false);
const subloading = ref(false);

const loading = ref(false);
const total = ref(0);
const roleList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roleName: undefined,
  },
  form: {},
  rules: {
    roleName: [
      { required: true, message: "请输入角色名称！", trigger: "blur" },
    ],
  },
});
const { queryParams, form, rules } = toRefs(data);

const columns = ref([
  { key: 0, label: `角色名称`, visible: true },
  { key: 1, label: `描述`, visible: true },
  { key: 2, label: `创建人`, visible: true },
  { key: 3, label: `更新时间`, visible: true },
  { key: 4, label: `状态`, visible: true },
]);

//获取列表
function getList() {
  loading.value = false;
  getRoleList(queryParams.value)
    .then((res) => {
      roleList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//添加角色
function add() {
  reset();
  title.value = "新建角色";
  open.value = true;
}

//编辑橘色
function edit(row) {
  reset();
  title.value = "编辑角色";
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}

//删除角色
function remove(row) {
  ElMessageBox({
    title: `是否确认删除${row.roleName}角色？`,
    message: h("p", null, [
      h("span", null, "删除后赋予该角色的员工将无"),
      h("span", { style: "color: red" }, row.roleName),
      h("span", null, "角色的权限，请谨慎操作！"),
    ]),
    confirmButtonText: "确定",
    type: "warning",
  })
      .then(() => {
        return delRole(row.id);
      })
      .then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      })
      .catch(() => {});
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

//禁用/启用
function handleStatus(row) {
  let req = JSON.parse(JSON.stringify(row));
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.roleName + '"角色吗?')
    .then(function () {
      loading.value = true;
      return editrole(req);
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
      getList();
    })
    .catch(function () {
      row.status = row.status === "0" ? "1" : "0";
      loading.value = false;
    })
}

//跳转权限设置
function toSetPower(row) {
  proxy.$refs["setPowerRef"].opendialog({
    id: row.id,
    roleName: row.roleName,
    roleRemarks: row.roleRemarks,
    menuCheckStrictly:row.menuCheckStrictly
  });
}

//提交
function submitForm() {
  subloading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.id) {
        editrole(form.value)
          .then((res) => {
            proxy.$modal.msgSuccess("编辑成功！");
            cancel();
            getList();
          })
          .finally(() => {
            subloading.value = false;
          });
      } else {
        addrole(form.value)
          .then((res) => {
            proxy.$modal.msgSuccess("新建成功！");
            cancel();
            getList();
          })
          .finally(() => {
            subloading.value = false;
          });
      }
    } else {
      subloading.value = false;
    }
  });
}

//重置角色表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    roleName: undefined,
    remark: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}
</script>

<style lang="scss" scoped></style>
