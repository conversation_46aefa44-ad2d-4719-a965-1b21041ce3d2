import request from '@/utils/request'

//获取发送记录列表
export function selectSendRecords(query) {
    return request({
      url: '/collection/selectSendRecords',
      method: 'get',
      params: query
    })
  }

  //查看案件进度
export function getProce(query) {
  return request({
      url: `collection/selectReplayById/${query.id}`,
      method: 'get',
  })
}

//查询对应案件数量
export function getCount(query) {
  return request({
      url: '/collection/selectCount',
      method: 'get',
      params: query
  })
}
// 获取全部状态的总和
export function selectRecordNumber(query) {
  return request({
    url: `collection/selectRecordNumber`,
    method: 'get',
    params: query
  })
}

// 获取全部状态的总和
export function exportCsPage(data) {
  return request({
    url: `collection/exportPage`,
    method: 'post',
    data: data
  })
}
