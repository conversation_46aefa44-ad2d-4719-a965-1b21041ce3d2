<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="操作者" prop="operName">
        <el-input
          v-model="queryParams.operName"
          placeholder="请输入操作人员"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="操作类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_oper_type"
            :key="dict.code"
            :label="dict.info"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.operTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['monitor:operlog:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleClean"
          v-hasPermi="['monitor:operlog:refresh']"
          >清空</el-button
        >
      </el-col> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="optList">
      <el-table-column
        label="操作时间"
        align="center"
        key="operTime"
        prop="operTime"
      />
      <el-table-column
        label="操作者"
        align="center"
        key="operName"
        prop="operName"
      />
      <el-table-column
        label="操作类型"
        align="center"
        key="businessType"
        prop="businessType"
        :formatter="businessTypeFor"
      />
      <el-table-column
        label="操作详情"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span>{{
            `${scope.row.title}`
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script setup name="Operlog">
import { selectTeamOperLog, getOperationOption } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const optList = ref([]);
const loading = ref(true);
const total = ref(0);
const title = ref("");
const sys_oper_type = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operName: undefined,
    operTime: [],
    businessType: undefined,
  },
});

const rangfiles = ['operTime'];
const { queryParams } = toRefs(data);

//查询登录日志
function getList() {
  loading.value = true;
  selectTeamOperLog(proxy.addFieldsRange(queryParams.value, rangfiles))
    .then((res) => {
      optList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//获取操作类型
function getOperationType(){
  getOperationOption().then((res) =>{
    sys_oper_type.value = res.data;
  })
}
getOperationType()

//搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value =  ({
    pageNum: 1,
    pageSize: 10,
    operName: undefined,
    operTime: [],
    businessType: undefined,
  }),
  handleQuery();
}

//操作类别 0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据
function businessTypeFor(row) {
  return ["其他", "新增", "修改", "删除", "授权", "导出", "导入", "强退", "生成代码", "清空数据",][
    row.businessType 
  ];
}


</script>
