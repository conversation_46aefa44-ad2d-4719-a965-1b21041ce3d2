<template>
  <div class="app-container">
    <el-form
      :model="form"
      label-position="right"
      :label-width="120"
      :rules="rules"
      ref="formRef"
    >
      <el-form-item label="消息标题" :rules="rules.messageTitle" prop="messageTitle">
        <el-input
          v-model="form.messageTitle"
          placeholder="请输入消息标题"
          style="width: 1200px"
          maxlength="128"
          clearable
        />
      </el-form-item>
      <el-form-item label="消息内容" :rules="rules.messageContent" prop="messageContent">
        <div style="width:1200px">
            <myEditor v-model:modelValue="form.messageContent" :imgUploadUrl="upload.url" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center mt20">
      <el-button @click="toBack">取消</el-button>
      <el-button type="primary" :loading="loading" plain @click="submit"
        >发布消息</el-button
      >
    </div>
  </div>
</template>
<script setup name="EditMessage">
import myEditor from "@/components/myEditor";
import { getToken } from "@/utils/auth";
import { selectMessageCenter, updateMessageCenter } from "@/api/message/system";
//全局配置
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    id:route.params.id
})
//表单配置
const loading = ref(false);
//消息类型
//表单配置参数
const data = reactive({
  form: {
    id:route.params.id,
    messageTitle: undefined,
    messageContent: undefined,
  },
  rules: {
    messageTitle: [
      {
        required: true,
        message: "请输入消息标题，长度控制在128个字符以内！",
        trigger: "change",
      },
    ],
    messageContent: [{ required: true, message: "请输入消息内容！", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//上传
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/message/upload",
});

//获取数据
function getList(){
  selectMessageCenter(queryParams.value)
      .then((res) => {
        res?.rows.forEach((item,index) =>{
          form.value.messageTitle = item.messageTitle;
          form.value.messageContent = item.messageContent;
        })
      })
      .catch(() => {
        loading.value = false;
      });
}
getList();

//返回
const toBack = () => {
  const obj = { path: "/message/systemMessage" };
  proxy.$tab.closeOpenPage(obj);
};

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
        loading.value =true;
        updateMessageCenter(form.value).then((res) => {
            loading.value = false;
            proxy.$modal.msgSuccess("操作成功！");
            toBack();
        })
        .catch(() => {
            loading.value = false;
        });
    }
  });
}

</script>
<style lang="scss" scoped></style>
