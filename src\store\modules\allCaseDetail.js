import {
  addNegotiateRecord,
} from "@/api/mediation/allCaseDetail";
import $modal from "@/plugins/modal"
const state = {
  yuguForm: {}, // 预估登记
  followingForm: {}, // 标签登记
  yuguFormRef: null, // 预估登记表单ref
  followingRef: null, // 标签登记表单ref
  loading: false, // 表单提交loading
  richEditValue: '', // 富文本编辑器值,
  caseAnswersRecord: {
    "self": false,
    "sendMediationNotice": false,
    "checkArrears": false,
    "sendArrearsScreenshot": false,
    "specialReduction": false,
    "repaymentMethodConfirmed": false,
    "repaymentTimeConfirmed": false,
    "fundingSourceConfirmed": false,
    "legalNoticeSent": false,
    "wechatAdded": false,
    "score": 0
  }, // 答案记录
  score: undefined, // 评分
  networkNameDatas: [], // 线路名称数据
  caseInfoData: {}, // 客户信息,
  customRelay: {}, // webrtc自定义透传
}
const mutations = {
  SET_YUGU_FORM: (state, data) => {
    state.yuguForm = data
  },
  SET_FOLLOWING_FORM: (state, data) => {
    state.followingForm = data
  },
  SET_YUGU_FORM_REF: (state, data) => {
    state.yuguFormRef = data
  },
  SET_FOLLOWING_FORM_REF: (state, data) => {
    state.followingRef = data
  },
  SET_LOADING: (state, data) => {
    state.loading = data
  },
  SET_RICH_EDIT_VALUE: (state, data) => {
    if (state.richEditValue !== data) {
      state.richEditValue = data
    }

  },
  SET_CASE_ANSWERS_RECORD: (state, data) => {
    state.caseAnswersRecord = data
  },
  SET_SCORE: (state, data) => {
    state.score = data
  },
  SET_NETWORK_NAME_DATAS: (state, data) => {
    state.networkNameDatas = data
  },
  SET_CASE_INFO_DATA: (state, data) => {
    state.caseInfoData = data
  },
  SET_CUSTOM_RELAY: (state, data) => {
    for (const key in data) {
      if (!data[key]) {
        data[key] = ''
      }
    }
    state.customRelay = data
  }
}
const actions = {
  getAllForm({ state }) { // 获取所有表单数据
    return new Promise((resolve, reject) => {
      try {
        let form = JSON.parse(JSON.stringify(state.followingForm))
        if (
          state.yuguForm.hasOwnProperty('preTransaction') &&
          state.yuguForm.preTransaction === 1
        ) {
          Object.assign(form, state.yuguForm)
        }
        for (const key in form) {
          if (form.hasOwnProperty(key) && Array.isArray(form[key])) {
            if (form[key].length > 0) {
              form[key] = form[key].join(',');
            } else {
              delete form[key];
            }
          }
        }
        resolve(form)
      } catch (error) {
        reject(error)
      }

    })
  },
  // 表单验证
  validateForm({ state }) { // 表单验证
    return new Promise((resolve, reject) => {
      try {
        state.followingRef.validate((valid2) => {
          if (!valid2) {
            resolve(false)
          }
          // 判断state.yuguForm对象中是否有preTransaction属性
          if (
            state.yuguForm.hasOwnProperty('preTransaction') &&
            state.yuguForm.preTransaction === 1
          ) {
            state.yuguFormRef.validate((valid1) => {
              if (valid1) {
                resolve(true)
              } else {
                resolve(false)
              }
            })
          } else {
            resolve(true)
          }
        })
      } catch (error) {
        console.log(error)
        reject(error)
      }
    })
  },

  submitForm({ commit, dispatch, state }, { caseId, remarks }) { // 提交表单
    return new Promise((resolve, reject) => {
      // 表单验证
      commit('SET_LOADING', true)
      dispatch('validateForm').then(valid => {
        if (valid) {
          // 获取所有表单数据
          dispatch('getAllForm').then(form => {
            let reqForm = { ...form, ...{ caseId: caseId } }
            if (remarks && remarks.length > 0) {
              Object.assign(reqForm, { remarks: remarks.join('，') })
            }
            Object.assign(reqForm, { caseAnswersRecord: state.caseAnswersRecord });
            addNegotiateRecord(reqForm).then(res => {
              $modal.msgSuccess('提交成功!');
              // 重置表单数据
              if (
                state.yuguForm.hasOwnProperty('preTransaction') &&
                state.yuguForm.preTransaction === 1
              ) {
                state.yuguFormRef.resetFields();
              }
              state.followingRef.resetFields();
              commit('SET_YUGU_FORM', {})
              commit('SET_FOLLOWING_FORM', {})
              commit('SET_LOADING', false)
              commit('SET_RICH_EDIT_VALUE', '')
              commit('SET_CASE_ANSWERS_RECORD', {
                "self": false,
                "sendMediationNotice": false,
                "checkArrears": false,
                "sendArrearsScreenshot": false,
                "specialReduction": false,
                "repaymentMethodConfirmed": false,
                "repaymentTimeConfirmed": false,
                "fundingSourceConfirmed": false,
                "legalNoticeSent": false,
                "wechatAdded": false,
                "score": 0
              })
              commit('SET_SCORE', undefined)
              resolve(true)
            }).finally(() => {
              commit('SET_LOADING', false)
            })
          }).catch(error => {
            commit('SET_LOADING', false)
            reject('获取表单数据失败')
          })
        } else {
          commit('SET_LOADING', false)
          reject('表单验证失败')
        }
      })

    })
  },

  updateYuguForm({ commit, dispatch }, data) { // 更新预估登记表单
    commit('SET_YUGU_FORM', data)
    dispatch('setRichEditValue', { type: null});
  },

  // 富文本编辑器值
  setRichEditValue({ commit, state }, { type, value }) { // type: rich: 富文本编辑器值
    if (type === 'rich') {
      commit('SET_RICH_EDIT_VALUE', value)
      return
    }
    const zhongwenkey = ["客户属性", "预期还款金额", "预期还款日期"]
    const key = ["customerAttribute", "promiseRepaymentMoney", "promiseRepaymentTime"];
    let result = "";
    key.forEach((item, index) => {
      result += `<p>${zhongwenkey[index]}：<span style='color:#409eff'>${state.yuguForm[item]}</span></p>`
    })

    let oldData = state.richEditValue;
    if (oldData && oldData !== '') {
      let oldDataArr = oldData.split("</p>");
      for (let i = 0; i < oldDataArr.length; i++) {
        const item = oldDataArr[i];
        // 判断item字符串中是否有 客户属性、预期还款金额、预期还款日期 这些字段
        if (zhongwenkey.some(key => item.indexOf(key) > -1)) {
          // 如果有，则删除
          oldDataArr.splice(i, 1)
          i--;
        }
      }
      oldData = oldDataArr.join("</p>");
    }
    result = oldData + result
    if (result !== state.richEditValue) {
      commit('SET_RICH_EDIT_VALUE', result)
    }
    
  },

  clearData({ commit }) {
    commit('SET_YUGU_FORM', {})
    commit('SET_FOLLOWING_FORM', {})
    commit('SET_YUGU_FORM_REF', null)
    commit('SET_FOLLOWING_FORM_REF', null)
    commit('SET_LOADING', false)
    commit('SET_RICH_EDIT_VALUE', '')
    commit('SET_CASE_ANSWERS_RECORD', {
      "self": false,
      "sendMediationNotice": false,
      "checkArrears": false,
      "sendArrearsScreenshot": false,
      "specialReduction": false,
      "repaymentMethodConfirmed": false,
      "repaymentTimeConfirmed": false,
      "fundingSourceConfirmed": false,
      "legalNoticeSent": false,
      "wechatAdded": false,
      "score": 0
    })
    commit('SET_SCORE', undefined)
    commit('SET_NETWORK_NAME_DATAS', [])
    commit('SET_CASE_INFO_DATA', {})
    commit('SET_CUSTOM_RELAY', {})
  }// 清空表单数据


}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}