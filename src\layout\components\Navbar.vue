<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="getters.sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!$store.state.settings.topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="$store.state.settings.topNav" />

    <div :class="['case-nav', (breadcrumbWidth && breadcrumbWidth > 50) ? (breadcrumbWidth > 200 ? 'changePositionLarge' : 'changePosition') : '']">
      <span class="nav-title">总案件:</span><span class="nav-content">{{ caseStatus.quantity }}</span>
      <span class="nav-title">总金额:</span><span class="nav-content">{{ numFilter(caseStatus.money) }}</span>
      <span class="nav-title">总回款额:</span><span class="nav-content">{{
        numFilter(caseStatus.collectionAmount) }}</span>
    </div>

    <div class="right-menu">
      <template v-if="getters.device !== 'mobile'">
        <el-popover placement="bottom" :width="400" title="站内消息通知" :ref="`popover-message`" trigger="hover">
          <template #reference>
            <el-badge :value="getters.count" :max="99" :hidden="getters.count == 0" @click="getMessage(0)"
              class="item message-badge"><el-icon class="meaasge-icon" :size="40">
                <Bell class="right-menu-item hover-effect" />
              </el-icon>
            </el-badge>
          </template>
          <el-row class="meaasge-box" v-for="item in messageList" :key="item.id" @click="goToMessageDetails(item.id)">
            <div class="message-content"><i class="reTip"></i>{{ item.messageContent }}</div>
            <div class="message-time">{{ item.createTime }}</div>
          </el-row>
          <el-row class="meaasge-box" v-if="messageList.length == 0">
            <div class="message-none">暂时没有最新的站内通知</div>
          </el-row>
        </el-popover>
        <el-tooltip content="清除缓存并刷新" effect="dark" placement="bottom">
          <el-icon class="refresh-icon" @click="refreshWindow" color="#444" :size="24">
            <Refresh />
          </el-icon>
        </el-tooltip>
        <!-- <el-tooltip content="官网地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->
        <el-tooltip content="全屏" effect="dark" placement="bottom">
          <screenfull id="screenfull" @click="toggleSideBar" class="right-menu-item hover-effect" />
        </el-tooltip>
        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <div class="sip-container" v-if="props.isPreTestSip">
        <el-dropdown @command="changeSipStateHandle" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img src="../../assets/images/free.png" class="user-avatar" v-if="sipState == '置闲'" />
            <img src="../../assets/images/busy.png" class="user-avatar" v-if="sipState == '置忙'"/>
            <span class="avatar-name">{{ sipState }}</span>
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item command="1">置闲</el-dropdown-item>
                <el-dropdown-item command="2">置忙</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img :src="getters.avatar" class="user-avatar" />
            <span class="avatar-name">{{ getters.name }}</span>
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/system/editPassword">
                <el-dropdown-item>修改密码</el-dropdown-item>
              </router-link>
              <router-link to="/message/homeMessage">
                <el-dropdown-item>消息中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 来电弹窗 -->
    <incomingCall ref="incomingCallRef"></incomingCall>

    <caseInformationDialog ref="caseInformationDialogRef"></caseInformationDialog>

    <!-- 工作手机来电弹窗 -->
    <workPhoneCall ref="workPhoneCallRef"></workPhoneCall>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import { selectCaseManageMoney, getNavMessage } from "@/api/common/common";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import RuoYiDoc from "@/components/RuoYi/Doc";
import { webSocketContent } from "@/api/common/webSocket";
import { getSipState, updateSipState } from "@/api/appreciation/callOut";
import incomingCall from "@/views/appreciation/dialog/incomingCall";
import caseInformationDialog from "@/views/appreciation/dialog/caseInformationDialog";
import workPhoneCall from "@/views/appreciation/dialog/workPhoneCall";
import { nextTick } from "vue";

const { proxy } = getCurrentInstance();
const store = useStore();
store.dispatch('call/checksip'); //检测当前账号是否分配坐席
store.dispatch('call/checkWorkPhone'); //检测当前账号是否开通工作手机
const getters = computed(() => store.getters);
const router = useRouter();
const route = useRoute();
const messageBox = ref(false);
const caseStatus = ref({
  quantity: 0,
  money: 0,
  collectionAmount: 0,
});
const localStoragePath = ref([
  "mycase-detail",
  "caseIndex-detail",
  "teamIndex-detail",
  "aduit-detail",
]);
const messageList = ref([]);
const count = ref(0);

// 登录状态
const isRegistered = computed(() => store.getters.isRegistered);

const props = defineProps({
  isPreTestSip: {
    type: Boolean,
    default: false,
  },
  isSip: {
    type: Boolean,
    default: false,
  }
})

// 面包屑大小
let breadcrumbWidth = ref(undefined);

function toggleSideBar() {
  store.dispatch("app/toggleSideBar");
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function getCaseManageMoney() {
  selectCaseManageMoney()
    .then((res) => {
      caseStatus.value = res.data;
    })
    .catch(() => {
      caseStatus.value = {
        quantity: 0,
        money: 0,
        collectionAmount: 0,
      };
    });
}
getCaseManageMoney();

//获取首页消息
function getMessage(type) {
  getNavMessage()
    .then((res) => {
      messageList.value = res.data;
      if (type == 0) {
        if (res.data?.length == 0) {
          proxy.$modal.msgWarning('没有最新的站内信息。');
          messageBox.value = false;
        } else {
          messageBox.value = !messageBox.value;
        }
      }
    })
    .catch(() => {
      messageList.value = [];
    });
}
getMessage();


watch(route, () => {
  getCaseManageMoney();
})

watch(route, () => {
  nextTick(() => {
    const element = document.querySelector('.breadcrumb-container');
    element && (breadcrumbWidth.value = element.getBoundingClientRect().width);
  })
}, {immediate: true})

const sipStateEnum = {
  1 : "置闲",
  2 : "置忙"
}
const sipState = ref(undefined);
const getSipStateHandle = () => {
  getSipState().then(res => {
    //1：置闲 2：置忙
    if (res.code == 200) { 
      sipState.value = sipStateEnum[res.data];
    }
  })
}

watch(() => props.isSip, (newValue) => {
  if (newValue) {
    getSipStateHandle();
  }
})

const updateSipStateHandle = (state) => {
  return new Promise((resolve, reject) => {
    if(!isRegistered.value) {
      proxy.$modal.msgWarning("请检查是否登录Mrtc");
      resolve(false);
      return;
    }
    let formData = new FormData();
    formData.append("sipState", state);
    updateSipState(formData).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      }
      resolve(res.code);
    })
  })
}

const changeSipStateHandle = async (command) => {
  console.log(command);
  let result;
  switch (command) {
    case "1":
      nextTick(async () => {
        result = await updateSipStateHandle(1);
        if (result == 200) {
            sipState.value = sipStateEnum[1];
            console.log(sipState.value);
        }
      })
      break;
    case "2":
      nextTick(async () => {
        result = await updateSipStateHandle(2);
        if (result == 200) {
            sipState.value = sipStateEnum[2];
            console.log(sipState.value);
        }
      })
      break;
    default:
      break;
  }
}

watch(() => store.getters.taskMessage, (newValue) => {
  console.log(newValue, "taskMessage")
  proxy.$refs.incomingCallRef.openDialog(newValue);
})

watch(() => store.getters.caseTaskMessage, (newValue) => {
  console.log(newValue, "caseTaskMessage")
  proxy.$refs.caseInformationDialogRef.openDialog(newValue);
})

watch(() => store.getters.workPhoneMessage, (newValue) => {
  console.log(newValue, "workPhoneMessage")
  proxy.$refs.workPhoneCallRef.openDialog(newValue);
})

//刷新并清除缓存
function refreshWindow() {
  let refreshFlag = false;
  localStoragePath.value.forEach(item => {
    if (route.path.includes(item)) {
      refreshFlag = true;
    }
  });
  let callLoginState = localStorage.getItem(`callLoginState`) || JSON.stringify({
    state: false,
    name: undefined,
    pwd: undefined
  });;
  if (refreshFlag) {
    let data = localStorage.getItem(`searchInfo/${route.params.caseId}`);
    localStorage.clear();
    localStorage.setItem(`searchInfo/${route.params.caseId}`, data);
  } else {
    if (route.path.includes('point')) {
      let data = localStorage.getItem(`point/${route.params.time}`);
      localStorage.clear();
      localStorage.setItem(`point/${route.params.time}`, data);
    } else if (route.path.includes('rules')) {
      let data = localStorage.getItem(`rules/${route.params.time}`);
      localStorage.clear();
      localStorage.setItem(`rules/${route.params.time}`, data);
    } else {
      localStorage.clear();
    }
  }
  localStorage.setItem(`callLoginState`, callLoginState);
  location.reload()
}

//获取消息数量
async function getCount() {
  await webSocketContent().then((res) => {
    count.value = res.number;
  });
}
getCount();

function goToMessageList() {
  router.push(`/message/homeMessage`);
}

function goToMessageDetails(id) {
  const query = { path: route.path }
  router.push({ path: `/message/home/<USER>/${id}`, query });
}


function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      store.dispatch("LogOut").then(() => {
        location.href = "/index";
      });
    })
    .catch(() => { });
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .case-nav {
    display: inline-block;
    font-size: 14px;
    line-height: 50px;
    margin-left: 60px;
    color: #666;
    font-weight: 600;
    position: relative;
    left: calc(50% - 450px);

    .nav-title {
      margin-left: 20px;
    }

    .nav-content {
      margin-left: 5px;
      color: #ff6633;
    }
  }

  .case-nav.changePosition {
    left: calc(50% - 550px);
  }

  .case-nav.changePositionLarge {
    left: calc(50% - 600px);
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .message-badge {
      margin-right: 8px;
    }

    .meaasge-icon svg {
      width: 40px;
      height: 40px;
      margin-top: 10px;
    }

    .sip-container {
      margin-right: 20px;
      min-width: 78px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 24px;
          height: 24px;
          border-radius: 10px;
        }

        .avatar-name {
          font-size: 14px;
          color: #666;
          margin-left: 10px;
          position: relative;
          top: -6px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 28px;
          height: 28px;
          border-radius: 10px;
        }

        .avatar-name {
          font-size: 14px;
          color: #666;
          margin-left: 10px;
          position: relative;
          top: -10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.meaasge-box {
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
  cursor: pointer;

  .red-point {
    display: inline-block;
    font-size: 48px;
    vertical-align: top;
    line-height: 20px;
    color: red;
    border-radius: 50%;
  }

  .reTip {
    display: inline-block;
    background: #f00;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    position: relative;
    z-index: 4;
    margin-right: 5px;
  }

  .message-content {
    display: inline-block;
    width: 330px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
  }

  .message-time {
    font-size: 15px;
    margin-top: 10px;
    margin-left: 15px;
  }

  .message-none {
    font-size: 15px;
    color: #999;
    text-align: center;
    height: 50px;
    line-height: 50px;
  }
}

.meaasge-box:hover,
.refresh-icon:hover {
  background-color: rgb(242 242 242);
}

.message-box:last-child {
  border-bottom: none;
}

.refresh-icon {
  padding: 23px 0px;
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
  line-height: 40px;
}
</style>
