import request from '@/utils/request';

//获取模板类型数据
export function getTemplateOptions(query) {
	return request({
		url: '/document/template/getOptions',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//新建发函提交
export function addMessage(data) {
	return request({
		url: '/document/message/add',
		method: 'post',
		data: data,
		gateway: 'sign'
	});
}

//律函-新建发函-校验文件
export function verifyFileMessage(query) {
	return request({
		url: '/document/message/verifyFile',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//律函-新建发函-校验文件
export function getMessageList(query) {
	return request({
		url: '/document/message/list',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//律函-获取审核状态选项
export function getProceOptions(query) {
	return request({
		url: '/document/message/getProceOptions',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//函件量-列表
export function getLetterList(query) {
	return request({
		url: '/document/item/list',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//函件量-查询审核状态列表
export function getProceList(query) {
	return request({
		url: '/document/item/getProceList',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//函件量-查询审核状态列表
export function getProceBatchList(query) {
	return request({
		url: '/document/item/getProceBatchList',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//函件量-预览
export function getPreview(query) {
	return request({
		url: '/document/item/getPreview',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

//函件量-签章文件
export function getSignFile(query) {
	return request({
		url: '/document/item/getSignFile',
		method: 'get',
		params: query,
		gateway: 'sign'
	});
}

// 发函-新建发函(压缩包pdf)
export function addZip(data) {
	return request({
		url: '/document/message/addZip',
		method: 'post',
		data: data,
		gateway: 'sign'
	});
}

// 发函-校验zip压缩包文件
export function verifyZipFile(data) {
	return request({
		url: '/document/message/verifyZipFile',
		method: 'post',
		data: data,
		gateway: 'sign'
	});
}

// 下载-导出函件
export function exportLawyerZip(data) {
	return request({
		url: '/document/message/export',
		method: 'post',
		data: data,
		gateway: 'sign'
	});
}

// 解析excel文件到数据库表
export function addByExcel(data) {
	data.webSide = 2
	return request({
		url: '/document/item/addByExcel',
		method: 'post',
		data,
		gateway: 'sign'
	});
}
// 校验导入物流excel
export function logisticsVerifyFile(params) {
	return request({
		url: '/document/item/verifyFile',
		method: 'get',
		params,
		gateway: 'sign'
	});
}
// 获取快递实时信息
export function getExpressInfo(data) {
	return request({
		url: '/document/item/getExpressInfo',
		method: 'post',
		data,
		gateway: 'sign'
	});
}
// 快递信息开启推送
export function pushInfo(params) {
	return request({
		url: '/document/item/pushInfo',
		method: 'get',
		params,
		gateway: 'sign'
	});
}
// 订阅回调接口 即callBackUrl 回调内容存储到数据库中
export function callBackUrl(params) {
	return request({
		url: '/document/item/callBackUrl',
		method: 'get',
		params,
		gateway: 'sign'
	});
}
