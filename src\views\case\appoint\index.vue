<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :class="{ 'form-h50': !showSearch }" :inline="true">
      <el-form-item label="案件量" style="width: 290px">
        <el-row>
          <el-col :span="11">
            <el-input v-model="queryParams.caseQuantity1" clearable />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input v-model="queryParams.caseQuantity2" clearable />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-table v-loading="loading" @sort-change="handleSortChange" class="mb10 mt10" ref="multipleTableRef"
      :data="caseList">
      <el-table-column v-if="columns[0].visible" label="ID" align="center" key="id" prop="id"></el-table-column>
      <el-table-column v-if="columns[1].visible" label="回款目标" sortable="targetBackMoney" align="center"
        key=" targetBackMoney" prop="targetBackMoney">
        <template #default="{ row }">
          <div>
            {{ percentFor(row.targetBackMoney) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[2].visible" label="分配案件量" sortable="caseQuantity" align="center" key="caseQuantity"
        prop="caseQuantity" />
      <el-table-column v-if="columns[3].visible" label="委托总金额" sortable="totalAmount" align="center" key="totalAmount"
        prop="totalAmount">
        <template #default="{ row }">
          <span>{{ numFilter(row.totalAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[4].visible" label="操作人" align="center" key="applicant" prop="applicant" />
      <el-table-column v-if="columns[5].visible" label="退案时间" align="center" key="returnCaseDate"
        prop="returnCaseDate" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>
<script setup name="Appoint">
import { getBatchNums } from "@/api/common/common";
import { allocatedList } from "@/api/case/appiont/allocated";

const { proxy } = getCurrentInstance();
//请求参数
const rangFields = ["entrustingCaseDate"]; //范围字段
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  entrustingBatchNum: undefined,
  entrustingCaseDate: [],
  caseQuantity1: undefined,
  caseQuantity2: undefined,
  totalAmount1: undefined,
  totalAmount2: undefined,
});

//表格数据
const caseList = ref([]);
const loading = ref(false);
const total = ref(0);
//表单数据集合
const batchs = ref([]);
const showSearch = ref(false)
const columns = ref([
  { key: 0, label: 'ID', visible: true },
  { key: 1, label: '回款目标', visible: true },
  { key: 2, label: '分配案件量', visible: true },
  { key: 3, label: '委托总金额', visible: true },
  { key: 4, label: '操作人', visible: true },
  { key: 5, label: '退案时间', visible: true },
])
//获取列表数据
function getList() {
  loading.value = true;
  allocatedList(proxy.addFieldsRange(queryParams.value, rangFields))
    .then((res) => {
      caseList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .then(() => {
      loading.value = false;
    });
}
getList();

//获取批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    entrustingBatchNum: undefined,
    entrustingCaseDate: [],
    caseQuantity1: undefined,
    caseQuantity2: undefined,
    totalAmount1: undefined,
    totalAmount2: undefined,
  };
  handleQuery();
}

// 重新排序
function handleSortChange({ prop, order }) {
  const orderObj = { targetBackMoney: 1, caseQuantity: 2, totalAmount: 3 }
  const orderBy = orderObj[prop];
  queryParams.value.sortOrder = proxy.orderEnum[order]
  queryParams.value.orderBy = orderBy;
  getList();
}

// 格式化百分比
function percentFor(num) {
  return num ? `${proxy.setNumberToFixedThree(num)}%` : '--'
}

// 格式化金额
function moneyFor(num) {
  return num ? `${proxy.setNumberToFixed(num)}` : '--'
}

</script>
<style lang="scss" scoped></style>
