<template>
    <div>
        <div class="selected-toolbar" v-if="rightToolbar || props.isSelectedAll">
            <SelectedAll v-if="props.isSelectedAll" v-model:allQuery="allQuery" :isPreKeep="props.isPreKeep"
                :selectedArr="props.selectedArr" :dataList="dataList">
                <template #content>
                    <slot name="selectedAllSlot" />
                </template>
            </SelectedAll>
            <right-toolbar v-if="rightToolbar" v-model:showSearch="showSearch" :columns="column"
                @queryTable="getList" />
        </div>
        <el-table :class="[`${props.isSelectedAll ? 'multiple-table' : ''}`, tableClass].join(' ')" v-loading="loading"
            ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange"
            @sort-change="handleSortChange">
            <el-table-column v-if="props.selection || props.isSelectedAll" type="selection" :selectable="selectable"
                width="50" align="center" />
            <el-table-column align="center" v-for="item in columnToolbar" :key="item.key" :label="item.label"
                :prop="item.prop" :sortable='item.sortable' :width="item.width">
                <template #default="scope">
                    <div>
                        <div v-if="!item.slot">
                            {{ item.formatter ? item?.formatter(scope.row) : `${scope.row[item.prop]}` }}
                        </div>
                        <slot :prop="item.prop" :scope="scope" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" :fixed="props.tableOperatFix" v-if="props.tableOperatArea"
                :width="props.tableOperatWidth">
                <template #default="scope">
                    <slot prop="tableOperatArea" :scope="scope" />
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="props.total > 0" :total="props.total" v-model:page="props.queryParams.pageNum"
            v-model:limit="props.queryParams.pageSize" @pagination="props.getList" />
    </div>
</template>
<script setup>
import SelectedAll from '@/components/SelectedAll';
const emits = defineEmits(['update:selectedArr', 'update:allQuery', 'update:showSearch'])
const props = defineProps({
    total: { type: Number, default: 0 },
    column: { type: Array, default: [] },
    dataList: { type: Array, default: [] },
    selectedArr: { type: Array, default: [] },
    loading: { type: Boolean, default: false },
    allQuery: { type: Boolean, default: false },
    isPreKeep: { type: Boolean, default: false },
    selection: { type: Boolean, default: false },
    showSearch: { type: Boolean, default: false },
    tableOperatArea: { type: Boolean, default: false },
    rightToolbar: { type: Boolean, default: false },
    isSelectedAll: { type: Boolean, default: false },
    getList: { type: Function },
    sortChange: { type: Function },
    selectionChange: { type: Function },
    tableClass: { type: String, default: '' },
    tableOperatFix: { type: String, default: 'right' },
    tableOperatWidth: { type: [String, Number], default: '' },
    queryParams: { type: Object, default: { pageSize: 10, pageNum: 1 } },
})
const allQuery = ref(false)
const showSearch = ref(false)
function selectable() {
    return !allQuery.value
}
function handleSelectionChange(selection) {
    if (props.selectionChange) {
        props.selectionChange(selection)
        return false
    }
    emits('update:selectedArr', selection)
}
function handleSortChange(data) {
    if (props.sortChange) {
        props.sortChange(data)
        return false
    }
}
const columnToolbar = computed(() => props.rightToolbar ? props.column.filter(item => item.visible) : props.column)
watch(() => [allQuery.value, showSearch.value], () => {
    emits('update:allQuery', allQuery.value)
    emits('update:showSearch', showSearch.value)
}, { immediate: true, deep: true })
</script>
<style lang="scss" scoped>
.selected-toolbar {
    position: relative;
    min-height: 44px;
}

.multiple-table .el-table__header-wrapper .el-table-column--selection .el-checkbox {
    display: none;
}
</style>