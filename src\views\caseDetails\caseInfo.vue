<template>
  <!-- 案件信息 -->
  <div class="caseinfo-wrap">
    <!-- 案件详情 -->
    <div class="case-info" v-show="infotype == '案件详情'">
      <div class="man-info flex-wrap">
        <div class="flex-wrap-item">
          <div class="title ">{{ infoBase?.clientName || "--" }}</div>
          <div class="text">
            {{ infoBase?.clientPhone || "--" }}
            <!-- <callBarVue v-if="route.query?.type == 'myCase'" :phoneState="infoBase?.phoneState"
              :caseId="route.params.caseId" :key="htrxCall" /> -->
            <workPhoneVue v-if="route.query?.type == 'record' || route.query?.type == 'myCase'" :phoneNumber="infoBase?.clientPhone" :caseId="route.params.caseId" :borrower="infoBase?.clientName"/>
          </div>
        </div>
        <div class="flex-wrap-item">
          <div class="title">证件号（{{ infoBase?.clientIdType || "--" }}）</div>
          <div class="text">{{ infoBase?.clientIdNum || "--" }}</div>
        </div>
        <div class="flex-wrap-item">
          <div style="display:inline-block;width:80px;margin-right:10px">
            <div class="title">减免状态</div>
            <div class="text">{{ infoBase?.state || "--" }}</div>
          </div>
          <div style="display:inline-block;vertical-align: top;margin-top: 10px;"
            v-if="route.query?.type == 'record' && infoBase?.stateNumber == 1">
            <el-button type="warning" plain @click="openReduction">申请减免</el-button>
          </div>
        </div>
        <div class="flex-wrap-item">
          <div class="title">当前跟进状态</div>
          <div class="text">{{ infoBase?.followUpState || "--" }}</div>
        </div>
      </div>

      <!-- <formCom :form="infoBase" :form2="infoLoan" :formList="infoBaseList" /> -->
    </div>
    <!-- 减免申请 -->
    <reductionVue @getDetails="getDetails" ref="reductionRef" />
    <img v-if="props.imgFor && props.imgFor()" class="status-img" :src="props.imgFor && props.imgFor()" alt="">
  </div>
</template>

<script setup>
import formCom from './components/formCom';
import reductionVue from "./dialog/reduction.vue";
import callBarVue from '@/components/callBar/index.vue'

const { proxy } = getCurrentInstance();
const store = useStore();
const htrxCall = computed(() => store.getters.htrxCall);
const route = useRoute();
const emit = defineEmits(["getcaseDetailInfo"]);

const props = defineProps({
  infoBase: {
    //案件详情信息
    type: Object,
    default: {},
  },
  infoLoan: {
    //案件详情信息
    type: Object,
    default: {},
  },
  imgFor: {
    type: Function
  }
});
const infotype = ref("案件详情");
//打开申请减免
function openReduction() {
  proxy.$refs["reductionRef"].opendialog(route.params.caseId, props.infoLoan?.remainingDue);
}

function getDetails() {
  emit('getcaseDetailInfo')
}


provide("getDetails", Function, true);
</script>

<style lang="scss" scoped>
.radio-btn {
  position: relative;
  padding: 20px;
  border-bottom: 1px solid rgba(171, 186, 223, 0.1);
}

.caseinfo-wrap {
  position: relative;
  margin-bottom: 0;

  .status-img {
    position: absolute;
    top: 0;
    right: 20px;
    width: 114px;
  }
}

.case-info {
  .other-info {
    min-height: 247px;
  }
}

.man-info.flex-wrap {
  padding: 20px;
  border-bottom: 1px solid rgba(171, 186, 223, 0.1);

  .flex-wrap-item {
    position: relative;
    padding: 0 20px;
    border-right: 1px solid rgba(171, 186, 223, 0.1);
    font-size: 14px;

    .text {
      color: #888888;
      font-size: 15px;
    }

    .btn {
      position: absolute;
      right: 5px;
      top: 2px;
    }
  }

  .flex-wrap-item:first-child {
    padding-left: 0;
  }

  .flex-wrap-item:last-child {
    padding-right: 0;
  }

  .title {
    margin-bottom: 5px;
    font-size: 14px;
  }
}

.other-info {
  padding: 20px;
  min-height: 328px;
}

.other-info :deep(.el-form-item--default) {
  margin-bottom: 0px;
}

.form-item-text {
  width: 100%;
  word-wrap: break-word;
  line-height: 20px;
  padding-right: 5px;
}

:deep(.top-right-btn) {
  top: 20px;
  right: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
}
</style>
