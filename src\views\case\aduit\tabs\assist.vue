<template>
  <div>
    <el-form
      class="form-content h-50 mt20"
      :class="{ 'h-auto': showSearch }"
      :model="queryParams"
      label-position="right"
      :label-width="100"
      ref="queryRef"
      :inline="true"
    >
      <el-form-item label="案件ID" prop="caseId">
        <el-input
          style="width: 336px"
          v-model="queryParams.caseId"
          placeholder="请输入案件ID"
          clearable
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>
      <el-form-item label="申请人">
        <el-input
          style="width: 336px"
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="antiShake(handleQuery)"
        />
      </el-form-item>

      <!-- <el-form-item label="审核时间">
        <el-date-picker
          style="width: 336px"
          v-model="queryParams.examineTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item> -->
      <el-form-item label="申请时间" style="width: 336px">
        <el-date-picker
          style="width: 336px"
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <el-row class="mb40 mt10">
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <div class="handle-div mt20 mb10">
      <el-button
        v-if="activeTab === '0' && checkPermi([`case:aduit:pass:back`])"
        type="primary"
        plain
        :disabled="loading || ids.length == 0"
        @click="handle(0)"
        >通过</el-button
      >
      <el-button
        v-if="activeTab === '0' && checkPermi([`case:aduit:unpass:back`])"
        type="primary"
        plain
        :disabled="loading || ids.length == 0"
        @click="handle(1)"
        >不通过</el-button
      >
    </div>
    <!-- <selectedAll
      ref="selectedAllRef"
      :selectedArr="selectedArr"
      :dataList="caseList"
    >
      <template #content>
        <span class="case-data-list"
          >案件数量：<i class="danger">{{ caseNum }}</i></span
        >
      </template>
    </selectedAll> -->
    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.code"
        :label="item.info"
        :name="item.code"
      />
    </el-tabs>
    <el-table
      v-loading="loading"
      class="multiple-table"
      ref="multipleTableRef"
      :data="caseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="50"
        :selectable="checkSelectable"
        align="center"
      />
      <el-table-column
        label="案件ID"
        align="center"
        key="caseId"
        prop="caseId"
        v-if="columnsMap[0].visible"
        :width="columnsMap[0].width"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label
                class="ml5"
                v-if="row.label && row.label != 7"
                :code="row.label"
              />
            </el-tooltip>
            <el-button type="text" @click="toDetails(row, $index)">
              {{ row.caseId }}</el-button
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="委托方"
        align="center"
        prop="entrustingPartyName"
        v-if="columnsMap[1].visible"
        :width="columnsMap[1].width"
      />
      <el-table-column
        label="批次号"
        align="center"
        prop="batchNo"
        v-if="columnsMap[2].visible"
        :width="columnsMap[2].width"
      />
      <el-table-column
        label="客户姓名"
        align="center"
        key="clientName"
        prop="clientName"
        v-if="columnsMap[3].visible"
        :width="columnsMap[3].width"
      />
      <el-table-column
        label="委案金额"
        align="center"
        prop="entrustMoney"
        :width="columnsMap[4].width"
        v-if="columnsMap[4].visible"
      />
      <el-table-column
        label="申请原因"
        align="center"
        prop="reason"
        :width="columnsMap[5].width"
        v-if="columnsMap[5].visible"
      />
      <!-- <el-table-column
        label="委案日期"
        align="center"
        prop="entrustingCaseDate"
        :width="columnsMap[3].width"
        v-if="columnsMap[3].visible"
      />
      <el-table-column
        label="退案日期"
        width="180"
        align="center"
        prop="returnCaseDate"
        v-if="columnsMap[4].visible"
      >
      </el-table-column> -->

      <el-table-column
        label="申请人"
        align="center"
        prop="employeeName"
        v-if="columnsMap[6].visible"
        :width="columnsMap[6].width"
      />
      <el-table-column
        label="协助调解人"
        width="180"
        align="center"
        prop="applyTargetName"
        v-if="columnsMap[7].visible"
      >
      </el-table-column>
      <el-table-column
        label="申请时间"
        width="180"
        align="center"
        prop="createTime"
        v-if="columnsMap[8].visible"
      >
      </el-table-column>
      <!-- <el-table-column
        label="申请原因"
        align="center"
        v-if="columnsMap[8].visible"
      >
        <template #default="{ row }">
          <Tooltip :content="row.remarks" :length="15" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="协助调解状态"
        width="180"
        align="center"
        prop="status"
        v-if="columnsMap[9].visible"
      >
      </el-table-column>

      <!-- <el-table-column
        label="审核状态"
        v-if="columnsMap[11].visible"
        align="center"
      >
        <template #default="{ row, $index }">
          <el-popover
            placement="bottom"
            :width="500"
            :ref="`popover-${$index}`"
            trigger="click"
          >
            <template #reference>
              <el-button @click="showPopover(row)" type="text">
                {{ nowAuditStatusEnum[row.approveState] }}
              </el-button>
            </template>
            <el-table :data="gridData">
              <el-table-column
                width="200"
                property="approveTime"
                label="处理时间"
              />
              <el-table-column
                width="100"
                property="reviewer"
                label="处理人"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                width="100"
                property="approveStart"
                :formatter="approveStartFor"
                label="处理状态"
              />
              <el-table-column
                width="100"
                property="reason"
                :formatter="reasonFor"
                label="原因"
              />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        label="审核时间"
        align="center"
        prop="examineTime"
        v-if="columnsMap[10].visible"
      />
      <el-table-column
        label="审批人"
        align="center"
        prop="reviewer"
        v-if="columnsMap[10].visible"
      /> -->
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="row.status === '待处理'"
            v-hasPermi="['case:aduit:pass:back']"
            type="text"
            :disabled="loading"
            @click="handle(0, row)"
          >
            通过
          </el-button>
          <el-button
            v-if="row.status === '待处理'"
            v-hasPermi="['case:aduit:unpass:back']"
            type="text"
            :disabled="loading"
            @click="handle(1, row)"
          >
            不通过
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <imagePreview
      ref="imagePreviewRef"
      v-show="false"
      :src="srcUrl"
      :width="20"
      :height="20"
    />
    <!-- 审核不通过 -->
    <el-dialog
      title="审核不通过"
      v-model="open"
      width="600px"
      :before-close="cancel"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="formRef">
        <el-form-item
          label="原因"
          prop="reason"
          :rules="[{ required: true, message: '请输入原因', trigger: 'blur' }]"
        >
          <el-input
            v-model="form.reason"
            type="textarea"
            maxlength="300"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="notPass">
            确定审核
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="Aduit">
import { WriteBack, selectApproveProceSix } from "@/api/case/aduit/aduit";
import { nowAuditStatusEnum } from "@/utils/enum";
import {replaceNull} from "@/utils/common"
import {
  getApprovalList,
  agreeApproval,
  notPassApproval,
} from "@/api/approval";

import { checkPermi } from "@/utils/permission";

//全局数据
const { proxy } = getCurrentInstance();
const router = useRouter();
//表单配置信息
const showSearch = ref(false);
//表格配置数据
const loading = ref(false);
const total = ref(0);
const tabList = ref([
  { code: "0", info: "待处理" },
  { code: "2", info: "已同意" },
  { code: "3", info: "未同意" },
  { code: "all", info: "全部" },
]);
//表格数据
const caseList = ref([]);
const gridData = ref([]);
const ids = ref([]); //列表选中id集合
const single = ref(true); //是否可操作
const selectedArr = ref([]); //列表选中集合

const settleState = ref("all");
const activeTab = ref("0");
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 50,
    approveStarts: undefined,
  },
});
const { queryParams } = toRefs(data);
//多选列表
const examineStateList = ref([]);
//列显示/隐藏
const columns = ref([
  { key: 0, label: `案件ID`, visible: true, width: 80 },
  { key: 1, label: `委托方`, visible: true, width: 150 },
  { key: 2, label: `批次号`, visible: true, width: 250 },
  { key: 3, label: `客户姓名`, visible: true, width: 100 },
  { key: 4, label: `委案金额`, visible: true, width: 100 },
  { key: 5, label: `申请原因`, visible: true, width: 100 },
  { key: 6, label: `申请人`, visible: true, width: 100 },
  { key: 7, label: `协助调解人`, visible: true, width: 100 },
  { key: 8, label: `申请时间`, visible: true, width: 150 },
  { key: 9, label: `协助调解状态`, visible: true, width: 150 },
  // { key: 10, label: `处理时间`, visible: true, width: 150 },
  // { key: 11, label: `审核状态`, visible: true, width: 150 },
]);

// 创建columnsMap计算属性，方便通过key访问列配置
const columnsMap = computed(() => {
  const map = {};
  columns.value.forEach((column) => {
    map[column.key] = column;
  });
  return map;
});

const rangfiles = ["examineTime", "createTime"];
//弹窗属性
const open = ref(false);
const form = ref({});
const subloading = ref(false);
const srcUrl = ref("");

//获取列表
function getList() {
  loading.value = true;
  queryParams.value.approveProcessState =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.approveState =
    activeTab.value == "all" ? undefined : activeTab.value;
  queryParams.value.repaymentType =
    settleState.value == "all" ? undefined : settleState.value;
  let req = {
    approveCode: "teamwork",
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    approveProcessState: queryParams.value.approveProcessState,
    approveState: queryParams.value.approveProcessState,
    approveData: {
      ...proxy.addFieldsRange(queryParams.value, rangfiles),
    },
  };
  getApprovalList(req)
    .then((res) => {
      caseList.value = replaceNull(res.rows);
      total.value = res.total;
      loading.value = false;
    })
    .catch((err) => {
      loading.value = false;
    });
}
provide("getList", Function, true);
getList();

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 50,
    approveStarts: undefined,
    caseId: undefined,
    applicant: undefined,
    examineTime: [],
    createTime: [],
  };
  getList();
  examineStateList.value = [];
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId;
  let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = {
    query: queryChange,
    type: "caseAudit",
  };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({
    path: `/case/aduit-detail/aduitDetails/${caseId}`,
    query: { query: { type: "caseAudit" } },
  });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.approveId);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//tab切换
function tabChange() {
  getList();
}

//气泡框展示
function showPopover(row) {
  let req = { applyId: row.approveId };
  selectApproveProceSix(req)
    .then((res) => {
      gridData.value = res.data;
    })
    .catch(() => {
      loading.value = false;
    });
}

//案件状态 0-通过 1-不通过 2-待处理
function approveStartFor(row) {
  const stutasEnum = {
    0: "已同意",
    1: "未同意",
    2: "待处理",
    3: "已完成",
    5: "已退案关闭",
  };
  return stutasEnum[row.approveStart];
}

//通过原因
function reasonFor(row) {
  return row.refuseReason ? row.refuseReason : `--`;
}

//审核操作
function handle(type, row) {
  let req = {
    approveCode: "teamwork",
    approveIds: row ? [row.approveId] : ids.value,
  };
  if (req.approveIds.length === 0)
    return proxy.$modal.msgWarning("请选择操作的案件！");
  if (0 === type) {
    //通过
    proxy.$modal
      .confirm("此操作将通过选中的申请, 是否继续?")
      .then(function () {
        loading.value = true;
        return agreeApproval(req);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功！");
      })
      .catch((err) => {
        loading.value = false;
      });
  } else {
    reset();
    Object.assign(form.value, req);
    open.value = true;
  }
}

//重置取消审核表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    reason: undefined,
  };
}

//取消审核不通过
function cancel() {
  if (subloading.value) return false;
  reset();
  open.value = false;
}

//提交审核不通过
function notPass() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      form.value.reason =
        form.value.reason == "" ? undefined : form.value.reason;
      subloading.value = true;
      notPassApproval(form.value)
        .then(() => {
          subloading.value = false;
          proxy.$modal.msgSuccess("操作成功！");
          getList();
          cancel();
        })
        .finally(() => {
          subloading.value = false;
        });
    }
  });
}

// 表格是否可以选择
function checkSelectable() {
  return !proxy.$refs["selectedAllRef"]?.isAll;
}
const caseNum = computed(() => {
  return proxy.$refs["selectedAllRef"]?.isAll
    ? total.value
    : selectedArr.value.length;
});
const allQuery = computed(() => {
  return proxy.$refs["selectedAllRef"]?.isAll;
});

watch(caseList, (newval, preval) => {
  if (newval.length > 0) {
    //处理禁用表格复选框时无法选中的情况
    if (allQuery.value) {
      // allQuery.value = false;
      nextTick(() => {
        caseList.value.forEach((item, index) => {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        });
      });
    } else {
      nextTick(() => {
        caseList.value?.forEach((item, index) => {
          if (ids.value?.includes(item.id)) {
            proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
          }
        });
      });
    }
  }
});
</script>
<style lang="scss" scoped>
.h-50 {
  overflow: hidden;
  height: 50px;
}

.h-auto {
  height: auto !important;
}

.text-flex {
  display: inline-flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
}

.text-flex .text-danger {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.form-content {
  .el-form-item {
    width: 30% !important;
  }
}

.top-right-btn {
  z-index: 1;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>
