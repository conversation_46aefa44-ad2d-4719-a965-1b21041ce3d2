import request from '@/utils/request'

//获取还款渠道下拉
export function selectRepaymentSetup(query) {
  return request({
    url: '/collection/selectRepaymentSetup',
    method: 'get'
  })
}

//获取还款渠道必填字段
export function getRegisterPayment(id) {
  return request({
    url: '/collection/getRegisterPayment',
    method: 'get',
    params: {id: id}
  })
}

//案件回款
export function insertPayment(data) {
  return request({
    url: '/collection/insertPayment',
    method: 'post',
    data: data
  })
}