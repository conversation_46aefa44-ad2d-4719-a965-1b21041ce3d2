<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb10">
            <el-col :span="1.5">
                <el-button v-hasPermi="['assets:oftentpl:add']" type="primary" plain icon="Plus"
                    @click="addTpl">创建常用模版</el-button>
            </el-col>
        </el-row>

        <div class="info-tip mb10">
            <el-icon class="info-tip-icon" :size="16"><warning-filled /></el-icon>
            <div>
                <p>
                    设置完模版后，可在转让方管理流程中创建转让方导入信息中选中【常用模版】，快捷进行操作！
                </p>
            </div>
        </div>

        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
            <el-table-column label="模版名称" align="center" prop="name" />
            <el-table-column label="操作人" align="center" prop="updateBy">
                <template #default="{ row }">
                    {{ row.updateBy || row.createBy }}
                </template>
            </el-table-column>
            <el-table-column label="操作时间" align="center" prop="updateTime">
                <template #default="{ row }">
                    {{ row.updateTime || row.createTime }}
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="state">
                <template #default="{ row }">
                    <el-switch class="myswitch" v-model="row.state" active-color="#2ECC71" active-value="0"
                        inactive-value="1" active-text="开" inactive-text="关"
                        v-if="checkPermi(['assets:oftentpl:status'])" @change="stateChange(row)"></el-switch>
                    <span v-else>{{ row.state == 0 ? '开启' : '关闭' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button v-hasPermi="['assets:oftentpl:edit']" type="text" 
                        @click="edit(row.id)">编辑模板</el-button>
                    <el-button v-hasPermi="['assets:oftentpl:downTpl']" type="text" 
                        @click="downTpl(row)">下载导入模版</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="Oftentpl">
import { getOftenTpl, updateState } from "@/api/assets/oftentpl";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
});

const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

//获取列表
function getList() {
    loading.value = true;
    getOftenTpl(queryParams.value)
        .then((res) => {
            dataList.value = res.rows;
            total.value = res.total;
        })
        .finally(() => {
            loading.value = false;
        });
}

//创建模板
function addTpl() {
    const path = route.path
    router.push({ path: "/assets/oftentpl-handle/handeloftenTpl",query:{path} });
}

//编辑模板
function edit(id) {
    const path = route.path
    router.push({ path: "/assets/oftentpl-handle/handeloftenTpl", query: { id: id,path } });
}

//状态修改
function stateChange(row) {
    updateState({
        id: row.id,
        state: row.state,
    })
        .then((res) => {
            //proxy.$modal.msgSuccess("修改成功");
        })
        .catch(() => {
            //getList();
            row.state = row.state === "0" ? "1" : "0";
        });
}
getList();

//模板下载
function downTpl(row) {
    proxy.download(
        "/teamTemplates/downloadTemplate",
        { id: row.id },
        `tpl_${row.name}.xlsx`,
        { gateway: 'cis' }
    );
}
</script>

<style scoped></style>