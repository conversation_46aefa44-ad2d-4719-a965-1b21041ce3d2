import { getToken } from "@/utils/auth";
import store from "@/store";
import { ElMessageBox } from "element-plus";
import { ElNotification } from "element-plus";
import { getWebSocketAddress } from "./common";
//websocket对象
let ws = undefined;
//是否需要重连
let lockReturn = false;
//超时状态
let timeoutNum = null;
//超时对象
let timeoutObj = null;
//服务时间
let serverTimeoutObj = null;
//链接地址
const VITE_APP_WS_BASE_URL = import.meta.env.VITE_APP_WS_BASE_URL 
//超时时间
const timeout = 60 * 1000 * 5;
//信息数组
const wsMessageList = ref([]);
//消息开始
const messageStart = ref(false);
//定义消息字段
const message = {
  state: {
    count: 0,
    WebSocketUrl: undefined
  },
  mutations: {
    SET_COUNT: (state, count) => {
      state.count = count;
    },
    SET_WEBSOCKETURL(state, value) {
      state.WebSocketUrl = value
    }
  },
  actions: {
    getWebSocketAddressAction({ commit, dispatch }) {
      // getWebSocketAddress().then(res => {
      //   commit('SET_WEBSOCKETURL', res.data)
      // })
    },
  }
};

//此socket主要为了接受消息
export function webSocketContent() {
  //返回的信息
  //websocket链接；
  if (!getToken()) webSocketContent();
  return new Promise((resolve) => {
    const { message } = store.state
    const WebSocketUrl = message.WebSocketUrl ? message.WebSocketUrl : VITE_APP_WS_BASE_URL
    ws = new WebSocket(WebSocketUrl + `/${getToken()}`);
    //开启方法
    ws.onopen = (message) => {
      console.log("Websocket通信开始，开始发送消息！");
      lockReturn = true;
      //开启成功后发送心跳包
      startWsHeartBeat();
      ws.send("heartCheck");
    };
    //消息接收
    ws.onmessage = (message) => {
      messageStart.value = true;
      resetHeartBeat();
      let reminderMode = JSON.parse(message?.data)?.reminderMode;
      if (reminderMode === 1001) { // 通话相关状态
        let { data } = JSON.parse(message.data);
        if (store.getters.callState != data.callStatus) {
          store.commit('call/SET_CALL_STATE', data.callStatus)
          // if (data.callStatus == 0) { //结束通话
          //   store.dispatch('call/endTime')
          // }
        }

        if (data.phone) {
          store.commit('call/SET_CALLING_PHONE', data.phone)
        }
      } else if (reminderMode === 1002) {
        console.log("reminderMode");
        let { data } = JSON.parse(message.data);
        store.commit('call/SET_INCOMING_CALL', data);
      } else if (reminderMode === 1003) {
        console.log("caseManage reminderMode");
        let { data } = JSON.parse(message.data);
        store.commit('call/SET_CASE_INCOMING_CALL', data);
      } else if (reminderMode === 1004) {
        console.log("workPhone reminderMode");
        let { data } = JSON.parse(message.data);
        store.commit('call/SET_WORK_PHONE_INCOMING_CALL', data);
      }
      if (message?.data) {
        if (reminderMode == 1) {
          wsMessageList.value.push(message?.data);
        }
        // wsMessageList.value.forEach(item => {
        //     let jsonItem = JSON.parse(item)
        //     if (jsonItem.reminderMode == 1) {
        //         reminderMode = true;
        //     }
        // });
        let res = {
          number: JSON.parse(message?.data).data.unreadNum,
          wsMessageList: wsMessageList.value,
          reminderMode: reminderMode,
        };
        store.commit("SET_COUNT", JSON.parse(message?.data).data.unreadNum);
        openWindow(res);
        resolve(res);
      }
    };
    //错误方法
    ws.onerror = (message) => {
      console.log("WebSocket通信错误！");
      console.log("错误原因：" + message);
    };
    //通信关闭
    ws.onclose = (message) => {
      console.log("websocket关闭");
      //重启webSocket
      lockReturn = false;
      reContent();
    };
  })
}

//重启
function reContent() {
  //判断是否连接中
  if (lockReturn) return;
  //链接
  lockReturn = true;
  //清除超时状态
  timeoutNum && clearTimeout(timeoutNum);
  //状态赋值
  timeoutNum = setTimeout(() => {
    webSocketContent();
    lockReturn = false;
  }, 3000);
  wsMessageList.value = [];
  messageStart.value = false;
}
//开始发送心跳包
function startWsHeartBeat() {
  timeoutObj && clearTimeout(timeoutObj);
  serverTimeoutObj && clearTimeout(serverTimeoutObj);
  timeoutObj = setInterval(() => {
    if (ws.readyState != 1) {
      reContent();
    }
  }, timeout);
}
//重置websocket心跳
function resetHeartBeat() {
  clearTimeout(timeoutObj);
  clearTimeout(serverTimeoutObj);
  startWsHeartBeat();
}

//根据消息即时弹窗
function openWindow(res) {
  if (res.number > 0 && res.reminderMode == 1) {
    closeNotice();
    ElNotification({
      title: `${res.number == 1 ? JSON.parse(res.wsMessageList[0])?.title : "消息通知"
        }`,
      message: h(
        "div",
        {
          style: "width: 250px;text-align:left;padding:20px 0px 10px 0px",
        },
        [
          h(
            "div",
            null,
            [
              h(
                "span",
                null,
                `${res.number == 1
                  ? JSON.parse(res.wsMessageList[0])?.content
                  : `您有${res.number ?? 0}条未读消息，请及时处理！`
                }`
              ),
            ]
          ),
          h(
            "div",
            {
              style: "color: #409EFF;cursor: pointer;display:block;margin-top:30px;text-align:right",
            },
            [
              h(
                "button",
                {
                  class: 'el-button el-button--primary el-button--default',
                  style: 'background-color: #fff;color:#409eff;--el-button-bg-color:#409eff;--el-button-border-color:#409eff;--el-button-hover-bg-color:rgb(102, 177, 255);--el-button-hover-border-color:rgb(102, 177, 255);--el-button-active-bg-color:rgb(58, 142, 230);--el-button-active-border-color:rgb(58, 142, 230);',
                  onclick: () => closeNotice()
                },
                "取消",
              ),
              h(
                "span",
                {
                  class: 'el-button el-button--primary el-button--default',
                  style: 'background-color: #409eff;--el-button-bg-color:#409eff;--el-button-border-color:#409eff;--el-button-hover-bg-color:rgb(102, 177, 255);--el-button-hover-border-color:rgb(102, 177, 255);--el-button-active-bg-color:rgb(58, 142, 230);--el-button-active-border-color:rgb(58, 142, 230);',
                  onclick: () => goToMessageList()
                },
                "查看",
              )
            ]

          ),
        ],
      ),
      position: "bottom-right",
      type: "warning",
      duration: 0,
    });
  }
}

//跳转首页信息
function goToMessageList() {
  window.location.href = "/message/homeMessage";
}

//关闭消息
function closeNotice() {
  ElNotification.closeAll()
}

export default message;
