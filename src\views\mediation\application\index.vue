<template>
  <div class="app-container">
    <el-tabs tab-position="left" v-model="tabActive">
      <el-tab-pane
        name="0"
        label="我的通话"
      >
        <!-- 我的回款 -->
        <mycall v-if="tabActive === '0'" ref="mycallRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import mycall from "./tab_page/mycall.vue";

const tabActive = ref('0')

</script>

<style scoped></style>
