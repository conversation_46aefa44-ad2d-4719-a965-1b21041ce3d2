<template>
  <div class="app-container">
    <el-tabs tab-position="left" v-model="tabActive">
      <el-tab-pane
        name="0"
        label="我的通话"
      >
        <!-- 我的回款 -->
        <mycall v-if="tabActive === '0'" ref="mycallRef" />
      </el-tab-pane>

      <el-tab-pane
        name="1"
        label="我的短信"
      >
        <myMessage v-if="tabActive === '1'" ref="myMessageRef" />
        </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { checkPermi } from "@/utils/permission";
import mycall from "./tab_page/mycall.vue";
import myMessage from "./tab_page/myMessage.vue";

const tabActive = ref('0')

</script>

<style scoped></style>
