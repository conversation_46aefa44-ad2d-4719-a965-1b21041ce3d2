// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// switch
.myswitch .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

.systemBox{
  position: fixed;
  right: 10px;
  bottom: 10px;
}

.myswitch .el-switch__label--left {
  z-index: 9;
  left: -10px;
}

.myswitch .el-switch__label--right {
  z-index: 9;
  left: -34px;
}

.myswitch .el-switch__label.is-active {
  display: block;
  color: #FFFFFF;
  text-align: right;
}

.myswitch.el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 50px !important;
}

.myswitch .el-switch__label *{
  font-size: 12px;
  position: relative;
  left: 2px;
}



.el-select .el-select__tags-text{
  max-width: 100px !important;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .icon-flag{
  position: unset !important; 
  right: unset !important; 
  top: unset !important;  
  font-size: 14px !important;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}

// steps
.wcc-el-steps {
  padding-top: 44px;
}

// .wcc-el-steps .el-step__head.is-wait,
.wcc-el-steps .el-step__head.is-process {
  color: #967474;
  border-color: #e8e8e8;
}

.wcc-el-steps .el-step__icon {
  width: 40px;
  height: 40px;
}

.wcc-el-steps .el-step__icon-inner {
  font-weight: normal;
  font-size: 20px;
}

.wcc-el-steps .el-step__title.is-wait {
  color: #888888;
  font-weight: normal;
}

.wcc-el-steps .el-step__title.is-process{
  color: var(--el-color-primary);
  font-weight: normal;
}

.wcc-el-steps .el-step__description.is-process,
.wcc-el-steps .el-step__description.is-wait {
  color: #bbbbbb;
}

.wcc-el-steps .el-step__head.is-process,
.wcc-el-steps .el-step__head.is-finish {
  color: #ffffff;
  border-color: #326fea;
}

.wcc-el-steps .el-step__head.is-process .el-step__icon,
.wcc-el-steps .el-step__head.is-finish .el-step__icon {
  background-color: #326fea;
}

.wcc-el-steps .el-step.is-center .el-step__line {
  left: 60%;
  right: -40%;
}

.wcc-el-steps .el-step__head.is-finish .el-step__line {
  background-color: #326fea;
}
.wcc-el-steps .el-step__head .el-step__line-inner{
  border-color: unset;
}

.wcc-el-steps.un-position .el-step.is-center .el-step__line {
  left: 60%;
}

.wcc-el-steps .el-step.is-horizontal .el-step__line {
  top: 19px;
}

.wcc-el-steps.un-position .el-step__main {
  position: unset;
}
// 解决el-input设置类型为number时，中文输入法光标上移问题
.el-input {
  input[type="number"] {
    line-height: 1px !important;
  }
}

.multiple-table .el-table__header-wrapper .el-table-column--selection .el-checkbox {
  display: none;
}

.el-popper.is-customized {
  // background-color: #c9e7ff;
  // opacity: .58;
  // color: #000000;
  background-color: #737375;
  color: #ffffff;
}

.el-popper.is-customized .el-popper__arrow::before {
  // background-color: #c9e7ff;
  // opacity: .58;
  // color: #000000;
  background-color: #737375;
  color: #ffffff;
}