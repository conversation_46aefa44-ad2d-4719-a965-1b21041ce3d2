<template>
    <el-dialog title="设置状态" v-model="open" width="650px" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
            <el-form-item label="已选员工" :error="validSelected" required>
                <div class="selected-user" v-for="(item,index) in selectedUsers" :key="item.id">
                    <div>
                        <span class="name">{{item.employeeName}}</span>
                        <span class="role">{{item.departments+' / '+item.theRole}}</span>
                    </div>
                    <el-icon class="close" @click="remove(index)"><close /></el-icon>
                </div>
            </el-form-item>
            <el-form-item label="账号状态" prop="accountStatus">
                <el-radio-group v-model="form.accountStatus">
                    <el-radio :label="0">启用</el-radio>
                    <el-radio :label="1">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
    import { getRoleOptions } from '@/api/system/roles'
    import { updateAccountStatus } from '@/api/system/user'

    const { proxy } = getCurrentInstance();
    const emit = defineEmits(['getList']);
    const selectedUsers = ref([]);
    const validSelected = ref(undefined);
    
    const open = ref(false)
    const loading = ref(false);
    const roleOptions = ref([]);

    const data = reactive({
        form: {
            accountStatus:0
        },
        rules: {
            accountStatus: [{required: true, message: '请选择账号状态', trigger: 'change'}]
        }
    })
    const { form , rules } = toRefs(data);

    function opendialog(selecteds) {
        getRoleOptions().then(res => {
            roleOptions.value = res.data;
            reset();
            selectedUsers.value = JSON.parse(JSON.stringify(selecteds))
            open.value = true;
        })
    }

    //删除已选员工
    function remove(index) {
        selectedUsers.value.splice(index,1)
    }

    //提交
    function submitForm() {
        loading.value = true;
        try {
            if (selectedUsers.value.length === 0) {
                validSelected.value = '请选择员工';
                throw new Error("请选择员工");
            } else {
                validSelected.value = undefined;
            }
            proxy.$refs['formRef'].validate(valid => {
                if (valid) {
                    let req = [];
                    let form_obj = JSON.parse(JSON.stringify(form.value))

                    selectedUsers.value.map(item => {
                        let obj = {id: item.id}
                        Object.assign(obj,form_obj)
                        req.push(obj)
                    })
                    updateAccountStatus(req).then(res => {
                        proxy.$modal.msgSuccess('修改成功！');
                        emit('getList');
                        cancel();
                    }).finally(() => {
                        loading.value = false;
                    })
                } else {
                    loading.value = false
                }
            })
        } catch (error) {
            loading.value = false;
            // throw error
        }
            
            
    }

    //重置表单
    function reset() {
        proxy.resetForm("formRef");
        form.value = {
            accountStatus: 0,
        }
    }

    //取消
    function cancel() {
        reset();
        open.value = false;
    }

    defineExpose({
        opendialog
    })
</script>

<style lang="scss" scoped>
    .selected-user {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;
        .name {
            font-weight: 450;
            margin-right: 10px;
        }
        .role {
            font-size: 12px;
            color: #888888;
        }
        .close{
            cursor: pointer;
        }
    }
</style>