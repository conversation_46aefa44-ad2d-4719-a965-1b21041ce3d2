import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'
import Inspect from 'vite-plugin-inspect'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    build: {
      minify: 'esbuild',
      chunkSizeWarningLimit: 600, //文件大于600kb警告
      rollupOptions: {
        output: {
          //文件分割成更小的模块
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString();
            }
          }
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ],
      },
    },
    plugins: [
      createVitePlugins(env, command === 'build'),
      Inspect(),
    ],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      host: '0.0.0.0',
      port: 8098,
      open: true,
      proxy: {
        '/dev-api': {
          // target: 'http://*************:8080',// 本地环境
          // target: 'http://************:8070/prod-api',// 个贷测试
          // target: 'https://appeal-uat.amcmj.com/prod-api',// 演示环境
          // target: 'https://ts-appeal.amcmj.com/prod-api',// 个贷测试
          // target: 'https://appeal-uat.amcmj.com/prod-api',// 演示环境
          // target: 'https://appeal-prod.amcmj.com/prod-api',// 正式环境
          target: 'https://saas-appeal-test.amcmj.com/prod-api',// saas标准版
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        }
      },
    },
  }
})
