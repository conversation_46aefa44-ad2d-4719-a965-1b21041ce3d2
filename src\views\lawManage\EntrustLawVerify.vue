<template>
    <div class='fxn-body'>
        <div class="fxn-search">
            <el-form :inline="true" :model="SearchForm" :rules="SearchRules" ref="SearchForm">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="案件ID" prop="id">
                            <el-input v-model="SearchForm.id" placeholder="请输入案件ID"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="审核时间" prop="deal_time">
                            <el-date-picker v-model="SearchForm.deal_time" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="申请时间" prop="add_time">
                            <el-date-picker v-model="SearchForm.add_time" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="申请人" prop="add_name">
                            <el-input v-model="SearchForm.add_name" placeholder="请输入债务人"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="fxn-search-btn">
                    <el-button @click="resetForm('SearchForm')">重置</el-button>
                    <el-button type="primary" @click="getDataList(1,10,true)">查询</el-button>
                </div>
            </el-form>
        </div>
        <!-- Search End -->

        <div class="fxn-section">
            <!-- <div v-show="activeName == '1'" class="fxn-operate">
                <el-button type="primary" @click="Examine(2)">通过</el-button>
                <el-button @click="Examine(3)">不通过</el-button>
            </div> -->
            <!-- Operate End -->
            
            <div class="fxn-tabs">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane v-for="(item,index) in StatusTabs" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
                </el-tabs>
            </div>
            <!-- Tabs End -->

            <el-table class="fxn-caselist-table" v-loading="loading" ref="multipleTable" :data="DataList" :header-cell-style="{background:'#EEEFF4',color:'#888888',width: '100%'}" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="42"></el-table-column>
                <el-table-column label="案件ID" align="center" width="80">
                    <template slot-scope="scope">
                        <router-link :to="{path:'/CaseDetail',query: {id: scope.row.case_id,noncollector: true}}" class="fxn-text-blue" tag="a" target="_blank">{{scope.row.case_id}}</router-link>
                    </template>
                </el-table-column>
                <el-table-column label="批次号" align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.bill_no == '' || scope.row.bill_no == undefined">--</span>
                        <span v-else>{{scope.row.bill_no}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="case_name" label="客户姓名" align="center"></el-table-column>
                <el-table-column label="委案日期" align="center">
                    <template slot-scope="scope">
                        {{scope.row.apply_entrust_start | dateTime}}
                    </template>
                </el-table-column>
                <el-table-column label="退案日期" align="center">
                    <template slot-scope="scope">
                        {{scope.row.apply_entrust_end | dateTime}}
                    </template>
                </el-table-column>
                <el-table-column label="申请时间" align="center">
                    <template slot-scope="scope">
                        {{scope.row.add_time | dateTime(1)}}
                    </template>
                </el-table-column>
                <el-table-column prop="add_name" label="申请人" align="center"></el-table-column>
                <el-table-column prop="reason" label="申请原因" align="center"></el-table-column>
                <el-table-column prop="status" label="审核状态" align="center" :formatter="ChangeStatus"></el-table-column>
                <el-table-column prop="deal_time" label="审核时间" align="center">
                    <template slot-scope="scope">
                        {{scope.row.deal_time | dateTime(1)}}
                    </template>
                </el-table-column>
                <el-table-column prop="deal_name" label="审核人" align="center"></el-table-column>
            </el-table>
            <!-- Table End -->

            <div class="fxn-page">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="this.page" :page-sizes="[10, 50, 500, 1000]" :page-size="this.perPage" layout="total, sizes, prev, pager, next, jumper" :total="this.count"></el-pagination>
            </div>
        </div>

        <!-- 审批 -->
        <el-dialog :title="verifyTitle" :visible.sync="dialogVerify">
            <el-form :model="VerifyForm" :rules="VerifyRules" ref="VerifyForm">
                <el-form-item label="审核原因" :label-width="formLabelWidth" prop="reason">
                    <el-input type="textarea" rows="5" v-model="VerifyForm.reason" placeholder="请输入审核原因"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
				<el-button @click="dialogVerify = false">取 消</el-button>
				<el-button type="primary" @click="SubmitExamine('VerifyForm')">确定审批</el-button>
			</div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        data(){
            return{
                count: 0,
                page: 1,
                perPage: 10,
                loading: false,

                //查询
                queryForm: {},
                SearchForm:{
                    id: '',
                    deal_time: '',
                    add_time: '',
                    add_name: '',
                },
                SearchRules:{},

                //Tabs
                activeName: '1',
                StatusTabs: [
                    {value: '1', label: '待审核'},
                    {value: '2', label: '已通过'},
                    {value: '3', label: '未通过'},
                    {value: '99', label: '全部'},
                    // {value: '98', label: '已完成'},
                ],

                DataList: [],               //列表

                //审批
                SelectionChange:[],         //选中的案件
                verifyTitle: '',
                dialogVerify:false,
                formLabelWidth: '120px',
                VerifyForm:{
                    id: '',
                    reason: '',
                    status: '',
                },
                VerifyRules:{},
            }
        },
        methods:{

            //tabs click
            handleClick() {
                this.getDataList(1,10,this.type);
            },

            //获取列表选中数据
            handleSelectionChange(val){
                this.SelectionChange = val;
            },

             //每页显示数据量变更
            handleSizeChange(val) {
                this.perPage = val;
                this.getDataList(this.page,this.perPage,this.type);
            },

            //页码变更
            handleCurrentChange(val) {
                this.page = val;
                this.getDataList(this.page,this.perPage,this.type);
            },

            //清空
            resetForm(){
                this.$refs.SearchForm.resetFields();
                this.getDataList();
            },

            //获取列表数据
            getDataList(page,perPage,type) {
                this.loading = true;
                this.type = type;
                this.page = page ? page : 1;
                this.perPage =  perPage ? perPage : 10;
                if (type) {
                    this.queryForm = {
                        page: this.page,
                        perPage : this.perPage,
                        type: 5,
                        source: 1,
                        case_id: this.SearchForm.id,
                        deal_time: String(this.SearchForm.deal_time),
                        add_time: String(this.SearchForm.add_time),
                        add_name: this.SearchForm.add_name,
                        status: this.activeName,
                    }
                } else {
                    this.queryForm = {
                        page: this.page,
                        perPage : this.perPage,
                        type: 5,
                        source: 1,
                        status: this.activeName,
                    }
                }
                this.get('/osapi/Help/applyList',this.queryForm)
                .then( res => {
                    let { code , msg , data } = res.data;
                    if (code != 1) {
                        this.$message({
                            type: 'error',
                            message: msg
                        })
                        this.loading = false;
                    } else {
                        this.DataList = res.data.data.data;
                        this.count = res.data.data.count;
                        this.page = res.data.data.page;
                        this.perPage = res.data.data.perPage;
                        this.loading = false;
                    }
                })
            },

            //审核
            Examine(status){
                this.verifyTitle = status == 2 ? '通过' : '不通过';
                let arr = this.SelectionChange
                if( arr == '' || arr == undefined){
                    this.$message({
                        message : '请选择需要审核的案件',
                        type: 'error'
                    })
                    return false;
                }    
                let multis = []
                for (var i=0;i<this.SelectionChange.length;i++){
                    multis.push(this.SelectionChange[i].id)
                }

                this.dialogVerify = true;
                this.VerifyForm.id = String(multis);
                this.VerifyForm.status = status;
            },

            //提交审核
            SubmitExamine(formName){
                this.post('/osapi/Help/applyDeal',{
                    type: 5,
                    id: this.VerifyForm.id,
                    status: this.VerifyForm.status,
                    result: this.VerifyForm.reason,
                })
                .then(res=>{
                    let { msg, code, data } = res.data
                    if( code != 1 ){
                        this.$message({
                            message : msg,
                            type: 'error'
                        })
                    }else{
                        this.$message({
                            type: 'success',
                            message: '审核成功!'
                        });
                        this.dialogVerify = false;
                        this.getDataList();
                    }
                })
            },

            //转换列表中==状态
            ChangeStatus (row){
                if(row.status == 1){
                    return '待审核'
                }else if(row.status == 2){
                    return '审核通过'
                }else if(row.status == 3){
                    return '审核不通过'
                }else if(row.status == 6){
                    return '已撤销'
                }
            },
        },
        mounted(){
            this.getDataList();
        },
        watch: {
            dialogVerify(val) {
                if (!val) {
                    this.$refs.VerifyForm.resetFields();
                }
            }
        }
    }
</script>

<style scoped>
</style>