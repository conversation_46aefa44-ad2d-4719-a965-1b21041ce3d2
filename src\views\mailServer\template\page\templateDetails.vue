<template>
    <el-dialog title="预览" v-model="open" width="650px" :before-close="cancel" append-to-body>
        <div class="case-pdf" v-loading="loading">
            <previewPdf v-if="pdfSrc && pdfSrc.length > 0" :pdfSrc="pdfSrc" :total="total"/>
        </div>
        <template #footer>
            <div class="dialog-footer" style="text-align:center">
                <el-button type="primary"  @click="cancel">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="TemplateDetails">
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
import { getPreview } from "@/api/lawyer/template";
//全局变量
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(false);
const pdfSrc = ref(undefined);
const total=ref(undefined)
//打开弹窗
function opendialog(id){
    loading.value = true
    let req = { id };
    // 获取预览
    getPreview(req).then((res) =>{
        pdfSrc.value = res.data.url;
        total.value = res.data.total;
        open.value = true;
        console.log(open.value)
    }).finally(() => {
        loading.value = false;
    })
}

//获取预览
async function getPreviewData(id){
  
}

//关闭弹窗
function cancel(){
    open.value = false;
    pdfSrc.value = false
}

defineExpose({
  opendialog
});

</script>

<style scoped>
.case-pdf{
    width: 100%;
    height: 550px;
    overflow: auto;
    overflow-x: hidden;
}
</style>