<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="4" :xs="24">
                <div class="head-container">
                    <el-button type="primary" plain icon="Plus" v-hasPermi="['assets:side:add']" @click="addAssets"
                        style="margin-bottom: 8px">创建委托方</el-button>
                </div>
                <div class="head-container">
                    <el-input v-model="assetsName" placeholder="请输入名称" clearable prefix-icon="Search"
                        style="margin-bottom: 20px">
                    </el-input>
                </div>
                <div class="head-container">
                    <div class="home-style" @click="clickAll">
                        <el-icon>
                            <home-filled />
                        </el-icon>
                        <span class="ml5">全部</span>
                    </div>
                    <el-tree :data="assetsOptions" :props="{ label: 'label', children: 'children' }"
                        :expand-on-click-node="false" :filter-node-method="filterNode" node-key="id" ref="assetsTreeRef"
                        default-expand-all highlight-current @node-click="handleNodeClick">
                        <template #default="{ node, data }">
                            <span class="custom-tree-node">
                                <el-tooltip effect="light" :content="node.label" placement="right-start">
                                    <span class="el-tree-node__label">{{ node.label }}</span>
                                </el-tooltip>
                                <span v-if="isshowhandele(data)">
                                    <el-tooltip v-show="checkPermi(['assets:side:edit'])" content="修改委托方" placement="top">
                                        <el-button type="text" icon="Edit"
                                            @click="treeHandleEdit(data)"></el-button>
                                    </el-tooltip>
                                    <el-tooltip v-show="checkPermi(['assets:side:add'])" content="创建委托方" placement="top">
                                        <el-button type="text" icon="Plus"
                                            @click.stop="addTreeHandle(data)"></el-button>
                                    </el-tooltip>
                                    <el-tooltip v-show="checkPermi(['assets:side:del'])" content="删除委托方" placement="top">
                                        <el-button type="text" icon="DeleteFilled"
                                            @click="removeTreeHandle(data)"></el-button>
                                    </el-tooltip>
                                </span>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </el-col>
            <!-- 左侧委托方树结构 End -->
            <el-col :span="20" :xs="24">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" v-hasPermi="['assets:product:add']"
                            @click="addProduct">创建项目</el-button>
                    </el-col>
                    <right-toolbar @queryTable="getList" :columns="columns" :types="[2]"></right-toolbar>
                </el-row>

                <el-table v-loading="loading" :data="assetsList">
                    <el-table-column label="委托方ID" prop="ownerId" key="ownerId" align="center"
                        v-if="columns[0].visible" />
                    <el-table-column label="委托方名称" prop="ownerName" key="ownerName" align="center"
                        v-if="columns[1].visible" />
                    <el-table-column label="产品名称" prop="name" key="name" align="center" v-if="columns[2].visible"
                        show-overflow-tooltip />
                    <el-table-column label="案件总量" prop="caseTotalNum" key="caseTotalNum" align="center" sortable
                        v-if="columns[3].visible" show-overflow-tooltip>
                        <template #default="{ row }">
                            <!-- {{ formatAmountWithComma(row.caseTotalNum || 0) }} -->
                            {{ row.caseTotalNum ?? '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="委托总额（元）" prop="caseTotalMoney" key="caseTotalMoney" align="center" sortable
                        v-if="columns[4].visible" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ numFilter(row.caseTotalMoney || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="批次数量" prop="batchNumber" key="batchNumber" align="center" sortable
                        v-if="columns[5].visible" show-overflow-tooltip>
                        <template #default="{ row }">
                            <!-- {{ formatAmountWithComma(row.batchNumber || 0) }} -->
                            {{ row.batchNumber ?? '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作人" prop="updateBy" key="updateBy" align="center"
                        v-if="columns[6].visible">
                        <template #default="{ row }">
                            {{ row.updateBy || row.createBy }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作时间" prop="updateTime" key="updateTime" align="center"
                        v-if="columns[7].visible" show-overflow-tooltip />
                    <el-table-column label="状态" prop="state" key="state" align="center" v-if="columns[8].visible">
                        <template #default="{ row }">
                            <el-switch class="myswitch" v-model="row.state" active-color="#2ECC71" active-value="0"
                                inactive-value="1" v-if="checkPermi(['assets:product:status'])" active-text="开"
                                inactive-text="关" @change="stateChange(row)"></el-switch>
                            <span v-else>{{ row.state == 0 ? '开启' : '关闭' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="{ row }">
                            <el-dropdown trigger="hover" @command="handlesCommand" hide-on-click>
                                <span class="el-dropdown-link">
                                    操作栏
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item :command="{ data: row, func: toAsset }"
                                            v-if="checkPermi(['assets:product:batchNum'])">查看批次</el-dropdown-item>
                                        <el-dropdown-item v-if="checkPermi(['assets:product:downTpl'])"
                                            :command="{ data: row, func: downTplFile }">下载导入模板</el-dropdown-item>
                                        <el-dropdown-item :command="{ data: row, func: toAsset }"
                                            v-if="checkPermi(['assets:product:importNow'])">立即导入</el-dropdown-item>
                                        <el-dropdown-item v-if="checkPermi(['assets:product:edit'])"
                                            :command="{ data: row, func: editPro }">编辑</el-dropdown-item>
                                        <el-dropdown-item v-if="checkPermi(['assets:product:del'])"
                                            :command="{ data: row, func: removePro }">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>

        <!-- 添加修改委托方 -->
        <el-dialog :title="title" v-model="open" width="600px" append-to-body>
            <el-form :model="form" :rules="rules" ref="asstesRef" label-position="right" label-width="150px">
                <el-form-item label="委托方名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入委托方名称" style="width:280px;" show-word-limit maxlength="50"></el-input>
                </el-form-item>
                <el-form-item label="代号" prop="shortName">
                    <el-input v-model="form.shortName" placeholder="请输入委托方代号（纯字母）" style="width:280px;" show-word-limit maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="社会统一信用代码" prop="unifiedCode">
                    <el-input v-model="form.unifiedCode" placeholder="请输入社会统一信用代码" style="width:280px;" maxlength="100"
                        show-word-limit></el-input>
                </el-form-item>
                <el-form-item>
                    <p class="text-danger">注：请选择委托方准入公司全称（工商登记营业执照全称）</p>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" :loading="subloading" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Assetside">
import {
    assetOwnerTree,
    addAssetOwner,
    editAssetOwner,
    delAssetOwner,
    getProList,
    setProdStata,
    delProduct,
    getDictTransferor,
    addName,
} from "@/api/assets/assetside";

import { checkPermi } from "@/utils/permission";
import { numFilter, formatAmountWithComma } from "@/utils/common";

const router = useRouter();
const { proxy } = getCurrentInstance();

const assetsName = ref("");
const assetsOptions = ref([]);
const options = ref([]);
// 委托方创建修改
const data = reactive({
    form: {
        name: undefined,
        shortName: undefined,
        unifiedCode: undefined
    },
    rules: {
        name: [{ required: true, message: "请输入委托方名称", trigger: "blur" }],
        shortName: [{ required: true, pattern: /^[a-zA-z]+$/ , message: "请输入委托方代号（纯字母）", trigger: "blur" }],
    },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        ownerId: undefined,
    },
});
const subloading = ref(false);
const title = ref("");
const total = ref(0);
const open = ref(false);

const { form, rules, queryParams } = toRefs(data);

const loading = ref(false);
const assetsList = ref([]);
const currentOwnerId = ref(undefined);
// 列显隐信息
const columns = ref([
    { key: 0, label: `委托方ID`, visible: true },
    { key: 1, label: `委托方名称`, visible: true },
    { key: 2, label: `产品名称`, visible: true },
    { key: 3, label: `案件总量`, visible: true },
    { key: 4, label: `委托总额（元）`, visible: true },
    { key: 5, label: `批次数量`, visible: true },
    { key: 6, label: `操作人`, visible: true },
    { key: 7, label: `操作时间`, visible: true },
    { key: 8, label: `状态`, visible: true },
]);

//获取产品列表
function getList() {
    loading.value = true;
    console.log(queryParams.value);
    getProList(queryParams.value)
        .then((res) => {
            loading.value = false;
            total.value = res.total;
            assetsList.value = res.rows;
        })
        .catch(() => {
            loading.value = false;
        });
}
getList();

//新增委托方
function addAssets() {
    reset();
    title.value = "创建委托方";
    open.value = true;
}
//添加产品
function addTreeHandle(data) {
    currentOwnerId.value = data.id.split(":")[1];
    router.push({
        path: "/assets/assetside-add/addproduct/" + currentOwnerId.value,
    });
}

//委托方状态修改
function stateChange(row) {
    setProdStata({
        id: row.id,
        state: row.state,
    })
        .then((res) => {
        })
        .catch(() => {
            row.state = row.state === "0" ? "1" : "0";
        });
}

//是否显示操作资产树按钮
function isshowhandele(data) {
    if (data.id.split(":")[0] === "owner") {
        return true;
    } else {
        return false;
    }
}

//判断字符串是否全为大写字母
function isAllCaps(str) {
  return /^[A-Z]+$/.test(str);
}

// 添加/修改委托方提交按钮
function submitForm() {
    proxy.$refs["asstesRef"].validate((valid) => {
        if (valid) {
            subloading.value = true;
            if (form.value.id != undefined) {
                //修改
                options.value.forEach((item, index) => {
                    if (item.info == form.value.name) {
                        form.value.shortName = item.code
                    }
                })
                editAssetOwner(form.value)
                    .then((res) => {
                        subloading.value = false;
                        proxy.$modal.msgSuccess("修改成功！");
                        getTreeselect();
                        cancel();
                    })
                    .catch(() => {
                        subloading.value = false;
                    });
            } else {
                // options.value.forEach((item, index) => {
                //     if (item.info == form.value.name) {
                //         form.value.shortName = item.code
                //     }
                // })
                // if(!isAllCaps(form.value.shortName)) {
                //     proxy.$modal.msgWarning("代号需要字母大写");
                //     subloading.value = false;
                //     return;
                // }
                addAssetOwner(form.value)
                    .then((res) => {
                        subloading.value = false;
                        proxy.$modal.msgSuccess("添加成功！");
                        getTreeselect();
                        cancel();
                    })
                    .catch(() => {
                        subloading.value = false;
                    });
            }
        }
    });
}

//重置表单
function reset() {
    proxy.resetForm("asstesRef");
    form.value = {
        name: undefined,
        shortName: undefined,
        unifiedCode: undefined
    };
}

//取消按钮
function cancel() {
    open.value = false;
    reset();
}


//获取下拉树
function getTreeselect() {
    assetOwnerTree().then((res) => {
        assetsOptions.value = res.data;
    });
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

// 点击项目
function clickAll() {
    queryParams.value.pageNum = 1;
    queryParams.value.ownerId = undefined;
    queryParams.value.id = undefined;
    //清空选中
    proxy.$refs["assetsTreeRef"].setCurrentKey(null);
    getList();
}

/** 节点单击事件 */
function handleNodeClick(data) {
    queryParams.value.pageNum = 1;
    let arr = data.id.split(":");
    if (arr[0] == "owner") {
        currentOwnerId.value = arr[1];
        queryParams.value.ownerId = arr[1];
        queryParams.value.id = undefined;
    } else {
        currentOwnerId.value = undefined;
        queryParams.value.ownerId = undefined;
        queryParams.value.id = arr[1];
    }
    getList();
}

//编辑委托方
function treeHandleEdit(data) {
    console.log(data);
    form.value = {
        id: data.id.split(":")[1],
        name: data.label,
        unifiedCode: data.unifiedCode,
        shortName: data.shortName
    };
    title.value = "编辑委托方";
    open.value = true;
}

//跳转 创建项目
function addProduct() {
    router.push({
        path: "/assets/assetside-add/addproduct/" + (currentOwnerId.value || "*"),
    });
}

//编辑产品
function editPro(row) {
    router.push({
        path: `/assets/assetside-edit/editProduct/${row.id}`,
        query: { ownername: row.ownerName, ownerId: row.ownerId },
    });
}

//删除委托方
function removeTreeHandle(data) {
    proxy.$modal
        .confirm("本次操作将会删除该记录，是否继续？")
        .then(function () {
            loading.value = true;
            return delAssetOwner(data.id.split(":")[1]);
        })
        .then(() => {
            proxy.$modal.msgSuccess("删除成功！");
            getTreeselect();
            loading.value = false;
        }).catch((err) => {
            loading.value = false;
        });
}

//删除产品
function removePro(row) {
    proxy.$modal
        .confirm(`本次操作将会删除${row.ownerName}资产方下${row.name}产品的所有批次及案件且不可恢复，是否继续`)
        .then(function () {
            loading.value = true;
            return delProduct(row.id);
        })
        .then(() => {
            proxy.$modal.msgSuccess("删除成功");
            getTreeselect();
            getList();
        }).catch((err) => {
            loading.value = false;
        });
}

//操作栏
function handlesCommand(command) {
    command.func(command.data);
}

//跳转资产管理
function toAsset(row) {
    // router.push({ path: "/assets/asset", query: { productId: row.id } });
    router.push({ path: "/assetsManagement/caseImport", query: { productId: row.id } });

}

//下载导入模板
function downTplFile(row) {
    proxy.download(
        "/teamProduct/downloadTemplate",
        { id: row.id },
        `tpl_${row.name}.xlsx`,
        {gateway: 'cis'}
    );
}

//筛选委托方下拉及产品
watch(assetsName, (val) => {
    proxy.$refs["assetsTreeRef"].filter(val);
});

getTreeselect();

//搜索关键字
function searchName() {
    // getDictTransferor().then((res) => {
    //     options.value = res.data;
    // })
}
searchName()
</script>

<style lang="scss" scoped>
.home-style {
    font-size: 16px;
    color: #606266;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
}

.custom-tree-node {
    width: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;

    :deep(.el-button + .el-button) {
        margin-left: 4px;
    }
}

.el-tree-node__label {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>