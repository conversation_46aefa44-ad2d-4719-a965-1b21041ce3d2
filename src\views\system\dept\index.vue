<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <div class="head-container">
          <el-button type="primary" plain icon="Plus" v-hasPermi="['system:dept:add']" @click="addDepartment"
            style="margin-bottom: 8px">创建部门</el-button>
        </div>
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入名称" clearable prefix-icon="Search" style="margin-bottom: 20px">
          </el-input>
        </div>

        <div class="head-container">
          <el-tree :data="deptOptions" :props="{ label: 'name', children: 'children' }" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="deptTreeRef" default-expand-all @node-click="handleNodeClick">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <el-tooltip effect="light" :content="data.name" placement="right-start">
                  <span class="el-tree-node__label">{{ data.name }}</span>
                </el-tooltip>
                <span class="btn-group">
                  <el-tooltip v-if="data.rank !== 0 && checkPermi(['system:dept:edit'])" content="修改部门" placement="top">
                    <el-button type="text" icon="Edit" @click="editDepartment(node, data)"></el-button>
                  </el-tooltip>
                  <el-tooltip v-if="data.rank < 6 && checkPermi(['system:dept:add'])" content="创建部门" placement="top">
                    <el-button type="text" icon="Plus" @click="addDepartment(data)"></el-button>
                  </el-tooltip>
                  <el-tooltip v-if="data.rank !== 0 && checkPermi(['system:dept:detele'])" content="删除部门"
                    placement="top">
                    <el-button type="text" icon="DeleteFilled" @click="removeDepartment(data)"></el-button>
                  </el-tooltip>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-col>

      <el-col :span="18" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
          <el-form-item prop="value">
            <el-input v-model="queryParams.value" placeholder="姓名/账号/手机" clearable style="width: 240px"
              @keyup.enter="antiShake(handleQuery)" />
          </el-form-item>
          <el-form-item prop="roleId">
            <el-select v-model="queryParams.roleId" placeholder="选择角色" clearable filterable :reserve-keyword="false"
              @focus="getRoles" style="width: 240px">
              <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="accountStatus">
            <el-select v-model="queryParams.accountStatus" placeholder="账号状态" clearable style="width: 240px">
              <el-option label="启用" :value="0"></el-option>
              <el-option label="禁用" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="workingState">
            <el-select v-model="queryParams.workingState" placeholder="工作状态" clearable style="width: 240px">
              <el-option label="在职" :value="0"></el-option>
              <el-option label="离职" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-area">
          <div class="operation-area-btn">
            <el-button type="primary" plain v-if="checkPermi(['system:user:add'])" icon="Plus" @click="addNewEmployees"
              :disabled="loading">
              新增员工
            </el-button>
            <el-button type="success" plain v-if="checkPermi(['system:dept:edit'])" icon="Edit"
              :disabled="single || loading" @click="handleUserDept">
              修改部门
            </el-button>
            <el-button plain v-if="checkPermi(['system:role:edit'])" icon="Edit" :disabled="single || loading"
              @click="deitTheRole">
              修改角色
            </el-button>
            <el-button type="danger" plain v-if="checkPermi(['system:set:status'])" icon="Edit"
              :disabled="single || loading" @click="accountStatusSet">
              设置状态
            </el-button>
            <el-button type="warning" plain v-if="checkPermi(['system:set:password'])" icon="Edit"
              :disabled="single || loading" @click="pwdReset">
              重置密码
            </el-button>
            <el-button type="info" plain v-if="checkPermi(['system:user:import'])" icon="Upload" @click="importUser"
              :disabled="loading">
              导入员工
            </el-button>
            <el-button type="primary" plain v-if="checkPermi(['system:user:seat'])" :disabled="single || loading"
              @click="openAllotSeat">
              分配坐席
            </el-button>
            <el-button type="success" plain v-if="checkPermi(['system:seat:password'])" :disabled="single || loading"
              @click="openResetSeat">
              坐席密码重置
            </el-button>
            <el-button type="info" plain v-hasPermi="['seat:list:datasync']" :disabled="loading" 
              @click="handleDataSync">
              同步坐席
            </el-button>
          </div>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </div>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="姓名" align="center" key="employeeName" prop="employeeName" v-if="columns[0].visible" />
          <el-table-column label="登录账号" align="center" key="loginAccount" prop="loginAccount"
            v-if="columns[1].visible" />
          <el-table-column label="所属部门" align="center" key="departments" prop="departments" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="角色" align="center" key="theRole" prop="theRole" v-if="columns[3].visible" />
          <el-table-column label="员工工号" align="center" key="employeesWorking" prop="employeesWorking"
            v-if="columns[4].visible" />
          <el-table-column label="手机" align="center" key="phoneNumber" prop="phoneNumber" v-if="columns[5].visible" />
          <el-table-column label="坐席账号/密码" align="center" key="sipNumber" prop="sipNumber" v-if="columns[6].visible">
            <template #default="{ row }">
              <span v-if="row.sipNumber">{{ `${row.sipNumber}/${row.sipPassword}` }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="工作状态" align="center" key="workingState" prop="workingState" v-if="columns[7].visible">
            <template #default="scope">
              <el-tag class="ml-2" :type="scope.row.workingState == 0 ? '' : 'danger'">{{
                scope.row.workingState == 0 ? "在职" : "离职"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="账号状态" align="center" key="accountStatus" prop="accountStatus"
            v-if="columns[8].visible">
            <template #default="scope">
              <el-tag class="ml-2" :type="scope.row.accountStatus == 0 ? '' : 'danger'">{{
                scope.row.accountStatus == 0 ? "启用" : "禁用"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" :width="330">
            <template #default="scope">
              <el-button type="text" @click="openAllotSeat($event, [scope.row])"
                v-hasPermi="['system:user:seat']">分配坐席</el-button>
              <el-button type="text" @click="openResetSeat($event, [scope.row])"
                v-hasPermi="['system:seat:password']">坐席密码重置</el-button>
              <el-button type="text" v-if="!scope.row.bindWorkPhone && checkPermi(['system:user:bindWorkPhone'])" @click="handleBindWorkPhone(scope.row)">绑定工作手机</el-button>
              <el-button type="text" v-if="scope.row.bindWorkPhone && checkPermi(['system:user:unbindWorkPhone'])" @click="handleUnbindWorkPhone(scope.row)">解绑工作手机</el-button>
              <el-button type="text" @click="editTheEmployee(scope.row)"
                v-hasPermi="['system:user:edit']">编辑</el-button>
              <!-- <el-button
                type="text"
                @click="userDetails(scope.row)"
                v-hasPermi="['system:user:details']"
                >详情</el-button
              > -->
              <el-button type="text" @click="removeAnEmployee(scope.row)"
                v-hasPermi="['system:user:delete']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 新建、编辑部门 -->
    <deptVue ref="deptRef" :deptOptions="deptOptions" @getDeptTree="getDeptTree" @getList="getList" />

    <!-- 新建/编辑员工 -->
    <useraddeditVue ref="useraddeditRef" @getList="getList" />

    <!-- 修改部门 -->
    <editUserDeptVue ref="editUserDeptRef" @getList="getList" />

    <!-- 修改角色 -->
    <editUserRoleVue ref="editUserRoleRef" @getList="getList" />

    <!-- 设置账号状态 -->
    <editUserStatusVue ref="editUserStatusRef" @getList="getList" />

    <!-- 重置密码 -->
    <resetUserpwdVue ref="resetUserpwdRef" @getList="getList" />

    <!-- 分配坐席 -->
    <allotSeat ref="allotSeatRef" @getList="getList" />

    <!-- 坐席密码重置 -->
    <resetSeat ref="resetSeatRef" @getList="getList" />

    <!-- 绑定工作手机 -->
    <bindWorkPhone ref="bindWorkPhoneRef" @getList="getList" />
  </div>
</template>

<script setup name="Dept">
import deptVue from "./dialog/dept";
import useraddeditVue from "./dialog/useraddedit";
import editUserDeptVue from "./dialog/editUserDept";
import editUserRoleVue from "./dialog/editUserRole";
import editUserStatusVue from "./dialog/editUserStatus";
import resetUserpwdVue from "./dialog/resetUserpwd";
import allotSeat from "./dialog/allotSeat";
import resetSeat from "./dialog/resetSeatpwd";
import bindWorkPhone from "./dialog/bindWorkPhone.vue";
import { checkPermi } from "@/utils/permission";

import { deptTree, deleteDept } from "@/api/system/dept";
import { getUserList, delUser } from "@/api/system/user";
import { getRoleOptions } from "@/api/system/roles";
import { sipDataSync } from '@/api/seat/seatlist'
import { unbind, getOpenWorkPhone } from "@/api/workPhone/index";

const { proxy } = getCurrentInstance();
const router = useRouter();
const deptName = ref("");
const showSearch = ref(true);
const deptOptions = ref([]); //左侧部门数据
const roleOptions = ref([]); //角色下拉
const departmentId = ref(undefined);

const loading = ref(false);
const userList = ref([]);
const single = ref(true);
const total = ref(0);
const selectedArr = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    value: undefined,
    roleId: undefined,
    accountStatus: undefined,
    workingState: undefined,
  },
});
const { queryParams } = toRefs(data);

// 列显隐信息
const columns = ref([
  { key: 0, label: `姓名`, visible: true },
  { key: 1, label: `登录账号`, visible: true },
  { key: 2, label: `所属部门`, visible: true },
  { key: 3, label: `角色`, visible: true },
  { key: 4, label: `员工工号`, visible: true },
  { key: 5, label: `手机`, visible: true },
  { key: 6, label: `坐席账号/密码`, visible: true },
  { key: 7, label: `工作状态`, visible: true },
  { key: 8, label: `账号状态`, visible: true },
]);

const store = useStore();
const name = computed(() => store.getters.name);

//获取列表
function getList() {
  loading.value = true;
  getUserList(queryParams.value)
    .then((res) => {
      userList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//获取树结构
function getDeptTree() {
  deptTree().then((res) => {
    deptOptions.value = deptOptionsHandle(res.data);
  });
}
getDeptTree();

//部门数据处理(增加层级)
function deptOptionsHandle(data) {
  function handle(data, ranks) {
    let rank = ranks || 0;
    data.map((item) => {
      item.rank = rank;
      if (item.children && item.children.length !== 0) {
        handle(item.children, rank + 1);
      }
    });
  }
  handle(data);
  return data;
}

//创建部门
function addDepartment(data) {
  proxy.$refs["deptRef"].opendialog({ parentId: data.id });
}

// 通过条件过滤节点
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

//筛选树结构数据
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});

//节点单击事件
function handleNodeClick(data) {
  let arr = data.id.split(":");
  if (arr[0] == "Dept") {
    queryParams.value.departmentId = arr[1];
    departmentId.value = arr[1];
  } else {
    queryParams.value.departmentId = undefined;
  }
  queryParams.value.pageNum = 1;
  getList();
}

//编辑部门
function editDepartment(node, data) {
  let form = {
    id: data.id.split(":")[1],
    deptName: data.name,
    parentId: node.parent.data.id,
  };
  proxy.$refs["deptRef"].opendialog(form);
}

//删除部门
function removeDepartment(data) {
  proxy.$modal
    .confirm(`是否确认删除${data.name}部门`)
    .then(() => {
      loading.value = true;
      let id = data.id.split(":")[1];
      return deleteDept(id);
    })
    .then(() => {
      proxy.$modal.msgSuccess("删除成功");
      getDeptTree();
    }).finally(() => {
      loading.value = false;
    });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

//选择条数
function handleSelectionChange(selection) {
  //ids.value = selection.map(item => item.id);
  single.value = selection.length == 0;
  selectedArr.value = selection;
}

//新增员工
function addNewEmployees() {
  let data = { departmentId: departmentId.value };
  proxy.$refs["useraddeditRef"].opendialog(data);
}

//编辑员工
function editTheEmployee(row) {
  proxy.$refs["useraddeditRef"].opendialog(row);
}

//员工详情
function userDetails(row) {
  let userInfo = {
    roleId: row.id,
    roleIds: row.roleId,
    employeeName: row.employeeName,
    departments: row.departments,
    theRole: row.theRole,
  };
  localStorage.setItem(`userInfo/${row.id}`, JSON.stringify(userInfo));
  router.push({ path: `/setting/user-detail/userDetails/${row.id}` });
}

//删除员工
function removeAnEmployee(row) {
  proxy.$modal
    .confirm(`是否确认删除登陆账号为${row.loginAccount}的员工？`)
    .then(() => {
      loading.value = true;
      return delUser(row.id);
    })
    .then(() => {
      proxy.$modal.msgSuccess("删除成功");
      getList();
    }).catch(() => {
      loading.value = false;
    });
}

//修改部门
function handleUserDept() {
  proxy.$refs["editUserDeptRef"].opendialog(selectedArr.value);
}

//修改角色
function deitTheRole() {
  proxy.$refs["editUserRoleRef"].opendialog(selectedArr.value);
}
//设置状态
function accountStatusSet() {
  proxy.$refs["editUserStatusRef"].opendialog(selectedArr.value);
}

//重置密码
function pwdReset() {
  proxy.$refs["resetUserpwdRef"].opendialog(selectedArr.value);
}

//导入员工
function importUser() {
  router.push("/system/dept-user/importUser");
}

//获取角色
function getRoles() {
  getRoleOptions().then((res) => {
    roleOptions.value = res.data;
  });
}

//分配坐席
function openAllotSeat(e, val) {
  let selected = [];
  if (val) {
    selected = val;
  } else {
    selected = selectedArr.value;
  }
  proxy.$refs["allotSeatRef"].opendialog(selected);
}

//坐席密码重置
function openResetSeat(e, arr) {
  let selected = [];
  if (arr) {
    if (!arr[0].sipNumber || arr[0].sipNumber == "") {
      proxy.$modal.msgWarning("该员工暂未分配坐席，请分配坐席后再试！");
      return false;
    }
    selected = arr;
  } else {
    selected = selectedArr.value;
  }
  proxy.$refs["resetSeatRef"].opendialog(selected);
}

//数据同步
function handleDataSync() {
  sipDataSync().then(res => {
    proxy.$modal.msgSuccess('数据已同步！')
    handleQuery()
  })
}

// 绑定工作手机
async function handleBindWorkPhone(row) {
  const { id, employeeName, loginAccount } = row;
  let flag = false, teamId;
  try {
    const res = await getOpenWorkPhone();
    if (res.code == 200) {
      flag = res.data.openWorkPhone;
      teamId = res.data.teamId;
    }
  } catch (error) {
    console.error('获取工作手机开通状态失败:', error);
    return;
  }
  if (!flag) {
    // 未开通工作手机功能
    proxy.$modal.confirm(
      "未开通工作手机功能，请联系客服开通该服务", 
      '系统提示', 
      { beforeClose: undefined }
    );
  } else {
    // 开通了工作手机功能，打开绑定工作手机弹窗
    proxy.$refs["bindWorkPhoneRef"].opendialog({id, employeeName, teamId, loginAccount});
  }
}

//解绑工作手机
function handleUnbindWorkPhone(row) {
  const { id, loginAccount } = row;
  const req = { userId: id };
  proxy.$modal
    .confirm("确认解绑该工作手机吗？")
    .then(() => {
      unbind(req).then((res) => {
        proxy.$modal.msgSuccess(res.msg);
        if (name.value == loginAccount) {
          store.dispatch('call/checkWorkPhone');
        }
        getList();
      });
    })
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
  width: 90%;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;

  :deep(.el-button + .el-button) {
    margin-left: 4px;
  }
}

.btn-group {
  display: inline-block;
  min-width: 32px;
}

:deep(.el-tree-node__label) {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.operation-area {
  position: relative;

  .el-button {
    margin-right: 12px;
    margin-bottom: 10px;
  }

  .el-button+.el-button {
    margin-left: 0;
  }

  .operation-area-btn {
    width: calc(100% - 120px);
  }

  .top-right-btn {
    top: 0;
  }
}
</style>
