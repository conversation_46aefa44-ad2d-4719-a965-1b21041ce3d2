<template>
    <el-dialog title="物流信息" v-model="open" append-to-body width="650px" @before-close="cancel">
        <div v-if="form.returnCode == 400">
            没有相关的物流信息
        </div>
        <div v-if="form.returnCode != 400">
            <div class="list">
                <div class="item">物流单号：{{ form.nu }} </div>
                <div class="item">公司编码： {{ form.com }}</div>
                <div class="item">收件地址： {{ form.address }} </div>
            </div>
            <div class="transport-info">
                <h2>运输信息</h2>
                <el-timeline style="max-width: 600px">
                    <el-timeline-item v-for="(item, index) in form.data" :key="index">
                        <div class="time-content">{{ item.time }} <span v-if="index == 0" class="primary">最新</span>
                        </div>
                        <div :class="`info-content ${index == 0 && 'primary'}`">{{ item.context }}</div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </div>
        <template #footer>
            <div>
                <el-button @click="cancel">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { getExpressInfo } from '@/api/lawyer/lawyer';

const open = ref(false)
const form = ref(undefined)
const dataList = ref([
    { content: 'Event start', timestamp: '2018-04-15', },
    { content: 'Approved', timestamp: '2018-04-13', },
    { content: 'Success', timestamp: '2018-04-11', },
])
function openDialog(data) {
    getExpressInfo(data).then(res => {
        form.value = res.data
        open.value = true
    })
}
function cancel() {
    form.value = {}
    open.value = false
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped>
.list {
    .item {
        font-size: 16px;
        line-height: 32px;
    }
}

.transport-info {
    .primary {
        color: #409EFF;
    }

    h2 {
        font-weight: bold;
        padding: 10px 0 20px;
    }

    .time-content {
        color: #999;
    }

    .info-content {
        margin-top: 10px;
    }

    .el-timeline {
        max-height: 450px !important;
        overflow: auto;
    }
}
</style>