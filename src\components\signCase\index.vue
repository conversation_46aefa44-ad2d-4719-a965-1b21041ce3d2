<template>
   <el-dialog title="标记案件" v-model="open" width="450px" :before-close="cancel" append-to-body>
         <el-radio-group v-model="signCase" @change="signChange">
            <el-radio v-for="item in tags"  
            :key="item.id"
            :label="item.code"
            :value="item" ><el-icon style="margin-left:20px" :size="20" :color="signColorFor(item.code)"><price-tag /></el-icon><span  class="icon-label">{{  item.labelContent }}</span></el-radio>
        </el-radio-group>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
   </el-dialog>
</template>

<script setup>
import { selectLabel } from '@/api/common/common'
import { selectMarkCase } from "@/api/case/index/index";
 const { proxy } = getCurrentInstance();

const props = defineProps({
  checkedType: {
    //本页选中，搜索结果选中
    type: String,
  },
  query: {
    //查询条件
    type: Object,
  },
  selected: {
    //表格选中
    type: Array,
  }
});

const open = ref(false);
const signCase = ref(0);
const tags = ref([]);
const loading = ref(false);


async function openDialog(){
    LabelList();
    open.value = true;
    props.query.code = signCase.value;

    if (props.checkedType == "本页选中" || props.checkedType === undefined) {
        props.query.condition  = false;
    } else {
        props.query.pageSize = undefined;
        props.query.pageNum = undefined;
    }
}

//标签
function LabelList() {
  selectLabel().then((res) => {
    tags.value = res.data;
  });
}

//标签切换
function signChange(){
    props.query.code = signCase.value;
}

//提交
function submit(){
    loading.value = true;
    selectMarkCase(props.query).then(res =>{
        proxy.$modal.msgSuccess(res.msg);
        loading.value = false;
        cancel();
    }).catch(()=>{
        loading.value = false;
    })
}

//设置颜色
function signColorFor(index){
    return ['#000','#ea68a2','orange','green','blue','purple','yellow'][index];
}

//取消或关闭
function cancel() {
  open.value = false;
}
defineExpose({
  openDialog,
});
</script>

<style lang='scss' scoped>
.icon-label{
    position: relative;
    left: 10px;
    top: -4px;
}
.el-radio{
    width: 150px;
    margin-left: 15px;
}

</style>