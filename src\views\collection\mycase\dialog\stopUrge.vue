<template>
  <el-dialog
    title="提示"
    v-model="open"
    width="960px"
    :before-close="cancel"
    append-to-body
  >
    <table class="my-table" v-loading="loading">
      <thead>
        <tr class="text-left">
          <td colspan="4">案件信息</td>
        </tr>
      </thead>
      <tbody class="text-center">
        <tr>
          <td>案件量</td>
          <td>总金额</td>
          <td>已分配</td>
          <td>未分配</td>
        </tr>
        <tr>
          <td>{{ listdata.zongshu }}</td>
          <td>{{ listdata.zongjine }}</td>
          <td>{{ listdata.yifenpei }}</td>
          <td>{{ listdata.weifenpei }}</td>
        </tr>
      </tbody>
    </table>
    <!-- <div class="hint text-danger mt10">
      此操作会将案件状态申请为停催，资产端审核通过后，案件会退回资产端, 是否继续?
    </div> -->
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { statisticsCase, stopUrge } from "@/api/collection/mycase";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);

const open = ref(false);
const loading = ref(false);
const form = ref({});
const listdata = ref({
  zongshu: 0,
  zongjine: 0,
  yifenpei: 0,
  weifenpei: 0,
});
const subloading = ref(false);

//打开弹窗
function opendialog(data) {
  form.value = data;
  statisticsCase(data).then((res) => {
    listdata.value = res.data || {
      zongshu: 0,
      zongjine: 0,
      yifenpei: 0,
      weifenpei: 0,
    };
    open.value = true;
  });
}

//提交
function submit() {
  subloading.value = false;
  stopUrge(form.value)
    .then((res) => {
      proxy.$modal.msgSuccess("申请提交成功！");
      emit("getList");
      cancel();
    })
    .finally(() => {
      subloading.value = false;
    });
}

//取消
function cancel() {
  listdata.value = {
    zongshu: 0,
    zongjine: 0,
    yifenpei: 0,
    weifenpei: 0,
  };
  form.value = {};
  open.value = false;
}

defineExpose({
  opendialog,
});

</script>

<style scoped></style>
