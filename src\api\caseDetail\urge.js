import request from '@/utils/request'

//查询联系渠道
export function selectDictDataContact() {
  return request({
    url: '/dropDown/selectDictDataContact',
    method: 'get'
  })
}

//获取跟进状态及催收状态
export function selectDictDataLinkage() {
  return request({
    url: '/dropDown/selectDictDataLinkage',
    method: 'get'
  })
}

//查询催记标签
export function selectStaffLabel() {
  return request({
    url: '/collection/selectStaffLabel',
    method: 'get'
  })
}

//添加催记标签
export function insertStaffLabel(data) {
  return request({
    url: '/collection/insertStaffLabel',
    method: 'post',
    data: data
  })
}

//删除催记标签
export function deleteStaffLabel(id) {
  return request({
    url: '/collection/deleteStaffLabel/' + id,
    method: 'delete'
  })
}

//保存催记
export function insertUrgeRecord(data) {
  return request({
    url: '/collection/insertUrgeRecord',
    method: 'post',
    data: data
  })
}

//查询调诉阶段
export function selectContactChannel(query) {
  return request({
    url: '/stage/register/getStage',
    method: 'get',
    params: query
  })
}

//查询保全阶段
export function selectSecurity(query) {
  return request({
    url: '/collection/selectSecurity',
    method: 'post',
    params: query
  })
}

//保存调诉记录
export function insertComplaint(data) {
  return request({
    url: '/stage/register/addRegisterRecord',
    method: 'post',
    data: data
  })
}

