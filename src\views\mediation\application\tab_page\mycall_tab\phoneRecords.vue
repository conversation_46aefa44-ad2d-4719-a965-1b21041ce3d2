<template>
  <div>
    <el-form
      :inline="true"
      :model="queryParams"
      ref="queryRef"
      label-width="80px"
    >
      <el-form-item label="主叫号码" prop="callingNumber">
        <el-input
          v-model="queryParams.callingNumber"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被叫号码" prop="calledNumber">
        <el-input
          v-model="queryParams.calledNumber"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="callType">
        <el-select
          v-model="queryParams.callType"
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option label="手动呼叫" value="callOutManual"></el-option>
          <el-option label="API点击呼叫" value="clickCallOut"></el-option>
          <el-option label="呼入" value="inbound"></el-option>
          <el-option label="预测试外呼" value="newauto"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="呼叫时间" prop="startTime">
        <el-date-picker
          v-model="startTime"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="通话时长">
        <div class="range-scope" style="width: 240px">
          <NumberInput v-model="queryParams.duration1" :decimals="0" />
          <span>-</span>
          <NumberInput v-model="queryParams.duration2" :decimals="0" />
        </div>
      </el-form-item> -->
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
        >搜索</el-button
      >
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <div class="mb20">
      <el-radio-group v-model="engageType" @change="engageTypeChange">
        <el-radio-button label="y">接通</el-radio-button>
        <el-radio-button label="n">未接通</el-radio-button>
        <el-radio-button label="全部">全部</el-radio-button>
      </el-radio-group>
    </div>
    <div class="mb20">
      <el-alert
        title="注：默认只展示当月的通话记录。最多可查询近3个月内的明细记录！"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="案件ID" prop="caseId" align="center" />
      <el-table-column label="借款人" prop="createTime" align="center" />
      <el-table-column
        label="通话时长"
        prop="duration"
        align="center"
        :formatter="callTime"
      />
      <el-table-column label="主叫号码" prop="callingNumber" align="center" />
      <el-table-column label="被叫号码" prop="calledNumber" align="center" />
      <el-table-column label="外显号码" prop="explicitNumber" align="center" />
      <el-table-column
        label="呼叫类型"
        prop="callType"
        align="center"
        :formatter="ChangeCallType"
      />
      <el-table-column label="操作" fixed="right" :width="200">
        <template #default="{ row, $index }">
          <musicPlayer
            v-if="row.recording && row.recording != ''"
            :ref="`audioRef${row.recording}`"
            :audioSrc="getaudioSrc(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import musicPlayer from "@/components/MusicPlay/musicPlayer"
import { phoneRecordList } from "@/api/mediation/application"

const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    callingNumber: undefined,
    calledNumber: undefined,
    engageType: null
  }
});

const { queryParams } = toRefs(data);
const startTime = ref(undefined);

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const getList = async () => {
  try {
    loading.value = true
    const reqForm = {
      ...queryParams.value,
      engageType: queryParams.value.engageType === '全部' ? null : queryParams.value.engageType
    }

    const { code, msg, rows, total: totalCount } = await phoneRecordList(reqForm)
    
    if (code === 200) {
      dataList.value = rows
      total.value = totalCount
    } else {
      ElMessage.error(msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取通话记录失败:', error)
    ElMessage.error('获取通话记录失败')
  } finally {
    loading.value = false
  }
};
getList();

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    callingNumber: undefined,
    calledNumber: undefined,
    engageType: null
  }
  getList()
}

// 通话时长
const formatDuration = (row) => {
  const duration = row.duration
  if (!duration || duration <= 0) return '--'
  
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60
  
  const parts = []
  if (hours > 0) parts.push(`${hours}时`)
  if (minutes > 0) parts.push(`${minutes}分`)
  if (seconds > 0) parts.push(`${seconds}秒`)
  
  return parts.join('') || '--'
}

// 优化音频源获取方法
const getAudioSrc = (row) => row.recording

function formatCallDirection(row, column, cellValue) {
  if (cellValue === 0) return '呼出';
  if (cellValue === 1) return '呼入';
  return '未知';
}
</script>

<style scoped></style>
