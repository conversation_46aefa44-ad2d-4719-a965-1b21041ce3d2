<template>
  <el-table :data="listData" :loading="loading">
    <el-table-column label="外访时间" prop="outsideStart" key="outsideStart" align="center" />
    <el-table-column label="外访客户姓名" prop="clientName" key="clientName" align="center" />
    <el-table-column label="外访记录" prop="record" key="record" align="center">
      <template #default="{ row }">
        <el-button type="text" @click="toOpenContent(row, 0)" v-if="row.map?.record > 0">
          {{ row.map?.record }}
        </el-button>
        <span v-if="row.map?.record == 0">{{ row.map?.record }}</span>
      </template>
    </el-table-column>
    <el-table-column label="照片" prop="photo" key="photo" align="center">
      <template #default="{ row }">
        <el-button type="text" @click="toOpenContent(row, 1)" v-if="row.map?.photo > 0">{{ row.map?.photo }}</el-button>
        <span v-if="row.map?.photo == 0">{{ row.map?.photo }}</span>
      </template>
    </el-table-column>
    <el-table-column label="录音" prop="soundRecording " key="soundRecording " align="center">
      <template #default="{ row }">
        <el-button type="text" @click="toOpenContent(row, 2)" v-if="row.map?.soundRecording > 0">{{
          row.map?.soundRecording }}</el-button>
        <span v-if="row.map?.soundRecording == 0">{{ row.map?.soundRecording }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作人" prop="registrant" key="registrant" align="center" />
    <el-table-column label="签到地址" prop="checkAddress" key="checkAddress" align="center" />
    <el-table-column label="签到时间" prop="checkDate" key="checkDate" align="center" />
    <el-table-column label="签到照片" prop="checkPhoto" key="checkPhoto" align="center">
      <template #default="{ row }">
        <el-icon :size="24" v-if="row.checkPhoto && row.checkPhoto != ''" @click="openCheckalter(row)">
          <PictureFilled />
        </el-icon>
        <span v-if="!row.checkPhoto">--</span>
      </template>
    </el-table-column>
    <el-table-column label="外访结束时间" prop="outsideEnd" key="outsideEnd" align="center" />
  </el-table>
  <!-- 外访信息 -->
  <outsideContent ref="outsideContentRef" />
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
import outsideContent from '../dialog/outsideContent.vue';
import { selectOutsideRecord } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const props = defineProps({
  params: { type: Object, default: {} },
  handleDownload: { type: Function },
});
const route = useRoute();
const loading = ref(false)
const listData = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: route.params.caseId
  },
});
const { queryParams } = toRefs(data);
//获取列表
function getList() {
  let reqForm = JSON.parse(JSON.stringify(queryParams.value))
  reqForm = { ...reqForm, ...props.params }
  loading.value = true;
  selectOutsideRecord(reqForm).then((res) => {
    listData.value = res.rows;
    total.value = res.total;
  }).catch(() => {
    listData.value = [];
  }).finally(() => {
    loading.value = false;
  });
}
getList();
//查看凭证
function openCheckalter(row) {
  window.open(row.checkPhoto, "_blank");
}
//打开资源类型
function toOpenContent(row, type) {
  proxy.$refs['outsideContentRef'].opendialog(row.id, type)
}

defineExpose({ getList })
</script>

<style scoped></style>
