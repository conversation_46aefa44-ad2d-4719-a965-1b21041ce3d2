<template>
  <!-- 跟进结果（催收记录） -->
  <div class="handle-urage caseinfo-wrap pd20">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="144px">
      <el-form-item label="联系人" prop="liaison" v-show="!props.isOutgoingCall">
        <el-select v-model="form.liaison" placeholder="请选择联系人" style="width: 240px">
          <el-option v-for="item in contactOptions" :key="item.id"
            :label="`${item.contactName}(${item.contactRelation})`" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="联系渠道" prop="contactMedium" v-show="!props.isOutgoingCall">
        <el-radio-group v-model="form.contactMedium">
          <el-radio v-for="item in contactChannel" :key="item" :label="item">
            {{ item }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="跟进状态" prop="followUpState">
        <el-radio-group v-model="form.followUpState" @change="followChange">
          <el-radio v-for="item in followOptions" :key="item.dictLabel" :label="item.dictLabel">
            {{ item.dictLabel }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="催收状态" prop="urgeState">
        <el-radio-group class="pt5" v-model="form.urgeState" size="small">
          <el-radio class="mb8" v-for="item in urgeOptions" :key="item" :label="item" border>{{ item }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 承诺还款显示 -->
      <div class="flex" v-if="form.urgeState == '承诺还款'">
        <div class="flex-item">
          <el-form-item label="承诺还款时间" prop="promiseRepaymentTime" label-width="124px">
            <el-date-picker v-model="form.promiseRepaymentTime" value-format="YYYY-MM-DD" type="date"
              placeholder="请选择还款时间" style="width: 240px" />
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="承诺还款金额" prop="promiseRepaymentMoney" label-width="124px">
            <el-input v-model="form.promiseRepaymentMoney" type="number" placeholder="请输入还款金额" style="width: 240px" />
          </el-form-item>
        </div>
      </div>
      <!-- 承诺分期还款 -->
      <div class="flex" v-if="form.urgeState == '承诺分期还款'">
        <div class="flex-item">
          <el-form-item label="承诺分期还款" prop="promiseByStages" label-width="124px">
            <el-input-number v-model="form.promiseByStages" :min="1" :max="24" style="width: 150px" />
            &nbsp;期
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="承诺每期还款日" prop="promiseRepaymentDay" label-width="124px">
            <el-input-number v-model="form.promiseRepaymentDay" :min="1" :max="31" style="width: 150px" />
            &nbsp;日
          </el-form-item>
        </div>
        <div class="flex-item">
          <el-form-item label="承诺每期还款金额" prop="promiseEveryMoney" label-width="140px">
            <el-input type="number" v-model="form.promiseEveryMoney" placeholder="请输入每期还款金额" style="width: 240px" />
            &nbsp;元
          </el-form-item>
        </div>
      </div>

      <el-form-item v-if="form.urgeState == '另约时间'" label="另约时间" prop="anotherTime">
        <el-date-picker v-model="form.anotherTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择还款时间"
          style="width: 240px" />
      </el-form-item>
      <el-form-item>
        <el-tag class="mr10" v-for="(tag, index) in dynamicTags" :key="tag.id" closable :disable-transitions="false"
          @close="removeTag(tag, index)" @click="addRemraks(tag)">
          {{ tag.labelContent }}
        </el-tag>
        <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" @keyup.enter="addTab" @blur="addTab"
          placeholder="请输入标签内容" style="width: 136px" />
        <el-button v-else icon="Plus" type="success" plain @click="showInput">
          添加标签内容
        </el-button>
      </el-form-item>
      <el-form-item label="" prop="content">
        <div>沟通内容（可使用标签便捷服务）：</div>
        <el-input v-model="form.content" type="textarea" maxlength="300" show-word-limit :rows="5" placeholder="请输入" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain :loading="loading" @click="saveUrge">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import {
  selectDictDataContact,
  selectDictDataLinkage,
  selectStaffLabel,
  insertStaffLabel,
  deleteStaffLabel,
  insertUrgeRecord,
} from "@/api/caseDetail/urge";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["setCurrentRow", "getcaseDetailInfo", "getUrgeList"]);
const route = useRoute();

const props = defineProps({
  contactOptions: {
    type: Array,
    default: [],
  },
  selected: {
    type: Object,
  },
  caseId: {
    type: [Number, String]
  },
  //预测式外呼
  isOutgoingCall: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const data = reactive({
  form: {
    liaison: undefined,
    contactMedium: "电话",
    followUpState: "重点跟进",
    urgeState: undefined,
    promiseRepaymentTime: undefined,
    promiseRepaymentMoney: undefined,
    promiseByStages: undefined,
    promiseEveryMoney: undefined,
    promiseRepaymentDay: undefined,
    content: "",
    contactId: undefined
  },
  rules: {
    liaison: [{ required: true, message: "请选择联系人", trigger: "change" }],
    contactMedium: [{ required: true, message: "请选择联系渠道", trigger: "change" }],
    followUpState: [{ required: true, message: "请选择跟进状态", trigger: "change" }],
    urgeState: [{ required: true, message: "请选择催收状态", trigger: "change" }],
    promiseRepaymentMoney: [
      { required: true, trigger: 'blur', message: '请输⼊承诺还款金额' },
      { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
    ],
    promiseEveryMoney: [
      { required: true, trigger: 'blur', message: '请输⼊承诺每期还款金额' },
      { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
    ],
    promiseRepaymentDay: [
      { required: true, message: "请输入承诺分期还款日", trigger: "blur" },
      {
        pattern: /^(([1-9])|([0-2]\d)|30|31)$/,
        message: "请输⼊正确的格式,最大日期为31",
      }
    ],
    promiseByStages: [
      { required: true, message: "请输入承诺分期还款期数", trigger: "blur" },
      {
        pattern: /^(([1-9])|([0-1]\d)|21|22|23|24|20)$/,
        message: "请输⼊正确的格式,最大为24期",
      },
    ]
  },
});

const { form, rules } = toRefs(data);

const contactChannel = ref([]); //联系渠道
const followOptions = ref([]); //跟进状态
const urgeOptions = ref([]); //催收状态

const dynamicTags = ref([]); //标签
const inputValue = ref("");
const inputVisible = ref(false);

form.value.liaison = computed({
  get() {
    return props.selected.id;
  },
  set(val) {
    let update_data = {};
    if (val) {
      for (let i = 0; i < props.contactOptions.length; i++) {
        let item = props.contactOptions[i];
        if (val == item.id) {
          update_data = item;
          break;
        }
      }
      emit("setCurrentRow", update_data);
    } else {
      emit("setCurrentRow", props.contactOptions[0]);
    }
  },
});

//获取联系渠道
selectDictDataContact().then((res) => {
  contactChannel.value = res.data;
});

//获取跟进状态
selectDictDataLinkage().then((res) => {
  followOptions.value = res.data;
  for (let i = 0; i < followOptions.value.length; i++) {
    const item = followOptions.value[i];
    if (item.dictLabel == form.value.followUpState) {
      urgeOptions.value = item.remark;
      break;
    }
  }
});

//查询标签
function getUrgeLabel() {
  selectStaffLabel().then((res) => {
    dynamicTags.value = res.data;
  });
}
getUrgeLabel();

//跟进状态切换
function followChange(val) {
  form.value.urgeState = undefined;
  //获取催收状态
  for (let i = 0; i < followOptions.value.length; i++) {
    const item = followOptions.value[i];
    if (item.dictLabel == val) {
      urgeOptions.value = item.remark;
      break;
    }
  }
}

//显示添加标签输入框
function showInput() {
  inputVisible.value = true;
  nextTick(() => {
    proxy.$refs["InputRef"].focus();
  });
}

//添加标签
function addTab() {
  if (inputValue.value) {
    insertStaffLabel({ labelContent: inputValue.value }).then((res) => {
      getUrgeLabel();
    });
  }
  inputVisible.value = false;
  inputValue.value = "";
}

//删除标签
function removeTag(tag, index) {
  deleteStaffLabel(tag.id).then(() => {
    dynamicTags.value.splice(index, 1);
  });
}

//备注添加标签内容
function addRemraks(tag) {
  let str = tag.labelContent;
  let content = form.value.content;
  if (content && content != "") {
    let i = content.indexOf(tag.labelContent);
    if (i > -1) {
      if (i === 0) {
        //在头部
        return false;
      }
      if (i > 0) {
        if (content.indexOf("," + tag.labelContent + ",") > -1) {
          //在中部
          return false;
        } else {
          //在尾部
          let j = content.lastIndexOf("," + tag.labelContent);
          if (j + ("," + tag.labelContent).length === content.length) {
            return false;
          }
        }
      }
    }
    str = "," + str;
  }
  form.value.content += str;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  const keys = ["liaison", "followUpState", "content"];
  Object.keys(form.value).map((key) => {
    if (keys.indexOf(key) === -1) {
      form.value[key] = undefined;
    }
  });
  form.value.followUpState = "重点跟进";
  form.value.content = "";
  followChange("重点跟进");
}

//保存催记
function saveUrge() {
  if (!form.value.urgeState) proxy.$modal.msgWarning('请选择催收状态！')
  form.value.contactMedium = props.isOutgoingCall ? "电话" : form.value.contactMedium;
  loading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req = JSON.parse(JSON.stringify(form.value));
      req.caseId = props.caseId;
      for (let i = 0; i < props.contactOptions.length; i++) {
        const item = props.contactOptions[i];
        if (item.id === req.liaison) {
          req.liaison = item.contactName;
          req.contactMode = item.contactPhone;
          req.relation = item.contactRelation;
          req.contactId = item.id;
        }
      }
      // req.caseId = route.params.caseId;
      req.caseId = props.isOutgoingCall ? req.caseId : route.params.caseId;
      req.urgeTpye = 0;
      insertUrgeRecord(req)
        .then((res) => {
          proxy.$modal.msgSuccess("保存成功！");
          emit("getUrgeList");
          reset();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;

  .flex-item {
    margin-left: 20px;
  }
}
</style>
