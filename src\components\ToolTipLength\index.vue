<template>
  <div>
    <el-tooltip v-if="content?.length > length" placement="top" :hide-after="10">
      <template #content>
        <p :style="`max-width: ${width}px`">{{ content }}</p>
      </template>
      <div>
        <span>
          {{ content.length > length ? `${content.substring(0, length)}...` : content }}
        </span>
      </div>
    </el-tooltip>
    <span v-else>{{ content }}</span>
  </div>
</template>
<script setup>
defineProps({
  content: { type: String, default: "" },
  length: { type: Number, default: 8 },
  width: { type: Number, default: 300 },
});
</script>
