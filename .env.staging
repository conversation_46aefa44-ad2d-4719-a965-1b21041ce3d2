# 页面标题
VITE_APP_TITLE = 调解管理系统

# 测试环境配置
VITE_APP_ENV = 'staging'

# 调诉管理系统/测试环境
VITE_APP_BASE_API = '/prod-api/appeal'

# websocket 配置
# VITE_APP_WS_BASE_URL = 'wss://***********:7444/message-api/pushMessage/'
VITE_APP_WS_BASE_URL = 'wss://appeal-test.amcmj.com/message-api/pushMessage'

# webrtc 配置
# VITE_APP_WEBRTC_API_URL = 'https://call9.amcmj.com/'
# VITE_APP_WEBRTC_WSS_URI = 'wss://call9.amcmj.com:7443/'
# VITE_APP_WEBRTC_SIP_DOMAIN = '***********:7088'
VITE_APP_WEBRTC_API_URL = 'https://call-prod.amcmj.com/prod-api/'
VITE_APP_WEBRTC_WSS_URI = 'wss://call-prod.amcmj.com:7443'
VITE_APP_WEBRTC_SIP_DOMAIN = '***********:7088'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip