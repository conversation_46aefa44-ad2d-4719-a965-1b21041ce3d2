<template>
  <div class="app-container">
    <div class="app-content">
      <div class="app-query">
        <el-form :model="queryParams" ref="queryRef" :inline="true">
          <el-form-item>
            <el-input v-model="queryParams.serialNo" placeholder="请输入文书单号" style="width: 240px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="mt20 mb20">
          <el-button type="primary" plain :disabled="ids.length === 0" @click="downSigntrue()">导出文书内容</el-button>
          <el-button type="primary" plain @click="downSigntrueList()">导出文书列表</el-button>
        </div>
      </div>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="文书单号" align="center" prop="serialNo" />
        <el-table-column label="模板名称" align="center" prop="templateName" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="250px" fixed="right">
          <template #default="{ row }">
            <div>
              <el-button type="text" link @click="previewSigntrue(row)">预览</el-button>
              <el-button type="text" v-if="row.status == 2" link @click="batchCreate(row)">签章文件</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-area">
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <!-- 详情 -->
    <templateDetailsBox ref="templateDetailsBoxRef" />
  </div>
</template>

<script setup name="LawyerDetails">
import { getLetterList, getSignFile } from "@/api/writ/lawyer";
import templateDetailsBox from "./templateDetails";

//全局变量
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const ids = ref([]);
//查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    status: undefined,
    deliveryWay: undefined,
  },
});
//查询之后的参数
const queryData = ref({
  serialNo: undefined,
  status: undefined,
  sendStatusList: undefined,
  deliveryWayList: undefined,
  letterId: route.params.id,
});
const { queryParams } = toRefs(data);
//数据参数
const dataList = ref([]);
const total = ref(0);
//禁用
const single = ref(true);
//获取列表
function getList() {
  loading.value = true;
  const req = JSON.parse(JSON.stringify(queryParams.value));
  req.sendStatusList = req.sendStatusList?.join(",");
  req.deliveryWayList = req.deliveryWayList?.join(",");
  getLetterList(req)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();


/** 多选框选中数据 */
function downSigntrue(row) {
  proxy.downloadforjson(
    "/document/item/export",
    { letterId: route.params.id, ids: ids.value },
    `签章文件_${new Date().getTime()}.zip`
  );
}
// 导出文书列表
async function downSigntrueList() {
  const req = ids.value.length > 0 ? { ids: ids.value } : queryData.value;
  await proxy.downloadforjson(
    "/document/item/exportExcel",
    req,
    `文书列表_${new Date().getTime()}.xlsx`
  );
}

//查询操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  queryData.value.serialNo = queryParams.value.serialNo;
  queryData.value.status = queryParams.value.status;
  queryData.value.deliveryWay = queryParams.value.deliveryWay;
  queryData.value.sendStatus = queryParams.value.sendStatus;
  queryData.value.letterId = queryParams.value.letterId;
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  let signStatusList = selection.map((item) => item.status);
  //判断通过不通过按钮状态
  let statusFlag = false;
  if (signStatusList.length > 0) {
    signStatusList.forEach((item, index) => {
      if ([1].includes(item)) {
        single.value = true;
        statusFlag = true;
      }
    });
    if (!statusFlag) {
      single.value = false;
    }
  } else {
    single.value = true;
  }
}

//重置操作
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    letterId: route.params.id,
    serialNo: undefined,
    status: undefined,
  };
  getList();
}

//模板详情
function previewSigntrue(row) {
  proxy.$refs["templateDetailsBoxRef"].opendialog(row.id);
}


//生成签章
function batchCreate(row) {
  let req = {
    id: row.id,
  };
  getSignFile(req).then((res) => {
    window.open(res.data.url, "_blank");
  });
}
</script>

<style scoped>
.minus-left {
  margin-left: -40px;
}

.select-button {
  float: right;
}

.avatar_img {
  width: 45px;
  height: 45px;
}
</style>
