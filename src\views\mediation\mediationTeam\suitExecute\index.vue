<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" class="side-edge">
        <div class="head-container mb10 pl20">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          团队执行排名
        </div>
        <div class="dept-list">
          <div class="dept-item" v-for="dept in allDept" :key="dept.id">
            <div :class="`${activeDept == dept.id ? 'active' : ''}`" @click="handleChangeRanke(dept.id)">
              {{ `${dept.name}(${dept.caseNum || 0})` }}
            </div>
            <!-- <div :class="`employ-item ${activeDept == employ.id ? 'active' : ''}`" v-for="employ in dept.children"
              :key="employ.id">
              <div @click="handleChangeRanke(employ.id, 1)">{{ `${employ.name}(${employ.caseNum || 0})` }}</div>
            </div> -->
          </div>
        </div>
      </el-col>

      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" :loading="loading" ref="queryRef" inline label-position="right" label-width="auto"
          :class="`${showSearch ? 'form-auto' : 'form-h50'}`">
          <el-form-item prop="caseId" label="案件ID">
            <el-input v-model="queryParams.caseId" style="width: 280px" placeholder="输入搜索关键词" />
          </el-form-item>
          <el-form-item prop="clientName" label="被告">
            <el-input v-model="queryParams.clientName" style="width: 280px" placeholder="输入搜索关键词" />
          </el-form-item>
          <el-form-item prop="caseStatus" label="执行状态">
            <el-select placeholder="请选执行状态" style="width: 280px" v-model="queryParams.caseStatus">
              <el-option label="执行立案" value="执行立案" />
              <el-option label="判决审理" value="判决审理" />
              <el-option label="执行完成" value="执行完成" />
            </el-select>
          </el-form-item>
          <el-form-item prop="involvedWith" label="案件涉及">
            <el-input v-model="queryParams.involvedWith" style="width: 280px" placeholder="输入搜索关键词" />
          </el-form-item>
          <el-form-item prop="executiveCourt" label="执行法院">
            <el-select placeholder="请选择执行法院" style="width: 280px" v-model="queryParams.executiveCourt"
              @focus="getclosed">
              <el-option v-for="item in trialCourtOption" :label="item.info" :key="item.code" :value="item.info" />
            </el-select>
          </el-form-item>
          <el-form-item label="标的额">
            <div class="range-scope" style="width: 280px">
              <el-input v-model="queryParams.amount1" />
              <span style="margin: 0 8px">:</span>
              <el-input v-model="queryParams.amount2" />
            </div>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="loading" icon="Search" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
          <el-button :disabled="selectedArr.length == 0" :loading="loading" @click="handleExport"
            v-if="checkPermi(['mediationTeam:suitExecute:download'])" type="primary">批量导出</el-button>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <el-tabs v-model="activeTab" @tab-click="tabChange">
          <el-tab-pane label="执行立案" name="执行立案" />
          <el-tab-pane label="判决审理" name="判决审理" />
          <el-tab-pane label="执行完成" name="执行完成" />
          <el-tab-pane label="执行撤案" name="执行撤案" />
          <el-tab-pane label="终本案件" name="终本案件" />
          <el-tab-pane label="全部" name="全部" />
        </el-tabs>
        <selectedAll ref="selectedAllRef" v-model:allQuery="allQuery" :selectedArr="selectedArr" :dataList="dataList"
          :cusTableRef="proxy.$refs.multipleTableRef">
          <template #content>
            <span class="case-data-list">
              客户数量：<i class="danger">{{ statistics.size || 0 }}</i>
            </span>
            <span class="case-data-list">
              初始债权总额：<i class="danger">{{ statistics.money || 0 }}</i>
            </span>
            <span class="case-data-list">
              初始债权本金：<i class="danger">{{ statistics.principal || 0 }}</i>
            </span>
          </template>
        </selectedAll>
        <el-table v-loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" :data="dataList">
          <el-table-column type="selection" :selectable="checkSelectable" width="50" align="center" />
          <el-table-column v-if="columns[0].visible" label="案件ID" align="center" prop="caseId" width="100">
            <template #default="{ row }">
              <el-button type="text" link @click="toDetails(row)">{{ row.caseId }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="被告" align="center" prop="clientName" width="120" />
          <el-table-column v-if="columns[2].visible" label="标的额" align="center" prop="remainingDue" width="120" />
          <el-table-column v-if="columns[3].visible" label="身份证号码" align="center" prop="clientIdcard" width="180px" />
          <el-table-column v-if="columns[4].visible" label="户籍地" align="center" prop="clientCensusRegister" width="160"
            show-overflow-tooltip />
          <el-table-column v-if="columns[5].visible" label="执行状态" align="center" prop="caseStatus" width="120" />
          <el-table-column v-if="columns[6].visible" label="结案标的额" align="center" prop="concludeCaseAmount"
            width="120" />
          <el-table-column v-if="columns[7].visible" label="案件涉及" align="center" prop="involvedWith" width="120" />
          <el-table-column v-if="columns[8].visible" label="被执行人下落不明" align="center" prop="isMissing"
            :formatter="row => isNoEnum[row.isMissing]" width="130" />
          <el-table-column v-if="columns[9].visible" label="执行法院" align="center" prop="executiveCourt" width="120"
            show-overflow-tooltip />
          <el-table-column v-if="columns[10].visible" label="结案案由" align="center" prop="closedReason" width="120" />
          <el-table-column v-if="columns[11].visible" label="结案方式" align="center" prop="closedMode" width="100" />
          <el-table-column v-if="columns[12].visible" label="有无结案文书" align="center" key="isFile" prop="isFile"
            :width="120" :formatter="row => ({ 0: '是', 1: '否' }[row.isFile])" />
          <el-table-column v-if="columns[13].visible" label="跟进人员" align="center" prop="updateBy" width="120" />
          <el-table-column v-if="columns[14].visible" label="最近一次跟进时间" align="center" prop="updateTime" width="160px" />
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="SuitExecute">
import { checkPermi } from "@/utils/permission";
import {
  getDeptTreeWithDisposeStage,
  selectExecuteList,
  selectExecuteMoney,
} from "@/api/team/suitExecute";
import { getCourtOptions } from "@/api/common/common"; //法院下拉
import { formatParams } from "@/utils/common";
import { isNoEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const router = useRouter();

//左侧团队树排名
const allDept = ref([]);
const activeDept = ref(undefined)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  stringDeptId: undefined,
  disposeStage: undefined,
  caseId: undefined,
  clientName: undefined,
  caseStatus: undefined,
  involvedWith: undefined,
  executiveCourt: undefined,
  amount1: undefined,
  amount2: undefined,
});
const trialCourtOption = ref([]);
const showSearch = ref(false)
const activeTab = ref("全部");
const dataList = ref([]);
const rangFiles = [];
const loading = ref(false);
const total = ref(0);
const selectedArr = ref([]);
const ids = ref([]);
const allQuery = ref(false);
const statistics = ref({
  size: 0,
  money: 0,
  principal: 0,
});
const columns = ref([
  { key: 0, label: '案件ID', visible: true },
  { key: 1, label: '被告', visible: true },
  { key: 2, label: '标的额', visible: true },
  { key: 3, label: '身份证号码', visible: true },
  { key: 4, label: '户籍地', visible: true },
  { key: 5, label: '执行状态', visible: true },
  { key: 6, label: '结案标的额', visible: true },
  { key: 7, label: '案件涉及', visible: true },
  { key: 8, label: '被执行人下落不明', visible: true },
  { key: 9, label: '执行法院', visible: true },
  { key: 10, label: '结案案由', visible: true },
  { key: 11, label: '结案方式', visible: true },
  { key: 12, label: '有无结案文书', visible: true },
  { key: 13, label: '跟进人员', visible: true },
  { key: 14, label: '最近一次跟进时间', visible: true },
])
// 获取列表
function getList() {
  const reqForm = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)))
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['执行立案', '判决审理', '执行完成', '执行撤案', '终本案件']
  reqForm.smallStageList = String(reqForm.smallStageList)
  loading.value = true;
  selectExecuteList(reqForm).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => loading.value = false);
}
getList();

//获取团队排名
function getTeamTreeData() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangFiles)
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['执行立案', '判决审理', '执行完成', '执行撤案', '终本案件']
  reqForm.smallStageList = String(reqForm.smallStageList)
  delete reqForm.pageNum
  delete reqForm.pageSize
  getDeptTreeWithDisposeStage(reqForm).then((res) => {
    allDept.value = res.data
  });
}
getTeamTreeData();
function handleChangeRanke(val, type) {
  queryParams.value.stringDeptId = val
  activeDept.value = val
  queryParams.value.pageNum = 1
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}
// 获取机构列表
getCourts();
function getCourts() {
  getCourtOptions().then((res) => {
    trialCourtOption.value = res.data;
  });
}

// 搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// 重置
function resetQuery() {
  activeDept.value = undefined
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

// 批量导出
function handleExport() {
  const reqForm = getReqParams()
  proxy.downloadforjson("/team/exportWithExecuteCase", reqForm, `诉讼执行_${new Date().getTime()}.xlsx`);
}

//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!allQuery.value && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0, };
      reslove()
      return false
    }
    selectExecuteMoney(getReqParams()).then((res) => {
      statistics.value = res.data;
    }).finally(() => reslove());
  })
}

// tab筛选
function tabChange() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList()
    getTeamTreeData()
  })
}

//表格选择
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.caseId);
  selectedArr.value = selection;
}

// 表格是否可以选择
function checkSelectable() {
  return !allQuery.value;
}

//跳转案件详情
function toDetails(row, index) {
  const caseId = row.caseId;
  let queryChange = proxy.addFieldsRange(queryParams.value, rangFiles);
  queryChange.pageNum =
    (queryChange.pageNum - 1) * queryChange.pageSize + index + 1;
  queryChange.pageSize = 1;
  let searchInfo = { query: queryChange };
  localStorage.setItem(`searchInfo/${caseId}`, JSON.stringify(searchInfo));
  router.push({ path: `/collection/mycase-detail/caseDetails/${caseId}` });
}
function getReqParams() {
  const reqParams = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangFiles)))
  const reqForm = formatParams(reqParams, selectedArr, allQuery)
  reqForm.condition = allQuery.value
  reqForm.smallStageList = activeTab.value != '全部' ? [activeTab.value] : ['执行立案', '判决审理', '执行完成', '执行撤案', '终本案件']
  return reqForm
}
watch(() => selectedArr.value, () => {
  nextTick(() => {
    if (!loading.value) {
      loading.value = true
      getStaticForQuery().finally(() => loading.value = false)
    }
  })
}, { immediate: true, deep: true })
</script>

<style scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>

<style lang="scss" scoped>
.side-edge {
  height: calc(100vh - 125px);
  padding: 0 !important;
  border-right: 2px solid #eee;
}

.head-container {
  color: #333;
  font-weight: bold;
}

.dept-list {
  color: #5a5a5a;
  cursor: pointer;
  height: 80vh;
  overflow: auto;

  .active {
    border-right: 2px solid #60b2ff;
    background-color: #e6f7ff;
  }

  .dept-item {
    width: 100%;
    line-height: 44px;
    padding-left: 20px;
  }

  .employ-item {
    width: 100%;
    line-height: 44px;
    height: 44px;
    padding-left: 20px;
  }
}
</style>