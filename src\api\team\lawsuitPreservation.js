import request from '@/utils/request'
//获取机构树结构
export function DeptTreeWithStage(params) {
  return request({
    url: '/team/DeptTreeWithStage',
    method: 'get',
    params
  })
}

// 根据条件查询列表信息
export function getStageList(query) {
  return request({
    url: '/team/getStageList',
    method: 'get',
    params: query,
  })
}

// 获取债权统计
export function selectStageWithMoney(data) {
  return request({
    url: '/team/selectStageWithMoney',
    method: 'post',
    data: data,
  })
}
