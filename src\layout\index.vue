<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }" @click="close($event)">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar @setLayout="setLayout" :isPreTestSip="isPreTestSip" :isSip="isSip"/>
        <tags-view v-if="needTagsView" />
        <div ref="messageRef" class="message-slideshow" v-if="slide && messageList.length > 0">
          <el-icon class="slideShow-icon" :size="20" color="#ffac06">
            <WarningFilled />
          </el-icon><span class="slideShow-text">{{
            systemInfo?.length > 50 ? `${systemInfo.substring(0, 50)}......` : systemInfo
          }}</span>
          <el-icon class="close-icon" @click="slide = false" :size="20">
            <Close />
          </el-icon>
        </div>
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>

    <!-- 右侧悬浮呼叫 -->
    <callBarVue v-if="showCallBtn" :btnType="1" />
  </div>
</template>

<script setup>
import { useWindowSize } from "@vueuse/core";
import Sidebar from "./components/Sidebar/index.vue";
import callBarVue from '@/components/callBar/index.vue'

import { AppMain, Navbar, Settings, TagsView } from "./components";
import defaultSettings from "@/settings";
import { checkUserSip } from "@/api/system/user";
import { selectRestrictedState } from "@/api/system/menu";
import { getNavMessage, getBaiduMapKey } from "@/api/common/common";
import { loadBaiduMapScript } from "../utils/common";

const store = useStore();
//是否显示呼叫按钮
const showCallBtn = computed(() => store.getters.showCallBtn);
provide('showCallBtn', showCallBtn)

const theme = computed(() => store.state.settings.theme);
const sideTheme = computed(() => store.state.settings.sideTheme);
const sidebar = computed(() => store.state.app.sidebar);
const device = computed(() => store.state.app.device);
const needTagsView = computed(() => store.state.settings.tagsView);
const fixedHeader = computed(() => store.state.settings.fixedHeader);
const unselect = ref(false);
const getters = computed(() => store.getters);
const messageCount = computed(() => store.state.message.count);

const { proxy } = getCurrentInstance();
const slide = ref(true);
const messageList = ref([]);
const systemInfo = ref(undefined);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === "mobile",
  unselect: unselect.value,
}));

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

const isPreTestSip = ref(undefined);
const isSip = ref(undefined);

watchEffect(() => {
  if (device.value === "mobile" && sidebar.value.opened) {
    store.dispatch("app/closeSideBar", { withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    store.dispatch("app/toggleDevice", "mobile");
    store.dispatch("app/closeSideBar", { withoutAnimation: true });
  } else {
    store.dispatch("app/toggleDevice", "desktop");
  }
});

function handleClickOutside() {
  store.dispatch("app/closeSideBar", { withoutAnimation: false });
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}

function getFileSelect() {
  selectRestrictedState()
    .then((res) => {
      unselect.value = res?.data !== 0;
    })
}
getFileSelect();
//开启Mrtc
checkUserSip().then((res) => {
  if (res.data?.isSip) {
    //Mrtc
    let option = {
      fixedTop: 85,
      autoLogin: true,
      user: res.data?.account || null,
      password: res?.data.password || null,
      API_URL: import.meta.env.VITE_APP_WEBRTC_API_URL,
      WSS_URI: import.meta.env.VITE_APP_WEBRTC_WSS_URI,
      SIP_DOMAIN: import.meta.env.VITE_APP_WEBRTC_SIP_DOMAIN,
    };
    webrtcSDK.init(option, (e) => {
      let { isRegistered, iscalling, isincall } = e;
      store.commit("webrtc/SET_IS_REGISTERED", isRegistered);
      store.commit("webrtc/SET_IS_CALLING", iscalling);
      store.commit("webrtc/SET_IS_IN_CALL", isincall);
    });

    isSip.value = res.data.isSip;
    isPreTestSip.value = res.data.isPreTestSip;
  }
});


//首页消息
function homeMessage() {
  getNavMessage()
    .then((res) => {
      messageList.value = res.data;
      systemInfo.value = res?.data[0]?.messageContent;
    })
    .catch(() => {
      messageList.value = [];
    });
  setTimeout(function () {
    slide.value = false;
  }, 10000);
}
homeMessage();

const close = (e) => {
  let callId = document.getElementById("sdk-container");
  if (callId && !callId.contains(e.target)) {
    webrtcSDK.handleExpand(false);
  }
};

function getBaiduMapKeyFun() {
  getBaiduMapKey().then((res) => {
    let ak = ''
    if (res.data && typeof res.data == 'object') {
      ak = res.data.legalVal
    } else {
      ak = res.data || ''
    }
    loadBaiduMapScript(ak)
  })
}
onMounted(() => {
  store.dispatch('getWebSocketAddressAction')
  // 确保callback函数在全局作用域定义
  // getBaiduMapKeyFun()
})
watch(
  () => messageCount.value,
  () => {
    homeMessage();
    slide.value = true;
  }
);
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}

.message-slideshow {
  background-color: #fffcec;
  border: 1px solid rgb(255 251 143);
  color: #666;
  font-size: 14px;
  height: 35px;

  .slideShow-icon {
    margin: 7px 10px;
    display: inline-block;
  }

  .slideShow-text {
    display: inline-block;
    line-height: 35px;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .close-icon {
    float: right;
    margin-right: 10px;
    margin-top: 10px;
    cursor: pointer;
  }
}

.unselect {
  -webkit-touch-callout: none;
  /*系统默认菜单被禁用*/
  -webkit-user-select: none;
  /*webkit浏览器*/
  -khtml-user-select: none;
  /*早期浏览器*/
  -moz-user-select: none;
  /*火狐*/
  -ms-user-select: none;
  /*IE10*/
  user-select: none;
}

.unselect input {
  -webkit-user-select: auto;
  /*webkit浏览器*/
}

.unselect textarea {
  -webkit-user-select: auto;
  /*webkit浏览器*/
}
</style>
