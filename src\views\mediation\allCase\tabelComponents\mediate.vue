<template>
  <div>
    <el-form
      :model="queryParams"
      ref="queryRef"
      inline
      label-width="120px"
      :class="`${showSearch ? 'form-auto' : 'form-h50'}`"
    >
      <el-form-item prop="packageName" label="资产包名称">
        <el-input
          v-model="queryParams.packageName"
          style="width: 240px"
          placeholder="请输入资产包名称"
        />
      </el-form-item>
      <el-form-item prop="caseId" label="案件ID">
        <el-input
          v-model="queryParams.caseId"
          style="width: 240px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="被告">
        <el-input
          v-model="queryParams.clientName"
          style="width: 240px"
          placeholder="请输入被告"
        />
      </el-form-item>
      <el-form-item prop="mediationNum" label="调解号">
        <el-input
          v-model="queryParams.mediationNum"
          style="width: 240px"
          placeholder="请输入调解号"
        />
      </el-form-item>
      <el-form-item label="标的额">
        <div class="range-scope" style="width: 240px">
          <el-input v-model="queryParams.amount1" />
          <span>-</span>
          <el-input v-model="queryParams.amount2" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <div class="operation-revealing-area mb10">
      <!-- <el-button
        plain
        type="primary"
        @click="openCreateCaseTaskDialog"
        :disabled="single"
        v-hasPermi="['saasc:collection:createOutboundTasks']"
        >创建智能外呼任务</el-button
      > -->
      <el-button
        plain
        type="success"
        :disabled="single"
        v-hasPermi="['saasc:collection:phoneclean']"
        @click="openPhoneCleanDialog"
        >号码清洗</el-button
      >
      <el-button type="primary" plain @click="openhelpUrge"
      >申请协助调解</el-button
      >
      <el-button
        plain
        type="success"
        :disabled="single"
        @click="opendialog({ type: 2, row: selectedArr })"
        v-hasPermi="['saasc:case:insertKeep']"
        >申请留案</el-button
      >
      <!-- <el-button
        plain
        :disabled="single"
        type="primary"
        @click="openCreateCaseTaskAiDialog"
        v-hasPermi="['saasc:case:createOutboundTasks']"
        >创建智能AI外呼任务</el-button
      > -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
      <!-- <el-tab-pane label="待上传调解材料" name="待上传调解材料" />
            <el-tab-pane label="调解号" name="生成调解号" />
            <el-tab-pane label="司法确认材料" name="司法确认材料" />
            <el-tab-pane label="司法确认送达" name="司法确认送达" />
            <el-tab-pane label="司法确认书" name="下发司法确认书" /> -->
      <el-tab-pane label="全部" name="全部" />
    </el-tabs>
    <selectedAll
      ref="selectedAllRef"
      v-model:allQuery="queryParams.allQuery"
      :selectedArr="selectedArr"
      :dataList="dataList"
      :cusTableRef="proxy.$refs.multipleTableRef"
    >
      <template #content>
        <span class="case-data-list">
          案件数量：<i class="danger">{{ statistics.size || 0 }}</i>
        </span>
        <span class="case-data-list">
          初始债权总额：<i class="danger">{{ numFilter(statistics.money) }}</i>
        </span>
        <span class="case-data-list">
          初始债权本金：<i class="danger">{{
            numFilter(statistics.principal)
          }}</i>
        </span>
      </template>
    </selectedAll>

    <el-table
      v-loading="loading"
      ref="multipleTableRef"
      class="multiple-table"
      @selection-change="handleSelectionChange"
      :data="dataList"
    >
      <el-table-column
        type="selection"
        :selectable="checkSelectable"
        width="50"
        align="center"
      />
      <el-table-column
        v-if="columns[0].visible"
        label="案件ID"
        align="center"
        prop="caseId"
        width="120"
      >
        <template #default="{ row, $index }">
          <div class="df-center">
            <el-tooltip v-if="row.labelContent" placement="top">
              <template #content>{{ row.labelContent }}</template>
              <case-label
                class="ml5"
                v-if="row.label && row.label != 7"
                :code="row.label"
              />
            </el-tooltip>
            <el-button type="text" @click="toDetails(row, $index)">{{
              row.caseId
            }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[1].visible"
        label="资产包名称"
        align="center"
        prop="packageName"
        width="120px"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="转让方"
        align="center"
        prop="entrustingPartyName"
        width="120px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[3].visible"
        label="调解状态"
        align="center"
        prop="mediatedStage"
        width="120px"
      />
      <el-table-column
        v-if="columns[4].visible"
        label="被告"
        align="center"
        prop="clientName"
        width="120px"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="手机号码"
        align="center"
        prop="clientPhone"
        width="110px"
      />
      <el-table-column
        v-if="columns[6].visible"
        label="身份证号码"
        align="center"
        prop="clientIdNum"
        width="180px"
      />
      <el-table-column
        v-if="columns[7].visible"
        label="户籍地"
        align="center"
        prop="clientCensusRegister"
        width="160px"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="columns[8].visible"
        label="标的额"
        align="center"
        prop="remainingDue"
      >
        <template #default="{ row }">
          <span>{{ numFilter(row.remainingDue) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[9].visible"
        label="调解号"
        align="center"
        prop="mediationNum"
        width="110px"
      >
        <template #default="{ row }">
          <Tooltip :content="row.mediationNum" :length="10" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[10].visible"
        label="司法送达链接"
        align="center"
        prop="publicityLink"
        width="120px"
      >
        <template #default="{ row }">
          <Tooltip :content="row.publicityLink" :length="10" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[11].visible"
        label="跟进人员"
        align="center"
        prop="updateBy"
      />
      <el-table-column
        v-if="columns[12].visible"
        label="最近一次跟进时间"
        align="center"
        prop="updateTime"
        width="160px"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建智能外呼任务 -->
    <createCaseTaskDialog ref="createCaseTaskRef" @close="getList" />

    <!-- 号码清洗 -->
    <applyPhoneClean ref="applyPhoneCleanRef" @getList="getList" />

    <!-- 申请协催 -->
    <helpUrgeVue ref="helpUrgeRef" />

    <!-- 案件操作 -->
     <!--       :checkedType="checkedType[0]"
      :query="addFieldsRange(queryParams, rangfiles)" -->
    <handleDialog
      ref="handleDialogRef"
      @getList="getList"
      :destroy-on-close="true"
    />

    <!-- <createTaskDialog ref="createTaskRef" @close="getList" /> -->
  </div>
</template>

<script setup>
import createCaseTaskDialog from "@/views/appreciation/dialog/createCaseTask.vue";
// import createTaskDialog from "@/views/appreciation/voiceTask/dialog/createTask.vue";
import applyPhoneClean from "./mediateDialog/applyPhoneClean.vue";
import helpUrgeVue from "@/views/mediation/allCaseDetail/dialog/helpUrge.vue";
import handleDialog from "@/views/case/dialogIndex/handleDialog.vue";
import { getPhoneList } from "@/api/mediation/phoneMediation";
import {
  phoneMediationList,
  selectMediationWithMoney,
} from "@/api/team/appealCase";
import { formatParams2 } from "@/utils/common";
import { checkUserSip } from "@/api/system/user";

//全局数据
const router = useRouter();
const { proxy } = getCurrentInstance();
const loading = ref(false);
const dataList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
});
const statistics = ref({
  caseNum: 0,
  money: 0,
  principal: 0,
});
const activeTab = ref("全部");
const checkedType = ref(["本页选中"]);
const total = ref(0);
//需要拆分的字段
const rangfiles = [];
const selectedArr = ref([]);
const showSearch = ref(false);
const columns = ref([
  { key: 0, label: "案件ID", visible: true },
  { key: 1, label: "资产包名称", visible: true },
  { key: 2, label: "转让方", visible: true },
  { key: 3, label: "调解状态", visible: true },
  { key: 4, label: "被告", visible: true },
  { key: 5, label: "手机号码", visible: true },
  { key: 6, label: "身份证号码", visible: true },
  { key: 7, label: "户籍地", visible: true },
  { key: 8, label: "标的额", visible: true },
  { key: 9, label: "调解号", visible: true },
  { key: 10, label: "司法送达链接", visible: true },
  { key: 11, label: "跟进人员", visible: true },
  { key: 12, label: "最近一次跟进时间", visible: true },
]);

const single = ref(true);
const caseIds = ref([]);

//获取列表
function getList() {
  const reqForm = proxy.addFieldsRange(queryParams.value, rangfiles);
  reqForm.mediatedStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : [
          "待上传调解材料",
          "生成调解号",
          "司法确认材料",
          "司法确认送达",
          "下发司法确认书",
        ];
  reqForm.mediatedStageList = String(reqForm.mediatedStageList);
  reqForm.mineQuery = true;
  loading.value = true;
  getPhoneList(reqForm)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => (loading.value = false));
}
getList();

const openCreateCaseTaskAiDialog = () => {
  const query = JSON.parse(JSON.stringify(queryParams.value));
  proxy.$refs["createTaskRef"].openDialog(query, 0);
};
function checkSelectable() {
  return !queryParams.value.allQuery;
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  nextTick(() => {
    getList();
  });
}

function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  nextTick(() => {
    getList();
  });
}

//表格选择
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  single.value = !(selection.length != 0);
  caseIds.value = selection.map((item) => item.caseId);
}

//跳转案件详情
function toDetails(row, index) {
  // let queryChange = proxy.addFieldsRange(queryParams.value, rangfiles);
  // let pageNumber = (queryChange.pageNum - 1) * queryChange.pageSize + index + 1; //当前第几页(变动)
  // delete queryChange.pageNum;
  // delete queryChange.pageSize;
  // queryChange.mediatedStageList = activeTab.value != '全部' ? [activeTab.value] : ["待上传调解材料", "生成调解号", "司法确认材料", "司法确认送达", "下发司法确认书",]
  // let searchInfo = {
  //     query: {
  //         manageQueryParam: queryChange, //查询参数
  //         pageNumber: pageNumber, //当前第几页(变动)
  //         pageSize: 1, //一页一条
  //         pageIndex: 0, //当前第一条
  //         caseIdCurrent: row.caseId, //当前案件id
  //     }, //查询参数
  //     type: "mycase",
  //     total: total.value, //查询到的案件总数
  // };
  // localStorage.setItem(`searchInfo/${row.caseId}`, JSON.stringify(searchInfo));
  // router.push({ path: `/collection/mycase-detail/caseDetails/${row.caseId}`, query: { type: "myCase" } });
  router.push({ path: `/case/allcase-detail/caseDetails/${row.caseId}` });
}

//获取债权统计
function getStaticForQuery() {
  return new Promise((reslove, reject) => {
    if (!queryParams.value.allQuery && selectedArr.value.length == 0) {
      statistics.value = { size: 0, money: 0, principal: 0 };
      reslove();
      return false;
    }
    nextTick(() => {
      const reqForm = getReqParams();
      reqForm.mineQuery = true;
      selectMediationWithMoney(reqForm)
        .then((res) => {
          statistics.value = res.data;
        })
        .finally(() => reslove());
    });
  });
}

function getReqParams() {
  const reqParams = JSON.parse(
    JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles))
  );
  const reqForm = formatParams2(
    reqParams,
    selectedArr,
    queryParams.value.allQuery
  );
  reqForm.condition = queryParams.value.allQuery;
  reqForm.allQuery = queryParams.value.allQuery;
  if (reqForm.condition) {
    reqForm.ids && delete reqForm.ids;
  }
  reqForm.mediatedStageList =
    activeTab.value != "全部"
      ? [activeTab.value]
      : [
          "待上传调解材料",
          "生成调解号",
          "司法确认材料",
          "司法确认送达",
          "下发司法确认书",
        ];
  reqForm.mineQuery = true;
  return reqForm;
}

function openCreateCaseTaskDialog() {
  checkUserSip().then((res) => {
    if (res.data?.isPreTestSip) {
      //   const query = JSON.parse(JSON.stringify(proxy.addFieldsRange(queryParams.value, rangfiles)));
      //   query.condition = query.allQuery;
      //   if (!query.allQuery) query.caseIds = caseIds.value;
      const query = getReqParams();
      query.mineQuery = true;
      proxy.$refs["createCaseTaskRef"].openDialog(query, 0);
    } else {
      proxy.$modal.msgWarning("未分配预测式外呼坐席");
    }
  });
}

function openPhoneCleanDialog() {
  let form = {};
  if (queryParams.value.allQuery) {
    Object.assign(form, queryParams.value);
  } else {
    Object.assign(form, {
      caseIds: caseIds.value,
      allQuery: queryParams.value.allQuery,
    });
  }
  proxy.$refs["applyPhoneCleanRef"].opendialog(form);
}

watch(
  () => [selectedArr.value, queryParams.value.allQuery],
  () => {
    nextTick(() => {
      if (!loading.value) {
        loading.value = true;
        getStaticForQuery().finally(() => (loading.value = false));
      }
    });
  },
  { immediate: true, deep: true }
);

//打开申请协催
function openhelpUrge() {
  proxy.$refs["helpUrgeRef"].opendialog();
}

//打开回收案件等案件操作
function opendialog(data) {
  console.log("mid",data);
  let condition = undefined;
  let optCaseList = data.row?.filter((item, index) => {
    return item.caseState == 0 || item.caseState == 1 || item.caseState == 3;
  });
  if (data.type == 4) optCaseList = data.row;
  if (optCaseList.length == 0)
    return proxy.$modal.msgWarning("没有可以操作的案件");
  if (data.row?.length == 1) condition = false;
  proxy.$refs["handleDialogRef"].openDialog(data.type, optCaseList, condition);
}


defineExpose({ getList });


</script>

<style lang="scss" scoped></style>
