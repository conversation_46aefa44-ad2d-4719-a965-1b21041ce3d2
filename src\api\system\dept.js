import request from '@/utils/request'

// 部门树结构数据
export function deptTree(query) {
  console.log('部门树结构数据')
  return request({
    url: '/settings/selectDeptTreeType',
    method: 'get',
  })
}

//添加部门
export function addDept(data) {
  return request({
    url: '/settings/insertDept',
    method: 'post',
    data: data
  })
}

//修改部门
export function updateDept(data) {
  return request({
    url: '/settings/updateDept',
    method: 'put',
    data: data
  })
}

//删除部门
export function deleteDept(id) {
  return request({
    url: '/settings/deleteDept/' + id,
    method: 'put'
  })
}