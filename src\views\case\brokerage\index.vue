<template>
  <div class="app-container">
    <div class="select-date ml20">
      <div class="date-year">
        日期：
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          placeholder="YYYY"
          value-format="YYYY"
          @change="handleQuery($event, 'year')"
        />
      </div>
      <div class="date-month ml20">
        <div
          v-for="month in 12"
          :class="['month', queryParams.month === month && 'active']"
          :key="month"
          @click="handleQuery(month, 'month')"
        >
          {{ month }}月
        </div>
      </div>
      <div
        class="export-btn"
        v-if="
          form.detailsId &&
          [2, 3].includes(form.states) &&
          checkPermi(['brokerage:brokerageAccount:export'])
        "
      >
        <el-button type="primary" @click="handlePreviewPDF"> 导出 </el-button>
      </div>
    </div>
    <div v-if="form.detailsId">
      <div class="mt20 mb20">
        <el-tag size="large">{{ stutasFor(form.states) }}</el-tag>
      </div>
      <div class="brokerage-item withdrawal-assets">
        <div class="mb20">1、资产回收情况</div>
        <el-table
          v-loading="loading"
          class="mt10"
          ref="multipleTableRef"
          :data="brokerageData.assetsList"
        >
          <el-table-column
            label="转让方"
            align="center"
            key="transferorName"
            prop="transferorName"
          />
          <el-table-column
            label="资产批次号"
            align="center"
            key="batchNum"
            prop="batchNum"
          />
          <el-table-column label="案件id" align="center" key="caseId" prop="caseId" />
          <el-table-column
            label="回款金额"
            align="center"
            key="repaymentMoney"
            prop="repaymentMoney"
          >
            <template #default="{ row }">
              {{ numFilter(row.repaymentMoney) }}
            </template>
          </el-table-column>
          <el-table-column
            label="实际回款时间"
            align="center"
            key="repaymentDate"
            prop="repaymentDate"
          />
          <el-table-column
            label="服务费率（%）"
            align="center"
            key="serviceRate"
            prop="serviceRate"
          />
          <el-table-column label="佣金" align="center" key="brokerage" prop="brokerage">
            <template #default="{ row }">
              {{ numFilter(row.brokerage) }}
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getAssetsList"
        />
      </div>
      <div class="brokerage-item reward-punish">
        <div class="mb20">2、机构风险奖罚</div>
        <el-table
          v-loading="loading"
          class="mt10"
          ref="multipleTableRef"
          :data="brokerageData.riskList"
        >
          <el-table-column
            label="奖罚名称"
            align="center"
            key="rewardName"
            prop="rewardName"
          ></el-table-column>
          <el-table-column
            label="属性"
            align="center"
            key="positiveNegative"
            prop="positiveNegative"
          >
            <template #default="{ row }">
              <span>
                {{ row.positiveNegative == 1 ? "+" : "-" }}{{ formatNumber(row.floatingRate) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            key="remarks"
            prop="remarks"
          >
            <template #default="{row}">
              <Tooltip :content="row.remarks" :length="50" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="brokerage-item total">
        <div class="mb20">3、总计</div>
        <div class="total-table">
          <div>
            <div>回收合计</div>
            <div>风险奖罚合计</div>
            <div>总计</div>
          </div>
          <div>
            <div>{{ numFilter(brokerageData.totalRecycling) }}元</div>
            <div>{{ numFilter(brokerageData.totalRisk) }}元</div>
            <div>{{ numFilter(brokerageData.total) }}元</div>
          </div>
        </div>
      </div>
      <div class="brokerage-item text-center btn" v-if="form.states == 1">
        <el-button
          size="large"
          type="info"
          v-if="checkPermi(['brokerage:brokerageAccount:dissent'])"
          @click="openDialog(true)"
        >
          有异议
        </el-button>
        <el-button
          size="large"
          type="primary"
          v-if="checkPermi(['brokerage:brokerageAccount:confirm'])"
          @click="openDialog(false)"
        >
          确认
        </el-button>
      </div>
    </div>
    <div v-else class="not-data">当月没有处理的数据！</div>

    <el-dialog title="提示" width="600px" v-model="open" @close="cancel">
      <div class="hint">
        <el-icon>
          <WarningFilled class="reason-icon" />
        </el-icon>
        <span v-if="isDissent">是否确认有异议?</span>
        <div v-else>
          <div>是否确认?</div>
          <span>此操作将会确认佣金结算，请确定是否核对完成！</span>
        </div>
      </div>
      <el-form
        v-if="isDissent"
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="124px"
      >
        <el-form-item label="有异议原因" prop="rejectReason">
          <el-input
            type="textarea"
            v-model="form.rejectReason"
            placeholder="请输入有异议原因"
            maxlength="300"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submit"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="BrokerageAccount">
import {
  getDetailsBrokerage,
  getRecoveryAssetById,
  getRiskTeamById,
  getStatisticsMoneyById,
  rejectObjection,
  confirmBrokerage,
  exportFileName,
} from "@/api/case/brokerage/brokerage";
import { checkPermi } from "@/utils/permission";
import { nextTick, reactive } from "vue";
import { useRouter } from "vue-router";

const { proxy } = getCurrentInstance();
const router = useRouter();

//请求参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  year: new Date().getFullYear().toString(),
  month: new Date().getMonth() + 1,
  settlementDate: `${new Date().getFullYear()}-${new Date().getMonth() + 1}`,
});
const data = reactive({
  form: {
    rejectReason: "",
    detailsId: undefined,
    states: undefined,
  },
  rules: {
    rejectReason: [{ required: true, trigger: "blur", message: "请输入有异议原因" }],
  },
});
const open = ref(false);
// 是否有异议
const isDissent = ref(true);
const { form, rules } = toRefs(data);
const brokerageData = reactive({
  assetsList: [], // 资产回收列表
  riskList: [], // 机构风险列表
  totalRecycling: undefined, // 回收合计
  totalRisk: undefined, // 风险奖罚合计
  total: undefined, // 总计
});
const loading = ref(false);
const total = ref(0);

//获取列表数据
async function getList() {
  loading.value = true;
  try {
    const req = JSON.parse(JSON.stringify(queryParams.value));
    const res = await getDetailsBrokerage({
      settlementDate: req.settlementDate,
    });
    if (!Object.hasOwnProperty.call(res, "data")) {
      loading.value = false;
      form.value.detailsId = undefined;
      form.value.states = undefined;
      return false;
    }
    const { id = undefined, states = undefined } = res?.data;
    form.value.detailsId = id;
    form.value.states = states;
    nextTick(() => {
      getAssetsList();
      getRiskTeamById({ detailsId: id, ...req }).then((res) => {
        brokerageData.riskList = res.data || [];
      });
      getStatisticsMoneyById({ detailsId: id }).then((res) => {
        brokerageData.totalRecycling = res.data.totalRecycling || 0;
        brokerageData.totalRisk = res.data.totalRisk || 0;
        brokerageData.total = res.data.total || 0;
        loading.value = false;
      });
    });
  } catch (error) {
    loading.value = false;
    proxy.$modal.msgError(error.message);
  }
}
getList();

// 导出文件
function handlePreviewPDF() {
  const req = { detailsId: form.value.detailsId };
  exportFileName(req)
    .then((res) => {
      proxy.download("/brokerageTeam/exportInformation", req, res.data);
      proxy.$modal.msgSuccess("操作成功");
    })
    .catch((err) => {
      proxy.$modal.msgWarning(err.message);
    });
}

// 获取资产回收列表
function getAssetsList() {
  const req = JSON.parse(JSON.stringify(queryParams.value));
  getRecoveryAssetById({
    detailsId: form.value.detailsId,
    pageNum: req.pageNum,
    pageSize: req.pageSize,
  }).then((res) => {
    brokerageData.assetsList = res.rows || [];
    total.value = res.total || 0;
  });
}

// 根据 年/月 查询数据
function handleQuery(date, type) {
  if (date === queryParams.value.month) {
    return false;
  }
  let year = undefined;
  let month = undefined;

  if (type == "month") {
    month = date;
    year = queryParams.value.year;
  } else {
    year = date;
    month = queryParams.value.month;
  }
  queryParams.value.pageNum = 1;
  queryParams.value[type] = date;
  queryParams.value.settlementDate = `${year}-${month}`;
  getList();
}

// 打开弹窗
function openDialog(value) {
  open.value = true;
  isDissent.value = value;
}

// 提交
function submit() {
  loading.value = true;
  const requse = isDissent.value ? rejectObjection : confirmBrokerage;
  requse(form.value)
    .then((res) => {
      proxy.$modal.msgSuccess("提交成功！");
      loading.value = false;
      open.value = false;
      getList();
    })
    .catch((error) => {
      open.value = false;
      loading.value = false;
      proxy.$modal.msgError(error.msg);
    });
}

function cancel() {
  proxy.resetForm("formRef");
  form.value.rejectReason = undefined;
  proxy.$refs["formRef"].resetFields();
  open.value = false;
}

// 状态
function stutasFor(stuta) {
  return ["待处理", "待确认", "已确认", "已结佣", "已驳回"][stuta];
}

// 保留两位小数
function formatNumber(input) {
  // 首先，检查输入是否是一个字符串
  if (typeof input === 'string') {
    // 如果是，尝试将其转换为数字
    let num = Number(input);
    // 检查转换是否成功
    if (isNaN(num)) {
      console.error('输入的字符串不能被转换为数字');
      return 0.00;
    }
    // 如果成功，使用 toFixed 方法将数字转换为保留两位小数的字符串
    return num.toFixed(2);
  } else if (typeof input === 'number') {
    // 如果输入已经是一个数字，直接使用 toFixed 方法
    return input.toFixed(2);
  } else {
    // 如果输入既不是字符串也不是数字，返回一个错误
    console.error('输入必须是一个数字或可以转换为数字的字符串');
    return 0.00;
  }
}


</script>
<style lang="scss" scoped>
.select-date {
  display: flex;
  .date-year {
    line-height: 44px;
  }
  .date-month {
    display: flex;
    align-items: center;
    & > div {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 28px;
      font-size: 14px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      border: 1px solid #ebebeb;
      background-color: #fafafa;
      cursor: pointer;
    }
    .active {
      color: #1890ff;
      border-bottom: none;
      background-color: #fff;
    }
  }
  .export-btn {
    line-height: 44px;
    margin-left: 40px;
  }
}
.brokerage-item {
  margin-bottom: 50px;
}
.total-table {
  display: flex;
  border: 1px solid #ccc;

  & > div {
    flex: 1;
    text-align: center;
    line-height: 60px;
    :first-child {
      border-right: none;
    }
    :nth-child(2) {
      border-top: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
    }
  }
  :first-child {
    border-right: 1px solid #ccc;
  }
}
.hint {
  display: flex;
  align-items: center;
  margin-left: 25px;
  line-height: 44px;
  font-size: 18px;
  margin-bottom: 20px;
  span {
    margin-left: 5px;
  }
}
.not-data {
  margin: 50px auto;
}
.reason-icon {
  color: #ff9900;
}
</style>
