import request from '@/utils/request'

//获取转让方-产品树 
export function assetOwnerTree() {
    return request({
        url: '/teamOwner/getTree',
        method: 'get',
		gateway: 'cis'
    })
}

//添加转让方
export function addAssetOwner(data) {
    return request({
        url: '/teamOwner/add',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//编辑转让方
export function editAssetOwner(data) {
    return request({
        url: '/teamOwner/edit',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//删除转让方
export function delAssetOwner(id) {
    let data = {
        id: id
    }
    return request({
        url: '/teamOwner/deleted',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//获取产品列表
export function getProList(query) {
    return request({
        url: '/teamProduct/list',
        method: 'get',
        params: query,
		gateway: 'cis'
    })
}

//修改产品状态
export function setProdStata(data) {
    return request({
        url: '/teamProduct/updateState',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//获取转让方列表
export function assetOwnerList() {
    return request({
        url: '/teamOwner/list',
        method: 'get',
        gateway: 'cis'
    })
}

//检查产品类型
export function checkProName(query) {
    return request({
        url: '/asset/product/checkName',
        method: 'get',
        params:query
    })
}

//获取模板字段
export function getTplFields(tplid) {
    return request({
        url: '/teamProduct/getInitTemplate',
        method: 'get',
        params: {currencyTemplateId: tplid},
		gateway: 'cis'
    })
} 

//创建产品
export function addProduct(data) {
    return request({
        url: '/teamProduct/add',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//获取产品详情
export function productDetail(id) {
    let data = {
        id: id
    }
    return request({
        url: '/teamProduct/findProduct',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//编辑产品
export function editProduct(data) {
    return request({
        url: '/teamProduct/edit',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//删除产品
export function delProduct(id) {
    let data = {
        id: id
    }
    return request({
        url: '/teamProduct/deleted',
        method: 'post',
        data: data,
		gateway: 'cis'
    })
}

//关键词搜索
export function getDictTransferor(query) {
    return request({
        url: '/asset/owner/getDictTransferor',
        method: 'get',
        params:query
    })
}

//关键词搜索
export function getDictProductType(query) {
    return request({
        url: '/asset/product/getDictProductType',
        method: 'get',
        params:query
    })
}

//创建方下拉框
export function addName(query) {
    return request({
        url: '/asset/owner/add',
        method: 'post',
        params:query
    })
}