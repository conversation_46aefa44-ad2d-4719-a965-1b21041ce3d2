import request from '@/utils/request'

//案件管理列表
export function caseManageList(query) {
    return request({
        url: '/case/selectCase',
        method: 'get',
        params: query
    })
}

//案件分配搜索结果
export function strategyAllocationQuery(data) {
    return request({
        url: '/team/selectTeamCases',
        method: 'post',
        data: data
    })
}

//案件分配搜索案件量和案件结果
export function selectCaseManagesMoney(data) {
    return request({
        url: '/case/selectCaseManagesMoney',
        method: 'post',
        data: data
    })
}

//模板分案导入
export function templateImport(data) {
    return request({
        url: '/case/importData',
        method: 'post',
        data: data
    })
}

//回收案件
export function reclaimCase(data) {
    return request({
        url: '/case/updateRecovery',
        method: 'PUT',
        data: data
    })
}

//停催
export function insertStop(data) {
    return request({
        url: '/team/insertStop',
        method: 'post',
        data: data
    })
}

//留案
export function insertKeep(data) {
    return request({
        url: '/team/insertKeep',
        method: 'post',
        data: data
    })
}

//退案
export function insertRetreat(data) {
    return request({
        url: '/team/insertRetreat',
        method: 'post',
        data: data
    })
}

//指定分案提交
export function updateCase(data) {
    return request({
        url: '/case/updateCase',
        method: 'PUT',
        data: data
    })
}

//分案模板预览
export function previewAllocationResults(data) {
    return request({
        url: '/case/specifyDivisionalData',
        method: 'post',
        data: data
    })
}

//获取导出催记字段
export function selectTickField(query) {
    return request({
        url: '/case/selectTickField',
        method: 'get',
        params: query
    })
}

//提交标记案件
export function selectMarkCase(data) {
    return request({
        url: '/team/selectMarkCase',
        method: 'post',
        data: data
    })
}

//案批量操作(停催)
export function importDataStop(data) {
    return request({
        url: '/case/importDataStop',
        method: 'post',
        data: data
    })
}

//案批量操作(留案)
export function importKeepCase(data) {
    return request({
        url: '/case/importKeepCase',
        method: 'post',
        data: data
    })
}

//案批量操作(退案)
export function importDataBack(data) {
    return request({
        url: '/case/importDataBack',
        method: 'post',
        data: data
    })
}

//案批量操作(停催)
export function insertStopUrging(data) {
    return request({
        url: '/case/insertStopUrging',
        method: 'post',
        data: data
    })
}

//案批量操作(退案)
export function insertDataBack(data) {
    return request({
        url: '/case/insertDataBack',
        method: 'post',
        data: data
    })
}

//案批量操作(留案)
export function insertKeepCase(data) {
    return request({
        url: '/case/insertKeepCase',
        method: 'post',
        data: data
    })
}

//规则分案预览
export function ruleSplitPreview(data) {
    return request({
        url: 'case/ruleSplitPreview',
        method: 'post',
        data: data
    })
}

//规则分案提交
export function writeRuleDivision(data) {
    return request({
        url: 'case/writeRuleDivision',
        method: 'post',
        data: data
    })
}

//查询异步进度
export function scheduleAllocation(query) {
    return request({
        url: '/schedule/scheduleAllocation',
        method: 'get',
        params: query
    })
}

//查询异步进度
export function exportCase(data) {
    return request({
        url: '/team/exportCase',
        method: 'post',
        data: data
    })
}

// 催记导出
export function selectUrgeRecord(data) {
    return request({
        url: '/team/selectUrgeRecord',
        method: 'post',
        data: data
    })
}

// 恢复案件
export function recoverCase(data) {
    return request({
        url: '/case/recoverCase',
        method: 'post',
        data: data
    })
}

//恢复案件
export function queryAllIdRestore(query) {
    return request({
        url: '/case/queryAllIdRestore',
        method: 'get',
        params: query
    })
}
