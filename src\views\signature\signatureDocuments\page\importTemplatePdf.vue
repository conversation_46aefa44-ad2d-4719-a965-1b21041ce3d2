<template>
  <el-input v-show="false" v-model="sourceFileUrl" />
  <el-upload
    ref="uploadRef"
    drag
    :limit="1"
    :accept="props.fileType"
    :headers="upload.headers"
    :action="upload.url"
    :before-upload="handleFileUploadBefore"
    :on-change="handleEditChange"
    :before-remove="handleRemove"
    :on-success="handleContractFileSuccess"
    :file-list="fileCoverList"
    :auto-upload="false"
  >
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">
      <div>将文件拖到此处，或<em>点击上传</em></div>
      <div style="color: #999">注意：1.文件需小于20M</div>
      <div style="color: #999">
        2. 文件仅支持{{ props.fileType.split(".")[1] }}格式，请优先使用
      </div>
      <div style="color: #999" v-if="props.fileType === '.zip'">
        3、PDF文件内容格式需和模版相同
      </div>
    </div>
  </el-upload>
</template>
<script setup name="ImportTemplatePdf">
import { getToken } from "@/utils/auth";
import { getCurrentInstance, reactive, ref } from "vue";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["update:sourceFileUrl"]);
const props = defineProps({
  sourceFileUrl: String,
  fileName: { type: String, default: "文件.zip" },
  fileType: { type: String, default: ".zip" },
});
const sourceFileUrl = ref({ url: props.sourceFileUrl, name: props.fileName });
//下载
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/letter/message/upload",
});
upload.url = upload.url.replace("appeal", "sign");
const fileCoverList = ref([]);
onMounted(() => {
  props.fileName && props.sourceFileUrl
    ? (fileCoverList.value = [
        { url: props.sourceFileUrl, name: props.fileName },
      ])
    : [];
});

// 文件上传前的处理
const handleFileUploadBefore = (file, fileList) => {
  if (!(file.name.indexOf(props.fileType) > -1)) {
    proxy.$modal.msgWarning(
      "文件类型错误!请上传" + props.fileType.split(".")[1] + "格式文件"
    );
    return false;
  }
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    return false;
  }
};

// 文件列表变化
function handleEditChange(file, fileList) {
  if (!(file.name.indexOf(props.fileType) > -1)) {
    proxy.$modal.msgWarning(
      "文件类型错误!请上传" + props.fileType.split(".")[1] + "格式文件"
    );
    fileList.pop();
    return false;
  }
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20MB!");
    fileList.pop();
    return false;
  }
  if (fileList.length > 0) {
    fileCoverList.value = fileList;
    submitFile();
  }
}

// 删除
function handleRemove() {
  fileCoverList.value = [];
  emits("update:sourceFileUrl", undefined);
  sourceFileUrl.value = undefined;
}

// 上传文件上传成功处理
function handleContractFileSuccess(response, file, fileList) {
  const { code, data, msg } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(msg ? msg : `文件《${file.name}》上传失败！`);
    file.status = "error";
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    sourceFileUrl.value = { url: data.fileUrl[0], name: data.firstName[0] };
    emits("update:sourceFileUrl", data.fileUrl[0]);
    emits("update:firstName", data.firstName[0]);
    props.fileType == ".pdf" && emits("getPage", data.fileUrl[0]);
  }
}

// 上传到服务器
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}
</script>
<style lang="scss" scoped>
:deep(.el-upload-dragger) {
  width: 500px;
  height: 250px;
}
</style>