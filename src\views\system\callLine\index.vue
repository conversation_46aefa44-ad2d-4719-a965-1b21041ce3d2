<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="线路名称" prop="networkName">
        <el-select
          v-model="queryParams.networkName"
          placeholder="请选择线路"
          style="width: 240px"
        >
          <el-option value="普通线路">普通线路</el-option>
          <el-option value="律所线路">律所线路</el-option>
          <el-option value="调解线路">调解线路</el-option>
          <el-option value="白名单线路">白名单线路</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:callLine:add']"
          type="primary"
          plain
          icon="Plus"
          @click="openHanlderNumber('add', null)"
          >添加号码</el-button
        >
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="线路名称"
        align="center"
        key="networkName"
        prop="networkName"
      />
      <el-table-column
        label="主叫(中继)号码"
        align="center"
        key="callFrom"
        prop="callFrom"
      />
      <el-table-column
        label="中继标识"
        align="center"
        key="relaySign"
        prop="relaySign"
      />
      <el-table-column
        label="呼叫前缀"
        align="center"
        key="outCallPrefix"
        prop="outCallPrefix"
      />
      <el-table-column
        label="所属坐席"
        align="center"
        key="sipNumber"
        prop="sipNumber"
      />
      <el-table-column
        label="添加时间"
        align="center"
        key="createdTime"
        prop="createdTime"
      />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:callLine:edit']"
            type="text"
            @click="openHanlderNumber('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasPermi="['system:callLine:del']"
            type="text"
            @click="handleDel(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑号码 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="78px">
        <el-form-item label="线路名称" prop="networkName">
          <el-select
            v-model="form.networkName"
            placeholder="请选择线路"
            style="width: 100%"
          >
            <el-option value="普通线路">普通线路</el-option>
            <el-option value="律所线路">律所线路</el-option>
            <el-option value="调解线路">调解线路</el-option>
            <el-option value="白名单线路">白名单线路</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主叫号码" prop="callFrom">
          <el-input
            v-model="form.callFrom"
            maxlength="30"
            show-word-limit
            type="text"
            placeholder="请输入主叫号码"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="中继标识" prop="relaySign">
          <el-input
            v-model="form.relaySign"
            maxlength="30"
            show-word-limit
            type="text"
            placeholder="请输入中继标识"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="呼叫前缀" prop="outCallPrefix">
          <el-input
            v-model="form.outCallPrefix"
            maxlength="30"
            show-word-limit
            type="text"
            placeholder="请输入呼叫前缀"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="所属坐席" prop="sipNumber">
          <el-select
            v-model="form.sipNumber"
            placeholder="请选择坐席"
            style="width: 100%"
          >
            <el-option
              v-for="item in sipOptions"
              :key="item.id"
              :value="item.sipNumber"
              >{{ item.sipNumber }}</el-option
            >
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="subloading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CallLine">
import {
  callNetworkList,
  callNetworkAdd,
  callNetworkEdit,
  callNetworkDel,
} from "@/api/system/callLine";
import { getUnassignedSipList } from "@/api/system/user";
const { proxy } = getCurrentInstance();

const loading = ref(false);
const total = ref(0);
const dataList = ref([]);

const title = ref("");
const open = ref(false);
const sipOptions = ref([]);
const subloading = ref(false);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    networkName: undefined,
  },
  form: {
    networkName: undefined,
    callFrom: undefined,
    relaySign: undefined,
    outCallPrefix: undefined,
    sipNumber: undefined,
  },
  rules: {
    networkName: [
      { required: true, message: "请输入线路名称！", trigger: "blur" },
    ],
    callFrom: [
      { required: true, message: "请输入主叫号码！", trigger: "blur" },
    ],
    relaySign: [
      { required: true, message: "请输入中继标识！", trigger: "blur" },
    ],
    // outCallPrefix: [
    //   { required: true, message: "请输入呼叫前缀！", trigger: "blur" },
    // ],
    sipNumber: [{ required: true, message: "请选择坐席！", trigger: "blur" }],
  },
});
const { queryParams, form, rules } = toRefs(data);

//获取列表
function getList() {
  loading.value = true;
  callNetworkList(queryParams.value)
    .then((res) => {
      total.value = res.total;
      dataList.value = res.rows;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 打开添加/编辑号码弹窗
function openHanlderNumber(type, row) {
  if (type === "add") {
    title.value = "添加号码";
  } else if (type === "edit") {
    title.value = "编辑号码";
    for (const key in form.value) {
      form.value[key] = row[key] || undefined;
    }
    Object.assign(form.value, { id: row.id });
  }
  open.value = true;
  getUnassignedSipList().then((res) => {
    sipOptions.value = res.data;
  });
}

//提交
function submitForm() {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      subloading.value = true;
      const api = title.value === "添加号码" ? callNetworkAdd : callNetworkEdit;
      console.log(form.value)
      api(form.value).then(() => {
        proxy.$modal.msgSuccess("操作成功！");
        reset();
        open.value = false;
        getList();
      });
    } else {
      proxy.$message.error("请检查输入项！");
    }
    subloading.value = false;
  });
}

//重置角色表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    networkName: undefined,
    callFrom: undefined,
    relaySign: undefined,
    outCallPrefix: undefined,
    sipNumber: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

// 删除操作
function handleDel(row) {
  proxy.$modal
    .confirm("此操作将永久删除该号码, 是否继续?")
    .then(() => {
      return callNetworkDel(row.id);
    })
    .then(() => {
      proxy.$modal.msgSuccess("删除成功！");
      getList();
    });
}
</script>

<style scoped></style>
