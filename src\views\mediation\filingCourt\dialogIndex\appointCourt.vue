<template>
    <el-dialog title="预约开庭" v-model="open" width="750px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-form-item prop="trialLawyer" label="开庭律师">
                        <el-input v-model="form.trialLawyer" style="width:100%" :maxlength="20" placeholder="请输入开庭律师" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="undertakingLawyer" label="承办律师">
                        <el-input v-model="form.undertakingLawyer" style="width:100%" :maxlength="20"
                            placeholder="请输入承办律师" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="trialTime" label="开庭时间">
                        <el-date-picker style="width:100%" v-model="form.trialTime" value-format="YYYY-MM-DD"
                            type="date" placeholder="请选择开庭时间" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="trialCity" label="开庭城市">
                        <el-cascader v-model="form.trialCity" style="width:100%" :props="{ value: 'label' }"
                            :options="areaOptions" placeholder="请输入开庭城市" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="courtId" label="开庭法院">
                        <el-select placeholder="请选择保全法院" style="width:80%" v-model="form.courtId" clearable>
                            <el-option v-for="item in courtOption" :label="item.info" :key="item.code"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="location" label="应到处所">
                        <el-input v-model="form.location" style="width:100%" placeholder="请输入应到处所" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="perkMoney" label="补贴金额">
                        <el-input v-model="form.perkMoney" style="width:100%" placeholder="请输入补贴金额" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="trialMethod" label="开庭方式">
                        <el-select v-model="form.trialMethod" style="width:100%" placeholder="请选择开庭方式">
                            <el-option v-for="item in ['线上', '线下']" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="content" label="服务内容">
                        <el-select v-model="form.content" filterable clearable style="width:100%" placeholder="请选择服务内容">
                            <el-option v-for="item in serveOption" :key="item.code" :label="item.info"
                                :value="item.info" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="trialSum" label="开庭次数">
                        <el-select style="width:100%" v-model="form.trialSum" placeholder="请选择开庭次数">
                            <el-option v-for="item in sessionsNumOption" :key="item.code" :label="item.info"
                                :value="item.info" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="remark" label="备注">
                        <el-input style="width:100%" v-model="form.remark" type="textarea" placeholder="请输入备注"
                            :maxlength="800" show-word-limit :rows="5" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="subimt" type="primary">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { getCourtOptions, municipalGovernment } from '@/api/common/common'
import { insertFilingCourt } from '@/api/mediation/filingCourt'
const props = defineProps({
    getList: { type: Function }
})
const { proxy } = getCurrentInstance()
const open = ref(false)
const areaOptions = ref(undefined)
const loading = ref(false)
const courtOption = ref([])
const serveOption = ref([
    { code: 0, info: '开庭' },
    { code: 1, info: '调解' },
    { code: 2, info: '听证' },
    { code: 3, info: '谈判' },
    { code: 4, info: '签笔录' },
    { code: 5, info: '会见' },
    { code: 6, info: '阅卷' },
    { code: 7, info: '认罪认罚' },
    { code: 8, info: '调查取证' },
])
const sessionsNumOption = ref([
    { code: 0, info: '第一次' },
    { code: 1, info: '第二次' },
    { code: 2, info: '第三次' },
    { code: 3, info: '第四次' },
    { code: 4, info: '第五次' },
])

const data = reactive({
    form: {},
    rules: {
        trialLawyer: [{ required: true, message: '请选择开庭律师', trigger: 'blur' }],
        undertakingLawyer: [{ required: true, message: '请选择承办律师', trigger: 'blur' }],
        trialTime: [{ required: true, message: '请选择开庭时间', trigger: 'blur' }],
        trialCityId: [{ required: true, message: '请输入开庭城市', trigger: 'blur' }],
        courtId: [{ required: true, message: '请输入开庭法院', trigger: 'blur' }],
        location: [{ required: true, message: '请输入应到处所', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)
function subimt() {
    proxy.$refs['formRef'].validate((vaild) => {
        if (vaild) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            reqForm.trialCity = reqForm.trialCity ? reqForm.trialCity?.join('') : ''
            const court = courtOption.value.find(item => item.code == form.value.courtId)
            reqForm.court = court?.info
            reqForm.trialCourt = court?.info
            reqForm.courtId = court?.code
            insertFilingCourt(reqForm).then(res => {
                if (res.code == 200) {
                    props.getList && props.getList()
                    proxy.$modal.msgSuccess('操作成功！')
                    cancel()
                }
            })
        }
    })
}
function openDialog(data) {
    open.value = true
    form.value = { ...data.query, ...data }
}
function cancel() {
    open.value = false
    form.value = {}
}
getMunicipalGovernment()
function getMunicipalGovernment() {
    municipalGovernment().then(res => {
        areaOptions.value = res.data
    })
}

// 获取机构列表
getCourts()
function getCourts() {
    getCourtOptions().then((res) => {
        courtOption.value = res.data
    })
}
defineExpose({ openDialog })
</script>