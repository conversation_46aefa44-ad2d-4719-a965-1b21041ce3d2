<template>
    <div class="app-container">
        <el-radio-group class="mb20" v-model="activeTab">
            <el-radio-button label="0" name="0">调解案件</el-radio-button>
            <!-- <el-radio-button label="1" name="1">网上立案</el-radio-button>
            <el-radio-button label="2" name="2">立案开庭</el-radio-button>
            <el-radio-button label="3" name="3">判决与结果</el-radio-button>
            <el-radio-button label="4" name="4">诉讼执行</el-radio-button>
            <el-radio-button label="5" name="5">诉讼保全</el-radio-button> -->
        </el-radio-group>
        <mediate ref="mediateRef" v-if="activeTab == 0" />
        <online ref="onlineRef" v-if="activeTab == 1" />
        <filling ref="fillingRef" v-if="activeTab == 2" />
        <sentence ref="sentenceRef" v-if="activeTab == 3" />
        <lawsuitExecut ref="lawsuitExecutRef" v-if="activeTab == 4" />
        <lawsuitKeep ref="lawsuitKeepRef" v-if="activeTab == 5" />
    </div>
</template>

<script setup name="AllCase">
import mediate from './tabelComponents/mediate.vue';
import online from './tabelComponents/online.vue';
import filling from './tabelComponents/filling.vue';
import sentence from './tabelComponents/sentence.vue';
import lawsuitExecut from './tabelComponents/lawsuitExecut.vue';
import lawsuitKeep from './tabelComponents/lawsuitKeep.vue';
//全局数据
const { proxy } = getCurrentInstance();
const defaultProps = {
    children: "children",
    label: "name",
};
const activeTab = ref('0')
const activeNode = ref(null)
const filterText = ref(null)
const refs = {
    0: "mediateRef",
    1: "onlineRef",
    2: "fillingRef",
    3: "sentenceRef",
    4: "lawsuitExecutRef",
    5: "lawsuitKeepRef",
}
//节点点击事件
function getNode(node) {
    proxy.$refs[refs[activeTab.value]]?.getList()
}

//机构过滤
const filterNode = (value, data) => {
    if (!value) return true;
    return data.name.indexOf(value) !== -1;
};
watch(() => activeTab.value, () => {
    proxy.$refs[refs[activeTab.value]]?.getList()
}, { immediate: true, deep: true })

watch(() => activeNode.value, () => {
    const treeListDom = document.querySelector('.tree-list')
    if (treeListDom) {
        const treeContentArr = treeListDom.querySelectorAll('.el-tree-node__content')
        treeContentArr.forEach(item => {
            item.style.backgroundColor = '#fff'
        })
    }
    nextTick(() => {
        const treeActiveDom = document.querySelector('.treeActive')
        if (treeActiveDom) {
            treeActiveDom.parentNode.style.backgroundColor = '#ebf5ff';
        }
    })
}, { immediate: true, deep: true })
//搜索部门
watch(filterText, (val) => {
    proxy.$refs["treeRef"].filter(val);
});

</script>

<style lang="scss" scoped>
.title {
    color: rgb(136, 136, 136);
}
</style>