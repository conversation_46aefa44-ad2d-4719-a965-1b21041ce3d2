<template>
    <div>
        <div class="mb10 mt10" style="text-align:right;margin-right:50px">
            <el-button type="primary" @click="handleDownloadMerge()">合并下载</el-button>
            <el-button type="primary">合并打印</el-button>
            <el-button type="primary" :disabled="urls.length == 0"
                @click="handleBatchDownload(`证据材料_${+new Date()}.zip`)">批量下载</el-button>
        </div>
        <el-table :data="dataList" max-height="350px" @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center" />
            <el-table-column label="序号" prop="id" key="id" align="center" />
            <el-table-column label="使用阶段" prop="serviceStage" key="serviceStage" align="center" />
            <el-table-column label="证据类型" prop="evidenceType" key="evidenceType" align="center" />
            <el-table-column label="证据材料名称" prop="firstName" key="firstName" align="center" />
            <el-table-column label="上传人员" prop="founder" key="founder" align="center" />
            <el-table-column label="操作">
                <template #default="{ row }">
                    <el-button type="text" link v-if="isPackage(row.fileUrl)"
                        @click="previewSigntrue(row.fileUrl)">预览</el-button>
                    <el-button type="text" link v-if="row.fileUrl"
                        @click="handleDownload(row.fileUrl, `${row.firstName}.pdf`)">下载</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script setup>
import { selectLogistics } from "@/api/caseDetail/detail";
const fileUrl = 'http://**********:9000/demo/2024/05/13/5d16edd7-61b1-4ce2-9c61-0d0d70cb169b.pdf'
const firstName = `民事起诉状_${+new Date()}.pdf`
const route = useRoute()
const props = defineProps({
    urls: { type: Array, default: [] },
    previewSigntrue: { type: Function },
    handleDownload: { type: Function },
    handleBatchDownload: { type: Function },
    handleSelectionChange: { type: Function },
    isPackage: { type: Function },
})
const dataList = ref([])
const total = ref(44)
//证据材料
getList()
function getList() {
    let req = { caseId: route.params.caseId };
    selectLogistics(req).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    })
}

// 合并下载
function handleDownloadMerge() {
    props.handleDownload(fileUrl, `民事起诉状_${+new Date()}.pdf`)
}
</script>