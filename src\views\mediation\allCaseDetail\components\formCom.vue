<template>
    <div class="list">
        <div v-for="(item, index) in formList" :key="index" :class="`item ${item.class}`">
            <span class="label">{{ item.info }}：</span>
            <span class="value">{{ valueFor(item) }}</span>
        </div>
    </div>
</template>
<script setup>

const { proxy } = getCurrentInstance()
const props = defineProps({
    form: { type: Object, default: {} },
    form2: { type: Object, default: {} },
    formList: { type: Array, default: [] }
})
function valueFor(row) {
    if (props.form[row.code] == 0) {
        props.form[row.code] = String(props.form[row.code])
    }

    if (row.value || row.value == 0) {
        row.value = row.value == 0 ? String(row.value) : row.value
        return row.value ? row.value : '--'
    }
    if (row.isVal) {
        return props.form2[row.code] ? props.form2[row.code] : '--'
    }

    if (row.isPercent) {
        return props.form[row.code] ? `${props.form[row.code]}%` : '--'
    }
    if (row.isNum) {
        return props.form[row.code] ? proxy.numFilter(props.form[row.code]) : '--'
    }
    return props.form[row.code] ? props.form[row.code] : '--'
}
</script>
<style lang="scss" scoped>
.list {
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
    font-size: 14px;

    .item {
        width: 25%;
        line-height: 32px;
        color: #606266;

        .value {
            color: #000;
        }
    }
}

.text-red {
    color: red !important;
    font-weight: bold !important;

    .value {
        color: red !important;
    }
}

.text-blue {
    color: #409eff !important;
    font-weight: bold !important;

    .value {
        color: #409eff !important;
    }
}
</style>