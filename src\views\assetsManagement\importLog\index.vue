<template>
  <div class="app-container">
    <el-radio-group v-model="radioType" @change="resetQuery">
      <el-radio-button label="0" v-hasPermi="['assets:importLog:exportCase']">导入案件</el-radio-button>
      <el-radio-button label="1" v-hasPermi="['assets:importLog:exportPerson']">导入联系人</el-radio-button>
      <el-radio-button label="2" v-hasPermi="['assets:importLog:exportReply']">导入还款计划</el-radio-button>
      <el-radio-button label="3" v-hasPermi="['assets:importLog:exportNote']">导入沟通记录</el-radio-button>
      <el-radio-button label="4" v-hasPermi="['assets:importLog:exportBatch']">导入批量操作</el-radio-button>
      <el-radio-button label="5" v-hasPermi="['assets:importLog:exportData']">导入资料</el-radio-button>
    </el-radio-group>

    <el-form class="form-content mt20" :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="导入时间" style="width: 308px">
        <el-date-picker v-model="queryParams.importTime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item v-if="['4', '5'].indexOf(radioType) < -1" label="导入批次" prop="importBatchNum">
        <el-select v-model="queryParams.importBatchNum" placeholder="请输入或选择" clearable filterable :reserve-keyword="false"
          @focus="getBatchNumList" style="width: 240px">
          <el-option v-for="item in batchNums" :key="item.info" :label="item.info" :value="item.info" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="radioType == '5'" label="压缩包名称" prop="fileName">
        <el-input v-model="queryParams.fileName" placeholder="请输入压缩包名称" />
      </el-form-item>
      <el-form-item v-if="radioType == '5'" label="导入状态" prop="importStart">
        <el-select v-model="queryParams.importStart" placeholder="请输入或选择" clearable filterable :reserve-keyword="false"
          @focus="getAllStateList" style="width: 240px">
          <el-option v-for="item in stateAllArr" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="radioType != '5'" label="导入状态" prop="importStart">
        <el-select v-model="queryParams.importStart" placeholder="请输入或选择" clearable filterable :reserve-keyword="false"
          @focus="getstatesArr" style="width: 240px">
          <el-option v-for="item in statesArr" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="radioType == '0'" label="资产包名称" prop="packageNameList">
        <el-select v-model="packageNameList" placeholder="请输入或选择资产包名称" clearable filterable :reserve-keyword="false"
          multiple collapse-tags collapse-tags-tooltip @change="changePackageName" style="width: 240px">
          <el-option v-for="item in backList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row class="mb8">
      <el-col :span="1.5">
        <div class="hint-flex" style="height: 32px">
          <el-tooltip class="hint" effect="light" placement="bottom-start">
            <div>
              <svg-icon class="hint-item" icon-class="question" />
            </div>
            <template #content>
              <div class="info-tip">
                <el-icon class="info-tip-icon" :size="16"><warning-filled /></el-icon>
                <div>
                  <p>
                    以文件形式导入的数据，在导入后进行导入进度跟踪（对导入的大量数据系统需要一定时间处理，如有问题请联系官方客服）
                  </p>
                </div>
              </div>
            </template>
          </el-tooltip>
        </div>
      </el-col>
      <right-toolbar @queryTable="getList" :columns="columns[radioType]" :types="[2, 3]"></right-toolbar>
    </el-row>

    <!-- 导入案件列表 -->
    <imCase v-if="radioType === '0'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />

    <!-- 导入联系人 -->
    <imContact v-if="radioType === '1'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />

    <!-- 导入还款计划 -->
    <imPlan v-if="radioType === '2'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />

    <!-- 导入沟通记录 -->
    <imUrage v-if="radioType === '3'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />

    <!-- 导入沟通记录 -->
    <imBatch v-if="radioType === '4'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />
    <!-- 导入资料 -->
    <imData v-if="radioType === '5'" :dataList="dataList" :loading="loading" :columns="columns[radioType]" />


    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script setup name="ImportLog">
import imCase from "./table/imCase.vue";
import imContact from "./table/imContact.vue";
import imPlan from "./table/imPlan.vue";
import imUrage from "./table/imUrge.vue";
import imBatch from "./table/imBatch.vue";
import imData from "./table/imData.vue";
import { getPackName } from "@/api/assets/asset/asset";
import { getBatchNums } from "@/api/assets/casemanage";
import {
  getImportState,
  imCaseList,
  imContactList,
  imPlanList,
  imUrgeList,
  imBatchList,
  imDataList,
  getAllState
} from "@/api/assets/importlog";
const { proxy } = getCurrentInstance();
const route = useRoute();

const radioType = ref();
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    importTime: [],
    importBatchNum: undefined,
    importStart: undefined,
    fileName: undefined,
    packageName: undefined
  },
});
const { queryParams } = toRefs(data);
const batchNums = ref([]);
const statesArr = ref([]);
const dataList = ref([]);
const stateAllArr = ref([])
const backList = ref([]);
const packageNameList = ref([]);

const apis = [imCaseList, imContactList, imPlanList, imUrgeList, imBatchList, imDataList];
const columns = ref([
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入批次/文件名称`, visible: true },
    { key: 2, label: `资产包名称`, visible: true },
    { key: 3, label: `导入类型`, visible: true },
    { key: 4, label: `成功数量`, visible: true },
    { key: 5, label: `转让方`, visible: true },
    { key: 6, label: `产品类型`, visible: true },
    { key: 7, label: `操作人`, visible: true },
    { key: 8, label: `导入时间`, visible: true },
    { key: 9, label: `导入状态`, visible: true },
  ],
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入批次/文件名称`, visible: true },
    { key: 2, label: `导入类型`, visible: true },
    { key: 3, label: `成功数量`, visible: true },
    { key: 4, label: `转让方`, visible: true },
    { key: 5, label: `产品类型`, visible: true },
    { key: 6, label: `操作人`, visible: true },
    { key: 7, label: `导入时间`, visible: true },
    { key: 8, label: `导入状态`, visible: true },
  ],
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入文件名称`, visible: true },
    { key: 2, label: `导入类型`, visible: true },
    { key: 3, label: `产品类型【转让方】`, visible: true },
    { key: 4, label: `成功数量`, visible: true },
    { key: 5, label: `操作人`, visible: true },
    { key: 6, label: `导入时间`, visible: true },
    { key: 7, label: `导入状态`, visible: true },
  ],
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入文件名称`, visible: true },
    { key: 2, label: `导入类型`, visible: true },
    { key: 3, label: `产品类型【转让方】`, visible: true },
    { key: 4, label: `成功数量`, visible: true },
    { key: 5, label: `操作人`, visible: true },
    { key: 6, label: `导入时间`, visible: true },
    { key: 7, label: `导入状态`, visible: true },
  ],
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入文件名称`, visible: true },
    { key: 2, label: `导入类型`, visible: true },
    { key: 3, label: `成功数量`, visible: true },
    { key: 4, label: `操作人`, visible: true },
    { key: 5, label: `导入时间`, visible: true },
    { key: 6, label: `导入状态`, visible: true },
  ],
  [
    { key: 0, label: `ID`, visible: true },
    { key: 1, label: `导入压缩包名称`, visible: true },
    { key: 2, label: `导入类型`, visible: true },
    { key: 3, label: `成功数量`, visible: true },
    { key: 4, label: `操作人`, visible: true },
    { key: 5, label: `导入时间`, visible: true },
    { key: 6, label: `导入状态`, visible: true },
  ],
]);

//获取列表
function getList() {
  loading.value = true;
  const reqForm = proxy.addFieldsRange(queryParams.value, ["importTime"])
  apis[radioType.value](reqForm).then((res) => {
      loading.value = false;
      dataList.value = res.rows;
      total.value = res.total;
    }).catch(() => {
      dataList.value = []
      total.value = 0
      loading.value = false;
    });
}

onBeforeMount(() => {
  queryParams.value.importBatchNum = route.query.batch || undefined;
  radioType.value = route.query.type || "0";
  getList();
});

//获取批次下拉
function getBatchNumList() {
  getBatchNums().then((res) => {
    batchNums.value = res.data;
  });
}

// 获取状态（导入资料）下拉
function getAllStateList(params) {
  getAllState().then(res => {
    stateAllArr.value = res.data
  })
}

//获取状态下拉
function getstatesArr() {
  getImportState().then((res) => {
    statesArr.value = res.data;
  });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//获取资产包
function getPackList() {
  getPackName().then(res => {
    backList.value = res.data
  })
}
getPackList();

//切换资产包
function changePackageName() {
  queryParams.value.packageNameList = packageNameList.value?.toString()
}

//重置
function resetQuery() {
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    importTime: [],
    importBatchNum: undefined,
    importStart: undefined,
  };
  packageNameList.value = []
  getList();
}
</script>

<style scoped>
.hint-flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
}

.hint-item {
  font-size: 18px;
  color: #5a5e66;
  cursor: pointer;
}

.info-tip {
  border: unset;
}
</style>
