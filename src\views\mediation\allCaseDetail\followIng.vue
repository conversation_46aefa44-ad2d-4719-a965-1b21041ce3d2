<template>
  <div class="follow-ing">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="108px">
      <el-form-item prop="contactLabel" label="是否可联">
        <el-radio-group v-model="form.contactLabel">
          <el-radio label="可联">可联</el-radio>
          <el-radio label="不可联">不可联</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="wechatLabel" label="是否添加微信">
        <el-radio-group v-model="form.wechatLabel">
          <el-radio label="已加微信">已加微信</el-radio>
          <el-radio label="未加微信">未加微信</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="intentionLabel" label="客户分类">
        <el-radio-group v-model="form.intentionLabel">
          <el-radio label="X">X(已结清客户)</el-radio>
          <el-radio label="S">S(已分期客户)</el-radio>
          <el-radio label="A">A(高意向客户)</el-radio>
          <el-radio label="B">B(中意向客户)</el-radio>
          <el-radio label="C">C(低意向客户)</el-radio>
          <el-radio label="D">D(放弃客户)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="contactInfoLabel" label="联系情况">
        <el-checkbox-group v-model="form.contactInfoLabel">
          <el-checkbox label="普通线路联系">普通线路联系</el-checkbox>
          <el-checkbox label="律所联系路线">律所联系路线</el-checkbox>
          <el-checkbox label="调解联系路线">调解联系路线</el-checkbox>
          <el-checkbox label="白名单联系路线">白名单联系路线</el-checkbox>
          <el-checkbox label="已发短信">已发短信</el-checkbox>
          <el-checkbox label="失联">失联</el-checkbox>
          <el-checkbox label="正在协商中">正在协商中</el-checkbox>
          <el-checkbox label="敏感客户">敏感客户</el-checkbox>
          <el-checkbox label="1">新案</el-checkbox>
          <el-checkbox label="2">留案</el-checkbox>
          <el-checkbox label="3">轮换</el-checkbox>
          <el-checkbox label="预成交">预成交</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item prop="caseAttribute" label="案件属性">
        <el-checkbox-group v-model="form.caseAttribute">
          <el-checkbox label="1">新案</el-checkbox>
          <el-checkbox label="2">留案</el-checkbox>
          <el-checkbox label="3">轮换</el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';

const { proxy } = getCurrentInstance();
const store = useStore();

const form = ref({
  contactLabel: undefined,
  wechatLabel: undefined,
  intentionLabel: undefined,
  contactInfoLabel: [],
  caseAttribute: [],
})
const rules = ref({
  contactLabel: [
    { required: true, message: '请选择是否可联', trigger: 'change' },
  ],
  wechatLabel: [
    { required: true, message: '请选择是否添加微信', trigger: 'change' },
  ],
  intentionLabel: [
    { required: true, message: '请选择客户分类', trigger: 'change' },
  ],
})

onMounted(() => {
  store.commit('allCaseDetail/SET_FOLLOWING_FORM_REF', proxy.$refs.formRef)
})

// 处理联系情况数据（选项中有两种不同类型数据，需要区分）
function handleContactInfoLabelData(data) {
  return new Promise((resolve, reject) => {
    let reqForm = JSON.parse(JSON.stringify(data))
    if (reqForm.contactInfoLabel.length > 0) {
      // 判断contactInfoLabel中是否有 1、2、3 三个选项，有的话就添加到caseAttribute中并删除
      for (let i = 0; i < reqForm.contactInfoLabel.length; i++) {
        const item = reqForm.contactInfoLabel[i];
        if (item === '1' || item === '2' || item === '3') {
          reqForm.caseAttribute.push(item)
          reqForm.contactInfoLabel.splice(i, 1)
          i--;
        }
      }
    }
    resolve(reqForm)
  })
}

// 监听表单所有值变化，有变化就保存到store中
watch(() => form, (newval) => {
  handleContactInfoLabelData(newval.value).then((reqForm) => {
    store.commit('allCaseDetail/SET_FOLLOWING_FORM', reqForm)
  })
}, { deep: true })
watch(() => store.state.allCaseDetail.score, (newVal) => {
  if (newVal === undefined) {
    form.value.intentionLabel = undefined
  }
  if (newVal < 1) {
    form.value.intentionLabel = 'D'
  }
  if (newVal > 0 && newVal <= 3) {
    form.value.intentionLabel = 'C'
  }
  if (newVal > 3 && newVal <= 6) {
    form.value.intentionLabel = 'B'
  }
  if (newVal > 6) {
    form.value.intentionLabel = 'A'
  }
}, { deep: true, immediate: true })
</script>

<style scoped>
.follow-ing {
  padding: 10px;
  border: 1px solid #eee;
}
</style>
