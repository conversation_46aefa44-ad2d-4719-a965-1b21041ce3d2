<template>
  <div class="caseinfo-wrap ">
    <el-tabs v-model="activeName" class="pl20" @tab-click="handleClick">
      <el-tab-pane v-for="(item) in filterTabList" :label="item.label" :key="item.label" :name="item.label" />
      <el-tab-pane label="公共记录" name="公共记录" />
    </el-tabs>
    <el-radio-group class="pl20 pb15" v-model="recordType">
      <el-radio-button v-for="item in childTab" :key="item.prop" :label="item.prop">
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <tiaojieVue :params="params" ref="tiaojieRef" v-if="recordType == '调诉记录'" />
    <keepVue :params="params" ref="keepRef" v-if="recordType == '保全记录'" />
    <chargeVue :params="params" ref="chargeRef" v-if="recordType === '收费记录'" :handleDownload="handleDownload" />
    <urgeVue :params="params" ref="urgeRef" v-if="recordType == '沟通记录'" />
    <collectionRecord :params="params" ref="collectionRecordRef" v-if="recordType == '催收记录'" />
    <repayVue :params="params" ref="repayRef" v-if="recordType == '回款记录'" />
    <helpUrgeVue :params="params" ref="helpUrgeRef" v-if="recordType == '协催记录'" />
    <reductionVue :params="params" ref="reductionRef" v-if="recordType == '减免记录'" />
    <lawsuitVue :params="params" ref="lawsuitRef" v-if="recordType == '诉讼记录'" />
    <noteVue :params="params" ref="noteRef" v-if="recordType == '便签记录'" />
    <complaintVue :params="params" ref="complaintRef" v-if="recordType == '投诉记录'" />
    <foreignVue :params="params" ref="foreignRef" v-if="recordType == '外访记录'" />
    <logisticsVue :params="params" ref="logisticsRef" v-if="recordType == '物流记录'" />
    <executeVue :params="params" ref="executeRef" v-if="recordType == '执行记录'" />
    <tuningVue :params="params" ref="tuningRef" v-if="recordType == '调执记录'" />
  </div>
</template>

<script setup>
import keepVue from "./recordTable/keep"
import tiaojieVue from './recordTable/tiaojie';
import collectionRecord from './recordTable/collectionRecord';
import chargeVue from './recordTable/charge';
import urgeVue from "./recordTable/urge.vue";
import repayVue from "./recordTable/repay.vue";
import helpUrgeVue from "./recordTable/helpUrge.vue";
import reductionVue from "./recordTable/reduction.vue";
import lawsuitVue from "./recordTable/lawsuit.vue";
import noteVue from "./recordTable/note.vue";
import complaintVue from "./recordTable/complaint.vue";
import foreignVue from "./recordTable/foreign.vue";
import logisticsVue from "./recordTable/logistics.vue";
import executeVue from "./recordTable/execute.vue";
import tuningVue from "./recordTable/tuning.vue";
import { selectWithTab } from "@/api/caseDetail/records";
const { proxy } = getCurrentInstance();
const route = useRoute();
const loading = ref(false);
const listInfo = ref(undefined);
const recordType = ref(undefined);
const activeName = ref('')
const filterTabList = ref([])
const tabList = ref([
  {
    label: "催收记录",
    prop: { webSide: 1 },
    children: [
      { label: '催收记录', prop: '催收记录' },
      { label: '协催记录', prop: '协催记录' },
      { label: '外访记录', prop: '外访记录' },
    ]
  },
  {
    label: "调执记录",
    prop: { webSide: 3 },
    children: [
      { label: '调执记录', prop: '调执记录' },
      { label: '保全记录', prop: '保全记录', webSide: 3 },
      { label: '收费记录', prop: '收费记录', webSide: 3 },
      { label: '沟通记录', prop: '沟通记录' },
      { label: '物流记录', prop: '物流记录' },
      { label: '执行记录', prop: '执行记录' },
    ]
  },
  {
    label: "调诉执记录",
    prop: { webSide: 3 },
    children: [
      { label: '调诉执记录', prop: '调诉记录' },
      { label: '保全记录', prop: '保全记录', webSide: 3 },
      { label: '收费记录', prop: '收费记录', webSide: 3 },
      { label: '沟通记录', prop: '沟通记录' },
      { label: '物流记录', prop: '物流记录' },
      { label: '执行记录', prop: '执行记录' },
    ]
  },
  {
    label: "公共记录",
    children: [
      { label: '回款记录', prop: '回款记录' },
      { label: '减免记录', prop: '减免记录' },
      { label: '投诉记录', prop: '投诉记录' },
      { label: '便签记录', prop: '便签记录' },
    ]
  },
])
//下载
function handleDownload(fileUrl, fileName) {
  // ie和浏览器兼容模式会有问题，可以用下面代码调试。
  try {
    exportFile(fileUrl, fileName) // 调用方式
  } catch (err) {
    // 兼容模式下，IE
    const exportBlob = new Blob([data]);
    if (navigator.userAgent.indexOf('Trident') > -1) {
      window.navigator.msSaveBlob(data, fileName);
    } else {
      exportFile(fileUrl, fileName) // 调用方式
    }
  };
}
function exportFile(data, fileName) {
  // 地址不存在时，禁止操作
  if (!data) return;
  loading.value = true
  proxy.$modal.notify('正在下载中...')
  // 下载文件并保存到本地
  const callback = (data) => {
    // 创建a标签，使用 html5 download 属性下载，
    const link = document.createElement('a');
    // 创建url对象
    const objectUrl = window.URL.createObjectURL(new Blob([data]));
    link.style.display = 'none';
    link.href = objectUrl;
    // 自定义文件名称， fileName
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    // 适当释放url
    window.URL.revokeObjectURL(objectUrl);
  };
  // 把接口返回的url地址转换为 blob
  const xhr = new XMLHttpRequest();
  xhr.open('get', data, true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    // 返回文件流，进行下载处理
    callback(xhr.response);
    proxy.$modal.msgSuccess('操作成功！')
    loading.value = false
  };
  xhr.send(); // 不要忘记发送
};
onMounted(() => {
  listInfo.value = JSON.parse(localStorage.getItem(`searchInfo/${route.params.caseId}`))
})
getTabList()
function getTabList() {
  filterTabList.value = []
  selectWithTab({ caseId: route.params.caseId }).then(res => {
    res.data.forEach(i1 => {
      const tabObj = tabList.value.find(i2 => i2.label == i1.info)
      if (tabObj) {
        filterTabList.value.push(tabObj)
      }
    });
    activeName.value = res.data[0].info
    nextTick(() => {
      recordType.value = filterTabList.value.find(item => item.label == activeName.value).children[0].prop
    })
  })
}

function getReocrdList() {
  const reocrdProxy = {
    调解记录: 'tiaojieRef',
    保全记录: 'keepRef',
    收费记录: 'chargeRef',
    沟通记录: 'urgeRef',
    催收记录: 'collectionRecordRef',
    回款记录: 'repayRef',
    协催记录: 'helpUrgeRef',
    减免记录: 'reductionRef',
    诉讼记录: 'lawsuitRef',
    便签记录: 'noteRef',
    投诉记录: 'complaintRef',
    外访记录: 'foreignRef',
    物流记录: 'logisticsRef',
    调执记录: 'tuningRef',
  }
  proxy.$refs[reocrdProxy[recordType.value]]?.getList()
}
const childTab = computed(() => {
  const arr = tabList.value.find(item => item.label == activeName.value)?.children || []
  recordType.value = arr[0]?.prop
  return arr
})

const params = computed(() => {
  const obj = tabList.value.find(item => item.label == activeName.value)?.prop || {}
  return obj
})
defineExpose({ getReocrdList })
</script>

<style scoped>
:deep(.pagination-container .el-pagination) {
  position: unset;
  float: right;
}
</style>
