<template>
    <el-dialog title="债权总金额设置" v-model="open" width="750px" :before-close="cancel" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
            <el-form-item label="计息方式" prop="entrustMoneyId">
                <el-select
                 v-model="form.entrustMoneyId" 
                 placeholder="请选择" 
                 style="width:240px">
                    <el-option v-for="item in formulas" :key="item.code" :label="item.info" :value="item.code"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="loading"  @click="submit">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
    import { entrustMoneyOptions } from '@/api/system/tactic'
    import { updateEntrustMoney } from '@/api/assets/asset/asset'
    const emit = defineEmits(["queryList"]);
    const { proxy } = getCurrentInstance();
    const open = ref(false);
    const loading = ref(false);
    const data = reactive({
        form: {
            id: undefined,
            entrustMoneyId: undefined
        },
        rules: {
            entrustMoneyId: [{ required: true, message: '请选择债权总金额公式！', trigger: 'change'}],
        }
    })
    const { form , rules } = toRefs(data);

    const formulas = ref([]);

    //打开弹窗
    function opendialog(id,entrustMoneyId) {
        entrustMoneyOptions().then(res => {
            formulas.value = res.data;
        })
        form.value.id = id;
        form.value.entrustMoneyId = entrustMoneyId?.toString()
        console.log('form.value.id', form.value.id)
        open.value = true
    }

    //提交
    function submit() {
        loading.value = true;
        proxy.$refs['formRef'].validate(valid => {
            if (valid) {
                updateEntrustMoney(form.value).then(res => {
                    proxy.$modal.msgSuccess('设置成功！')
                    emit("queryList");
                    loading.value = false;
                    cancel()
                }).catch(() => {
                    loading.value = false;
                })
            } else {
                loading.value = false;
            }
        })
    }

    //重置
    function reset() {
        proxy.resetForm('formRef');
        form.value = {
            id: undefined,
            entrustMoneyId: undefined
        }
    }

    //取消
    function cancel() {
        reset()
        open.value = false
    }

    defineExpose({
        opendialog
    })
</script>

<style scoped>
   
</style>