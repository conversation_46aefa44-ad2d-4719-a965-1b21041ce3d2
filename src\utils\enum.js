// 排序字段
export const orderEnum = { ascending: 1, descending: 2 }

// 是、否
export const isNoEnum = { 0: '否', 1: '是' }

// 结案方式
export const closeWayEnum = { 0: '执行完毕', 1: '不予执行', 2: '驳回申请' }

// 寄送方式
export const sendEnum = { 0: '待寄送', 1: '运输中', 2: '已送达', 3: '已拒收' }

// 物流状态
export const logisticsStatusEnum = { 0: '在途', 1: '揽收', 2: '疑难', 3: '签收', 4: '退签', 5: '派件', 6: '退回', 7: '转投', 10: '待清关', 11: '清关中', 12: '已清关', 13: '清关异常', 14: '拒签' }

// 呼叫类型 callOutManual=直拨,clickCallOut=点呼,inbound=呼入,outbound=呼出，newauto=预测式外呼
export const callOutEnum = { callOutManual: '直拨', clickCallOut: '点呼', outbound: '呼出', inbound: '呼入', newauto: '预测式外呼' }

// 签章状态
export const signStatusEnum = { 1: '已签章', 2: '签章失败', null: '未签章' }

// 审核状态
export const auditStatusEnum = { 0: "待审核", 1: "审核中", 2: "已通过", 3: "未通过" }

// 0-待审核,1-审核中，2-已通过,3-未通过,4-已撤销,5-已作废 当前审批审核状态
export const nowAuditStatusEnum = { 0: "待审核", 1: "审核中", 2: "已通过", 3: "未通过", 4: "已撤销", 5: "已作废" }

// 案件类型
export const pageTypeEnum = {
    exeOfPaybacks: '回款登记',
    filingCourt: '立案开庭',
    iegalPreservation: '诉讼保全',
    judgmentResult: '判决与结果',
    lawsuitCarry: '诉讼执行',
    mediationTeam: '调诉团队',
    onlineFiling: '网上立案',
    phoneMediation: '电话调解',
    mediateCarry: '调解执行',
}

// 工作手机呼叫类型
export const workPhoneCallOutEnum = { 0: '呼出', 1: '呼入' }